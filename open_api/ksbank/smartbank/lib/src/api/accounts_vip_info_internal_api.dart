//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:dio/dio.dart';

import 'package:ksbank_api_smartbank/src/api_util.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_account_vip_info_list_response.dart';

class AccountsVipInfoInternalApi {
  final Dio _dio;

  final Serializers _serializers;

  const AccountsVipInfoInternalApi(this._dio, this._serializers);

  /// Lấy danh sách thông tin tài khoản VIP
  ///
  ///
  /// Parameters:
  /// * [fromDate] - fromDate: từ ngày, format dd/MM/yyyy
  /// * [toDate] - toDate: đến ngày, format dd/MM/yyyy
  /// * [branch] - branch: Đơn vị TKSĐ
  /// * [managerUnit] - managerUnit: đơn vị người quản lý
  /// * [vipGroup] - vipGroup: nhóm VIP
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponseGetAccountVipInfoListResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponseGetAccountVipInfoListResponse>>
      getAccountVipInfo({
    String? fromDate,
    String? toDate,
    String? branch,
    String? managerUnit,
    String? vipGroup,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/api/accountVipInfo/internal/v1';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'http',
            'scheme': 'bearer',
            'name': 'Authorization',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (fromDate != null)
        r'fromDate': encodeQueryParameter(
            _serializers, fromDate, const FullType(String)),
      if (toDate != null)
        r'toDate':
            encodeQueryParameter(_serializers, toDate, const FullType(String)),
      if (branch != null)
        r'branch':
            encodeQueryParameter(_serializers, branch, const FullType(String)),
      if (managerUnit != null)
        r'managerUnit': encodeQueryParameter(
            _serializers, managerUnit, const FullType(String)),
      if (vipGroup != null)
        r'vipGroup': encodeQueryParameter(
            _serializers, vipGroup, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponseGetAccountVipInfoListResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponseGetAccountVipInfoListResponse),
            ) as BaseResponseGetAccountVipInfoListResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponseGetAccountVipInfoListResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }
}
