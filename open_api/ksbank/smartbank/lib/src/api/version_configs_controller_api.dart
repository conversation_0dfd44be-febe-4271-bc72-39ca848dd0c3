//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:dio/dio.dart';

import 'package:ksbank_api_smartbank/src/api_util.dart';
import 'package:ksbank_api_smartbank/src/model/base_response_get_version_configs_response.dart';

class VersionConfigsControllerApi {
  final Dio _dio;

  final Serializers _serializers;

  const VersionConfigsControllerApi(this._dio, this._serializers);

  /// L<PERSON>y danh sách VersionConfigs
  /// L<PERSON>y danh sách VersionConfigs
  ///
  /// Parameters:
  /// * [type]
  /// * [phone]
  /// * [platform]
  /// * [version]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponseGetVersionConfigsResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponseGetVersionConfigsResponse>> getVersionConfigs({
    required String type,
    required String phone,
    String? platform,
    String? version,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/versionConfigs/v1';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'http',
            'scheme': 'bearer',
            'name': 'Authorization',
          },
        ],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'type': encodeQueryParameter(_serializers, type, const FullType(String)),
      if (platform != null)
        r'platform': encodeQueryParameter(
            _serializers, platform, const FullType(String)),
      if (version != null)
        r'version':
            encodeQueryParameter(_serializers, version, const FullType(String)),
      r'phone':
          encodeQueryParameter(_serializers, phone, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponseGetVersionConfigsResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponseGetVersionConfigsResponse),
            ) as BaseResponseGetVersionConfigsResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponseGetVersionConfigsResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }
}
