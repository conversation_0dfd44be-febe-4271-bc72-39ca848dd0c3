// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_logwork_by_period_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserLogworkByPeriodInfo extends UserLogworkByPeriodInfo {
  @override
  final SalaryPeriodInfo? period;
  @override
  final double? realWorkDays;
  @override
  final double? realDaysOff;
  @override
  final double? totalWorkDays;
  @override
  final int? lastUpdated;
  @override
  final BuiltList<UserLogworkItem>? logwork;

  factory _$UserLogworkByPeriodInfo(
          [void Function(UserLogworkByPeriodInfoBuilder)? updates]) =>
      (UserLogworkByPeriodInfoBuilder()..update(updates))._build();

  _$UserLogworkByPeriodInfo._(
      {this.period,
      this.realWorkDays,
      this.realDaysOff,
      this.totalWorkDays,
      this.lastUpdated,
      this.logwork})
      : super._();
  @override
  UserLogworkByPeriodInfo rebuild(
          void Function(UserLogworkByPeriodInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserLogworkByPeriodInfoBuilder toBuilder() =>
      UserLogworkByPeriodInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserLogworkByPeriodInfo &&
        period == other.period &&
        realWorkDays == other.realWorkDays &&
        realDaysOff == other.realDaysOff &&
        totalWorkDays == other.totalWorkDays &&
        lastUpdated == other.lastUpdated &&
        logwork == other.logwork;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, period.hashCode);
    _$hash = $jc(_$hash, realWorkDays.hashCode);
    _$hash = $jc(_$hash, realDaysOff.hashCode);
    _$hash = $jc(_$hash, totalWorkDays.hashCode);
    _$hash = $jc(_$hash, lastUpdated.hashCode);
    _$hash = $jc(_$hash, logwork.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserLogworkByPeriodInfo')
          ..add('period', period)
          ..add('realWorkDays', realWorkDays)
          ..add('realDaysOff', realDaysOff)
          ..add('totalWorkDays', totalWorkDays)
          ..add('lastUpdated', lastUpdated)
          ..add('logwork', logwork))
        .toString();
  }
}

class UserLogworkByPeriodInfoBuilder
    implements
        Builder<UserLogworkByPeriodInfo, UserLogworkByPeriodInfoBuilder> {
  _$UserLogworkByPeriodInfo? _$v;

  SalaryPeriodInfoBuilder? _period;
  SalaryPeriodInfoBuilder get period =>
      _$this._period ??= SalaryPeriodInfoBuilder();
  set period(SalaryPeriodInfoBuilder? period) => _$this._period = period;

  double? _realWorkDays;
  double? get realWorkDays => _$this._realWorkDays;
  set realWorkDays(double? realWorkDays) => _$this._realWorkDays = realWorkDays;

  double? _realDaysOff;
  double? get realDaysOff => _$this._realDaysOff;
  set realDaysOff(double? realDaysOff) => _$this._realDaysOff = realDaysOff;

  double? _totalWorkDays;
  double? get totalWorkDays => _$this._totalWorkDays;
  set totalWorkDays(double? totalWorkDays) =>
      _$this._totalWorkDays = totalWorkDays;

  int? _lastUpdated;
  int? get lastUpdated => _$this._lastUpdated;
  set lastUpdated(int? lastUpdated) => _$this._lastUpdated = lastUpdated;

  ListBuilder<UserLogworkItem>? _logwork;
  ListBuilder<UserLogworkItem> get logwork =>
      _$this._logwork ??= ListBuilder<UserLogworkItem>();
  set logwork(ListBuilder<UserLogworkItem>? logwork) =>
      _$this._logwork = logwork;

  UserLogworkByPeriodInfoBuilder() {
    UserLogworkByPeriodInfo._defaults(this);
  }

  UserLogworkByPeriodInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _period = $v.period?.toBuilder();
      _realWorkDays = $v.realWorkDays;
      _realDaysOff = $v.realDaysOff;
      _totalWorkDays = $v.totalWorkDays;
      _lastUpdated = $v.lastUpdated;
      _logwork = $v.logwork?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserLogworkByPeriodInfo other) {
    _$v = other as _$UserLogworkByPeriodInfo;
  }

  @override
  void update(void Function(UserLogworkByPeriodInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserLogworkByPeriodInfo build() => _build();

  _$UserLogworkByPeriodInfo _build() {
    _$UserLogworkByPeriodInfo _$result;
    try {
      _$result = _$v ??
          _$UserLogworkByPeriodInfo._(
            period: _period?.build(),
            realWorkDays: realWorkDays,
            realDaysOff: realDaysOff,
            totalWorkDays: totalWorkDays,
            lastUpdated: lastUpdated,
            logwork: _logwork?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'period';
        _period?.build();

        _$failedField = 'logwork';
        _logwork?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'UserLogworkByPeriodInfo', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
