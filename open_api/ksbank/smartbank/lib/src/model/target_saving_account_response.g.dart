// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'target_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TargetSavingAccountResponse extends TargetSavingAccountResponse {
  @override
  final String? cifNo;
  @override
  final String? accountNo;
  @override
  final String? accountName;
  @override
  final String? alias;
  @override
  final double? rate;
  @override
  final num? targetAmount;
  @override
  final num? availableBalance;
  @override
  final num? accountBalance;
  @override
  final int? accountStatusId;
  @override
  final String? imageUrl;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final num? currentAmount;
  @override
  final num? anticipatedInterest;
  @override
  final num? amountRateCompletion;

  factory _$TargetSavingAccountResponse(
          [void Function(TargetSavingAccountResponseBuilder)? updates]) =>
      (TargetSavingAccountResponseBuilder()..update(updates))._build();

  _$TargetSavingAccountResponse._(
      {this.cifNo,
      this.accountNo,
      this.accountName,
      this.alias,
      this.rate,
      this.targetAmount,
      this.availableBalance,
      this.accountBalance,
      this.accountStatusId,
      this.imageUrl,
      this.contractDate,
      this.dueDate,
      this.currentAmount,
      this.anticipatedInterest,
      this.amountRateCompletion})
      : super._();
  @override
  TargetSavingAccountResponse rebuild(
          void Function(TargetSavingAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TargetSavingAccountResponseBuilder toBuilder() =>
      TargetSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TargetSavingAccountResponse &&
        cifNo == other.cifNo &&
        accountNo == other.accountNo &&
        accountName == other.accountName &&
        alias == other.alias &&
        rate == other.rate &&
        targetAmount == other.targetAmount &&
        availableBalance == other.availableBalance &&
        accountBalance == other.accountBalance &&
        accountStatusId == other.accountStatusId &&
        imageUrl == other.imageUrl &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        currentAmount == other.currentAmount &&
        anticipatedInterest == other.anticipatedInterest &&
        amountRateCompletion == other.amountRateCompletion;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, alias.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, targetAmount.hashCode);
    _$hash = $jc(_$hash, availableBalance.hashCode);
    _$hash = $jc(_$hash, accountBalance.hashCode);
    _$hash = $jc(_$hash, accountStatusId.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, currentAmount.hashCode);
    _$hash = $jc(_$hash, anticipatedInterest.hashCode);
    _$hash = $jc(_$hash, amountRateCompletion.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TargetSavingAccountResponse')
          ..add('cifNo', cifNo)
          ..add('accountNo', accountNo)
          ..add('accountName', accountName)
          ..add('alias', alias)
          ..add('rate', rate)
          ..add('targetAmount', targetAmount)
          ..add('availableBalance', availableBalance)
          ..add('accountBalance', accountBalance)
          ..add('accountStatusId', accountStatusId)
          ..add('imageUrl', imageUrl)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('currentAmount', currentAmount)
          ..add('anticipatedInterest', anticipatedInterest)
          ..add('amountRateCompletion', amountRateCompletion))
        .toString();
  }
}

class TargetSavingAccountResponseBuilder
    implements
        Builder<TargetSavingAccountResponse,
            TargetSavingAccountResponseBuilder> {
  _$TargetSavingAccountResponse? _$v;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _alias;
  String? get alias => _$this._alias;
  set alias(String? alias) => _$this._alias = alias;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  num? _targetAmount;
  num? get targetAmount => _$this._targetAmount;
  set targetAmount(num? targetAmount) => _$this._targetAmount = targetAmount;

  num? _availableBalance;
  num? get availableBalance => _$this._availableBalance;
  set availableBalance(num? availableBalance) =>
      _$this._availableBalance = availableBalance;

  num? _accountBalance;
  num? get accountBalance => _$this._accountBalance;
  set accountBalance(num? accountBalance) =>
      _$this._accountBalance = accountBalance;

  int? _accountStatusId;
  int? get accountStatusId => _$this._accountStatusId;
  set accountStatusId(int? accountStatusId) =>
      _$this._accountStatusId = accountStatusId;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  num? _currentAmount;
  num? get currentAmount => _$this._currentAmount;
  set currentAmount(num? currentAmount) =>
      _$this._currentAmount = currentAmount;

  num? _anticipatedInterest;
  num? get anticipatedInterest => _$this._anticipatedInterest;
  set anticipatedInterest(num? anticipatedInterest) =>
      _$this._anticipatedInterest = anticipatedInterest;

  num? _amountRateCompletion;
  num? get amountRateCompletion => _$this._amountRateCompletion;
  set amountRateCompletion(num? amountRateCompletion) =>
      _$this._amountRateCompletion = amountRateCompletion;

  TargetSavingAccountResponseBuilder() {
    TargetSavingAccountResponse._defaults(this);
  }

  TargetSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNo = $v.cifNo;
      _accountNo = $v.accountNo;
      _accountName = $v.accountName;
      _alias = $v.alias;
      _rate = $v.rate;
      _targetAmount = $v.targetAmount;
      _availableBalance = $v.availableBalance;
      _accountBalance = $v.accountBalance;
      _accountStatusId = $v.accountStatusId;
      _imageUrl = $v.imageUrl;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _currentAmount = $v.currentAmount;
      _anticipatedInterest = $v.anticipatedInterest;
      _amountRateCompletion = $v.amountRateCompletion;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TargetSavingAccountResponse other) {
    _$v = other as _$TargetSavingAccountResponse;
  }

  @override
  void update(void Function(TargetSavingAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TargetSavingAccountResponse build() => _build();

  _$TargetSavingAccountResponse _build() {
    final _$result = _$v ??
        _$TargetSavingAccountResponse._(
          cifNo: cifNo,
          accountNo: accountNo,
          accountName: accountName,
          alias: alias,
          rate: rate,
          targetAmount: targetAmount,
          availableBalance: availableBalance,
          accountBalance: accountBalance,
          accountStatusId: accountStatusId,
          imageUrl: imageUrl,
          contractDate: contractDate,
          dueDate: dueDate,
          currentAmount: currentAmount,
          anticipatedInterest: anticipatedInterest,
          amountRateCompletion: amountRateCompletion,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
