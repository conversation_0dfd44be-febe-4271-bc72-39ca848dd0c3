// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_target_saving_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateTargetSavingAccountRequest
    extends UpdateTargetSavingAccountRequest {
  @override
  final String? imageUrl;
  @override
  final String? name;
  @override
  final int? isAddFromTransaction;
  @override
  final num? roundingCoefficient;

  factory _$UpdateTargetSavingAccountRequest(
          [void Function(UpdateTargetSavingAccountRequestBuilder)? updates]) =>
      (UpdateTargetSavingAccountRequestBuilder()..update(updates))._build();

  _$UpdateTargetSavingAccountRequest._(
      {this.imageUrl,
      this.name,
      this.isAddFromTransaction,
      this.roundingCoefficient})
      : super._();
  @override
  UpdateTargetSavingAccountRequest rebuild(
          void Function(UpdateTargetSavingAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateTargetSavingAccountRequestBuilder toBuilder() =>
      UpdateTargetSavingAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateTargetSavingAccountRequest &&
        imageUrl == other.imageUrl &&
        name == other.name &&
        isAddFromTransaction == other.isAddFromTransaction &&
        roundingCoefficient == other.roundingCoefficient;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, isAddFromTransaction.hashCode);
    _$hash = $jc(_$hash, roundingCoefficient.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateTargetSavingAccountRequest')
          ..add('imageUrl', imageUrl)
          ..add('name', name)
          ..add('isAddFromTransaction', isAddFromTransaction)
          ..add('roundingCoefficient', roundingCoefficient))
        .toString();
  }
}

class UpdateTargetSavingAccountRequestBuilder
    implements
        Builder<UpdateTargetSavingAccountRequest,
            UpdateTargetSavingAccountRequestBuilder> {
  _$UpdateTargetSavingAccountRequest? _$v;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  int? _isAddFromTransaction;
  int? get isAddFromTransaction => _$this._isAddFromTransaction;
  set isAddFromTransaction(int? isAddFromTransaction) =>
      _$this._isAddFromTransaction = isAddFromTransaction;

  num? _roundingCoefficient;
  num? get roundingCoefficient => _$this._roundingCoefficient;
  set roundingCoefficient(num? roundingCoefficient) =>
      _$this._roundingCoefficient = roundingCoefficient;

  UpdateTargetSavingAccountRequestBuilder() {
    UpdateTargetSavingAccountRequest._defaults(this);
  }

  UpdateTargetSavingAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _imageUrl = $v.imageUrl;
      _name = $v.name;
      _isAddFromTransaction = $v.isAddFromTransaction;
      _roundingCoefficient = $v.roundingCoefficient;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateTargetSavingAccountRequest other) {
    _$v = other as _$UpdateTargetSavingAccountRequest;
  }

  @override
  void update(void Function(UpdateTargetSavingAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateTargetSavingAccountRequest build() => _build();

  _$UpdateTargetSavingAccountRequest _build() {
    final _$result = _$v ??
        _$UpdateTargetSavingAccountRequest._(
          imageUrl: imageUrl,
          name: name,
          isAddFromTransaction: isAddFromTransaction,
          roundingCoefficient: roundingCoefficient,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
