// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_info_qr_code_napas_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferInfoQrCodeNapasResponse
    extends TransferInfoQrCodeNapasResponse {
  @override
  final String? accountNo;
  @override
  final String? bankCode;
  @override
  final String? bankIdNapas;
  @override
  final String? bankName;
  @override
  final String? bankShortName;
  @override
  final String? bankImageType;
  @override
  final int? bankCitad;
  @override
  final num? amount;
  @override
  final String? description;
  @override
  final String? url;
  @override
  final BankCodeDto? bankInfo;

  factory _$TransferInfoQrCodeNapasResponse(
          [void Function(TransferInfoQrCodeNapasResponseBuilder)? updates]) =>
      (TransferInfoQrCodeNapasResponseBuilder()..update(updates))._build();

  _$TransferInfoQrCodeNapasResponse._(
      {this.accountNo,
      this.bankCode,
      this.bankIdNapas,
      this.bankName,
      this.bankShortName,
      this.bankImageType,
      this.bankCitad,
      this.amount,
      this.description,
      this.url,
      this.bankInfo})
      : super._();
  @override
  TransferInfoQrCodeNapasResponse rebuild(
          void Function(TransferInfoQrCodeNapasResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferInfoQrCodeNapasResponseBuilder toBuilder() =>
      TransferInfoQrCodeNapasResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferInfoQrCodeNapasResponse &&
        accountNo == other.accountNo &&
        bankCode == other.bankCode &&
        bankIdNapas == other.bankIdNapas &&
        bankName == other.bankName &&
        bankShortName == other.bankShortName &&
        bankImageType == other.bankImageType &&
        bankCitad == other.bankCitad &&
        amount == other.amount &&
        description == other.description &&
        url == other.url &&
        bankInfo == other.bankInfo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, bankCode.hashCode);
    _$hash = $jc(_$hash, bankIdNapas.hashCode);
    _$hash = $jc(_$hash, bankName.hashCode);
    _$hash = $jc(_$hash, bankShortName.hashCode);
    _$hash = $jc(_$hash, bankImageType.hashCode);
    _$hash = $jc(_$hash, bankCitad.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, url.hashCode);
    _$hash = $jc(_$hash, bankInfo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferInfoQrCodeNapasResponse')
          ..add('accountNo', accountNo)
          ..add('bankCode', bankCode)
          ..add('bankIdNapas', bankIdNapas)
          ..add('bankName', bankName)
          ..add('bankShortName', bankShortName)
          ..add('bankImageType', bankImageType)
          ..add('bankCitad', bankCitad)
          ..add('amount', amount)
          ..add('description', description)
          ..add('url', url)
          ..add('bankInfo', bankInfo))
        .toString();
  }
}

class TransferInfoQrCodeNapasResponseBuilder
    implements
        Builder<TransferInfoQrCodeNapasResponse,
            TransferInfoQrCodeNapasResponseBuilder> {
  _$TransferInfoQrCodeNapasResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _bankCode;
  String? get bankCode => _$this._bankCode;
  set bankCode(String? bankCode) => _$this._bankCode = bankCode;

  String? _bankIdNapas;
  String? get bankIdNapas => _$this._bankIdNapas;
  set bankIdNapas(String? bankIdNapas) => _$this._bankIdNapas = bankIdNapas;

  String? _bankName;
  String? get bankName => _$this._bankName;
  set bankName(String? bankName) => _$this._bankName = bankName;

  String? _bankShortName;
  String? get bankShortName => _$this._bankShortName;
  set bankShortName(String? bankShortName) =>
      _$this._bankShortName = bankShortName;

  String? _bankImageType;
  String? get bankImageType => _$this._bankImageType;
  set bankImageType(String? bankImageType) =>
      _$this._bankImageType = bankImageType;

  int? _bankCitad;
  int? get bankCitad => _$this._bankCitad;
  set bankCitad(int? bankCitad) => _$this._bankCitad = bankCitad;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _url;
  String? get url => _$this._url;
  set url(String? url) => _$this._url = url;

  BankCodeDtoBuilder? _bankInfo;
  BankCodeDtoBuilder get bankInfo => _$this._bankInfo ??= BankCodeDtoBuilder();
  set bankInfo(BankCodeDtoBuilder? bankInfo) => _$this._bankInfo = bankInfo;

  TransferInfoQrCodeNapasResponseBuilder() {
    TransferInfoQrCodeNapasResponse._defaults(this);
  }

  TransferInfoQrCodeNapasResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _bankCode = $v.bankCode;
      _bankIdNapas = $v.bankIdNapas;
      _bankName = $v.bankName;
      _bankShortName = $v.bankShortName;
      _bankImageType = $v.bankImageType;
      _bankCitad = $v.bankCitad;
      _amount = $v.amount;
      _description = $v.description;
      _url = $v.url;
      _bankInfo = $v.bankInfo?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferInfoQrCodeNapasResponse other) {
    _$v = other as _$TransferInfoQrCodeNapasResponse;
  }

  @override
  void update(void Function(TransferInfoQrCodeNapasResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferInfoQrCodeNapasResponse build() => _build();

  _$TransferInfoQrCodeNapasResponse _build() {
    _$TransferInfoQrCodeNapasResponse _$result;
    try {
      _$result = _$v ??
          _$TransferInfoQrCodeNapasResponse._(
            accountNo: accountNo,
            bankCode: bankCode,
            bankIdNapas: bankIdNapas,
            bankName: bankName,
            bankShortName: bankShortName,
            bankImageType: bankImageType,
            bankCitad: bankCitad,
            amount: amount,
            description: description,
            url: url,
            bankInfo: _bankInfo?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'bankInfo';
        _bankInfo?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransferInfoQrCodeNapasResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
