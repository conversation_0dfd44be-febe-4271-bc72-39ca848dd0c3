// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_ver3_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferVer3Response extends TransferVer3Response {
  @override
  final Transaction? transaction;
  @override
  final AdvanceTransferResponse? advanceTransferResponse;

  factory _$TransferVer3Response(
          [void Function(TransferVer3ResponseBuilder)? updates]) =>
      (TransferVer3ResponseBuilder()..update(updates))._build();

  _$TransferVer3Response._({this.transaction, this.advanceTransferResponse})
      : super._();
  @override
  TransferVer3Response rebuild(
          void Function(TransferVer3ResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferVer3ResponseBuilder toBuilder() =>
      TransferVer3ResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferVer3Response &&
        transaction == other.transaction &&
        advanceTransferResponse == other.advanceTransferResponse;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transaction.hashCode);
    _$hash = $jc(_$hash, advanceTransferResponse.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferVer3Response')
          ..add('transaction', transaction)
          ..add('advanceTransferResponse', advanceTransferResponse))
        .toString();
  }
}

class TransferVer3ResponseBuilder
    implements Builder<TransferVer3Response, TransferVer3ResponseBuilder> {
  _$TransferVer3Response? _$v;

  TransactionBuilder? _transaction;
  TransactionBuilder get transaction =>
      _$this._transaction ??= TransactionBuilder();
  set transaction(TransactionBuilder? transaction) =>
      _$this._transaction = transaction;

  AdvanceTransferResponseBuilder? _advanceTransferResponse;
  AdvanceTransferResponseBuilder get advanceTransferResponse =>
      _$this._advanceTransferResponse ??= AdvanceTransferResponseBuilder();
  set advanceTransferResponse(
          AdvanceTransferResponseBuilder? advanceTransferResponse) =>
      _$this._advanceTransferResponse = advanceTransferResponse;

  TransferVer3ResponseBuilder() {
    TransferVer3Response._defaults(this);
  }

  TransferVer3ResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transaction = $v.transaction?.toBuilder();
      _advanceTransferResponse = $v.advanceTransferResponse?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferVer3Response other) {
    _$v = other as _$TransferVer3Response;
  }

  @override
  void update(void Function(TransferVer3ResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferVer3Response build() => _build();

  _$TransferVer3Response _build() {
    _$TransferVer3Response _$result;
    try {
      _$result = _$v ??
          _$TransferVer3Response._(
            transaction: _transaction?.build(),
            advanceTransferResponse: _advanceTransferResponse?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transaction';
        _transaction?.build();
        _$failedField = 'advanceTransferResponse';
        _advanceTransferResponse?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransferVer3Response', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
