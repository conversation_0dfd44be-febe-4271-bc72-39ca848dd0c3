// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_vvip_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenVvipAccountRequest extends OpenVvipAccountRequest {
  @override
  final String? transactionNo;
  @override
  final String? accountNo;
  @override
  final String? fromAccount;
  @override
  final String? aliasName;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;
  @override
  final num? feeAmount;
  @override
  final String? referralCode;

  factory _$OpenVvipAccountRequest(
          [void Function(OpenVvipAccountRequestBuilder)? updates]) =>
      (OpenVvipAccountRequestBuilder()..update(updates))._build();

  _$OpenVvipAccountRequest._(
      {this.transactionNo,
      this.accountNo,
      this.fromAccount,
      this.aliasName,
      this.verifySoftOtp,
      this.feeAmount,
      this.referralCode})
      : super._();
  @override
  OpenVvipAccountRequest rebuild(
          void Function(OpenVvipAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenVvipAccountRequestBuilder toBuilder() =>
      OpenVvipAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenVvipAccountRequest &&
        transactionNo == other.transactionNo &&
        accountNo == other.accountNo &&
        fromAccount == other.fromAccount &&
        aliasName == other.aliasName &&
        verifySoftOtp == other.verifySoftOtp &&
        feeAmount == other.feeAmount &&
        referralCode == other.referralCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, fromAccount.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jc(_$hash, feeAmount.hashCode);
    _$hash = $jc(_$hash, referralCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenVvipAccountRequest')
          ..add('transactionNo', transactionNo)
          ..add('accountNo', accountNo)
          ..add('fromAccount', fromAccount)
          ..add('aliasName', aliasName)
          ..add('verifySoftOtp', verifySoftOtp)
          ..add('feeAmount', feeAmount)
          ..add('referralCode', referralCode))
        .toString();
  }
}

class OpenVvipAccountRequestBuilder
    implements Builder<OpenVvipAccountRequest, OpenVvipAccountRequestBuilder> {
  _$OpenVvipAccountRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _fromAccount;
  String? get fromAccount => _$this._fromAccount;
  set fromAccount(String? fromAccount) => _$this._fromAccount = fromAccount;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  num? _feeAmount;
  num? get feeAmount => _$this._feeAmount;
  set feeAmount(num? feeAmount) => _$this._feeAmount = feeAmount;

  String? _referralCode;
  String? get referralCode => _$this._referralCode;
  set referralCode(String? referralCode) => _$this._referralCode = referralCode;

  OpenVvipAccountRequestBuilder() {
    OpenVvipAccountRequest._defaults(this);
  }

  OpenVvipAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _accountNo = $v.accountNo;
      _fromAccount = $v.fromAccount;
      _aliasName = $v.aliasName;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _feeAmount = $v.feeAmount;
      _referralCode = $v.referralCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenVvipAccountRequest other) {
    _$v = other as _$OpenVvipAccountRequest;
  }

  @override
  void update(void Function(OpenVvipAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenVvipAccountRequest build() => _build();

  _$OpenVvipAccountRequest _build() {
    _$OpenVvipAccountRequest _$result;
    try {
      _$result = _$v ??
          _$OpenVvipAccountRequest._(
            transactionNo: transactionNo,
            accountNo: accountNo,
            fromAccount: fromAccount,
            aliasName: aliasName,
            verifySoftOtp: _verifySoftOtp?.build(),
            feeAmount: feeAmount,
            referralCode: referralCode,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'OpenVvipAccountRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
