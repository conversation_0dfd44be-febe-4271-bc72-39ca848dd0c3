// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_transaction_template_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListTransactionTemplateResponse
    extends ListTransactionTemplateResponse {
  @override
  final BuiltList<TransactionTemplateDto>? list;

  factory _$ListTransactionTemplateResponse(
          [void Function(ListTransactionTemplateResponseBuilder)? updates]) =>
      (ListTransactionTemplateResponseBuilder()..update(updates))._build();

  _$ListTransactionTemplateResponse._({this.list}) : super._();
  @override
  ListTransactionTemplateResponse rebuild(
          void Function(ListTransactionTemplateResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListTransactionTemplateResponseBuilder toBuilder() =>
      ListTransactionTemplateResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListTransactionTemplateResponse && list == other.list;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, list.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListTransactionTemplateResponse')
          ..add('list', list))
        .toString();
  }
}

class ListTransactionTemplateResponseBuilder
    implements
        Builder<ListTransactionTemplateResponse,
            ListTransactionTemplateResponseBuilder> {
  _$ListTransactionTemplateResponse? _$v;

  ListBuilder<TransactionTemplateDto>? _list;
  ListBuilder<TransactionTemplateDto> get list =>
      _$this._list ??= ListBuilder<TransactionTemplateDto>();
  set list(ListBuilder<TransactionTemplateDto>? list) => _$this._list = list;

  ListTransactionTemplateResponseBuilder() {
    ListTransactionTemplateResponse._defaults(this);
  }

  ListTransactionTemplateResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _list = $v.list?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListTransactionTemplateResponse other) {
    _$v = other as _$ListTransactionTemplateResponse;
  }

  @override
  void update(void Function(ListTransactionTemplateResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListTransactionTemplateResponse build() => _build();

  _$ListTransactionTemplateResponse _build() {
    _$ListTransactionTemplateResponse _$result;
    try {
      _$result = _$v ??
          _$ListTransactionTemplateResponse._(
            list: _list?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'list';
        _list?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListTransactionTemplateResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
