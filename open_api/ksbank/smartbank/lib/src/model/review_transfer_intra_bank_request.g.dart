// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_intra_bank_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferIntraBankRequest extends ReviewTransferIntraBankRequest {
  @override
  final String? cifNo;
  @override
  final int? transactionGroup;
  @override
  final String? description;
  @override
  final int? isAccount;
  @override
  final String? accountNoFrom;
  @override
  final String? accountNoTo;
  @override
  final num? amount;

  factory _$ReviewTransferIntraBankRequest(
          [void Function(ReviewTransferIntraBankRequestBuilder)? updates]) =>
      (ReviewTransferIntraBankRequestBuilder()..update(updates))._build();

  _$ReviewTransferIntraBankRequest._(
      {this.cifNo,
      this.transactionGroup,
      this.description,
      this.isAccount,
      this.accountNoFrom,
      this.accountNoTo,
      this.amount})
      : super._();
  @override
  ReviewTransferIntraBankRequest rebuild(
          void Function(ReviewTransferIntraBankRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferIntraBankRequestBuilder toBuilder() =>
      ReviewTransferIntraBankRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferIntraBankRequest &&
        cifNo == other.cifNo &&
        transactionGroup == other.transactionGroup &&
        description == other.description &&
        isAccount == other.isAccount &&
        accountNoFrom == other.accountNoFrom &&
        accountNoTo == other.accountNoTo &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewTransferIntraBankRequest')
          ..add('cifNo', cifNo)
          ..add('transactionGroup', transactionGroup)
          ..add('description', description)
          ..add('isAccount', isAccount)
          ..add('accountNoFrom', accountNoFrom)
          ..add('accountNoTo', accountNoTo)
          ..add('amount', amount))
        .toString();
  }
}

class ReviewTransferIntraBankRequestBuilder
    implements
        Builder<ReviewTransferIntraBankRequest,
            ReviewTransferIntraBankRequestBuilder> {
  _$ReviewTransferIntraBankRequest? _$v;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  ReviewTransferIntraBankRequestBuilder() {
    ReviewTransferIntraBankRequest._defaults(this);
  }

  ReviewTransferIntraBankRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNo = $v.cifNo;
      _transactionGroup = $v.transactionGroup;
      _description = $v.description;
      _isAccount = $v.isAccount;
      _accountNoFrom = $v.accountNoFrom;
      _accountNoTo = $v.accountNoTo;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferIntraBankRequest other) {
    _$v = other as _$ReviewTransferIntraBankRequest;
  }

  @override
  void update(void Function(ReviewTransferIntraBankRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferIntraBankRequest build() => _build();

  _$ReviewTransferIntraBankRequest _build() {
    final _$result = _$v ??
        _$ReviewTransferIntraBankRequest._(
          cifNo: cifNo,
          transactionGroup: transactionGroup,
          description: description,
          isAccount: isAccount,
          accountNoFrom: accountNoFrom,
          accountNoTo: accountNoTo,
          amount: amount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
