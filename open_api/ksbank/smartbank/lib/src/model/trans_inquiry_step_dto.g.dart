// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trans_inquiry_step_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransInquiryStepDto extends TransInquiryStepDto {
  @override
  final String? code;
  @override
  final int? step;
  @override
  final String? name;
  @override
  final String? color;
  @override
  final String? desc;

  factory _$TransInquiryStepDto(
          [void Function(TransInquiryStepDtoBuilder)? updates]) =>
      (TransInquiryStepDtoBuilder()..update(updates))._build();

  _$TransInquiryStepDto._(
      {this.code, this.step, this.name, this.color, this.desc})
      : super._();
  @override
  TransInquiryStepDto rebuild(
          void Function(TransInquiryStepDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransInquiryStepDtoBuilder toBuilder() =>
      TransInquiryStepDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransInquiryStepDto &&
        code == other.code &&
        step == other.step &&
        name == other.name &&
        color == other.color &&
        desc == other.desc;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, step.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, color.hashCode);
    _$hash = $jc(_$hash, desc.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransInquiryStepDto')
          ..add('code', code)
          ..add('step', step)
          ..add('name', name)
          ..add('color', color)
          ..add('desc', desc))
        .toString();
  }
}

class TransInquiryStepDtoBuilder
    implements Builder<TransInquiryStepDto, TransInquiryStepDtoBuilder> {
  _$TransInquiryStepDto? _$v;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  int? _step;
  int? get step => _$this._step;
  set step(int? step) => _$this._step = step;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _color;
  String? get color => _$this._color;
  set color(String? color) => _$this._color = color;

  String? _desc;
  String? get desc => _$this._desc;
  set desc(String? desc) => _$this._desc = desc;

  TransInquiryStepDtoBuilder() {
    TransInquiryStepDto._defaults(this);
  }

  TransInquiryStepDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _code = $v.code;
      _step = $v.step;
      _name = $v.name;
      _color = $v.color;
      _desc = $v.desc;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransInquiryStepDto other) {
    _$v = other as _$TransInquiryStepDto;
  }

  @override
  void update(void Function(TransInquiryStepDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransInquiryStepDto build() => _build();

  _$TransInquiryStepDto _build() {
    final _$result = _$v ??
        _$TransInquiryStepDto._(
          code: code,
          step: step,
          name: name,
          color: color,
          desc: desc,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
