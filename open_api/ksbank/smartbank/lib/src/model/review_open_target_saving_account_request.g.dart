// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_open_target_saving_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_VND =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('VND');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_USD =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('USD');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_ACB =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('ACB');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_JPY =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('JPY');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GOLD =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('GOLD');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_EUR =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('EUR');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GBP =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('GBP');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CHF =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('CHF');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_AUD =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('AUD');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CAD =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('CAD');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SGD =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('SGD');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_THB =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('THB');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NOK =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('NOK');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NZD =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('NZD');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_DKK =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('DKK');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_HKD =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('HKD');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SEK =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('SEK');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MYR =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('MYR');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_XAU =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('XAU');
const ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MMK =
    const ReviewOpenTargetSavingAccountRequestCurrencyEnum._('MMK');

ReviewOpenTargetSavingAccountRequestCurrencyEnum
    _$reviewOpenTargetSavingAccountRequestCurrencyEnumValueOf(String name) {
  switch (name) {
    case 'VND':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_VND;
    case 'USD':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_USD;
    case 'ACB':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_ACB;
    case 'JPY':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_JPY;
    case 'GOLD':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GOLD;
    case 'EUR':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_EUR;
    case 'GBP':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GBP;
    case 'CHF':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CHF;
    case 'AUD':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_AUD;
    case 'CAD':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CAD;
    case 'SGD':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SGD;
    case 'THB':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_THB;
    case 'NOK':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NOK;
    case 'NZD':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NZD;
    case 'DKK':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_DKK;
    case 'HKD':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_HKD;
    case 'SEK':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SEK;
    case 'MYR':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MYR;
    case 'XAU':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_XAU;
    case 'MMK':
      return _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<ReviewOpenTargetSavingAccountRequestCurrencyEnum>
    _$reviewOpenTargetSavingAccountRequestCurrencyEnumValues = BuiltSet<
        ReviewOpenTargetSavingAccountRequestCurrencyEnum>(const <ReviewOpenTargetSavingAccountRequestCurrencyEnum>[
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_VND,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_USD,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_ACB,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_JPY,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GOLD,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_EUR,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GBP,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CHF,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_AUD,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CAD,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SGD,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_THB,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NOK,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NZD,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_DKK,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_HKD,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SEK,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MYR,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_XAU,
  _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MMK,
]);

Serializer<ReviewOpenTargetSavingAccountRequestCurrencyEnum>
    _$reviewOpenTargetSavingAccountRequestCurrencyEnumSerializer =
    _$ReviewOpenTargetSavingAccountRequestCurrencyEnumSerializer();

class _$ReviewOpenTargetSavingAccountRequestCurrencyEnumSerializer
    implements
        PrimitiveSerializer<ReviewOpenTargetSavingAccountRequestCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[
    ReviewOpenTargetSavingAccountRequestCurrencyEnum
  ];
  @override
  final String wireName = 'ReviewOpenTargetSavingAccountRequestCurrencyEnum';

  @override
  Object serialize(Serializers serializers,
          ReviewOpenTargetSavingAccountRequestCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  ReviewOpenTargetSavingAccountRequestCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      ReviewOpenTargetSavingAccountRequestCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$ReviewOpenTargetSavingAccountRequest
    extends ReviewOpenTargetSavingAccountRequest {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? sourceAccountNo;
  @override
  final double? amount;
  @override
  final ReviewOpenTargetSavingAccountRequestCurrencyEnum? currency;

  factory _$ReviewOpenTargetSavingAccountRequest(
          [void Function(ReviewOpenTargetSavingAccountRequestBuilder)?
              updates]) =>
      (ReviewOpenTargetSavingAccountRequestBuilder()..update(updates))._build();

  _$ReviewOpenTargetSavingAccountRequest._(
      {this.bankCif,
      this.transactionNo,
      this.sourceAccountNo,
      this.amount,
      this.currency})
      : super._();
  @override
  ReviewOpenTargetSavingAccountRequest rebuild(
          void Function(ReviewOpenTargetSavingAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewOpenTargetSavingAccountRequestBuilder toBuilder() =>
      ReviewOpenTargetSavingAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewOpenTargetSavingAccountRequest &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        sourceAccountNo == other.sourceAccountNo &&
        amount == other.amount &&
        currency == other.currency;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewOpenTargetSavingAccountRequest')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('amount', amount)
          ..add('currency', currency))
        .toString();
  }
}

class ReviewOpenTargetSavingAccountRequestBuilder
    implements
        Builder<ReviewOpenTargetSavingAccountRequest,
            ReviewOpenTargetSavingAccountRequestBuilder> {
  _$ReviewOpenTargetSavingAccountRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  ReviewOpenTargetSavingAccountRequestCurrencyEnum? _currency;
  ReviewOpenTargetSavingAccountRequestCurrencyEnum? get currency =>
      _$this._currency;
  set currency(ReviewOpenTargetSavingAccountRequestCurrencyEnum? currency) =>
      _$this._currency = currency;

  ReviewOpenTargetSavingAccountRequestBuilder() {
    ReviewOpenTargetSavingAccountRequest._defaults(this);
  }

  ReviewOpenTargetSavingAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _sourceAccountNo = $v.sourceAccountNo;
      _amount = $v.amount;
      _currency = $v.currency;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewOpenTargetSavingAccountRequest other) {
    _$v = other as _$ReviewOpenTargetSavingAccountRequest;
  }

  @override
  void update(
      void Function(ReviewOpenTargetSavingAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewOpenTargetSavingAccountRequest build() => _build();

  _$ReviewOpenTargetSavingAccountRequest _build() {
    final _$result = _$v ??
        _$ReviewOpenTargetSavingAccountRequest._(
          bankCif: bankCif,
          transactionNo: transactionNo,
          sourceAccountNo: sourceAccountNo,
          amount: amount,
          currency: currency,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
