// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saving_type_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SavingTypeResponse extends SavingTypeResponse {
  @override
  final int? id;
  @override
  final String? icon;
  @override
  final String? name;
  @override
  final String? description;

  factory _$SavingTypeResponse(
          [void Function(SavingTypeResponseBuilder)? updates]) =>
      (SavingTypeResponseBuilder()..update(updates))._build();

  _$SavingTypeResponse._({this.id, this.icon, this.name, this.description})
      : super._();
  @override
  SavingTypeResponse rebuild(
          void Function(SavingTypeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SavingTypeResponseBuilder toBuilder() =>
      SavingTypeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SavingTypeResponse &&
        id == other.id &&
        icon == other.icon &&
        name == other.name &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, icon.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SavingTypeResponse')
          ..add('id', id)
          ..add('icon', icon)
          ..add('name', name)
          ..add('description', description))
        .toString();
  }
}

class SavingTypeResponseBuilder
    implements Builder<SavingTypeResponse, SavingTypeResponseBuilder> {
  _$SavingTypeResponse? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  String? _icon;
  String? get icon => _$this._icon;
  set icon(String? icon) => _$this._icon = icon;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  SavingTypeResponseBuilder() {
    SavingTypeResponse._defaults(this);
  }

  SavingTypeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _icon = $v.icon;
      _name = $v.name;
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SavingTypeResponse other) {
    _$v = other as _$SavingTypeResponse;
  }

  @override
  void update(void Function(SavingTypeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SavingTypeResponse build() => _build();

  _$SavingTypeResponse _build() {
    final _$result = _$v ??
        _$SavingTypeResponse._(
          id: id,
          icon: icon,
          name: name,
          description: description,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
