// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offline_saving_account_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OfflineSavingAccountDetailResponse
    extends OfflineSavingAccountDetailResponse {
  @override
  final String? accountNumber;
  @override
  final String? customerName;
  @override
  final String? termName;
  @override
  final String? accountName;
  @override
  final String? currency;
  @override
  final double? rate;
  @override
  final double? balance;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final num? interestAmount;
  @override
  final String? finalTypeName;
  @override
  final num? interestAmountEndOfTerm;
  @override
  final bool? blocked;
  @override
  final String? branchName;
  @override
  final String? alias;

  factory _$OfflineSavingAccountDetailResponse(
          [void Function(OfflineSavingAccountDetailResponseBuilder)?
              updates]) =>
      (OfflineSavingAccountDetailResponseBuilder()..update(updates))._build();

  _$OfflineSavingAccountDetailResponse._(
      {this.accountNumber,
      this.customerName,
      this.termName,
      this.accountName,
      this.currency,
      this.rate,
      this.balance,
      this.contractDate,
      this.dueDate,
      this.interestAmount,
      this.finalTypeName,
      this.interestAmountEndOfTerm,
      this.blocked,
      this.branchName,
      this.alias})
      : super._();
  @override
  OfflineSavingAccountDetailResponse rebuild(
          void Function(OfflineSavingAccountDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OfflineSavingAccountDetailResponseBuilder toBuilder() =>
      OfflineSavingAccountDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OfflineSavingAccountDetailResponse &&
        accountNumber == other.accountNumber &&
        customerName == other.customerName &&
        termName == other.termName &&
        accountName == other.accountName &&
        currency == other.currency &&
        rate == other.rate &&
        balance == other.balance &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        interestAmount == other.interestAmount &&
        finalTypeName == other.finalTypeName &&
        interestAmountEndOfTerm == other.interestAmountEndOfTerm &&
        blocked == other.blocked &&
        branchName == other.branchName &&
        alias == other.alias;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, termName.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, interestAmount.hashCode);
    _$hash = $jc(_$hash, finalTypeName.hashCode);
    _$hash = $jc(_$hash, interestAmountEndOfTerm.hashCode);
    _$hash = $jc(_$hash, blocked.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, alias.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OfflineSavingAccountDetailResponse')
          ..add('accountNumber', accountNumber)
          ..add('customerName', customerName)
          ..add('termName', termName)
          ..add('accountName', accountName)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('balance', balance)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('interestAmount', interestAmount)
          ..add('finalTypeName', finalTypeName)
          ..add('interestAmountEndOfTerm', interestAmountEndOfTerm)
          ..add('blocked', blocked)
          ..add('branchName', branchName)
          ..add('alias', alias))
        .toString();
  }
}

class OfflineSavingAccountDetailResponseBuilder
    implements
        Builder<OfflineSavingAccountDetailResponse,
            OfflineSavingAccountDetailResponseBuilder> {
  _$OfflineSavingAccountDetailResponse? _$v;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _termName;
  String? get termName => _$this._termName;
  set termName(String? termName) => _$this._termName = termName;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  double? _balance;
  double? get balance => _$this._balance;
  set balance(double? balance) => _$this._balance = balance;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  num? _interestAmount;
  num? get interestAmount => _$this._interestAmount;
  set interestAmount(num? interestAmount) =>
      _$this._interestAmount = interestAmount;

  String? _finalTypeName;
  String? get finalTypeName => _$this._finalTypeName;
  set finalTypeName(String? finalTypeName) =>
      _$this._finalTypeName = finalTypeName;

  num? _interestAmountEndOfTerm;
  num? get interestAmountEndOfTerm => _$this._interestAmountEndOfTerm;
  set interestAmountEndOfTerm(num? interestAmountEndOfTerm) =>
      _$this._interestAmountEndOfTerm = interestAmountEndOfTerm;

  bool? _blocked;
  bool? get blocked => _$this._blocked;
  set blocked(bool? blocked) => _$this._blocked = blocked;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _alias;
  String? get alias => _$this._alias;
  set alias(String? alias) => _$this._alias = alias;

  OfflineSavingAccountDetailResponseBuilder() {
    OfflineSavingAccountDetailResponse._defaults(this);
  }

  OfflineSavingAccountDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNumber = $v.accountNumber;
      _customerName = $v.customerName;
      _termName = $v.termName;
      _accountName = $v.accountName;
      _currency = $v.currency;
      _rate = $v.rate;
      _balance = $v.balance;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _interestAmount = $v.interestAmount;
      _finalTypeName = $v.finalTypeName;
      _interestAmountEndOfTerm = $v.interestAmountEndOfTerm;
      _blocked = $v.blocked;
      _branchName = $v.branchName;
      _alias = $v.alias;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OfflineSavingAccountDetailResponse other) {
    _$v = other as _$OfflineSavingAccountDetailResponse;
  }

  @override
  void update(
      void Function(OfflineSavingAccountDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OfflineSavingAccountDetailResponse build() => _build();

  _$OfflineSavingAccountDetailResponse _build() {
    final _$result = _$v ??
        _$OfflineSavingAccountDetailResponse._(
          accountNumber: accountNumber,
          customerName: customerName,
          termName: termName,
          accountName: accountName,
          currency: currency,
          rate: rate,
          balance: balance,
          contractDate: contractDate,
          dueDate: dueDate,
          interestAmount: interestAmount,
          finalTypeName: finalTypeName,
          interestAmountEndOfTerm: interestAmountEndOfTerm,
          blocked: blocked,
          branchName: branchName,
          alias: alias,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
