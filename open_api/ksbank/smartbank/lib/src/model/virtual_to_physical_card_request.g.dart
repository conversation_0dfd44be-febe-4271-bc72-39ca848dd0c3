// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'virtual_to_physical_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VirtualToPhysicalCardRequest extends VirtualToPhysicalCardRequest {
  @override
  final String? refCardId;
  @override
  final String? addressTakeCard;
  @override
  final String? addressType;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;

  factory _$VirtualToPhysicalCardRequest(
          [void Function(VirtualToPhysicalCardRequestBuilder)? updates]) =>
      (VirtualToPhysicalCardRequestBuilder()..update(updates))._build();

  _$VirtualToPhysicalCardRequest._(
      {this.refCardId,
      this.addressTakeCard,
      this.addressType,
      this.verifySoftOtp})
      : super._();
  @override
  VirtualToPhysicalCardRequest rebuild(
          void Function(VirtualToPhysicalCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VirtualToPhysicalCardRequestBuilder toBuilder() =>
      VirtualToPhysicalCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VirtualToPhysicalCardRequest &&
        refCardId == other.refCardId &&
        addressTakeCard == other.addressTakeCard &&
        addressType == other.addressType &&
        verifySoftOtp == other.verifySoftOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, refCardId.hashCode);
    _$hash = $jc(_$hash, addressTakeCard.hashCode);
    _$hash = $jc(_$hash, addressType.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VirtualToPhysicalCardRequest')
          ..add('refCardId', refCardId)
          ..add('addressTakeCard', addressTakeCard)
          ..add('addressType', addressType)
          ..add('verifySoftOtp', verifySoftOtp))
        .toString();
  }
}

class VirtualToPhysicalCardRequestBuilder
    implements
        Builder<VirtualToPhysicalCardRequest,
            VirtualToPhysicalCardRequestBuilder> {
  _$VirtualToPhysicalCardRequest? _$v;

  String? _refCardId;
  String? get refCardId => _$this._refCardId;
  set refCardId(String? refCardId) => _$this._refCardId = refCardId;

  String? _addressTakeCard;
  String? get addressTakeCard => _$this._addressTakeCard;
  set addressTakeCard(String? addressTakeCard) =>
      _$this._addressTakeCard = addressTakeCard;

  String? _addressType;
  String? get addressType => _$this._addressType;
  set addressType(String? addressType) => _$this._addressType = addressType;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  VirtualToPhysicalCardRequestBuilder() {
    VirtualToPhysicalCardRequest._defaults(this);
  }

  VirtualToPhysicalCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _refCardId = $v.refCardId;
      _addressTakeCard = $v.addressTakeCard;
      _addressType = $v.addressType;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VirtualToPhysicalCardRequest other) {
    _$v = other as _$VirtualToPhysicalCardRequest;
  }

  @override
  void update(void Function(VirtualToPhysicalCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VirtualToPhysicalCardRequest build() => _build();

  _$VirtualToPhysicalCardRequest _build() {
    _$VirtualToPhysicalCardRequest _$result;
    try {
      _$result = _$v ??
          _$VirtualToPhysicalCardRequest._(
            refCardId: refCardId,
            addressTakeCard: addressTakeCard,
            addressType: addressType,
            verifySoftOtp: _verifySoftOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'VirtualToPhysicalCardRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
