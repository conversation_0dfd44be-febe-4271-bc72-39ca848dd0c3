// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_item_mapper.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionItemMapper extends TransactionItemMapper {
  @override
  final String? id;
  @override
  final String? cifNo;
  @override
  final String? accountNo;
  @override
  final String? transactionNo;
  @override
  final String? refTransactionNo;
  @override
  final DateTime? transactionAt;
  @override
  final int? isAccount;
  @override
  final num? amount;
  @override
  final int? coefficient;
  @override
  final String? description;
  @override
  final String? partnerAccountNo;
  @override
  final String? partnerAccountName;
  @override
  final String? partnerAccountAlias;
  @override
  final String? partnerAccountAvatar;
  @override
  final String? partnerBankcodeId;
  @override
  final String? partnerBankIdNapas;
  @override
  final int? partnerCitadBankCode;
  @override
  final String? partnerBankName;
  @override
  final String? partnerBankShortName;
  @override
  final String? partnerBankCommonName;
  @override
  final String? partnerBankUrl;
  @override
  final String? partnerBankUrlType;
  @override
  final String? accountName;
  @override
  final int? transactionCategoryId;
  @override
  final String? transactionCategoryName;
  @override
  final String? transactionCategoryIconUrl;
  @override
  final String? transactionTypeName;
  @override
  final String? statusName;
  @override
  final String? transactionTypeId;
  @override
  final String? cardNo;
  @override
  final String? customerCode;
  @override
  final num? phoneCardValue;
  @override
  final String? serviceId;
  @override
  final String? serviceName;
  @override
  final String? serviceIconUrl;
  @override
  final String? supplierId;
  @override
  final int? currency;
  @override
  final String? currencyName;
  @override
  final bool? bigTransaction;

  factory _$TransactionItemMapper(
          [void Function(TransactionItemMapperBuilder)? updates]) =>
      (TransactionItemMapperBuilder()..update(updates))._build();

  _$TransactionItemMapper._(
      {this.id,
      this.cifNo,
      this.accountNo,
      this.transactionNo,
      this.refTransactionNo,
      this.transactionAt,
      this.isAccount,
      this.amount,
      this.coefficient,
      this.description,
      this.partnerAccountNo,
      this.partnerAccountName,
      this.partnerAccountAlias,
      this.partnerAccountAvatar,
      this.partnerBankcodeId,
      this.partnerBankIdNapas,
      this.partnerCitadBankCode,
      this.partnerBankName,
      this.partnerBankShortName,
      this.partnerBankCommonName,
      this.partnerBankUrl,
      this.partnerBankUrlType,
      this.accountName,
      this.transactionCategoryId,
      this.transactionCategoryName,
      this.transactionCategoryIconUrl,
      this.transactionTypeName,
      this.statusName,
      this.transactionTypeId,
      this.cardNo,
      this.customerCode,
      this.phoneCardValue,
      this.serviceId,
      this.serviceName,
      this.serviceIconUrl,
      this.supplierId,
      this.currency,
      this.currencyName,
      this.bigTransaction})
      : super._();
  @override
  TransactionItemMapper rebuild(
          void Function(TransactionItemMapperBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionItemMapperBuilder toBuilder() =>
      TransactionItemMapperBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionItemMapper &&
        id == other.id &&
        cifNo == other.cifNo &&
        accountNo == other.accountNo &&
        transactionNo == other.transactionNo &&
        refTransactionNo == other.refTransactionNo &&
        transactionAt == other.transactionAt &&
        isAccount == other.isAccount &&
        amount == other.amount &&
        coefficient == other.coefficient &&
        description == other.description &&
        partnerAccountNo == other.partnerAccountNo &&
        partnerAccountName == other.partnerAccountName &&
        partnerAccountAlias == other.partnerAccountAlias &&
        partnerAccountAvatar == other.partnerAccountAvatar &&
        partnerBankcodeId == other.partnerBankcodeId &&
        partnerBankIdNapas == other.partnerBankIdNapas &&
        partnerCitadBankCode == other.partnerCitadBankCode &&
        partnerBankName == other.partnerBankName &&
        partnerBankShortName == other.partnerBankShortName &&
        partnerBankCommonName == other.partnerBankCommonName &&
        partnerBankUrl == other.partnerBankUrl &&
        partnerBankUrlType == other.partnerBankUrlType &&
        accountName == other.accountName &&
        transactionCategoryId == other.transactionCategoryId &&
        transactionCategoryName == other.transactionCategoryName &&
        transactionCategoryIconUrl == other.transactionCategoryIconUrl &&
        transactionTypeName == other.transactionTypeName &&
        statusName == other.statusName &&
        transactionTypeId == other.transactionTypeId &&
        cardNo == other.cardNo &&
        customerCode == other.customerCode &&
        phoneCardValue == other.phoneCardValue &&
        serviceId == other.serviceId &&
        serviceName == other.serviceName &&
        serviceIconUrl == other.serviceIconUrl &&
        supplierId == other.supplierId &&
        currency == other.currency &&
        currencyName == other.currencyName &&
        bigTransaction == other.bigTransaction;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, refTransactionNo.hashCode);
    _$hash = $jc(_$hash, transactionAt.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, coefficient.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, partnerAccountNo.hashCode);
    _$hash = $jc(_$hash, partnerAccountName.hashCode);
    _$hash = $jc(_$hash, partnerAccountAlias.hashCode);
    _$hash = $jc(_$hash, partnerAccountAvatar.hashCode);
    _$hash = $jc(_$hash, partnerBankcodeId.hashCode);
    _$hash = $jc(_$hash, partnerBankIdNapas.hashCode);
    _$hash = $jc(_$hash, partnerCitadBankCode.hashCode);
    _$hash = $jc(_$hash, partnerBankName.hashCode);
    _$hash = $jc(_$hash, partnerBankShortName.hashCode);
    _$hash = $jc(_$hash, partnerBankCommonName.hashCode);
    _$hash = $jc(_$hash, partnerBankUrl.hashCode);
    _$hash = $jc(_$hash, partnerBankUrlType.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, transactionCategoryId.hashCode);
    _$hash = $jc(_$hash, transactionCategoryName.hashCode);
    _$hash = $jc(_$hash, transactionCategoryIconUrl.hashCode);
    _$hash = $jc(_$hash, transactionTypeName.hashCode);
    _$hash = $jc(_$hash, statusName.hashCode);
    _$hash = $jc(_$hash, transactionTypeId.hashCode);
    _$hash = $jc(_$hash, cardNo.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, phoneCardValue.hashCode);
    _$hash = $jc(_$hash, serviceId.hashCode);
    _$hash = $jc(_$hash, serviceName.hashCode);
    _$hash = $jc(_$hash, serviceIconUrl.hashCode);
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, currencyName.hashCode);
    _$hash = $jc(_$hash, bigTransaction.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionItemMapper')
          ..add('id', id)
          ..add('cifNo', cifNo)
          ..add('accountNo', accountNo)
          ..add('transactionNo', transactionNo)
          ..add('refTransactionNo', refTransactionNo)
          ..add('transactionAt', transactionAt)
          ..add('isAccount', isAccount)
          ..add('amount', amount)
          ..add('coefficient', coefficient)
          ..add('description', description)
          ..add('partnerAccountNo', partnerAccountNo)
          ..add('partnerAccountName', partnerAccountName)
          ..add('partnerAccountAlias', partnerAccountAlias)
          ..add('partnerAccountAvatar', partnerAccountAvatar)
          ..add('partnerBankcodeId', partnerBankcodeId)
          ..add('partnerBankIdNapas', partnerBankIdNapas)
          ..add('partnerCitadBankCode', partnerCitadBankCode)
          ..add('partnerBankName', partnerBankName)
          ..add('partnerBankShortName', partnerBankShortName)
          ..add('partnerBankCommonName', partnerBankCommonName)
          ..add('partnerBankUrl', partnerBankUrl)
          ..add('partnerBankUrlType', partnerBankUrlType)
          ..add('accountName', accountName)
          ..add('transactionCategoryId', transactionCategoryId)
          ..add('transactionCategoryName', transactionCategoryName)
          ..add('transactionCategoryIconUrl', transactionCategoryIconUrl)
          ..add('transactionTypeName', transactionTypeName)
          ..add('statusName', statusName)
          ..add('transactionTypeId', transactionTypeId)
          ..add('cardNo', cardNo)
          ..add('customerCode', customerCode)
          ..add('phoneCardValue', phoneCardValue)
          ..add('serviceId', serviceId)
          ..add('serviceName', serviceName)
          ..add('serviceIconUrl', serviceIconUrl)
          ..add('supplierId', supplierId)
          ..add('currency', currency)
          ..add('currencyName', currencyName)
          ..add('bigTransaction', bigTransaction))
        .toString();
  }
}

class TransactionItemMapperBuilder
    implements Builder<TransactionItemMapper, TransactionItemMapperBuilder> {
  _$TransactionItemMapper? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _refTransactionNo;
  String? get refTransactionNo => _$this._refTransactionNo;
  set refTransactionNo(String? refTransactionNo) =>
      _$this._refTransactionNo = refTransactionNo;

  DateTime? _transactionAt;
  DateTime? get transactionAt => _$this._transactionAt;
  set transactionAt(DateTime? transactionAt) =>
      _$this._transactionAt = transactionAt;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  int? _coefficient;
  int? get coefficient => _$this._coefficient;
  set coefficient(int? coefficient) => _$this._coefficient = coefficient;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _partnerAccountNo;
  String? get partnerAccountNo => _$this._partnerAccountNo;
  set partnerAccountNo(String? partnerAccountNo) =>
      _$this._partnerAccountNo = partnerAccountNo;

  String? _partnerAccountName;
  String? get partnerAccountName => _$this._partnerAccountName;
  set partnerAccountName(String? partnerAccountName) =>
      _$this._partnerAccountName = partnerAccountName;

  String? _partnerAccountAlias;
  String? get partnerAccountAlias => _$this._partnerAccountAlias;
  set partnerAccountAlias(String? partnerAccountAlias) =>
      _$this._partnerAccountAlias = partnerAccountAlias;

  String? _partnerAccountAvatar;
  String? get partnerAccountAvatar => _$this._partnerAccountAvatar;
  set partnerAccountAvatar(String? partnerAccountAvatar) =>
      _$this._partnerAccountAvatar = partnerAccountAvatar;

  String? _partnerBankcodeId;
  String? get partnerBankcodeId => _$this._partnerBankcodeId;
  set partnerBankcodeId(String? partnerBankcodeId) =>
      _$this._partnerBankcodeId = partnerBankcodeId;

  String? _partnerBankIdNapas;
  String? get partnerBankIdNapas => _$this._partnerBankIdNapas;
  set partnerBankIdNapas(String? partnerBankIdNapas) =>
      _$this._partnerBankIdNapas = partnerBankIdNapas;

  int? _partnerCitadBankCode;
  int? get partnerCitadBankCode => _$this._partnerCitadBankCode;
  set partnerCitadBankCode(int? partnerCitadBankCode) =>
      _$this._partnerCitadBankCode = partnerCitadBankCode;

  String? _partnerBankName;
  String? get partnerBankName => _$this._partnerBankName;
  set partnerBankName(String? partnerBankName) =>
      _$this._partnerBankName = partnerBankName;

  String? _partnerBankShortName;
  String? get partnerBankShortName => _$this._partnerBankShortName;
  set partnerBankShortName(String? partnerBankShortName) =>
      _$this._partnerBankShortName = partnerBankShortName;

  String? _partnerBankCommonName;
  String? get partnerBankCommonName => _$this._partnerBankCommonName;
  set partnerBankCommonName(String? partnerBankCommonName) =>
      _$this._partnerBankCommonName = partnerBankCommonName;

  String? _partnerBankUrl;
  String? get partnerBankUrl => _$this._partnerBankUrl;
  set partnerBankUrl(String? partnerBankUrl) =>
      _$this._partnerBankUrl = partnerBankUrl;

  String? _partnerBankUrlType;
  String? get partnerBankUrlType => _$this._partnerBankUrlType;
  set partnerBankUrlType(String? partnerBankUrlType) =>
      _$this._partnerBankUrlType = partnerBankUrlType;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  int? _transactionCategoryId;
  int? get transactionCategoryId => _$this._transactionCategoryId;
  set transactionCategoryId(int? transactionCategoryId) =>
      _$this._transactionCategoryId = transactionCategoryId;

  String? _transactionCategoryName;
  String? get transactionCategoryName => _$this._transactionCategoryName;
  set transactionCategoryName(String? transactionCategoryName) =>
      _$this._transactionCategoryName = transactionCategoryName;

  String? _transactionCategoryIconUrl;
  String? get transactionCategoryIconUrl => _$this._transactionCategoryIconUrl;
  set transactionCategoryIconUrl(String? transactionCategoryIconUrl) =>
      _$this._transactionCategoryIconUrl = transactionCategoryIconUrl;

  String? _transactionTypeName;
  String? get transactionTypeName => _$this._transactionTypeName;
  set transactionTypeName(String? transactionTypeName) =>
      _$this._transactionTypeName = transactionTypeName;

  String? _statusName;
  String? get statusName => _$this._statusName;
  set statusName(String? statusName) => _$this._statusName = statusName;

  String? _transactionTypeId;
  String? get transactionTypeId => _$this._transactionTypeId;
  set transactionTypeId(String? transactionTypeId) =>
      _$this._transactionTypeId = transactionTypeId;

  String? _cardNo;
  String? get cardNo => _$this._cardNo;
  set cardNo(String? cardNo) => _$this._cardNo = cardNo;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  num? _phoneCardValue;
  num? get phoneCardValue => _$this._phoneCardValue;
  set phoneCardValue(num? phoneCardValue) =>
      _$this._phoneCardValue = phoneCardValue;

  String? _serviceId;
  String? get serviceId => _$this._serviceId;
  set serviceId(String? serviceId) => _$this._serviceId = serviceId;

  String? _serviceName;
  String? get serviceName => _$this._serviceName;
  set serviceName(String? serviceName) => _$this._serviceName = serviceName;

  String? _serviceIconUrl;
  String? get serviceIconUrl => _$this._serviceIconUrl;
  set serviceIconUrl(String? serviceIconUrl) =>
      _$this._serviceIconUrl = serviceIconUrl;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  int? _currency;
  int? get currency => _$this._currency;
  set currency(int? currency) => _$this._currency = currency;

  String? _currencyName;
  String? get currencyName => _$this._currencyName;
  set currencyName(String? currencyName) => _$this._currencyName = currencyName;

  bool? _bigTransaction;
  bool? get bigTransaction => _$this._bigTransaction;
  set bigTransaction(bool? bigTransaction) =>
      _$this._bigTransaction = bigTransaction;

  TransactionItemMapperBuilder() {
    TransactionItemMapper._defaults(this);
  }

  TransactionItemMapperBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _cifNo = $v.cifNo;
      _accountNo = $v.accountNo;
      _transactionNo = $v.transactionNo;
      _refTransactionNo = $v.refTransactionNo;
      _transactionAt = $v.transactionAt;
      _isAccount = $v.isAccount;
      _amount = $v.amount;
      _coefficient = $v.coefficient;
      _description = $v.description;
      _partnerAccountNo = $v.partnerAccountNo;
      _partnerAccountName = $v.partnerAccountName;
      _partnerAccountAlias = $v.partnerAccountAlias;
      _partnerAccountAvatar = $v.partnerAccountAvatar;
      _partnerBankcodeId = $v.partnerBankcodeId;
      _partnerBankIdNapas = $v.partnerBankIdNapas;
      _partnerCitadBankCode = $v.partnerCitadBankCode;
      _partnerBankName = $v.partnerBankName;
      _partnerBankShortName = $v.partnerBankShortName;
      _partnerBankCommonName = $v.partnerBankCommonName;
      _partnerBankUrl = $v.partnerBankUrl;
      _partnerBankUrlType = $v.partnerBankUrlType;
      _accountName = $v.accountName;
      _transactionCategoryId = $v.transactionCategoryId;
      _transactionCategoryName = $v.transactionCategoryName;
      _transactionCategoryIconUrl = $v.transactionCategoryIconUrl;
      _transactionTypeName = $v.transactionTypeName;
      _statusName = $v.statusName;
      _transactionTypeId = $v.transactionTypeId;
      _cardNo = $v.cardNo;
      _customerCode = $v.customerCode;
      _phoneCardValue = $v.phoneCardValue;
      _serviceId = $v.serviceId;
      _serviceName = $v.serviceName;
      _serviceIconUrl = $v.serviceIconUrl;
      _supplierId = $v.supplierId;
      _currency = $v.currency;
      _currencyName = $v.currencyName;
      _bigTransaction = $v.bigTransaction;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionItemMapper other) {
    _$v = other as _$TransactionItemMapper;
  }

  @override
  void update(void Function(TransactionItemMapperBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionItemMapper build() => _build();

  _$TransactionItemMapper _build() {
    final _$result = _$v ??
        _$TransactionItemMapper._(
          id: id,
          cifNo: cifNo,
          accountNo: accountNo,
          transactionNo: transactionNo,
          refTransactionNo: refTransactionNo,
          transactionAt: transactionAt,
          isAccount: isAccount,
          amount: amount,
          coefficient: coefficient,
          description: description,
          partnerAccountNo: partnerAccountNo,
          partnerAccountName: partnerAccountName,
          partnerAccountAlias: partnerAccountAlias,
          partnerAccountAvatar: partnerAccountAvatar,
          partnerBankcodeId: partnerBankcodeId,
          partnerBankIdNapas: partnerBankIdNapas,
          partnerCitadBankCode: partnerCitadBankCode,
          partnerBankName: partnerBankName,
          partnerBankShortName: partnerBankShortName,
          partnerBankCommonName: partnerBankCommonName,
          partnerBankUrl: partnerBankUrl,
          partnerBankUrlType: partnerBankUrlType,
          accountName: accountName,
          transactionCategoryId: transactionCategoryId,
          transactionCategoryName: transactionCategoryName,
          transactionCategoryIconUrl: transactionCategoryIconUrl,
          transactionTypeName: transactionTypeName,
          statusName: statusName,
          transactionTypeId: transactionTypeId,
          cardNo: cardNo,
          customerCode: customerCode,
          phoneCardValue: phoneCardValue,
          serviceId: serviceId,
          serviceName: serviceName,
          serviceIconUrl: serviceIconUrl,
          supplierId: supplierId,
          currency: currency,
          currencyName: currencyName,
          bigTransaction: bigTransaction,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
