// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scheduling_object.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SchedulingObject extends SchedulingObject {
  @override
  final String? scheduleType;
  @override
  final String? fromDate;
  @override
  final String? toDate;
  @override
  final String? days;

  factory _$SchedulingObject(
          [void Function(SchedulingObjectBuilder)? updates]) =>
      (SchedulingObjectBuilder()..update(updates))._build();

  _$SchedulingObject._(
      {this.scheduleType, this.fromDate, this.toDate, this.days})
      : super._();
  @override
  SchedulingObject rebuild(void Function(SchedulingObjectBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SchedulingObjectBuilder toBuilder() =>
      SchedulingObjectBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SchedulingObject &&
        scheduleType == other.scheduleType &&
        fromDate == other.fromDate &&
        toDate == other.toDate &&
        days == other.days;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, scheduleType.hashCode);
    _$hash = $jc(_$hash, fromDate.hashCode);
    _$hash = $jc(_$hash, toDate.hashCode);
    _$hash = $jc(_$hash, days.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SchedulingObject')
          ..add('scheduleType', scheduleType)
          ..add('fromDate', fromDate)
          ..add('toDate', toDate)
          ..add('days', days))
        .toString();
  }
}

class SchedulingObjectBuilder
    implements Builder<SchedulingObject, SchedulingObjectBuilder> {
  _$SchedulingObject? _$v;

  String? _scheduleType;
  String? get scheduleType => _$this._scheduleType;
  set scheduleType(String? scheduleType) => _$this._scheduleType = scheduleType;

  String? _fromDate;
  String? get fromDate => _$this._fromDate;
  set fromDate(String? fromDate) => _$this._fromDate = fromDate;

  String? _toDate;
  String? get toDate => _$this._toDate;
  set toDate(String? toDate) => _$this._toDate = toDate;

  String? _days;
  String? get days => _$this._days;
  set days(String? days) => _$this._days = days;

  SchedulingObjectBuilder() {
    SchedulingObject._defaults(this);
  }

  SchedulingObjectBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _scheduleType = $v.scheduleType;
      _fromDate = $v.fromDate;
      _toDate = $v.toDate;
      _days = $v.days;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SchedulingObject other) {
    _$v = other as _$SchedulingObject;
  }

  @override
  void update(void Function(SchedulingObjectBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SchedulingObject build() => _build();

  _$SchedulingObject _build() {
    final _$result = _$v ??
        _$SchedulingObject._(
          scheduleType: scheduleType,
          fromDate: fromDate,
          toDate: toDate,
          days: days,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
