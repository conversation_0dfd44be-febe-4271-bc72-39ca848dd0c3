//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'payment_topup_request.g.dart';

/// PaymentTopupRequest
///
/// Properties:
/// * [transactionNo] - M<PERSON> giao dịch, được trả về khi gọi api review hoặc check limt
/// * [phoneNo] - S<PERSON> điện thoại cần nạp
/// * [accountNo] - Số tài khoản thực hiện thanh toán
/// * [amount] - Số tiền thực hiện thanh toán
/// * [cardValue] - Mệnh giá topup
/// * [productCode] - <PERSON><PERSON> sản phẩm cần thanh toán
/// * [topupType] - <PERSON><PERSON><PERSON> thứ<PERSON> nạ<PERSON>
/// * [pinCodeName] - T<PERSON>n của pin code (Dành cho mục đích ghi log)
/// * [pinCodeDesc] - Mô tả pin code (Dành cho mục đích ghi log)
/// * [supplierName] - Tên nhà cung cấp (Dành cho mục đích ghi log)
/// * [supplierUrl] - Icon nhà mạng (Dành cho mục đích ghi log)
/// * [softOtp]
@BuiltValue()
abstract class PaymentTopupRequest
    implements Built<PaymentTopupRequest, PaymentTopupRequestBuilder> {
  /// Mã giao dịch, được trả về khi gọi api review hoặc check limt
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số điện thoại cần nạp
  @BuiltValueField(wireName: r'phoneNo')
  String? get phoneNo;

  /// Số tài khoản thực hiện thanh toán
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  /// Số tiền thực hiện thanh toán
  @BuiltValueField(wireName: r'amount')
  num? get amount;

  /// Mệnh giá topup
  @BuiltValueField(wireName: r'cardValue')
  num? get cardValue;

  /// Mã sản phẩm cần thanh toán
  @BuiltValueField(wireName: r'productCode')
  String? get productCode;

  /// Loại thức nạp
  @BuiltValueField(wireName: r'topupType')
  PaymentTopupRequestTopupTypeEnum? get topupType;
  // enum topupTypeEnum {  TOPUP_PHONE,  TOPUP_DATA,  PINCODE_PHONE,  PINCODE_DATA,  PINCODE_GAME,  PAY_BILL,  };

  /// Tên của pin code (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'pinCodeName')
  String? get pinCodeName;

  /// Mô tả pin code (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'pinCodeDesc')
  String? get pinCodeDesc;

  /// Tên nhà cung cấp (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'supplierName')
  String? get supplierName;

  /// Icon nhà mạng (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'supplierUrl')
  String? get supplierUrl;

  @BuiltValueField(wireName: r'softOtp')
  VerifySoftOtpRequest? get softOtp;

  PaymentTopupRequest._();

  factory PaymentTopupRequest([void updates(PaymentTopupRequestBuilder b)]) =
      _$PaymentTopupRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PaymentTopupRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PaymentTopupRequest> get serializer =>
      _$PaymentTopupRequestSerializer();
}

class _$PaymentTopupRequestSerializer
    implements PrimitiveSerializer<PaymentTopupRequest> {
  @override
  final Iterable<Type> types = const [
    PaymentTopupRequest,
    _$PaymentTopupRequest
  ];

  @override
  final String wireName = r'PaymentTopupRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PaymentTopupRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.phoneNo != null) {
      yield r'phoneNo';
      yield serializers.serialize(
        object.phoneNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.cardValue != null) {
      yield r'cardValue';
      yield serializers.serialize(
        object.cardValue,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.productCode != null) {
      yield r'productCode';
      yield serializers.serialize(
        object.productCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.topupType != null) {
      yield r'topupType';
      yield serializers.serialize(
        object.topupType,
        specifiedType:
            const FullType.nullable(PaymentTopupRequestTopupTypeEnum),
      );
    }
    if (object.pinCodeName != null) {
      yield r'pinCodeName';
      yield serializers.serialize(
        object.pinCodeName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.pinCodeDesc != null) {
      yield r'pinCodeDesc';
      yield serializers.serialize(
        object.pinCodeDesc,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supplierName != null) {
      yield r'supplierName';
      yield serializers.serialize(
        object.supplierName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supplierUrl != null) {
      yield r'supplierUrl';
      yield serializers.serialize(
        object.supplierUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.softOtp != null) {
      yield r'softOtp';
      yield serializers.serialize(
        object.softOtp,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PaymentTopupRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PaymentTopupRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'phoneNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNo = valueDes;
          break;
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'cardValue':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.cardValue = valueDes;
          break;
        case r'productCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.productCode = valueDes;
          break;
        case r'topupType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(PaymentTopupRequestTopupTypeEnum),
          ) as PaymentTopupRequestTopupTypeEnum?;
          if (valueDes == null) continue;
          result.topupType = valueDes;
          break;
        case r'pinCodeName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.pinCodeName = valueDes;
          break;
        case r'pinCodeDesc':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.pinCodeDesc = valueDes;
          break;
        case r'supplierName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supplierName = valueDes;
          break;
        case r'supplierUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supplierUrl = valueDes;
          break;
        case r'softOtp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.softOtp.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PaymentTopupRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PaymentTopupRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PaymentTopupRequestTopupTypeEnum extends EnumClass {
  /// Loại thức nạp
  @BuiltValueEnumConst(wireName: r'TOPUP_PHONE')
  static const PaymentTopupRequestTopupTypeEnum TOPUP_PHONE =
      _$paymentTopupRequestTopupTypeEnum_TOPUP_PHONE;

  /// Loại thức nạp
  @BuiltValueEnumConst(wireName: r'TOPUP_DATA')
  static const PaymentTopupRequestTopupTypeEnum TOPUP_DATA =
      _$paymentTopupRequestTopupTypeEnum_TOPUP_DATA;

  /// Loại thức nạp
  @BuiltValueEnumConst(wireName: r'PINCODE_PHONE')
  static const PaymentTopupRequestTopupTypeEnum PINCODE_PHONE =
      _$paymentTopupRequestTopupTypeEnum_PINCODE_PHONE;

  /// Loại thức nạp
  @BuiltValueEnumConst(wireName: r'PINCODE_DATA')
  static const PaymentTopupRequestTopupTypeEnum PINCODE_DATA =
      _$paymentTopupRequestTopupTypeEnum_PINCODE_DATA;

  /// Loại thức nạp
  @BuiltValueEnumConst(wireName: r'PINCODE_GAME')
  static const PaymentTopupRequestTopupTypeEnum PINCODE_GAME =
      _$paymentTopupRequestTopupTypeEnum_PINCODE_GAME;

  /// Loại thức nạp
  @BuiltValueEnumConst(wireName: r'PAY_BILL')
  static const PaymentTopupRequestTopupTypeEnum PAY_BILL =
      _$paymentTopupRequestTopupTypeEnum_PAY_BILL;

  static Serializer<PaymentTopupRequestTopupTypeEnum> get serializer =>
      _$paymentTopupRequestTopupTypeEnumSerializer;

  const PaymentTopupRequestTopupTypeEnum._(String name) : super(name);

  static BuiltSet<PaymentTopupRequestTopupTypeEnum> get values =>
      _$paymentTopupRequestTopupTypeEnumValues;
  static PaymentTopupRequestTopupTypeEnum valueOf(String name) =>
      _$paymentTopupRequestTopupTypeEnumValueOf(name);
}
