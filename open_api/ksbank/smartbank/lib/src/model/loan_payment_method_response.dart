//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'loan_payment_method_response.g.dart';

/// Danh sách phương thức vay
///
/// Properties:
/// * [loanPaymentMethod] - Phương thức trả nợ
/// * [name] - Tên của phương thức trả nợ
@BuiltValue()
abstract class LoanPaymentMethodResponse
    implements
        Built<LoanPaymentMethodResponse, LoanPaymentMethodResponseBuilder> {
  /// Phương thức trả nợ
  @BuiltValueField(wireName: r'loanPaymentMethod')
  LoanPaymentMethodResponseLoanPaymentMethodEnum? get loanPaymentMethod;
  // enum loanPaymentMethodEnum {  DEBT_DECREASING,  FIXED_ANNUITY,  };

  /// Tên của phương thức trả nợ
  @BuiltValueField(wireName: r'name')
  String? get name;

  LoanPaymentMethodResponse._();

  factory LoanPaymentMethodResponse(
          [void updates(LoanPaymentMethodResponseBuilder b)]) =
      _$LoanPaymentMethodResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(LoanPaymentMethodResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<LoanPaymentMethodResponse> get serializer =>
      _$LoanPaymentMethodResponseSerializer();
}

class _$LoanPaymentMethodResponseSerializer
    implements PrimitiveSerializer<LoanPaymentMethodResponse> {
  @override
  final Iterable<Type> types = const [
    LoanPaymentMethodResponse,
    _$LoanPaymentMethodResponse
  ];

  @override
  final String wireName = r'LoanPaymentMethodResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    LoanPaymentMethodResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.loanPaymentMethod != null) {
      yield r'loanPaymentMethod';
      yield serializers.serialize(
        object.loanPaymentMethod,
        specifiedType: const FullType.nullable(
            LoanPaymentMethodResponseLoanPaymentMethodEnum),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    LoanPaymentMethodResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required LoanPaymentMethodResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'loanPaymentMethod':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                LoanPaymentMethodResponseLoanPaymentMethodEnum),
          ) as LoanPaymentMethodResponseLoanPaymentMethodEnum?;
          if (valueDes == null) continue;
          result.loanPaymentMethod = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  LoanPaymentMethodResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = LoanPaymentMethodResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class LoanPaymentMethodResponseLoanPaymentMethodEnum extends EnumClass {
  /// Phương thức trả nợ
  @BuiltValueEnumConst(wireName: r'DEBT_DECREASING')
  static const LoanPaymentMethodResponseLoanPaymentMethodEnum DEBT_DECREASING =
      _$loanPaymentMethodResponseLoanPaymentMethodEnum_DEBT_DECREASING;

  /// Phương thức trả nợ
  @BuiltValueEnumConst(wireName: r'FIXED_ANNUITY')
  static const LoanPaymentMethodResponseLoanPaymentMethodEnum FIXED_ANNUITY =
      _$loanPaymentMethodResponseLoanPaymentMethodEnum_FIXED_ANNUITY;

  static Serializer<LoanPaymentMethodResponseLoanPaymentMethodEnum>
      get serializer =>
          _$loanPaymentMethodResponseLoanPaymentMethodEnumSerializer;

  const LoanPaymentMethodResponseLoanPaymentMethodEnum._(String name)
      : super(name);

  static BuiltSet<LoanPaymentMethodResponseLoanPaymentMethodEnum> get values =>
      _$loanPaymentMethodResponseLoanPaymentMethodEnumValues;
  static LoanPaymentMethodResponseLoanPaymentMethodEnum valueOf(String name) =>
      _$loanPaymentMethodResponseLoanPaymentMethodEnumValueOf(name);
}
