// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_intra_bank_v2_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferIntraBankV2Request
    extends ReviewTransferIntraBankV2Request {
  @override
  final int? transactionGroup;
  @override
  final String? targetBankCode;
  @override
  final String? description;
  @override
  final int? isAccount;
  @override
  final String? accountNoFrom;
  @override
  final String? accountNoTo;
  @override
  final num? amount;

  factory _$ReviewTransferIntraBankV2Request(
          [void Function(ReviewTransferIntraBankV2RequestBuilder)? updates]) =>
      (ReviewTransferIntraBankV2RequestBuilder()..update(updates))._build();

  _$ReviewTransferIntraBankV2Request._(
      {this.transactionGroup,
      this.targetBankCode,
      this.description,
      this.isAccount,
      this.accountNoFrom,
      this.accountNoTo,
      this.amount})
      : super._();
  @override
  ReviewTransferIntraBankV2Request rebuild(
          void Function(ReviewTransferIntraBankV2RequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferIntraBankV2RequestBuilder toBuilder() =>
      ReviewTransferIntraBankV2RequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferIntraBankV2Request &&
        transactionGroup == other.transactionGroup &&
        targetBankCode == other.targetBankCode &&
        description == other.description &&
        isAccount == other.isAccount &&
        accountNoFrom == other.accountNoFrom &&
        accountNoTo == other.accountNoTo &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewTransferIntraBankV2Request')
          ..add('transactionGroup', transactionGroup)
          ..add('targetBankCode', targetBankCode)
          ..add('description', description)
          ..add('isAccount', isAccount)
          ..add('accountNoFrom', accountNoFrom)
          ..add('accountNoTo', accountNoTo)
          ..add('amount', amount))
        .toString();
  }
}

class ReviewTransferIntraBankV2RequestBuilder
    implements
        Builder<ReviewTransferIntraBankV2Request,
            ReviewTransferIntraBankV2RequestBuilder> {
  _$ReviewTransferIntraBankV2Request? _$v;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  ReviewTransferIntraBankV2RequestBuilder() {
    ReviewTransferIntraBankV2Request._defaults(this);
  }

  ReviewTransferIntraBankV2RequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionGroup = $v.transactionGroup;
      _targetBankCode = $v.targetBankCode;
      _description = $v.description;
      _isAccount = $v.isAccount;
      _accountNoFrom = $v.accountNoFrom;
      _accountNoTo = $v.accountNoTo;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferIntraBankV2Request other) {
    _$v = other as _$ReviewTransferIntraBankV2Request;
  }

  @override
  void update(void Function(ReviewTransferIntraBankV2RequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferIntraBankV2Request build() => _build();

  _$ReviewTransferIntraBankV2Request _build() {
    final _$result = _$v ??
        _$ReviewTransferIntraBankV2Request._(
          transactionGroup: transactionGroup,
          targetBankCode: targetBankCode,
          description: description,
          isAccount: isAccount,
          accountNoFrom: accountNoFrom,
          accountNoTo: accountNoTo,
          amount: amount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
