// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_saving_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_VND =
    const ReviewSavingAccountRequestCurrencyEnum._('VND');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_USD =
    const ReviewSavingAccountRequestCurrencyEnum._('USD');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_ACB =
    const ReviewSavingAccountRequestCurrencyEnum._('ACB');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_JPY =
    const ReviewSavingAccountRequestCurrencyEnum._('JPY');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_GOLD =
    const ReviewSavingAccountRequestCurrencyEnum._('GOLD');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_EUR =
    const ReviewSavingAccountRequestCurrencyEnum._('EUR');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_GBP =
    const ReviewSavingAccountRequestCurrencyEnum._('GBP');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_CHF =
    const ReviewSavingAccountRequestCurrencyEnum._('CHF');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_AUD =
    const ReviewSavingAccountRequestCurrencyEnum._('AUD');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_CAD =
    const ReviewSavingAccountRequestCurrencyEnum._('CAD');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_SGD =
    const ReviewSavingAccountRequestCurrencyEnum._('SGD');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_THB =
    const ReviewSavingAccountRequestCurrencyEnum._('THB');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_NOK =
    const ReviewSavingAccountRequestCurrencyEnum._('NOK');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_NZD =
    const ReviewSavingAccountRequestCurrencyEnum._('NZD');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_DKK =
    const ReviewSavingAccountRequestCurrencyEnum._('DKK');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_HKD =
    const ReviewSavingAccountRequestCurrencyEnum._('HKD');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_SEK =
    const ReviewSavingAccountRequestCurrencyEnum._('SEK');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_MYR =
    const ReviewSavingAccountRequestCurrencyEnum._('MYR');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_XAU =
    const ReviewSavingAccountRequestCurrencyEnum._('XAU');
const ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnum_MMK =
    const ReviewSavingAccountRequestCurrencyEnum._('MMK');

ReviewSavingAccountRequestCurrencyEnum
    _$reviewSavingAccountRequestCurrencyEnumValueOf(String name) {
  switch (name) {
    case 'VND':
      return _$reviewSavingAccountRequestCurrencyEnum_VND;
    case 'USD':
      return _$reviewSavingAccountRequestCurrencyEnum_USD;
    case 'ACB':
      return _$reviewSavingAccountRequestCurrencyEnum_ACB;
    case 'JPY':
      return _$reviewSavingAccountRequestCurrencyEnum_JPY;
    case 'GOLD':
      return _$reviewSavingAccountRequestCurrencyEnum_GOLD;
    case 'EUR':
      return _$reviewSavingAccountRequestCurrencyEnum_EUR;
    case 'GBP':
      return _$reviewSavingAccountRequestCurrencyEnum_GBP;
    case 'CHF':
      return _$reviewSavingAccountRequestCurrencyEnum_CHF;
    case 'AUD':
      return _$reviewSavingAccountRequestCurrencyEnum_AUD;
    case 'CAD':
      return _$reviewSavingAccountRequestCurrencyEnum_CAD;
    case 'SGD':
      return _$reviewSavingAccountRequestCurrencyEnum_SGD;
    case 'THB':
      return _$reviewSavingAccountRequestCurrencyEnum_THB;
    case 'NOK':
      return _$reviewSavingAccountRequestCurrencyEnum_NOK;
    case 'NZD':
      return _$reviewSavingAccountRequestCurrencyEnum_NZD;
    case 'DKK':
      return _$reviewSavingAccountRequestCurrencyEnum_DKK;
    case 'HKD':
      return _$reviewSavingAccountRequestCurrencyEnum_HKD;
    case 'SEK':
      return _$reviewSavingAccountRequestCurrencyEnum_SEK;
    case 'MYR':
      return _$reviewSavingAccountRequestCurrencyEnum_MYR;
    case 'XAU':
      return _$reviewSavingAccountRequestCurrencyEnum_XAU;
    case 'MMK':
      return _$reviewSavingAccountRequestCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<ReviewSavingAccountRequestCurrencyEnum>
    _$reviewSavingAccountRequestCurrencyEnumValues = BuiltSet<
        ReviewSavingAccountRequestCurrencyEnum>(const <ReviewSavingAccountRequestCurrencyEnum>[
  _$reviewSavingAccountRequestCurrencyEnum_VND,
  _$reviewSavingAccountRequestCurrencyEnum_USD,
  _$reviewSavingAccountRequestCurrencyEnum_ACB,
  _$reviewSavingAccountRequestCurrencyEnum_JPY,
  _$reviewSavingAccountRequestCurrencyEnum_GOLD,
  _$reviewSavingAccountRequestCurrencyEnum_EUR,
  _$reviewSavingAccountRequestCurrencyEnum_GBP,
  _$reviewSavingAccountRequestCurrencyEnum_CHF,
  _$reviewSavingAccountRequestCurrencyEnum_AUD,
  _$reviewSavingAccountRequestCurrencyEnum_CAD,
  _$reviewSavingAccountRequestCurrencyEnum_SGD,
  _$reviewSavingAccountRequestCurrencyEnum_THB,
  _$reviewSavingAccountRequestCurrencyEnum_NOK,
  _$reviewSavingAccountRequestCurrencyEnum_NZD,
  _$reviewSavingAccountRequestCurrencyEnum_DKK,
  _$reviewSavingAccountRequestCurrencyEnum_HKD,
  _$reviewSavingAccountRequestCurrencyEnum_SEK,
  _$reviewSavingAccountRequestCurrencyEnum_MYR,
  _$reviewSavingAccountRequestCurrencyEnum_XAU,
  _$reviewSavingAccountRequestCurrencyEnum_MMK,
]);

Serializer<ReviewSavingAccountRequestCurrencyEnum>
    _$reviewSavingAccountRequestCurrencyEnumSerializer =
    _$ReviewSavingAccountRequestCurrencyEnumSerializer();

class _$ReviewSavingAccountRequestCurrencyEnumSerializer
    implements PrimitiveSerializer<ReviewSavingAccountRequestCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[
    ReviewSavingAccountRequestCurrencyEnum
  ];
  @override
  final String wireName = 'ReviewSavingAccountRequestCurrencyEnum';

  @override
  Object serialize(Serializers serializers,
          ReviewSavingAccountRequestCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  ReviewSavingAccountRequestCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      ReviewSavingAccountRequestCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$ReviewSavingAccountRequest extends ReviewSavingAccountRequest {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? accountNumber;
  @override
  final double? amount;
  @override
  final ReviewSavingAccountRequestCurrencyEnum? currency;
  @override
  final String? termId;
  @override
  final double? rate;
  @override
  final int? finalTypeId;
  @override
  final String? finalAccountNumber;
  @override
  final num? balance;

  factory _$ReviewSavingAccountRequest(
          [void Function(ReviewSavingAccountRequestBuilder)? updates]) =>
      (ReviewSavingAccountRequestBuilder()..update(updates))._build();

  _$ReviewSavingAccountRequest._(
      {this.bankCif,
      this.transactionNo,
      this.accountNumber,
      this.amount,
      this.currency,
      this.termId,
      this.rate,
      this.finalTypeId,
      this.finalAccountNumber,
      this.balance})
      : super._();
  @override
  ReviewSavingAccountRequest rebuild(
          void Function(ReviewSavingAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewSavingAccountRequestBuilder toBuilder() =>
      ReviewSavingAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewSavingAccountRequest &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        accountNumber == other.accountNumber &&
        amount == other.amount &&
        currency == other.currency &&
        termId == other.termId &&
        rate == other.rate &&
        finalTypeId == other.finalTypeId &&
        finalAccountNumber == other.finalAccountNumber &&
        balance == other.balance;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, termId.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, finalTypeId.hashCode);
    _$hash = $jc(_$hash, finalAccountNumber.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewSavingAccountRequest')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('accountNumber', accountNumber)
          ..add('amount', amount)
          ..add('currency', currency)
          ..add('termId', termId)
          ..add('rate', rate)
          ..add('finalTypeId', finalTypeId)
          ..add('finalAccountNumber', finalAccountNumber)
          ..add('balance', balance))
        .toString();
  }
}

class ReviewSavingAccountRequestBuilder
    implements
        Builder<ReviewSavingAccountRequest, ReviewSavingAccountRequestBuilder> {
  _$ReviewSavingAccountRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  ReviewSavingAccountRequestCurrencyEnum? _currency;
  ReviewSavingAccountRequestCurrencyEnum? get currency => _$this._currency;
  set currency(ReviewSavingAccountRequestCurrencyEnum? currency) =>
      _$this._currency = currency;

  String? _termId;
  String? get termId => _$this._termId;
  set termId(String? termId) => _$this._termId = termId;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  int? _finalTypeId;
  int? get finalTypeId => _$this._finalTypeId;
  set finalTypeId(int? finalTypeId) => _$this._finalTypeId = finalTypeId;

  String? _finalAccountNumber;
  String? get finalAccountNumber => _$this._finalAccountNumber;
  set finalAccountNumber(String? finalAccountNumber) =>
      _$this._finalAccountNumber = finalAccountNumber;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  ReviewSavingAccountRequestBuilder() {
    ReviewSavingAccountRequest._defaults(this);
  }

  ReviewSavingAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _accountNumber = $v.accountNumber;
      _amount = $v.amount;
      _currency = $v.currency;
      _termId = $v.termId;
      _rate = $v.rate;
      _finalTypeId = $v.finalTypeId;
      _finalAccountNumber = $v.finalAccountNumber;
      _balance = $v.balance;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewSavingAccountRequest other) {
    _$v = other as _$ReviewSavingAccountRequest;
  }

  @override
  void update(void Function(ReviewSavingAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewSavingAccountRequest build() => _build();

  _$ReviewSavingAccountRequest _build() {
    final _$result = _$v ??
        _$ReviewSavingAccountRequest._(
          bankCif: bankCif,
          transactionNo: transactionNo,
          accountNumber: accountNumber,
          amount: amount,
          currency: currency,
          termId: termId,
          rate: rate,
          finalTypeId: finalTypeId,
          finalAccountNumber: finalAccountNumber,
          balance: balance,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
