// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_bill_schedule_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewBillScheduleResponse extends ReviewBillScheduleResponse {
  @override
  final String? transactionNumber;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;
  @override
  final String? productCode;
  @override
  final String? customerCode;
  @override
  final String? customerName;
  @override
  final String? customerAddress;

  factory _$ReviewBillScheduleResponse(
          [void Function(ReviewBillScheduleResponseBuilder)? updates]) =>
      (ReviewBillScheduleResponseBuilder()..update(updates))._build();

  _$ReviewBillScheduleResponse._(
      {this.transactionNumber,
      this.transNextStep,
      this.content,
      this.productCode,
      this.customerCode,
      this.customerName,
      this.customerAddress})
      : super._();
  @override
  ReviewBillScheduleResponse rebuild(
          void Function(ReviewBillScheduleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewBillScheduleResponseBuilder toBuilder() =>
      ReviewBillScheduleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewBillScheduleResponse &&
        transactionNumber == other.transactionNumber &&
        transNextStep == other.transNextStep &&
        content == other.content &&
        productCode == other.productCode &&
        customerCode == other.customerCode &&
        customerName == other.customerName &&
        customerAddress == other.customerAddress;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, customerAddress.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewBillScheduleResponse')
          ..add('transactionNumber', transactionNumber)
          ..add('transNextStep', transNextStep)
          ..add('content', content)
          ..add('productCode', productCode)
          ..add('customerCode', customerCode)
          ..add('customerName', customerName)
          ..add('customerAddress', customerAddress))
        .toString();
  }
}

class ReviewBillScheduleResponseBuilder
    implements
        Builder<ReviewBillScheduleResponse, ReviewBillScheduleResponseBuilder> {
  _$ReviewBillScheduleResponse? _$v;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _customerAddress;
  String? get customerAddress => _$this._customerAddress;
  set customerAddress(String? customerAddress) =>
      _$this._customerAddress = customerAddress;

  ReviewBillScheduleResponseBuilder() {
    ReviewBillScheduleResponse._defaults(this);
  }

  ReviewBillScheduleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNumber = $v.transactionNumber;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _productCode = $v.productCode;
      _customerCode = $v.customerCode;
      _customerName = $v.customerName;
      _customerAddress = $v.customerAddress;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewBillScheduleResponse other) {
    _$v = other as _$ReviewBillScheduleResponse;
  }

  @override
  void update(void Function(ReviewBillScheduleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewBillScheduleResponse build() => _build();

  _$ReviewBillScheduleResponse _build() {
    final _$result = _$v ??
        _$ReviewBillScheduleResponse._(
          transactionNumber: transactionNumber,
          transNextStep: transNextStep,
          content: content,
          productCode: productCode,
          customerCode: customerCode,
          customerName: customerName,
          customerAddress: customerAddress,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
