// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_online_saving_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_VND =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('VND');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_USD =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('USD');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_ACB =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('ACB');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_JPY =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('JPY');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_GOLD =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('GOLD');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_EUR =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('EUR');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_GBP =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('GBP');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_CHF =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('CHF');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_AUD =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('AUD');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_CAD =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('CAD');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_SGD =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('SGD');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_THB =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('THB');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_NOK =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('NOK');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_NZD =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('NZD');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_DKK =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('DKK');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_HKD =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('HKD');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_SEK =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('SEK');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_MYR =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('MYR');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_XAU =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('XAU');
const OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnum_MMK =
    const OpenOnlineSavingAccountRequestCurrencyEnum._('MMK');

OpenOnlineSavingAccountRequestCurrencyEnum
    _$openOnlineSavingAccountRequestCurrencyEnumValueOf(String name) {
  switch (name) {
    case 'VND':
      return _$openOnlineSavingAccountRequestCurrencyEnum_VND;
    case 'USD':
      return _$openOnlineSavingAccountRequestCurrencyEnum_USD;
    case 'ACB':
      return _$openOnlineSavingAccountRequestCurrencyEnum_ACB;
    case 'JPY':
      return _$openOnlineSavingAccountRequestCurrencyEnum_JPY;
    case 'GOLD':
      return _$openOnlineSavingAccountRequestCurrencyEnum_GOLD;
    case 'EUR':
      return _$openOnlineSavingAccountRequestCurrencyEnum_EUR;
    case 'GBP':
      return _$openOnlineSavingAccountRequestCurrencyEnum_GBP;
    case 'CHF':
      return _$openOnlineSavingAccountRequestCurrencyEnum_CHF;
    case 'AUD':
      return _$openOnlineSavingAccountRequestCurrencyEnum_AUD;
    case 'CAD':
      return _$openOnlineSavingAccountRequestCurrencyEnum_CAD;
    case 'SGD':
      return _$openOnlineSavingAccountRequestCurrencyEnum_SGD;
    case 'THB':
      return _$openOnlineSavingAccountRequestCurrencyEnum_THB;
    case 'NOK':
      return _$openOnlineSavingAccountRequestCurrencyEnum_NOK;
    case 'NZD':
      return _$openOnlineSavingAccountRequestCurrencyEnum_NZD;
    case 'DKK':
      return _$openOnlineSavingAccountRequestCurrencyEnum_DKK;
    case 'HKD':
      return _$openOnlineSavingAccountRequestCurrencyEnum_HKD;
    case 'SEK':
      return _$openOnlineSavingAccountRequestCurrencyEnum_SEK;
    case 'MYR':
      return _$openOnlineSavingAccountRequestCurrencyEnum_MYR;
    case 'XAU':
      return _$openOnlineSavingAccountRequestCurrencyEnum_XAU;
    case 'MMK':
      return _$openOnlineSavingAccountRequestCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<OpenOnlineSavingAccountRequestCurrencyEnum>
    _$openOnlineSavingAccountRequestCurrencyEnumValues = BuiltSet<
        OpenOnlineSavingAccountRequestCurrencyEnum>(const <OpenOnlineSavingAccountRequestCurrencyEnum>[
  _$openOnlineSavingAccountRequestCurrencyEnum_VND,
  _$openOnlineSavingAccountRequestCurrencyEnum_USD,
  _$openOnlineSavingAccountRequestCurrencyEnum_ACB,
  _$openOnlineSavingAccountRequestCurrencyEnum_JPY,
  _$openOnlineSavingAccountRequestCurrencyEnum_GOLD,
  _$openOnlineSavingAccountRequestCurrencyEnum_EUR,
  _$openOnlineSavingAccountRequestCurrencyEnum_GBP,
  _$openOnlineSavingAccountRequestCurrencyEnum_CHF,
  _$openOnlineSavingAccountRequestCurrencyEnum_AUD,
  _$openOnlineSavingAccountRequestCurrencyEnum_CAD,
  _$openOnlineSavingAccountRequestCurrencyEnum_SGD,
  _$openOnlineSavingAccountRequestCurrencyEnum_THB,
  _$openOnlineSavingAccountRequestCurrencyEnum_NOK,
  _$openOnlineSavingAccountRequestCurrencyEnum_NZD,
  _$openOnlineSavingAccountRequestCurrencyEnum_DKK,
  _$openOnlineSavingAccountRequestCurrencyEnum_HKD,
  _$openOnlineSavingAccountRequestCurrencyEnum_SEK,
  _$openOnlineSavingAccountRequestCurrencyEnum_MYR,
  _$openOnlineSavingAccountRequestCurrencyEnum_XAU,
  _$openOnlineSavingAccountRequestCurrencyEnum_MMK,
]);

Serializer<OpenOnlineSavingAccountRequestCurrencyEnum>
    _$openOnlineSavingAccountRequestCurrencyEnumSerializer =
    _$OpenOnlineSavingAccountRequestCurrencyEnumSerializer();

class _$OpenOnlineSavingAccountRequestCurrencyEnumSerializer
    implements PrimitiveSerializer<OpenOnlineSavingAccountRequestCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[
    OpenOnlineSavingAccountRequestCurrencyEnum
  ];
  @override
  final String wireName = 'OpenOnlineSavingAccountRequestCurrencyEnum';

  @override
  Object serialize(Serializers serializers,
          OpenOnlineSavingAccountRequestCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  OpenOnlineSavingAccountRequestCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      OpenOnlineSavingAccountRequestCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$OpenOnlineSavingAccountRequest extends OpenOnlineSavingAccountRequest {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? accountNumber;
  @override
  final double? amount;
  @override
  final OpenOnlineSavingAccountRequestCurrencyEnum? currency;
  @override
  final String? termId;
  @override
  final double? rate;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final int? finalTypeId;
  @override
  final String? finalTypeName;
  @override
  final String? finalAccountNumber;
  @override
  final num? interestAmountEndOfTerm;
  @override
  final String? referralCode;
  @override
  final VerifySoftOtpRequest? verifySoftOtpData;

  factory _$OpenOnlineSavingAccountRequest(
          [void Function(OpenOnlineSavingAccountRequestBuilder)? updates]) =>
      (OpenOnlineSavingAccountRequestBuilder()..update(updates))._build();

  _$OpenOnlineSavingAccountRequest._(
      {this.bankCif,
      this.transactionNo,
      this.accountNumber,
      this.amount,
      this.currency,
      this.termId,
      this.rate,
      this.contractDate,
      this.dueDate,
      this.finalTypeId,
      this.finalTypeName,
      this.finalAccountNumber,
      this.interestAmountEndOfTerm,
      this.referralCode,
      this.verifySoftOtpData})
      : super._();
  @override
  OpenOnlineSavingAccountRequest rebuild(
          void Function(OpenOnlineSavingAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenOnlineSavingAccountRequestBuilder toBuilder() =>
      OpenOnlineSavingAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenOnlineSavingAccountRequest &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        accountNumber == other.accountNumber &&
        amount == other.amount &&
        currency == other.currency &&
        termId == other.termId &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        finalTypeId == other.finalTypeId &&
        finalTypeName == other.finalTypeName &&
        finalAccountNumber == other.finalAccountNumber &&
        interestAmountEndOfTerm == other.interestAmountEndOfTerm &&
        referralCode == other.referralCode &&
        verifySoftOtpData == other.verifySoftOtpData;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, termId.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, finalTypeId.hashCode);
    _$hash = $jc(_$hash, finalTypeName.hashCode);
    _$hash = $jc(_$hash, finalAccountNumber.hashCode);
    _$hash = $jc(_$hash, interestAmountEndOfTerm.hashCode);
    _$hash = $jc(_$hash, referralCode.hashCode);
    _$hash = $jc(_$hash, verifySoftOtpData.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenOnlineSavingAccountRequest')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('accountNumber', accountNumber)
          ..add('amount', amount)
          ..add('currency', currency)
          ..add('termId', termId)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('finalTypeId', finalTypeId)
          ..add('finalTypeName', finalTypeName)
          ..add('finalAccountNumber', finalAccountNumber)
          ..add('interestAmountEndOfTerm', interestAmountEndOfTerm)
          ..add('referralCode', referralCode)
          ..add('verifySoftOtpData', verifySoftOtpData))
        .toString();
  }
}

class OpenOnlineSavingAccountRequestBuilder
    implements
        Builder<OpenOnlineSavingAccountRequest,
            OpenOnlineSavingAccountRequestBuilder> {
  _$OpenOnlineSavingAccountRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  OpenOnlineSavingAccountRequestCurrencyEnum? _currency;
  OpenOnlineSavingAccountRequestCurrencyEnum? get currency => _$this._currency;
  set currency(OpenOnlineSavingAccountRequestCurrencyEnum? currency) =>
      _$this._currency = currency;

  String? _termId;
  String? get termId => _$this._termId;
  set termId(String? termId) => _$this._termId = termId;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  int? _finalTypeId;
  int? get finalTypeId => _$this._finalTypeId;
  set finalTypeId(int? finalTypeId) => _$this._finalTypeId = finalTypeId;

  String? _finalTypeName;
  String? get finalTypeName => _$this._finalTypeName;
  set finalTypeName(String? finalTypeName) =>
      _$this._finalTypeName = finalTypeName;

  String? _finalAccountNumber;
  String? get finalAccountNumber => _$this._finalAccountNumber;
  set finalAccountNumber(String? finalAccountNumber) =>
      _$this._finalAccountNumber = finalAccountNumber;

  num? _interestAmountEndOfTerm;
  num? get interestAmountEndOfTerm => _$this._interestAmountEndOfTerm;
  set interestAmountEndOfTerm(num? interestAmountEndOfTerm) =>
      _$this._interestAmountEndOfTerm = interestAmountEndOfTerm;

  String? _referralCode;
  String? get referralCode => _$this._referralCode;
  set referralCode(String? referralCode) => _$this._referralCode = referralCode;

  VerifySoftOtpRequestBuilder? _verifySoftOtpData;
  VerifySoftOtpRequestBuilder get verifySoftOtpData =>
      _$this._verifySoftOtpData ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtpData(VerifySoftOtpRequestBuilder? verifySoftOtpData) =>
      _$this._verifySoftOtpData = verifySoftOtpData;

  OpenOnlineSavingAccountRequestBuilder() {
    OpenOnlineSavingAccountRequest._defaults(this);
  }

  OpenOnlineSavingAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _accountNumber = $v.accountNumber;
      _amount = $v.amount;
      _currency = $v.currency;
      _termId = $v.termId;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _finalTypeId = $v.finalTypeId;
      _finalTypeName = $v.finalTypeName;
      _finalAccountNumber = $v.finalAccountNumber;
      _interestAmountEndOfTerm = $v.interestAmountEndOfTerm;
      _referralCode = $v.referralCode;
      _verifySoftOtpData = $v.verifySoftOtpData?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenOnlineSavingAccountRequest other) {
    _$v = other as _$OpenOnlineSavingAccountRequest;
  }

  @override
  void update(void Function(OpenOnlineSavingAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenOnlineSavingAccountRequest build() => _build();

  _$OpenOnlineSavingAccountRequest _build() {
    _$OpenOnlineSavingAccountRequest _$result;
    try {
      _$result = _$v ??
          _$OpenOnlineSavingAccountRequest._(
            bankCif: bankCif,
            transactionNo: transactionNo,
            accountNumber: accountNumber,
            amount: amount,
            currency: currency,
            termId: termId,
            rate: rate,
            contractDate: contractDate,
            dueDate: dueDate,
            finalTypeId: finalTypeId,
            finalTypeName: finalTypeName,
            finalAccountNumber: finalAccountNumber,
            interestAmountEndOfTerm: interestAmountEndOfTerm,
            referralCode: referralCode,
            verifySoftOtpData: _verifySoftOtpData?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtpData';
        _verifySoftOtpData?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'OpenOnlineSavingAccountRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
