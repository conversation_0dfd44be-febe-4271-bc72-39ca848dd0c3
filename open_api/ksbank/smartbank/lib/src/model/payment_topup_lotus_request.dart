//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'payment_topup_lotus_request.g.dart';

/// PaymentTopupLotusRequest
///
/// Properties:
/// * [phoneNumber] - Số điện thoại
/// * [telecomProvider] - <PERSON><PERSON><PERSON> mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
/// * [amount] - <PERSON>ố tiền cần thanh toán
/// * [cardValue] - Mệnh giá thẻ
/// * [cardValueCode] - Mã nhà cung cấp (Mobile Phone)
/// * [cardValueName] - Tên mô tả hạn mức thẻ
/// * [topupType] - <PERSON><PERSON><PERSON> thức thanh toán : 1 - t<PERSON><PERSON> tr<PERSON>, 2 - <PERSON><PERSON><PERSON>
/// * [cifNo] - Bankinf: Mã cif của khách hàng thuộc 1 ngân hàng cụ thể
/// * [accountNo] - Bankinf: Số tài khoản ăn theo mã cif
@BuiltValue()
abstract class PaymentTopupLotusRequest
    implements
        Built<PaymentTopupLotusRequest, PaymentTopupLotusRequestBuilder> {
  /// Số điện thoại
  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueField(wireName: r'telecomProvider')
  PaymentTopupLotusRequestTelecomProviderEnum? get telecomProvider;
  // enum telecomProviderEnum {  MOBIFONE,  VINAPHONE,  VIETTEL,  VIETNAMOBILE,  GMOBILE,  };

  /// Số tiền cần thanh toán
  @BuiltValueField(wireName: r'amount')
  num? get amount;

  /// Mệnh giá thẻ
  @BuiltValueField(wireName: r'cardValue')
  num? get cardValue;

  /// Mã nhà cung cấp (Mobile Phone)
  @BuiltValueField(wireName: r'cardValueCode')
  String? get cardValueCode;

  /// Tên mô tả hạn mức thẻ
  @BuiltValueField(wireName: r'cardValueName')
  String? get cardValueName;

  /// Hình thức thanh toán : 1 - trả trước, 2 - Trả sau
  @BuiltValueField(wireName: r'topupType')
  PaymentTopupLotusRequestTopupTypeEnum? get topupType;
  // enum topupTypeEnum {  PREPAID,  POST_PAYMENT,  };

  /// Bankinf: Mã cif của khách hàng thuộc 1 ngân hàng cụ thể
  @BuiltValueField(wireName: r'cifNo')
  String? get cifNo;

  /// Bankinf: Số tài khoản ăn theo mã cif
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  PaymentTopupLotusRequest._();

  factory PaymentTopupLotusRequest(
          [void updates(PaymentTopupLotusRequestBuilder b)]) =
      _$PaymentTopupLotusRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PaymentTopupLotusRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PaymentTopupLotusRequest> get serializer =>
      _$PaymentTopupLotusRequestSerializer();
}

class _$PaymentTopupLotusRequestSerializer
    implements PrimitiveSerializer<PaymentTopupLotusRequest> {
  @override
  final Iterable<Type> types = const [
    PaymentTopupLotusRequest,
    _$PaymentTopupLotusRequest
  ];

  @override
  final String wireName = r'PaymentTopupLotusRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PaymentTopupLotusRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.telecomProvider != null) {
      yield r'telecomProvider';
      yield serializers.serialize(
        object.telecomProvider,
        specifiedType: const FullType.nullable(
            PaymentTopupLotusRequestTelecomProviderEnum),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.cardValue != null) {
      yield r'cardValue';
      yield serializers.serialize(
        object.cardValue,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.cardValueCode != null) {
      yield r'cardValueCode';
      yield serializers.serialize(
        object.cardValueCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cardValueName != null) {
      yield r'cardValueName';
      yield serializers.serialize(
        object.cardValueName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.topupType != null) {
      yield r'topupType';
      yield serializers.serialize(
        object.topupType,
        specifiedType:
            const FullType.nullable(PaymentTopupLotusRequestTopupTypeEnum),
      );
    }
    if (object.cifNo != null) {
      yield r'cifNo';
      yield serializers.serialize(
        object.cifNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PaymentTopupLotusRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PaymentTopupLotusRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNumber = valueDes;
          break;
        case r'telecomProvider':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                PaymentTopupLotusRequestTelecomProviderEnum),
          ) as PaymentTopupLotusRequestTelecomProviderEnum?;
          if (valueDes == null) continue;
          result.telecomProvider = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'cardValue':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.cardValue = valueDes;
          break;
        case r'cardValueCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardValueCode = valueDes;
          break;
        case r'cardValueName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardValueName = valueDes;
          break;
        case r'topupType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(PaymentTopupLotusRequestTopupTypeEnum),
          ) as PaymentTopupLotusRequestTopupTypeEnum?;
          if (valueDes == null) continue;
          result.topupType = valueDes;
          break;
        case r'cifNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cifNo = valueDes;
          break;
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PaymentTopupLotusRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PaymentTopupLotusRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PaymentTopupLotusRequestTelecomProviderEnum extends EnumClass {
  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'MOBIFONE')
  static const PaymentTopupLotusRequestTelecomProviderEnum MOBIFONE =
      _$paymentTopupLotusRequestTelecomProviderEnum_MOBIFONE;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'VINAPHONE')
  static const PaymentTopupLotusRequestTelecomProviderEnum VINAPHONE =
      _$paymentTopupLotusRequestTelecomProviderEnum_VINAPHONE;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'VIETTEL')
  static const PaymentTopupLotusRequestTelecomProviderEnum VIETTEL =
      _$paymentTopupLotusRequestTelecomProviderEnum_VIETTEL;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'VIETNAMOBILE')
  static const PaymentTopupLotusRequestTelecomProviderEnum VIETNAMOBILE =
      _$paymentTopupLotusRequestTelecomProviderEnum_VIETNAMOBILE;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'GMOBILE')
  static const PaymentTopupLotusRequestTelecomProviderEnum GMOBILE =
      _$paymentTopupLotusRequestTelecomProviderEnum_GMOBILE;

  static Serializer<PaymentTopupLotusRequestTelecomProviderEnum>
      get serializer => _$paymentTopupLotusRequestTelecomProviderEnumSerializer;

  const PaymentTopupLotusRequestTelecomProviderEnum._(String name)
      : super(name);

  static BuiltSet<PaymentTopupLotusRequestTelecomProviderEnum> get values =>
      _$paymentTopupLotusRequestTelecomProviderEnumValues;
  static PaymentTopupLotusRequestTelecomProviderEnum valueOf(String name) =>
      _$paymentTopupLotusRequestTelecomProviderEnumValueOf(name);
}

class PaymentTopupLotusRequestTopupTypeEnum extends EnumClass {
  /// Hình thức thanh toán : 1 - trả trước, 2 - Trả sau
  @BuiltValueEnumConst(wireName: r'PREPAID')
  static const PaymentTopupLotusRequestTopupTypeEnum PREPAID =
      _$paymentTopupLotusRequestTopupTypeEnum_PREPAID;

  /// Hình thức thanh toán : 1 - trả trước, 2 - Trả sau
  @BuiltValueEnumConst(wireName: r'POST_PAYMENT')
  static const PaymentTopupLotusRequestTopupTypeEnum POST_PAYMENT =
      _$paymentTopupLotusRequestTopupTypeEnum_POST_PAYMENT;

  static Serializer<PaymentTopupLotusRequestTopupTypeEnum> get serializer =>
      _$paymentTopupLotusRequestTopupTypeEnumSerializer;

  const PaymentTopupLotusRequestTopupTypeEnum._(String name) : super(name);

  static BuiltSet<PaymentTopupLotusRequestTopupTypeEnum> get values =>
      _$paymentTopupLotusRequestTopupTypeEnumValues;
  static PaymentTopupLotusRequestTopupTypeEnum valueOf(String name) =>
      _$paymentTopupLotusRequestTopupTypeEnumValueOf(name);
}
