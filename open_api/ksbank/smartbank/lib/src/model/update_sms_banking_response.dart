//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_sms_banking_response.g.dart';

/// UpdateSmsBankingResponse
///
/// Properties:
/// * [fee]
/// * [action]
/// * [regPhoneNumbers] - Số điện thoại nhận SMS biến động số dư
/// * [regCasaAccounts] - Danh sách tài khoản thanh toán nhận biến động (ngăn cách bằng dấu phẩy)
/// * [regTdAccounts] - Cờ check đăng ký nhận SMS cho tài khoản tiết kiệm (0 - Không đăng ký, 1 - Đăng ký)
/// * [regLoanAccounts] - C<PERSON> check đăng ký nhận SMS cho tài khoản vay (0 - Không đăng ký, 1 - <PERSON><PERSON><PERSON> ký)
@BuiltValue()
abstract class UpdateSmsBankingResponse
    implements
        Built<UpdateSmsBankingResponse, UpdateSmsBankingResponseBuilder> {
  @BuiltValueField(wireName: r'fee')
  num? get fee;

  @BuiltValueField(wireName: r'action')
  UpdateSmsBankingResponseActionEnum? get action;
  // enum actionEnum {  CANCEL,  REGISTER,  CHANGE_ACCOUNT,  };

  /// Số điện thoại nhận SMS biến động số dư
  @BuiltValueField(wireName: r'regPhoneNumbers')
  String? get regPhoneNumbers;

  /// Danh sách tài khoản thanh toán nhận biến động (ngăn cách bằng dấu phẩy)
  @BuiltValueField(wireName: r'regCasaAccounts')
  String? get regCasaAccounts;

  /// Cờ check đăng ký nhận SMS cho tài khoản tiết kiệm (0 - Không đăng ký, 1 - Đăng ký)
  @BuiltValueField(wireName: r'regTdAccounts')
  int? get regTdAccounts;

  /// Cờ check đăng ký nhận SMS cho tài khoản vay (0 - Không đăng ký, 1 - Đăng ký)
  @BuiltValueField(wireName: r'regLoanAccounts')
  int? get regLoanAccounts;

  UpdateSmsBankingResponse._();

  factory UpdateSmsBankingResponse(
          [void updates(UpdateSmsBankingResponseBuilder b)]) =
      _$UpdateSmsBankingResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateSmsBankingResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateSmsBankingResponse> get serializer =>
      _$UpdateSmsBankingResponseSerializer();
}

class _$UpdateSmsBankingResponseSerializer
    implements PrimitiveSerializer<UpdateSmsBankingResponse> {
  @override
  final Iterable<Type> types = const [
    UpdateSmsBankingResponse,
    _$UpdateSmsBankingResponse
  ];

  @override
  final String wireName = r'UpdateSmsBankingResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateSmsBankingResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.fee != null) {
      yield r'fee';
      yield serializers.serialize(
        object.fee,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.action != null) {
      yield r'action';
      yield serializers.serialize(
        object.action,
        specifiedType:
            const FullType.nullable(UpdateSmsBankingResponseActionEnum),
      );
    }
    if (object.regPhoneNumbers != null) {
      yield r'regPhoneNumbers';
      yield serializers.serialize(
        object.regPhoneNumbers,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.regCasaAccounts != null) {
      yield r'regCasaAccounts';
      yield serializers.serialize(
        object.regCasaAccounts,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.regTdAccounts != null) {
      yield r'regTdAccounts';
      yield serializers.serialize(
        object.regTdAccounts,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.regLoanAccounts != null) {
      yield r'regLoanAccounts';
      yield serializers.serialize(
        object.regLoanAccounts,
        specifiedType: const FullType.nullable(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateSmsBankingResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateSmsBankingResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'fee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.fee = valueDes;
          break;
        case r'action':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(UpdateSmsBankingResponseActionEnum),
          ) as UpdateSmsBankingResponseActionEnum?;
          if (valueDes == null) continue;
          result.action = valueDes;
          break;
        case r'regPhoneNumbers':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.regPhoneNumbers = valueDes;
          break;
        case r'regCasaAccounts':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.regCasaAccounts = valueDes;
          break;
        case r'regTdAccounts':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.regTdAccounts = valueDes;
          break;
        case r'regLoanAccounts':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.regLoanAccounts = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateSmsBankingResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateSmsBankingResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class UpdateSmsBankingResponseActionEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'CANCEL')
  static const UpdateSmsBankingResponseActionEnum CANCEL =
      _$updateSmsBankingResponseActionEnum_CANCEL;
  @BuiltValueEnumConst(wireName: r'REGISTER')
  static const UpdateSmsBankingResponseActionEnum REGISTER =
      _$updateSmsBankingResponseActionEnum_REGISTER;
  @BuiltValueEnumConst(wireName: r'CHANGE_ACCOUNT')
  static const UpdateSmsBankingResponseActionEnum CHANGE_ACCOUNT =
      _$updateSmsBankingResponseActionEnum_CHANGE_ACCOUNT;

  static Serializer<UpdateSmsBankingResponseActionEnum> get serializer =>
      _$updateSmsBankingResponseActionEnumSerializer;

  const UpdateSmsBankingResponseActionEnum._(String name) : super(name);

  static BuiltSet<UpdateSmsBankingResponseActionEnum> get values =>
      _$updateSmsBankingResponseActionEnumValues;
  static UpdateSmsBankingResponseActionEnum valueOf(String name) =>
      _$updateSmsBankingResponseActionEnumValueOf(name);
}
