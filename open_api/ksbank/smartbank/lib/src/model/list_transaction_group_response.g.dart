// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_transaction_group_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListTransactionGroupResponse extends ListTransactionGroupResponse {
  @override
  final BuiltList<TransactionCategory>? transactionGroups;

  factory _$ListTransactionGroupResponse(
          [void Function(ListTransactionGroupResponseBuilder)? updates]) =>
      (ListTransactionGroupResponseBuilder()..update(updates))._build();

  _$ListTransactionGroupResponse._({this.transactionGroups}) : super._();
  @override
  ListTransactionGroupResponse rebuild(
          void Function(ListTransactionGroupResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListTransactionGroupResponseBuilder toBuilder() =>
      ListTransactionGroupResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListTransactionGroupResponse &&
        transactionGroups == other.transactionGroups;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionGroups.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListTransactionGroupResponse')
          ..add('transactionGroups', transactionGroups))
        .toString();
  }
}

class ListTransactionGroupResponseBuilder
    implements
        Builder<ListTransactionGroupResponse,
            ListTransactionGroupResponseBuilder> {
  _$ListTransactionGroupResponse? _$v;

  ListBuilder<TransactionCategory>? _transactionGroups;
  ListBuilder<TransactionCategory> get transactionGroups =>
      _$this._transactionGroups ??= ListBuilder<TransactionCategory>();
  set transactionGroups(ListBuilder<TransactionCategory>? transactionGroups) =>
      _$this._transactionGroups = transactionGroups;

  ListTransactionGroupResponseBuilder() {
    ListTransactionGroupResponse._defaults(this);
  }

  ListTransactionGroupResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionGroups = $v.transactionGroups?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListTransactionGroupResponse other) {
    _$v = other as _$ListTransactionGroupResponse;
  }

  @override
  void update(void Function(ListTransactionGroupResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListTransactionGroupResponse build() => _build();

  _$ListTransactionGroupResponse _build() {
    _$ListTransactionGroupResponse _$result;
    try {
      _$result = _$v ??
          _$ListTransactionGroupResponse._(
            transactionGroups: _transactionGroups?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transactionGroups';
        _transactionGroups?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListTransactionGroupResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
