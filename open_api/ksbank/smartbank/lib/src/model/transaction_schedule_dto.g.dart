// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_schedule_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionScheduleDto extends TransactionScheduleDto {
  @override
  final String? id;
  @override
  final String? cifNo;
  @override
  final String? accountNoFrom;
  @override
  final num? amount;
  @override
  final int? whoCharge;
  @override
  final int? chargeAmount;
  @override
  final int? isAccount;
  @override
  final String? bankCodeId;
  @override
  final String? bankCode;
  @override
  final String? targetBankImage;
  @override
  final String? targetBankCodeCitAd;
  @override
  final String? targetBankImageType;
  @override
  final String? targetBankName;
  @override
  final String? shortName;
  @override
  final String? targetCommonBankName;
  @override
  final String? accountNoTo;
  @override
  final String? aliasName;
  @override
  final int? is247;
  @override
  final int? isNow;
  @override
  final int? isSaveToTemplate;
  @override
  final String? description;
  @override
  final int? transactionCategoryId;
  @override
  final String? categoryName;
  @override
  final String? categoryImage;
  @override
  final String? categoryImageType;
  @override
  final String? scheduleGroup;
  @override
  final DateTime? fromDate;
  @override
  final DateTime? toDate;
  @override
  final String? days;
  @override
  final String? apiUrl;
  @override
  final DateTime? createdAt;
  @override
  final String? createdBy;
  @override
  final DateTime? updatedAt;
  @override
  final String? updatedBy;
  @override
  final String? beneficiaryName;
  @override
  final int? regionId;
  @override
  final int? bankId;
  @override
  final String? branchId;
  @override
  final String? branchName;
  @override
  final String? idCard;
  @override
  final Date? issueDate;
  @override
  final String? placeBy;
  @override
  final String? scheduleId;

  factory _$TransactionScheduleDto(
          [void Function(TransactionScheduleDtoBuilder)? updates]) =>
      (TransactionScheduleDtoBuilder()..update(updates))._build();

  _$TransactionScheduleDto._(
      {this.id,
      this.cifNo,
      this.accountNoFrom,
      this.amount,
      this.whoCharge,
      this.chargeAmount,
      this.isAccount,
      this.bankCodeId,
      this.bankCode,
      this.targetBankImage,
      this.targetBankCodeCitAd,
      this.targetBankImageType,
      this.targetBankName,
      this.shortName,
      this.targetCommonBankName,
      this.accountNoTo,
      this.aliasName,
      this.is247,
      this.isNow,
      this.isSaveToTemplate,
      this.description,
      this.transactionCategoryId,
      this.categoryName,
      this.categoryImage,
      this.categoryImageType,
      this.scheduleGroup,
      this.fromDate,
      this.toDate,
      this.days,
      this.apiUrl,
      this.createdAt,
      this.createdBy,
      this.updatedAt,
      this.updatedBy,
      this.beneficiaryName,
      this.regionId,
      this.bankId,
      this.branchId,
      this.branchName,
      this.idCard,
      this.issueDate,
      this.placeBy,
      this.scheduleId})
      : super._();
  @override
  TransactionScheduleDto rebuild(
          void Function(TransactionScheduleDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionScheduleDtoBuilder toBuilder() =>
      TransactionScheduleDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionScheduleDto &&
        id == other.id &&
        cifNo == other.cifNo &&
        accountNoFrom == other.accountNoFrom &&
        amount == other.amount &&
        whoCharge == other.whoCharge &&
        chargeAmount == other.chargeAmount &&
        isAccount == other.isAccount &&
        bankCodeId == other.bankCodeId &&
        bankCode == other.bankCode &&
        targetBankImage == other.targetBankImage &&
        targetBankCodeCitAd == other.targetBankCodeCitAd &&
        targetBankImageType == other.targetBankImageType &&
        targetBankName == other.targetBankName &&
        shortName == other.shortName &&
        targetCommonBankName == other.targetCommonBankName &&
        accountNoTo == other.accountNoTo &&
        aliasName == other.aliasName &&
        is247 == other.is247 &&
        isNow == other.isNow &&
        isSaveToTemplate == other.isSaveToTemplate &&
        description == other.description &&
        transactionCategoryId == other.transactionCategoryId &&
        categoryName == other.categoryName &&
        categoryImage == other.categoryImage &&
        categoryImageType == other.categoryImageType &&
        scheduleGroup == other.scheduleGroup &&
        fromDate == other.fromDate &&
        toDate == other.toDate &&
        days == other.days &&
        apiUrl == other.apiUrl &&
        createdAt == other.createdAt &&
        createdBy == other.createdBy &&
        updatedAt == other.updatedAt &&
        updatedBy == other.updatedBy &&
        beneficiaryName == other.beneficiaryName &&
        regionId == other.regionId &&
        bankId == other.bankId &&
        branchId == other.branchId &&
        branchName == other.branchName &&
        idCard == other.idCard &&
        issueDate == other.issueDate &&
        placeBy == other.placeBy &&
        scheduleId == other.scheduleId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, whoCharge.hashCode);
    _$hash = $jc(_$hash, chargeAmount.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, bankCodeId.hashCode);
    _$hash = $jc(_$hash, bankCode.hashCode);
    _$hash = $jc(_$hash, targetBankImage.hashCode);
    _$hash = $jc(_$hash, targetBankCodeCitAd.hashCode);
    _$hash = $jc(_$hash, targetBankImageType.hashCode);
    _$hash = $jc(_$hash, targetBankName.hashCode);
    _$hash = $jc(_$hash, shortName.hashCode);
    _$hash = $jc(_$hash, targetCommonBankName.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, is247.hashCode);
    _$hash = $jc(_$hash, isNow.hashCode);
    _$hash = $jc(_$hash, isSaveToTemplate.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, transactionCategoryId.hashCode);
    _$hash = $jc(_$hash, categoryName.hashCode);
    _$hash = $jc(_$hash, categoryImage.hashCode);
    _$hash = $jc(_$hash, categoryImageType.hashCode);
    _$hash = $jc(_$hash, scheduleGroup.hashCode);
    _$hash = $jc(_$hash, fromDate.hashCode);
    _$hash = $jc(_$hash, toDate.hashCode);
    _$hash = $jc(_$hash, days.hashCode);
    _$hash = $jc(_$hash, apiUrl.hashCode);
    _$hash = $jc(_$hash, createdAt.hashCode);
    _$hash = $jc(_$hash, createdBy.hashCode);
    _$hash = $jc(_$hash, updatedAt.hashCode);
    _$hash = $jc(_$hash, updatedBy.hashCode);
    _$hash = $jc(_$hash, beneficiaryName.hashCode);
    _$hash = $jc(_$hash, regionId.hashCode);
    _$hash = $jc(_$hash, bankId.hashCode);
    _$hash = $jc(_$hash, branchId.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, idCard.hashCode);
    _$hash = $jc(_$hash, issueDate.hashCode);
    _$hash = $jc(_$hash, placeBy.hashCode);
    _$hash = $jc(_$hash, scheduleId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionScheduleDto')
          ..add('id', id)
          ..add('cifNo', cifNo)
          ..add('accountNoFrom', accountNoFrom)
          ..add('amount', amount)
          ..add('whoCharge', whoCharge)
          ..add('chargeAmount', chargeAmount)
          ..add('isAccount', isAccount)
          ..add('bankCodeId', bankCodeId)
          ..add('bankCode', bankCode)
          ..add('targetBankImage', targetBankImage)
          ..add('targetBankCodeCitAd', targetBankCodeCitAd)
          ..add('targetBankImageType', targetBankImageType)
          ..add('targetBankName', targetBankName)
          ..add('shortName', shortName)
          ..add('targetCommonBankName', targetCommonBankName)
          ..add('accountNoTo', accountNoTo)
          ..add('aliasName', aliasName)
          ..add('is247', is247)
          ..add('isNow', isNow)
          ..add('isSaveToTemplate', isSaveToTemplate)
          ..add('description', description)
          ..add('transactionCategoryId', transactionCategoryId)
          ..add('categoryName', categoryName)
          ..add('categoryImage', categoryImage)
          ..add('categoryImageType', categoryImageType)
          ..add('scheduleGroup', scheduleGroup)
          ..add('fromDate', fromDate)
          ..add('toDate', toDate)
          ..add('days', days)
          ..add('apiUrl', apiUrl)
          ..add('createdAt', createdAt)
          ..add('createdBy', createdBy)
          ..add('updatedAt', updatedAt)
          ..add('updatedBy', updatedBy)
          ..add('beneficiaryName', beneficiaryName)
          ..add('regionId', regionId)
          ..add('bankId', bankId)
          ..add('branchId', branchId)
          ..add('branchName', branchName)
          ..add('idCard', idCard)
          ..add('issueDate', issueDate)
          ..add('placeBy', placeBy)
          ..add('scheduleId', scheduleId))
        .toString();
  }
}

class TransactionScheduleDtoBuilder
    implements Builder<TransactionScheduleDto, TransactionScheduleDtoBuilder> {
  _$TransactionScheduleDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  int? _whoCharge;
  int? get whoCharge => _$this._whoCharge;
  set whoCharge(int? whoCharge) => _$this._whoCharge = whoCharge;

  int? _chargeAmount;
  int? get chargeAmount => _$this._chargeAmount;
  set chargeAmount(int? chargeAmount) => _$this._chargeAmount = chargeAmount;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _bankCodeId;
  String? get bankCodeId => _$this._bankCodeId;
  set bankCodeId(String? bankCodeId) => _$this._bankCodeId = bankCodeId;

  String? _bankCode;
  String? get bankCode => _$this._bankCode;
  set bankCode(String? bankCode) => _$this._bankCode = bankCode;

  String? _targetBankImage;
  String? get targetBankImage => _$this._targetBankImage;
  set targetBankImage(String? targetBankImage) =>
      _$this._targetBankImage = targetBankImage;

  String? _targetBankCodeCitAd;
  String? get targetBankCodeCitAd => _$this._targetBankCodeCitAd;
  set targetBankCodeCitAd(String? targetBankCodeCitAd) =>
      _$this._targetBankCodeCitAd = targetBankCodeCitAd;

  String? _targetBankImageType;
  String? get targetBankImageType => _$this._targetBankImageType;
  set targetBankImageType(String? targetBankImageType) =>
      _$this._targetBankImageType = targetBankImageType;

  String? _targetBankName;
  String? get targetBankName => _$this._targetBankName;
  set targetBankName(String? targetBankName) =>
      _$this._targetBankName = targetBankName;

  String? _shortName;
  String? get shortName => _$this._shortName;
  set shortName(String? shortName) => _$this._shortName = shortName;

  String? _targetCommonBankName;
  String? get targetCommonBankName => _$this._targetCommonBankName;
  set targetCommonBankName(String? targetCommonBankName) =>
      _$this._targetCommonBankName = targetCommonBankName;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  int? _is247;
  int? get is247 => _$this._is247;
  set is247(int? is247) => _$this._is247 = is247;

  int? _isNow;
  int? get isNow => _$this._isNow;
  set isNow(int? isNow) => _$this._isNow = isNow;

  int? _isSaveToTemplate;
  int? get isSaveToTemplate => _$this._isSaveToTemplate;
  set isSaveToTemplate(int? isSaveToTemplate) =>
      _$this._isSaveToTemplate = isSaveToTemplate;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _transactionCategoryId;
  int? get transactionCategoryId => _$this._transactionCategoryId;
  set transactionCategoryId(int? transactionCategoryId) =>
      _$this._transactionCategoryId = transactionCategoryId;

  String? _categoryName;
  String? get categoryName => _$this._categoryName;
  set categoryName(String? categoryName) => _$this._categoryName = categoryName;

  String? _categoryImage;
  String? get categoryImage => _$this._categoryImage;
  set categoryImage(String? categoryImage) =>
      _$this._categoryImage = categoryImage;

  String? _categoryImageType;
  String? get categoryImageType => _$this._categoryImageType;
  set categoryImageType(String? categoryImageType) =>
      _$this._categoryImageType = categoryImageType;

  String? _scheduleGroup;
  String? get scheduleGroup => _$this._scheduleGroup;
  set scheduleGroup(String? scheduleGroup) =>
      _$this._scheduleGroup = scheduleGroup;

  DateTime? _fromDate;
  DateTime? get fromDate => _$this._fromDate;
  set fromDate(DateTime? fromDate) => _$this._fromDate = fromDate;

  DateTime? _toDate;
  DateTime? get toDate => _$this._toDate;
  set toDate(DateTime? toDate) => _$this._toDate = toDate;

  String? _days;
  String? get days => _$this._days;
  set days(String? days) => _$this._days = days;

  String? _apiUrl;
  String? get apiUrl => _$this._apiUrl;
  set apiUrl(String? apiUrl) => _$this._apiUrl = apiUrl;

  DateTime? _createdAt;
  DateTime? get createdAt => _$this._createdAt;
  set createdAt(DateTime? createdAt) => _$this._createdAt = createdAt;

  String? _createdBy;
  String? get createdBy => _$this._createdBy;
  set createdBy(String? createdBy) => _$this._createdBy = createdBy;

  DateTime? _updatedAt;
  DateTime? get updatedAt => _$this._updatedAt;
  set updatedAt(DateTime? updatedAt) => _$this._updatedAt = updatedAt;

  String? _updatedBy;
  String? get updatedBy => _$this._updatedBy;
  set updatedBy(String? updatedBy) => _$this._updatedBy = updatedBy;

  String? _beneficiaryName;
  String? get beneficiaryName => _$this._beneficiaryName;
  set beneficiaryName(String? beneficiaryName) =>
      _$this._beneficiaryName = beneficiaryName;

  int? _regionId;
  int? get regionId => _$this._regionId;
  set regionId(int? regionId) => _$this._regionId = regionId;

  int? _bankId;
  int? get bankId => _$this._bankId;
  set bankId(int? bankId) => _$this._bankId = bankId;

  String? _branchId;
  String? get branchId => _$this._branchId;
  set branchId(String? branchId) => _$this._branchId = branchId;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _idCard;
  String? get idCard => _$this._idCard;
  set idCard(String? idCard) => _$this._idCard = idCard;

  Date? _issueDate;
  Date? get issueDate => _$this._issueDate;
  set issueDate(Date? issueDate) => _$this._issueDate = issueDate;

  String? _placeBy;
  String? get placeBy => _$this._placeBy;
  set placeBy(String? placeBy) => _$this._placeBy = placeBy;

  String? _scheduleId;
  String? get scheduleId => _$this._scheduleId;
  set scheduleId(String? scheduleId) => _$this._scheduleId = scheduleId;

  TransactionScheduleDtoBuilder() {
    TransactionScheduleDto._defaults(this);
  }

  TransactionScheduleDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _cifNo = $v.cifNo;
      _accountNoFrom = $v.accountNoFrom;
      _amount = $v.amount;
      _whoCharge = $v.whoCharge;
      _chargeAmount = $v.chargeAmount;
      _isAccount = $v.isAccount;
      _bankCodeId = $v.bankCodeId;
      _bankCode = $v.bankCode;
      _targetBankImage = $v.targetBankImage;
      _targetBankCodeCitAd = $v.targetBankCodeCitAd;
      _targetBankImageType = $v.targetBankImageType;
      _targetBankName = $v.targetBankName;
      _shortName = $v.shortName;
      _targetCommonBankName = $v.targetCommonBankName;
      _accountNoTo = $v.accountNoTo;
      _aliasName = $v.aliasName;
      _is247 = $v.is247;
      _isNow = $v.isNow;
      _isSaveToTemplate = $v.isSaveToTemplate;
      _description = $v.description;
      _transactionCategoryId = $v.transactionCategoryId;
      _categoryName = $v.categoryName;
      _categoryImage = $v.categoryImage;
      _categoryImageType = $v.categoryImageType;
      _scheduleGroup = $v.scheduleGroup;
      _fromDate = $v.fromDate;
      _toDate = $v.toDate;
      _days = $v.days;
      _apiUrl = $v.apiUrl;
      _createdAt = $v.createdAt;
      _createdBy = $v.createdBy;
      _updatedAt = $v.updatedAt;
      _updatedBy = $v.updatedBy;
      _beneficiaryName = $v.beneficiaryName;
      _regionId = $v.regionId;
      _bankId = $v.bankId;
      _branchId = $v.branchId;
      _branchName = $v.branchName;
      _idCard = $v.idCard;
      _issueDate = $v.issueDate;
      _placeBy = $v.placeBy;
      _scheduleId = $v.scheduleId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionScheduleDto other) {
    _$v = other as _$TransactionScheduleDto;
  }

  @override
  void update(void Function(TransactionScheduleDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionScheduleDto build() => _build();

  _$TransactionScheduleDto _build() {
    final _$result = _$v ??
        _$TransactionScheduleDto._(
          id: id,
          cifNo: cifNo,
          accountNoFrom: accountNoFrom,
          amount: amount,
          whoCharge: whoCharge,
          chargeAmount: chargeAmount,
          isAccount: isAccount,
          bankCodeId: bankCodeId,
          bankCode: bankCode,
          targetBankImage: targetBankImage,
          targetBankCodeCitAd: targetBankCodeCitAd,
          targetBankImageType: targetBankImageType,
          targetBankName: targetBankName,
          shortName: shortName,
          targetCommonBankName: targetCommonBankName,
          accountNoTo: accountNoTo,
          aliasName: aliasName,
          is247: is247,
          isNow: isNow,
          isSaveToTemplate: isSaveToTemplate,
          description: description,
          transactionCategoryId: transactionCategoryId,
          categoryName: categoryName,
          categoryImage: categoryImage,
          categoryImageType: categoryImageType,
          scheduleGroup: scheduleGroup,
          fromDate: fromDate,
          toDate: toDate,
          days: days,
          apiUrl: apiUrl,
          createdAt: createdAt,
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          beneficiaryName: beneficiaryName,
          regionId: regionId,
          bankId: bankId,
          branchId: branchId,
          branchName: branchName,
          idCard: idCard,
          issueDate: issueDate,
          placeBy: placeBy,
          scheduleId: scheduleId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
