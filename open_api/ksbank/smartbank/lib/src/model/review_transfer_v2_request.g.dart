// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_v2_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferV2Request extends ReviewTransferV2Request {
  @override
  final int? is247;
  @override
  final int? isNow;
  @override
  final String? transactionName;
  @override
  final int? transactionGroup;
  @override
  final String? sourceAccountNumber;
  @override
  final String? targetAccountNumber;
  @override
  final int? targetAccountType;
  @override
  final String? targetBankCode;
  @override
  final num? amount;
  @override
  final String? channelCode;

  factory _$ReviewTransferV2Request(
          [void Function(ReviewTransferV2RequestBuilder)? updates]) =>
      (ReviewTransferV2RequestBuilder()..update(updates))._build();

  _$ReviewTransferV2Request._(
      {this.is247,
      this.isNow,
      this.transactionName,
      this.transactionGroup,
      this.sourceAccountNumber,
      this.targetAccountNumber,
      this.targetAccountType,
      this.targetBankCode,
      this.amount,
      this.channelCode})
      : super._();
  @override
  ReviewTransferV2Request rebuild(
          void Function(ReviewTransferV2RequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferV2RequestBuilder toBuilder() =>
      ReviewTransferV2RequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferV2Request &&
        is247 == other.is247 &&
        isNow == other.isNow &&
        transactionName == other.transactionName &&
        transactionGroup == other.transactionGroup &&
        sourceAccountNumber == other.sourceAccountNumber &&
        targetAccountNumber == other.targetAccountNumber &&
        targetAccountType == other.targetAccountType &&
        targetBankCode == other.targetBankCode &&
        amount == other.amount &&
        channelCode == other.channelCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, is247.hashCode);
    _$hash = $jc(_$hash, isNow.hashCode);
    _$hash = $jc(_$hash, transactionName.hashCode);
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, sourceAccountNumber.hashCode);
    _$hash = $jc(_$hash, targetAccountNumber.hashCode);
    _$hash = $jc(_$hash, targetAccountType.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, channelCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewTransferV2Request')
          ..add('is247', is247)
          ..add('isNow', isNow)
          ..add('transactionName', transactionName)
          ..add('transactionGroup', transactionGroup)
          ..add('sourceAccountNumber', sourceAccountNumber)
          ..add('targetAccountNumber', targetAccountNumber)
          ..add('targetAccountType', targetAccountType)
          ..add('targetBankCode', targetBankCode)
          ..add('amount', amount)
          ..add('channelCode', channelCode))
        .toString();
  }
}

class ReviewTransferV2RequestBuilder
    implements
        Builder<ReviewTransferV2Request, ReviewTransferV2RequestBuilder> {
  _$ReviewTransferV2Request? _$v;

  int? _is247;
  int? get is247 => _$this._is247;
  set is247(int? is247) => _$this._is247 = is247;

  int? _isNow;
  int? get isNow => _$this._isNow;
  set isNow(int? isNow) => _$this._isNow = isNow;

  String? _transactionName;
  String? get transactionName => _$this._transactionName;
  set transactionName(String? transactionName) =>
      _$this._transactionName = transactionName;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _sourceAccountNumber;
  String? get sourceAccountNumber => _$this._sourceAccountNumber;
  set sourceAccountNumber(String? sourceAccountNumber) =>
      _$this._sourceAccountNumber = sourceAccountNumber;

  String? _targetAccountNumber;
  String? get targetAccountNumber => _$this._targetAccountNumber;
  set targetAccountNumber(String? targetAccountNumber) =>
      _$this._targetAccountNumber = targetAccountNumber;

  int? _targetAccountType;
  int? get targetAccountType => _$this._targetAccountType;
  set targetAccountType(int? targetAccountType) =>
      _$this._targetAccountType = targetAccountType;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _channelCode;
  String? get channelCode => _$this._channelCode;
  set channelCode(String? channelCode) => _$this._channelCode = channelCode;

  ReviewTransferV2RequestBuilder() {
    ReviewTransferV2Request._defaults(this);
  }

  ReviewTransferV2RequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _is247 = $v.is247;
      _isNow = $v.isNow;
      _transactionName = $v.transactionName;
      _transactionGroup = $v.transactionGroup;
      _sourceAccountNumber = $v.sourceAccountNumber;
      _targetAccountNumber = $v.targetAccountNumber;
      _targetAccountType = $v.targetAccountType;
      _targetBankCode = $v.targetBankCode;
      _amount = $v.amount;
      _channelCode = $v.channelCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferV2Request other) {
    _$v = other as _$ReviewTransferV2Request;
  }

  @override
  void update(void Function(ReviewTransferV2RequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferV2Request build() => _build();

  _$ReviewTransferV2Request _build() {
    final _$result = _$v ??
        _$ReviewTransferV2Request._(
          is247: is247,
          isNow: isNow,
          transactionName: transactionName,
          transactionGroup: transactionGroup,
          sourceAccountNumber: sourceAccountNumber,
          targetAccountNumber: targetAccountNumber,
          targetAccountType: targetAccountType,
          targetBankCode: targetBankCode,
          amount: amount,
          channelCode: channelCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
