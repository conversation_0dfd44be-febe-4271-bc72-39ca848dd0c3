//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'user_logwork_item.g.dart';

/// UserLogworkItem
///
/// Properties:
/// * [date]
/// * [workload]
/// * [type]
@BuiltValue()
abstract class UserLogworkItem
    implements Built<UserLogworkItem, UserLogworkItemBuilder> {
  @BuiltValueField(wireName: r'date')
  String? get date;

  @BuiltValueField(wireName: r'workload')
  double? get workload;

  @BuiltValueField(wireName: r'type')
  UserLogworkItemTypeEnum? get type;
  // enum typeEnum {  FULL_DATE,  NOT_FULL_DATE,  DATE_OFF,  NONE,  };

  UserLogworkItem._();

  factory UserLogworkItem([void updates(UserLogworkItemBuilder b)]) =
      _$UserLogworkItem;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UserLogworkItemBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UserLogworkItem> get serializer =>
      _$UserLogworkItemSerializer();
}

class _$UserLogworkItemSerializer
    implements PrimitiveSerializer<UserLogworkItem> {
  @override
  final Iterable<Type> types = const [UserLogworkItem, _$UserLogworkItem];

  @override
  final String wireName = r'UserLogworkItem';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UserLogworkItem object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.date != null) {
      yield r'date';
      yield serializers.serialize(
        object.date,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.workload != null) {
      yield r'workload';
      yield serializers.serialize(
        object.workload,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.type != null) {
      yield r'type';
      yield serializers.serialize(
        object.type,
        specifiedType: const FullType.nullable(UserLogworkItemTypeEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UserLogworkItem object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UserLogworkItemBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'date':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.date = valueDes;
          break;
        case r'workload':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.workload = valueDes;
          break;
        case r'type':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(UserLogworkItemTypeEnum),
          ) as UserLogworkItemTypeEnum?;
          if (valueDes == null) continue;
          result.type = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UserLogworkItem deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UserLogworkItemBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class UserLogworkItemTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'FULL_DATE')
  static const UserLogworkItemTypeEnum FULL_DATE =
      _$userLogworkItemTypeEnum_FULL_DATE;
  @BuiltValueEnumConst(wireName: r'NOT_FULL_DATE')
  static const UserLogworkItemTypeEnum NOT_FULL_DATE =
      _$userLogworkItemTypeEnum_NOT_FULL_DATE;
  @BuiltValueEnumConst(wireName: r'DATE_OFF')
  static const UserLogworkItemTypeEnum DATE_OFF =
      _$userLogworkItemTypeEnum_DATE_OFF;
  @BuiltValueEnumConst(wireName: r'NONE')
  static const UserLogworkItemTypeEnum NONE = _$userLogworkItemTypeEnum_NONE;

  static Serializer<UserLogworkItemTypeEnum> get serializer =>
      _$userLogworkItemTypeEnumSerializer;

  const UserLogworkItemTypeEnum._(String name) : super(name);

  static BuiltSet<UserLogworkItemTypeEnum> get values =>
      _$userLogworkItemTypeEnumValues;
  static UserLogworkItemTypeEnum valueOf(String name) =>
      _$userLogworkItemTypeEnumValueOf(name);
}
