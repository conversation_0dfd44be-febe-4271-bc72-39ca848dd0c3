// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pin_code_value_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PinCodeValueDto extends PinCodeValueDto {
  @override
  final String? productCode;
  @override
  final String? productName;
  @override
  final String? description;
  @override
  final num? productValue;
  @override
  final num? purchasingPrice;
  @override
  final num? discount;

  factory _$PinCodeValueDto([void Function(PinCodeValueDtoBuilder)? updates]) =>
      (PinCodeValueDtoBuilder()..update(updates))._build();

  _$PinCodeValueDto._(
      {this.productCode,
      this.productName,
      this.description,
      this.productValue,
      this.purchasingPrice,
      this.discount})
      : super._();
  @override
  PinCodeValueDto rebuild(void Function(PinCodeValueDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PinCodeValueDtoBuilder toBuilder() => PinCodeValueDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PinCodeValueDto &&
        productCode == other.productCode &&
        productName == other.productName &&
        description == other.description &&
        productValue == other.productValue &&
        purchasingPrice == other.purchasingPrice &&
        discount == other.discount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, productName.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, productValue.hashCode);
    _$hash = $jc(_$hash, purchasingPrice.hashCode);
    _$hash = $jc(_$hash, discount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PinCodeValueDto')
          ..add('productCode', productCode)
          ..add('productName', productName)
          ..add('description', description)
          ..add('productValue', productValue)
          ..add('purchasingPrice', purchasingPrice)
          ..add('discount', discount))
        .toString();
  }
}

class PinCodeValueDtoBuilder
    implements Builder<PinCodeValueDto, PinCodeValueDtoBuilder> {
  _$PinCodeValueDto? _$v;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  String? _productName;
  String? get productName => _$this._productName;
  set productName(String? productName) => _$this._productName = productName;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  num? _productValue;
  num? get productValue => _$this._productValue;
  set productValue(num? productValue) => _$this._productValue = productValue;

  num? _purchasingPrice;
  num? get purchasingPrice => _$this._purchasingPrice;
  set purchasingPrice(num? purchasingPrice) =>
      _$this._purchasingPrice = purchasingPrice;

  num? _discount;
  num? get discount => _$this._discount;
  set discount(num? discount) => _$this._discount = discount;

  PinCodeValueDtoBuilder() {
    PinCodeValueDto._defaults(this);
  }

  PinCodeValueDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _productCode = $v.productCode;
      _productName = $v.productName;
      _description = $v.description;
      _productValue = $v.productValue;
      _purchasingPrice = $v.purchasingPrice;
      _discount = $v.discount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PinCodeValueDto other) {
    _$v = other as _$PinCodeValueDto;
  }

  @override
  void update(void Function(PinCodeValueDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PinCodeValueDto build() => _build();

  _$PinCodeValueDto _build() {
    final _$result = _$v ??
        _$PinCodeValueDto._(
          productCode: productCode,
          productName: productName,
          description: description,
          productValue: productValue,
          purchasingPrice: purchasingPrice,
          discount: discount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
