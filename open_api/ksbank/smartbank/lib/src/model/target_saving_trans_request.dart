//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'target_saving_trans_request.g.dart';

/// TargetSavingTransRequest
///
/// Properties:
/// * [bankCif] - Số cif của khách hàng
/// * [transactionNo] - Số giao dịch do app mobile tự sinh, gồm chuỗi có 10 chữ số
/// * [targetAccountNo] - Số tài khoản tiết kiệm mục tiêu
/// * [sourceAccountNo] - Số tài khoản nguồn để chuyển tiền vào hoặc rút tiền ra
/// * [amount] - <PERSON><PERSON> tiền
/// * [tranType] - <PERSON><PERSON><PERSON> giao dịch. DEP : g<PERSON><PERSON> thêm, WTH: rút bớt
/// * [verifySoftOtp]
@BuiltValue()
abstract class TargetSavingTransRequest
    implements
        Built<TargetSavingTransRequest, TargetSavingTransRequestBuilder> {
  /// Số cif của khách hàng
  @BuiltValueField(wireName: r'bankCif')
  String? get bankCif;

  /// Số giao dịch do app mobile tự sinh, gồm chuỗi có 10 chữ số
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số tài khoản tiết kiệm mục tiêu
  @BuiltValueField(wireName: r'targetAccountNo')
  String? get targetAccountNo;

  /// Số tài khoản nguồn để chuyển tiền vào hoặc rút tiền ra
  @BuiltValueField(wireName: r'sourceAccountNo')
  String? get sourceAccountNo;

  /// Số tiền
  @BuiltValueField(wireName: r'amount')
  num? get amount;

  /// Loại giao dịch. DEP : gửi thêm, WTH: rút bớt
  @BuiltValueField(wireName: r'tranType')
  TargetSavingTransRequestTranTypeEnum? get tranType;
  // enum tranTypeEnum {  DEP,  WTH,  };

  @BuiltValueField(wireName: r'verifySoftOtp')
  VerifySoftOtpRequest? get verifySoftOtp;

  TargetSavingTransRequest._();

  factory TargetSavingTransRequest(
          [void updates(TargetSavingTransRequestBuilder b)]) =
      _$TargetSavingTransRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TargetSavingTransRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TargetSavingTransRequest> get serializer =>
      _$TargetSavingTransRequestSerializer();
}

class _$TargetSavingTransRequestSerializer
    implements PrimitiveSerializer<TargetSavingTransRequest> {
  @override
  final Iterable<Type> types = const [
    TargetSavingTransRequest,
    _$TargetSavingTransRequest
  ];

  @override
  final String wireName = r'TargetSavingTransRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TargetSavingTransRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.bankCif != null) {
      yield r'bankCif';
      yield serializers.serialize(
        object.bankCif,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.targetAccountNo != null) {
      yield r'targetAccountNo';
      yield serializers.serialize(
        object.targetAccountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sourceAccountNo != null) {
      yield r'sourceAccountNo';
      yield serializers.serialize(
        object.sourceAccountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.tranType != null) {
      yield r'tranType';
      yield serializers.serialize(
        object.tranType,
        specifiedType:
            const FullType.nullable(TargetSavingTransRequestTranTypeEnum),
      );
    }
    if (object.verifySoftOtp != null) {
      yield r'verifySoftOtp';
      yield serializers.serialize(
        object.verifySoftOtp,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TargetSavingTransRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TargetSavingTransRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'bankCif':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCif = valueDes;
          break;
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'targetAccountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.targetAccountNo = valueDes;
          break;
        case r'sourceAccountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.sourceAccountNo = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'tranType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(TargetSavingTransRequestTranTypeEnum),
          ) as TargetSavingTransRequestTranTypeEnum?;
          if (valueDes == null) continue;
          result.tranType = valueDes;
          break;
        case r'verifySoftOtp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.verifySoftOtp.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TargetSavingTransRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TargetSavingTransRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class TargetSavingTransRequestTranTypeEnum extends EnumClass {
  /// Loại giao dịch. DEP : gửi thêm, WTH: rút bớt
  @BuiltValueEnumConst(wireName: r'DEP')
  static const TargetSavingTransRequestTranTypeEnum DEP =
      _$targetSavingTransRequestTranTypeEnum_DEP;

  /// Loại giao dịch. DEP : gửi thêm, WTH: rút bớt
  @BuiltValueEnumConst(wireName: r'WTH')
  static const TargetSavingTransRequestTranTypeEnum WTH =
      _$targetSavingTransRequestTranTypeEnum_WTH;

  static Serializer<TargetSavingTransRequestTranTypeEnum> get serializer =>
      _$targetSavingTransRequestTranTypeEnumSerializer;

  const TargetSavingTransRequestTranTypeEnum._(String name) : super(name);

  static BuiltSet<TargetSavingTransRequestTranTypeEnum> get values =>
      _$targetSavingTransRequestTranTypeEnumValues;
  static TargetSavingTransRequestTranTypeEnum valueOf(String name) =>
      _$targetSavingTransRequestTranTypeEnumValueOf(name);
}
