// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'topup_value_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const TopupValueDtoProviderEnum _$topupValueDtoProviderEnum_MOBIFONE =
    const TopupValueDtoProviderEnum._('MOBIFONE');
const TopupValueDtoProviderEnum _$topupValueDtoProviderEnum_VINAPHONE =
    const TopupValueDtoProviderEnum._('VINAPHONE');
const TopupValueDtoProviderEnum _$topupValueDtoProviderEnum_VIETTEL =
    const TopupValueDtoProviderEnum._('VIETTEL');
const TopupValueDtoProviderEnum _$topupValueDtoProviderEnum_VIETNAMOBILE =
    const TopupValueDtoProviderEnum._('VIETNAMOBILE');
const TopupValueDtoProviderEnum _$topupValueDtoProviderEnum_GMOBILE =
    const TopupValueDtoProviderEnum._('GMOBILE');

TopupValueDtoProviderEnum _$topupValueDtoProviderEnumValueOf(String name) {
  switch (name) {
    case 'MOBIFONE':
      return _$topupValueDtoProviderEnum_MOBIFONE;
    case 'VINAPHONE':
      return _$topupValueDtoProviderEnum_VINAPHONE;
    case 'VIETTEL':
      return _$topupValueDtoProviderEnum_VIETTEL;
    case 'VIETNAMOBILE':
      return _$topupValueDtoProviderEnum_VIETNAMOBILE;
    case 'GMOBILE':
      return _$topupValueDtoProviderEnum_GMOBILE;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<TopupValueDtoProviderEnum> _$topupValueDtoProviderEnumValues =
    BuiltSet<TopupValueDtoProviderEnum>(const <TopupValueDtoProviderEnum>[
  _$topupValueDtoProviderEnum_MOBIFONE,
  _$topupValueDtoProviderEnum_VINAPHONE,
  _$topupValueDtoProviderEnum_VIETTEL,
  _$topupValueDtoProviderEnum_VIETNAMOBILE,
  _$topupValueDtoProviderEnum_GMOBILE,
]);

Serializer<TopupValueDtoProviderEnum> _$topupValueDtoProviderEnumSerializer =
    _$TopupValueDtoProviderEnumSerializer();

class _$TopupValueDtoProviderEnumSerializer
    implements PrimitiveSerializer<TopupValueDtoProviderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };

  @override
  final Iterable<Type> types = const <Type>[TopupValueDtoProviderEnum];
  @override
  final String wireName = 'TopupValueDtoProviderEnum';

  @override
  Object serialize(Serializers serializers, TopupValueDtoProviderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  TopupValueDtoProviderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      TopupValueDtoProviderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$TopupValueDto extends TopupValueDto {
  @override
  final num? cardValue;
  @override
  final String? cardValueCode;
  @override
  final String? cardValueName;
  @override
  final num? purchasingPrice;
  @override
  final num? referPrice;
  @override
  final bool? canPaid;
  @override
  final num? discountPercent;
  @override
  final TopupValueDtoProviderEnum? provider;

  factory _$TopupValueDto([void Function(TopupValueDtoBuilder)? updates]) =>
      (TopupValueDtoBuilder()..update(updates))._build();

  _$TopupValueDto._(
      {this.cardValue,
      this.cardValueCode,
      this.cardValueName,
      this.purchasingPrice,
      this.referPrice,
      this.canPaid,
      this.discountPercent,
      this.provider})
      : super._();
  @override
  TopupValueDto rebuild(void Function(TopupValueDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TopupValueDtoBuilder toBuilder() => TopupValueDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TopupValueDto &&
        cardValue == other.cardValue &&
        cardValueCode == other.cardValueCode &&
        cardValueName == other.cardValueName &&
        purchasingPrice == other.purchasingPrice &&
        referPrice == other.referPrice &&
        canPaid == other.canPaid &&
        discountPercent == other.discountPercent &&
        provider == other.provider;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jc(_$hash, cardValueCode.hashCode);
    _$hash = $jc(_$hash, cardValueName.hashCode);
    _$hash = $jc(_$hash, purchasingPrice.hashCode);
    _$hash = $jc(_$hash, referPrice.hashCode);
    _$hash = $jc(_$hash, canPaid.hashCode);
    _$hash = $jc(_$hash, discountPercent.hashCode);
    _$hash = $jc(_$hash, provider.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TopupValueDto')
          ..add('cardValue', cardValue)
          ..add('cardValueCode', cardValueCode)
          ..add('cardValueName', cardValueName)
          ..add('purchasingPrice', purchasingPrice)
          ..add('referPrice', referPrice)
          ..add('canPaid', canPaid)
          ..add('discountPercent', discountPercent)
          ..add('provider', provider))
        .toString();
  }
}

class TopupValueDtoBuilder
    implements Builder<TopupValueDto, TopupValueDtoBuilder> {
  _$TopupValueDto? _$v;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  String? _cardValueCode;
  String? get cardValueCode => _$this._cardValueCode;
  set cardValueCode(String? cardValueCode) =>
      _$this._cardValueCode = cardValueCode;

  String? _cardValueName;
  String? get cardValueName => _$this._cardValueName;
  set cardValueName(String? cardValueName) =>
      _$this._cardValueName = cardValueName;

  num? _purchasingPrice;
  num? get purchasingPrice => _$this._purchasingPrice;
  set purchasingPrice(num? purchasingPrice) =>
      _$this._purchasingPrice = purchasingPrice;

  num? _referPrice;
  num? get referPrice => _$this._referPrice;
  set referPrice(num? referPrice) => _$this._referPrice = referPrice;

  bool? _canPaid;
  bool? get canPaid => _$this._canPaid;
  set canPaid(bool? canPaid) => _$this._canPaid = canPaid;

  num? _discountPercent;
  num? get discountPercent => _$this._discountPercent;
  set discountPercent(num? discountPercent) =>
      _$this._discountPercent = discountPercent;

  TopupValueDtoProviderEnum? _provider;
  TopupValueDtoProviderEnum? get provider => _$this._provider;
  set provider(TopupValueDtoProviderEnum? provider) =>
      _$this._provider = provider;

  TopupValueDtoBuilder() {
    TopupValueDto._defaults(this);
  }

  TopupValueDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardValue = $v.cardValue;
      _cardValueCode = $v.cardValueCode;
      _cardValueName = $v.cardValueName;
      _purchasingPrice = $v.purchasingPrice;
      _referPrice = $v.referPrice;
      _canPaid = $v.canPaid;
      _discountPercent = $v.discountPercent;
      _provider = $v.provider;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TopupValueDto other) {
    _$v = other as _$TopupValueDto;
  }

  @override
  void update(void Function(TopupValueDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TopupValueDto build() => _build();

  _$TopupValueDto _build() {
    final _$result = _$v ??
        _$TopupValueDto._(
          cardValue: cardValue,
          cardValueCode: cardValueCode,
          cardValueName: cardValueName,
          purchasingPrice: purchasingPrice,
          referPrice: referPrice,
          canPaid: canPaid,
          discountPercent: discountPercent,
          provider: provider,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
