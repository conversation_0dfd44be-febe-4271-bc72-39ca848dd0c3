// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_supplier_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateSupplierResponse extends UpdateSupplierResponse {
  @override
  final bool? success;

  factory _$UpdateSupplierResponse(
          [void Function(UpdateSupplierResponseBuilder)? updates]) =>
      (UpdateSupplierResponseBuilder()..update(updates))._build();

  _$UpdateSupplierResponse._({this.success}) : super._();
  @override
  UpdateSupplierResponse rebuild(
          void Function(UpdateSupplierResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateSupplierResponseBuilder toBuilder() =>
      UpdateSupplierResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateSupplierResponse && success == other.success;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateSupplierResponse')
          ..add('success', success))
        .toString();
  }
}

class UpdateSupplierResponseBuilder
    implements Builder<UpdateSupplierResponse, UpdateSupplierResponseBuilder> {
  _$UpdateSupplierResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  UpdateSupplierResponseBuilder() {
    UpdateSupplierResponse._defaults(this);
  }

  UpdateSupplierResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateSupplierResponse other) {
    _$v = other as _$UpdateSupplierResponse;
  }

  @override
  void update(void Function(UpdateSupplierResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateSupplierResponse build() => _build();

  _$UpdateSupplierResponse _build() {
    final _$result = _$v ??
        _$UpdateSupplierResponse._(
          success: success,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
