//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/trans_next_step.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_vvip_account_response.g.dart';

/// ReviewVvipAccountResponse
///
/// Properties:
/// * [vipAccountNo] - Số TK
/// * [vipGroup] - Nh<PERSON><PERSON> Vip (định nghĩa ở Lotus, nếu thuộc nhóm số đẹp), giá trị trống nếu là số chọn
/// * [feeAmount] - Phí mở TK số đẹp
/// * [feeVat] - Phí VAT mở TK số đẹp
/// * [feeAmountIncludeVat] - Ph<PERSON> bao gồm VAT mở TK số đẹp
/// * [feeAmountStr] - Phí mở TK số đẹp String
/// * [feeAmountIncludeVatStr] - <PERSON><PERSON> bao gồm VAT mở TK số đẹp String
/// * [checkCode] - Mã kiểm tra
/// * [checkName] - Mô tả mã kiểm tra
/// * [transactionNumber] - Số giao dịch
/// * [transNextStep]
/// * [content] - Nội dung content app show nếu có
@BuiltValue()
abstract class ReviewVvipAccountResponse
    implements
        Built<ReviewVvipAccountResponse, ReviewVvipAccountResponseBuilder> {
  /// Số TK
  @BuiltValueField(wireName: r'vipAccountNo')
  String? get vipAccountNo;

  /// Nhóm Vip (định nghĩa ở Lotus, nếu thuộc nhóm số đẹp), giá trị trống nếu là số chọn
  @BuiltValueField(wireName: r'vipGroup')
  String? get vipGroup;

  /// Phí mở TK số đẹp
  @BuiltValueField(wireName: r'feeAmount')
  num? get feeAmount;

  /// Phí VAT mở TK số đẹp
  @BuiltValueField(wireName: r'feeVat')
  num? get feeVat;

  /// Phí bao gồm VAT mở TK số đẹp
  @BuiltValueField(wireName: r'feeAmountIncludeVat')
  num? get feeAmountIncludeVat;

  /// Phí mở TK số đẹp String
  @BuiltValueField(wireName: r'feeAmountStr')
  String? get feeAmountStr;

  /// Phí bao gồm VAT mở TK số đẹp String
  @BuiltValueField(wireName: r'feeAmountIncludeVatStr')
  String? get feeAmountIncludeVatStr;

  /// Mã kiểm tra
  @BuiltValueField(wireName: r'checkCode')
  ReviewVvipAccountResponseCheckCodeEnum? get checkCode;
  // enum checkCodeEnum {  KHONG_XAC_DINH,  DA_SU_DUNG,  DA_DANG_KI,  CHUA_SU_DUNG,  CHON_CHUA_SU_DUNG,  };

  /// Mô tả mã kiểm tra
  @BuiltValueField(wireName: r'checkName')
  String? get checkName;

  /// Số giao dịch
  @BuiltValueField(wireName: r'transactionNumber')
  String? get transactionNumber;

  @BuiltValueField(wireName: r'transNextStep')
  TransNextStep? get transNextStep;
  // enum transNextStepEnum {  NONE,  ENABLE_FACE_ID,  SHOW_GUIDE,  SHOW_GUIDE_GTTT_EXPIRED,  };

  /// Nội dung content app show nếu có
  @BuiltValueField(wireName: r'content')
  String? get content;

  ReviewVvipAccountResponse._();

  factory ReviewVvipAccountResponse(
          [void updates(ReviewVvipAccountResponseBuilder b)]) =
      _$ReviewVvipAccountResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewVvipAccountResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewVvipAccountResponse> get serializer =>
      _$ReviewVvipAccountResponseSerializer();
}

class _$ReviewVvipAccountResponseSerializer
    implements PrimitiveSerializer<ReviewVvipAccountResponse> {
  @override
  final Iterable<Type> types = const [
    ReviewVvipAccountResponse,
    _$ReviewVvipAccountResponse
  ];

  @override
  final String wireName = r'ReviewVvipAccountResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewVvipAccountResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.vipAccountNo != null) {
      yield r'vipAccountNo';
      yield serializers.serialize(
        object.vipAccountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.vipGroup != null) {
      yield r'vipGroup';
      yield serializers.serialize(
        object.vipGroup,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.feeAmount != null) {
      yield r'feeAmount';
      yield serializers.serialize(
        object.feeAmount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.feeVat != null) {
      yield r'feeVat';
      yield serializers.serialize(
        object.feeVat,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.feeAmountIncludeVat != null) {
      yield r'feeAmountIncludeVat';
      yield serializers.serialize(
        object.feeAmountIncludeVat,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.feeAmountStr != null) {
      yield r'feeAmountStr';
      yield serializers.serialize(
        object.feeAmountStr,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.feeAmountIncludeVatStr != null) {
      yield r'feeAmountIncludeVatStr';
      yield serializers.serialize(
        object.feeAmountIncludeVatStr,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.checkCode != null) {
      yield r'checkCode';
      yield serializers.serialize(
        object.checkCode,
        specifiedType:
            const FullType.nullable(ReviewVvipAccountResponseCheckCodeEnum),
      );
    }
    if (object.checkName != null) {
      yield r'checkName';
      yield serializers.serialize(
        object.checkName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNumber != null) {
      yield r'transactionNumber';
      yield serializers.serialize(
        object.transactionNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transNextStep != null) {
      yield r'transNextStep';
      yield serializers.serialize(
        object.transNextStep,
        specifiedType: const FullType.nullable(TransNextStep),
      );
    }
    if (object.content != null) {
      yield r'content';
      yield serializers.serialize(
        object.content,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewVvipAccountResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewVvipAccountResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'vipAccountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.vipAccountNo = valueDes;
          break;
        case r'vipGroup':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.vipGroup = valueDes;
          break;
        case r'feeAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.feeAmount = valueDes;
          break;
        case r'feeVat':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.feeVat = valueDes;
          break;
        case r'feeAmountIncludeVat':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.feeAmountIncludeVat = valueDes;
          break;
        case r'feeAmountStr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.feeAmountStr = valueDes;
          break;
        case r'feeAmountIncludeVatStr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.feeAmountIncludeVatStr = valueDes;
          break;
        case r'checkCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(ReviewVvipAccountResponseCheckCodeEnum),
          ) as ReviewVvipAccountResponseCheckCodeEnum?;
          if (valueDes == null) continue;
          result.checkCode = valueDes;
          break;
        case r'checkName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.checkName = valueDes;
          break;
        case r'transactionNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNumber = valueDes;
          break;
        case r'transNextStep':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(TransNextStep),
          ) as TransNextStep?;
          if (valueDes == null) continue;
          result.transNextStep = valueDes;
          break;
        case r'content':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.content = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewVvipAccountResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewVvipAccountResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class ReviewVvipAccountResponseCheckCodeEnum extends EnumClass {
  /// Mã kiểm tra
  @BuiltValueEnumConst(wireName: r'KHONG_XAC_DINH')
  static const ReviewVvipAccountResponseCheckCodeEnum KHONG_XAC_DINH =
      _$reviewVvipAccountResponseCheckCodeEnum_KHONG_XAC_DINH;

  /// Mã kiểm tra
  @BuiltValueEnumConst(wireName: r'DA_SU_DUNG')
  static const ReviewVvipAccountResponseCheckCodeEnum DA_SU_DUNG =
      _$reviewVvipAccountResponseCheckCodeEnum_DA_SU_DUNG;

  /// Mã kiểm tra
  @BuiltValueEnumConst(wireName: r'DA_DANG_KI')
  static const ReviewVvipAccountResponseCheckCodeEnum DA_DANG_KI =
      _$reviewVvipAccountResponseCheckCodeEnum_DA_DANG_KI;

  /// Mã kiểm tra
  @BuiltValueEnumConst(wireName: r'CHUA_SU_DUNG')
  static const ReviewVvipAccountResponseCheckCodeEnum CHUA_SU_DUNG =
      _$reviewVvipAccountResponseCheckCodeEnum_CHUA_SU_DUNG;

  /// Mã kiểm tra
  @BuiltValueEnumConst(wireName: r'CHON_CHUA_SU_DUNG')
  static const ReviewVvipAccountResponseCheckCodeEnum CHON_CHUA_SU_DUNG =
      _$reviewVvipAccountResponseCheckCodeEnum_CHON_CHUA_SU_DUNG;

  static Serializer<ReviewVvipAccountResponseCheckCodeEnum> get serializer =>
      _$reviewVvipAccountResponseCheckCodeEnumSerializer;

  const ReviewVvipAccountResponseCheckCodeEnum._(String name) : super(name);

  static BuiltSet<ReviewVvipAccountResponseCheckCodeEnum> get values =>
      _$reviewVvipAccountResponseCheckCodeEnumValues;
  static ReviewVvipAccountResponseCheckCodeEnum valueOf(String name) =>
      _$reviewVvipAccountResponseCheckCodeEnumValueOf(name);
}
