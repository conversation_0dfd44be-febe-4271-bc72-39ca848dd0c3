// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'target_saving_trans_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const TargetSavingTransRequestTranTypeEnum
    _$targetSavingTransRequestTranTypeEnum_DEP =
    const TargetSavingTransRequestTranTypeEnum._('DEP');
const TargetSavingTransRequestTranTypeEnum
    _$targetSavingTransRequestTranTypeEnum_WTH =
    const TargetSavingTransRequestTranTypeEnum._('WTH');

TargetSavingTransRequestTranTypeEnum
    _$targetSavingTransRequestTranTypeEnumValueOf(String name) {
  switch (name) {
    case 'DEP':
      return _$targetSavingTransRequestTranTypeEnum_DEP;
    case 'WTH':
      return _$targetSavingTransRequestTranTypeEnum_WTH;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<TargetSavingTransRequestTranTypeEnum>
    _$targetSavingTransRequestTranTypeEnumValues = BuiltSet<
        TargetSavingTransRequestTranTypeEnum>(const <TargetSavingTransRequestTranTypeEnum>[
  _$targetSavingTransRequestTranTypeEnum_DEP,
  _$targetSavingTransRequestTranTypeEnum_WTH,
]);

Serializer<TargetSavingTransRequestTranTypeEnum>
    _$targetSavingTransRequestTranTypeEnumSerializer =
    _$TargetSavingTransRequestTranTypeEnumSerializer();

class _$TargetSavingTransRequestTranTypeEnumSerializer
    implements PrimitiveSerializer<TargetSavingTransRequestTranTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'DEP': 'DEP',
    'WTH': 'WTH',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'DEP': 'DEP',
    'WTH': 'WTH',
  };

  @override
  final Iterable<Type> types = const <Type>[
    TargetSavingTransRequestTranTypeEnum
  ];
  @override
  final String wireName = 'TargetSavingTransRequestTranTypeEnum';

  @override
  Object serialize(
          Serializers serializers, TargetSavingTransRequestTranTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  TargetSavingTransRequestTranTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      TargetSavingTransRequestTranTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$TargetSavingTransRequest extends TargetSavingTransRequest {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? targetAccountNo;
  @override
  final String? sourceAccountNo;
  @override
  final num? amount;
  @override
  final TargetSavingTransRequestTranTypeEnum? tranType;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;

  factory _$TargetSavingTransRequest(
          [void Function(TargetSavingTransRequestBuilder)? updates]) =>
      (TargetSavingTransRequestBuilder()..update(updates))._build();

  _$TargetSavingTransRequest._(
      {this.bankCif,
      this.transactionNo,
      this.targetAccountNo,
      this.sourceAccountNo,
      this.amount,
      this.tranType,
      this.verifySoftOtp})
      : super._();
  @override
  TargetSavingTransRequest rebuild(
          void Function(TargetSavingTransRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TargetSavingTransRequestBuilder toBuilder() =>
      TargetSavingTransRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TargetSavingTransRequest &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        targetAccountNo == other.targetAccountNo &&
        sourceAccountNo == other.sourceAccountNo &&
        amount == other.amount &&
        tranType == other.tranType &&
        verifySoftOtp == other.verifySoftOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, targetAccountNo.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, tranType.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TargetSavingTransRequest')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('targetAccountNo', targetAccountNo)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('amount', amount)
          ..add('tranType', tranType)
          ..add('verifySoftOtp', verifySoftOtp))
        .toString();
  }
}

class TargetSavingTransRequestBuilder
    implements
        Builder<TargetSavingTransRequest, TargetSavingTransRequestBuilder> {
  _$TargetSavingTransRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _targetAccountNo;
  String? get targetAccountNo => _$this._targetAccountNo;
  set targetAccountNo(String? targetAccountNo) =>
      _$this._targetAccountNo = targetAccountNo;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  TargetSavingTransRequestTranTypeEnum? _tranType;
  TargetSavingTransRequestTranTypeEnum? get tranType => _$this._tranType;
  set tranType(TargetSavingTransRequestTranTypeEnum? tranType) =>
      _$this._tranType = tranType;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  TargetSavingTransRequestBuilder() {
    TargetSavingTransRequest._defaults(this);
  }

  TargetSavingTransRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _targetAccountNo = $v.targetAccountNo;
      _sourceAccountNo = $v.sourceAccountNo;
      _amount = $v.amount;
      _tranType = $v.tranType;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TargetSavingTransRequest other) {
    _$v = other as _$TargetSavingTransRequest;
  }

  @override
  void update(void Function(TargetSavingTransRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TargetSavingTransRequest build() => _build();

  _$TargetSavingTransRequest _build() {
    _$TargetSavingTransRequest _$result;
    try {
      _$result = _$v ??
          _$TargetSavingTransRequest._(
            bankCif: bankCif,
            transactionNo: transactionNo,
            targetAccountNo: targetAccountNo,
            sourceAccountNo: sourceAccountNo,
            amount: amount,
            tranType: tranType,
            verifySoftOtp: _verifySoftOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TargetSavingTransRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
