// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_close_online_saving_transfer_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VNPCloseOnlineSavingTransferResponse
    extends VNPCloseOnlineSavingTransferResponse {
  @override
  final BuiltList<CoreVnpTransactionResponse>? trans;
  @override
  final String? transactionNo;
  @override
  final String? transactionDate;
  @override
  final String? fromAcctRtxnNo;
  @override
  final String? toAcctRtxnNo;
  @override
  final String? rtxnNbrCharge;
  @override
  final String? amount;
  @override
  final String? withdrawalCode;
  @override
  final String? expireDate;
  @override
  final String? bankSigned;
  @override
  final String? action;
  @override
  final String? description;

  factory _$VNPCloseOnlineSavingTransferResponse(
          [void Function(VNPCloseOnlineSavingTransferResponseBuilder)?
              updates]) =>
      (VNPCloseOnlineSavingTransferResponseBuilder()..update(updates))._build();

  _$VNPCloseOnlineSavingTransferResponse._(
      {this.trans,
      this.transactionNo,
      this.transactionDate,
      this.fromAcctRtxnNo,
      this.toAcctRtxnNo,
      this.rtxnNbrCharge,
      this.amount,
      this.withdrawalCode,
      this.expireDate,
      this.bankSigned,
      this.action,
      this.description})
      : super._();
  @override
  VNPCloseOnlineSavingTransferResponse rebuild(
          void Function(VNPCloseOnlineSavingTransferResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VNPCloseOnlineSavingTransferResponseBuilder toBuilder() =>
      VNPCloseOnlineSavingTransferResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VNPCloseOnlineSavingTransferResponse &&
        trans == other.trans &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        fromAcctRtxnNo == other.fromAcctRtxnNo &&
        toAcctRtxnNo == other.toAcctRtxnNo &&
        rtxnNbrCharge == other.rtxnNbrCharge &&
        amount == other.amount &&
        withdrawalCode == other.withdrawalCode &&
        expireDate == other.expireDate &&
        bankSigned == other.bankSigned &&
        action == other.action &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, trans.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, fromAcctRtxnNo.hashCode);
    _$hash = $jc(_$hash, toAcctRtxnNo.hashCode);
    _$hash = $jc(_$hash, rtxnNbrCharge.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, withdrawalCode.hashCode);
    _$hash = $jc(_$hash, expireDate.hashCode);
    _$hash = $jc(_$hash, bankSigned.hashCode);
    _$hash = $jc(_$hash, action.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VNPCloseOnlineSavingTransferResponse')
          ..add('trans', trans)
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('fromAcctRtxnNo', fromAcctRtxnNo)
          ..add('toAcctRtxnNo', toAcctRtxnNo)
          ..add('rtxnNbrCharge', rtxnNbrCharge)
          ..add('amount', amount)
          ..add('withdrawalCode', withdrawalCode)
          ..add('expireDate', expireDate)
          ..add('bankSigned', bankSigned)
          ..add('action', action)
          ..add('description', description))
        .toString();
  }
}

class VNPCloseOnlineSavingTransferResponseBuilder
    implements
        Builder<VNPCloseOnlineSavingTransferResponse,
            VNPCloseOnlineSavingTransferResponseBuilder> {
  _$VNPCloseOnlineSavingTransferResponse? _$v;

  ListBuilder<CoreVnpTransactionResponse>? _trans;
  ListBuilder<CoreVnpTransactionResponse> get trans =>
      _$this._trans ??= ListBuilder<CoreVnpTransactionResponse>();
  set trans(ListBuilder<CoreVnpTransactionResponse>? trans) =>
      _$this._trans = trans;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _transactionDate;
  String? get transactionDate => _$this._transactionDate;
  set transactionDate(String? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _fromAcctRtxnNo;
  String? get fromAcctRtxnNo => _$this._fromAcctRtxnNo;
  set fromAcctRtxnNo(String? fromAcctRtxnNo) =>
      _$this._fromAcctRtxnNo = fromAcctRtxnNo;

  String? _toAcctRtxnNo;
  String? get toAcctRtxnNo => _$this._toAcctRtxnNo;
  set toAcctRtxnNo(String? toAcctRtxnNo) => _$this._toAcctRtxnNo = toAcctRtxnNo;

  String? _rtxnNbrCharge;
  String? get rtxnNbrCharge => _$this._rtxnNbrCharge;
  set rtxnNbrCharge(String? rtxnNbrCharge) =>
      _$this._rtxnNbrCharge = rtxnNbrCharge;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _withdrawalCode;
  String? get withdrawalCode => _$this._withdrawalCode;
  set withdrawalCode(String? withdrawalCode) =>
      _$this._withdrawalCode = withdrawalCode;

  String? _expireDate;
  String? get expireDate => _$this._expireDate;
  set expireDate(String? expireDate) => _$this._expireDate = expireDate;

  String? _bankSigned;
  String? get bankSigned => _$this._bankSigned;
  set bankSigned(String? bankSigned) => _$this._bankSigned = bankSigned;

  String? _action;
  String? get action => _$this._action;
  set action(String? action) => _$this._action = action;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  VNPCloseOnlineSavingTransferResponseBuilder() {
    VNPCloseOnlineSavingTransferResponse._defaults(this);
  }

  VNPCloseOnlineSavingTransferResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _trans = $v.trans?.toBuilder();
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _fromAcctRtxnNo = $v.fromAcctRtxnNo;
      _toAcctRtxnNo = $v.toAcctRtxnNo;
      _rtxnNbrCharge = $v.rtxnNbrCharge;
      _amount = $v.amount;
      _withdrawalCode = $v.withdrawalCode;
      _expireDate = $v.expireDate;
      _bankSigned = $v.bankSigned;
      _action = $v.action;
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VNPCloseOnlineSavingTransferResponse other) {
    _$v = other as _$VNPCloseOnlineSavingTransferResponse;
  }

  @override
  void update(
      void Function(VNPCloseOnlineSavingTransferResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VNPCloseOnlineSavingTransferResponse build() => _build();

  _$VNPCloseOnlineSavingTransferResponse _build() {
    _$VNPCloseOnlineSavingTransferResponse _$result;
    try {
      _$result = _$v ??
          _$VNPCloseOnlineSavingTransferResponse._(
            trans: _trans?.build(),
            transactionNo: transactionNo,
            transactionDate: transactionDate,
            fromAcctRtxnNo: fromAcctRtxnNo,
            toAcctRtxnNo: toAcctRtxnNo,
            rtxnNbrCharge: rtxnNbrCharge,
            amount: amount,
            withdrawalCode: withdrawalCode,
            expireDate: expireDate,
            bankSigned: bankSigned,
            action: action,
            description: description,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'trans';
        _trans?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'VNPCloseOnlineSavingTransferResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
