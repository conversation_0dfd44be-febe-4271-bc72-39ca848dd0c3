// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_pin_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResetPINCardRequest extends ResetPINCardRequest {
  @override
  final String? cardId;
  @override
  final String? idCardNo;
  @override
  final String? rawNewPIN;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;

  factory _$ResetPINCardRequest(
          [void Function(ResetPINCardRequestBuilder)? updates]) =>
      (ResetPINCardRequestBuilder()..update(updates))._build();

  _$ResetPINCardRequest._(
      {this.cardId, this.idCardNo, this.rawNewPIN, this.verifySoftOtp})
      : super._();
  @override
  ResetPINCardRequest rebuild(
          void Function(ResetPINCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResetPINCardRequestBuilder toBuilder() =>
      ResetPINCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResetPINCardRequest &&
        cardId == other.cardId &&
        idCardNo == other.idCardNo &&
        rawNewPIN == other.rawNewPIN &&
        verifySoftOtp == other.verifySoftOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, idCardNo.hashCode);
    _$hash = $jc(_$hash, rawNewPIN.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResetPINCardRequest')
          ..add('cardId', cardId)
          ..add('idCardNo', idCardNo)
          ..add('rawNewPIN', rawNewPIN)
          ..add('verifySoftOtp', verifySoftOtp))
        .toString();
  }
}

class ResetPINCardRequestBuilder
    implements Builder<ResetPINCardRequest, ResetPINCardRequestBuilder> {
  _$ResetPINCardRequest? _$v;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  String? _idCardNo;
  String? get idCardNo => _$this._idCardNo;
  set idCardNo(String? idCardNo) => _$this._idCardNo = idCardNo;

  String? _rawNewPIN;
  String? get rawNewPIN => _$this._rawNewPIN;
  set rawNewPIN(String? rawNewPIN) => _$this._rawNewPIN = rawNewPIN;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  ResetPINCardRequestBuilder() {
    ResetPINCardRequest._defaults(this);
  }

  ResetPINCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardId = $v.cardId;
      _idCardNo = $v.idCardNo;
      _rawNewPIN = $v.rawNewPIN;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResetPINCardRequest other) {
    _$v = other as _$ResetPINCardRequest;
  }

  @override
  void update(void Function(ResetPINCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResetPINCardRequest build() => _build();

  _$ResetPINCardRequest _build() {
    _$ResetPINCardRequest _$result;
    try {
      _$result = _$v ??
          _$ResetPINCardRequest._(
            cardId: cardId,
            idCardNo: idCardNo,
            rawNewPIN: rawNewPIN,
            verifySoftOtp: _verifySoftOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ResetPINCardRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
