// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenAccountResponse extends OpenAccountResponse {
  @override
  final String? accountNo;
  @override
  final String? accountName;
  @override
  final String? aliasName;
  @override
  final String? createdDate;
  @override
  final String? bankName;

  factory _$OpenAccountResponse(
          [void Function(OpenAccountResponseBuilder)? updates]) =>
      (OpenAccountResponseBuilder()..update(updates))._build();

  _$OpenAccountResponse._(
      {this.accountNo,
      this.accountName,
      this.aliasName,
      this.createdDate,
      this.bankName})
      : super._();
  @override
  OpenAccountResponse rebuild(
          void Function(OpenAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenAccountResponseBuilder toBuilder() =>
      OpenAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenAccountResponse &&
        accountNo == other.accountNo &&
        accountName == other.accountName &&
        aliasName == other.aliasName &&
        createdDate == other.createdDate &&
        bankName == other.bankName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, createdDate.hashCode);
    _$hash = $jc(_$hash, bankName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenAccountResponse')
          ..add('accountNo', accountNo)
          ..add('accountName', accountName)
          ..add('aliasName', aliasName)
          ..add('createdDate', createdDate)
          ..add('bankName', bankName))
        .toString();
  }
}

class OpenAccountResponseBuilder
    implements Builder<OpenAccountResponse, OpenAccountResponseBuilder> {
  _$OpenAccountResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  String? _createdDate;
  String? get createdDate => _$this._createdDate;
  set createdDate(String? createdDate) => _$this._createdDate = createdDate;

  String? _bankName;
  String? get bankName => _$this._bankName;
  set bankName(String? bankName) => _$this._bankName = bankName;

  OpenAccountResponseBuilder() {
    OpenAccountResponse._defaults(this);
  }

  OpenAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _accountName = $v.accountName;
      _aliasName = $v.aliasName;
      _createdDate = $v.createdDate;
      _bankName = $v.bankName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenAccountResponse other) {
    _$v = other as _$OpenAccountResponse;
  }

  @override
  void update(void Function(OpenAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenAccountResponse build() => _build();

  _$OpenAccountResponse _build() {
    final _$result = _$v ??
        _$OpenAccountResponse._(
          accountNo: accountNo,
          accountName: accountName,
          aliasName: aliasName,
          createdDate: createdDate,
          bankName: bankName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
