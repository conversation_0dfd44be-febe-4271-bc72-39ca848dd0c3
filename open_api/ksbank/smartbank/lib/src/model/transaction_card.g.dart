// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_card.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionCard extends TransactionCard {
  @override
  final String? amount;
  @override
  final String? transType;
  @override
  final String? transDateTime;
  @override
  final String? detail;
  @override
  final String? crdb;

  factory _$TransactionCard([void Function(TransactionCardBuilder)? updates]) =>
      (TransactionCardBuilder()..update(updates))._build();

  _$TransactionCard._(
      {this.amount, this.transType, this.transDateTime, this.detail, this.crdb})
      : super._();
  @override
  TransactionCard rebuild(void Function(TransactionCardBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionCardBuilder toBuilder() => TransactionCardBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionCard &&
        amount == other.amount &&
        transType == other.transType &&
        transDateTime == other.transDateTime &&
        detail == other.detail &&
        crdb == other.crdb;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, transType.hashCode);
    _$hash = $jc(_$hash, transDateTime.hashCode);
    _$hash = $jc(_$hash, detail.hashCode);
    _$hash = $jc(_$hash, crdb.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionCard')
          ..add('amount', amount)
          ..add('transType', transType)
          ..add('transDateTime', transDateTime)
          ..add('detail', detail)
          ..add('crdb', crdb))
        .toString();
  }
}

class TransactionCardBuilder
    implements Builder<TransactionCard, TransactionCardBuilder> {
  _$TransactionCard? _$v;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _transType;
  String? get transType => _$this._transType;
  set transType(String? transType) => _$this._transType = transType;

  String? _transDateTime;
  String? get transDateTime => _$this._transDateTime;
  set transDateTime(String? transDateTime) =>
      _$this._transDateTime = transDateTime;

  String? _detail;
  String? get detail => _$this._detail;
  set detail(String? detail) => _$this._detail = detail;

  String? _crdb;
  String? get crdb => _$this._crdb;
  set crdb(String? crdb) => _$this._crdb = crdb;

  TransactionCardBuilder() {
    TransactionCard._defaults(this);
  }

  TransactionCardBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _amount = $v.amount;
      _transType = $v.transType;
      _transDateTime = $v.transDateTime;
      _detail = $v.detail;
      _crdb = $v.crdb;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionCard other) {
    _$v = other as _$TransactionCard;
  }

  @override
  void update(void Function(TransactionCardBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionCard build() => _build();

  _$TransactionCard _build() {
    final _$result = _$v ??
        _$TransactionCard._(
          amount: amount,
          transType: transType,
          transDateTime: transDateTime,
          detail: detail,
          crdb: crdb,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
