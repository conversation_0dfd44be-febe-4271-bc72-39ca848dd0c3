// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_phone_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentPhoneCardResponse extends PaymentPhoneCardResponse {
  @override
  final BuiltList<PaidPhoneCardResponse>? paidPhoneCards;

  factory _$PaymentPhoneCardResponse(
          [void Function(PaymentPhoneCardResponseBuilder)? updates]) =>
      (PaymentPhoneCardResponseBuilder()..update(updates))._build();

  _$PaymentPhoneCardResponse._({this.paidPhoneCards}) : super._();
  @override
  PaymentPhoneCardResponse rebuild(
          void Function(PaymentPhoneCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentPhoneCardResponseBuilder toBuilder() =>
      PaymentPhoneCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentPhoneCardResponse &&
        paidPhoneCards == other.paidPhoneCards;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, paidPhoneCards.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentPhoneCardResponse')
          ..add('paidPhoneCards', paidPhoneCards))
        .toString();
  }
}

class PaymentPhoneCardResponseBuilder
    implements
        Builder<PaymentPhoneCardResponse, PaymentPhoneCardResponseBuilder> {
  _$PaymentPhoneCardResponse? _$v;

  ListBuilder<PaidPhoneCardResponse>? _paidPhoneCards;
  ListBuilder<PaidPhoneCardResponse> get paidPhoneCards =>
      _$this._paidPhoneCards ??= ListBuilder<PaidPhoneCardResponse>();
  set paidPhoneCards(ListBuilder<PaidPhoneCardResponse>? paidPhoneCards) =>
      _$this._paidPhoneCards = paidPhoneCards;

  PaymentPhoneCardResponseBuilder() {
    PaymentPhoneCardResponse._defaults(this);
  }

  PaymentPhoneCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _paidPhoneCards = $v.paidPhoneCards?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentPhoneCardResponse other) {
    _$v = other as _$PaymentPhoneCardResponse;
  }

  @override
  void update(void Function(PaymentPhoneCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentPhoneCardResponse build() => _build();

  _$PaymentPhoneCardResponse _build() {
    _$PaymentPhoneCardResponse _$result;
    try {
      _$result = _$v ??
          _$PaymentPhoneCardResponse._(
            paidPhoneCards: _paidPhoneCards?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'paidPhoneCards';
        _paidPhoneCards?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PaymentPhoneCardResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
