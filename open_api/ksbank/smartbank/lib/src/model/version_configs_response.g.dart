// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'version_configs_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VersionConfigsResponse extends VersionConfigsResponse {
  @override
  final int? id;
  @override
  final String? type;
  @override
  final String? description;
  @override
  final bool? activate;
  @override
  final int? iosVersion;
  @override
  final int? androidVersion;
  @override
  final String? phones;
  @override
  final int? valueNumber;

  factory _$VersionConfigsResponse(
          [void Function(VersionConfigsResponseBuilder)? updates]) =>
      (VersionConfigsResponseBuilder()..update(updates))._build();

  _$VersionConfigsResponse._(
      {this.id,
      this.type,
      this.description,
      this.activate,
      this.iosVersion,
      this.androidVersion,
      this.phones,
      this.valueNumber})
      : super._();
  @override
  VersionConfigsResponse rebuild(
          void Function(VersionConfigsResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VersionConfigsResponseBuilder toBuilder() =>
      VersionConfigsResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VersionConfigsResponse &&
        id == other.id &&
        type == other.type &&
        description == other.description &&
        activate == other.activate &&
        iosVersion == other.iosVersion &&
        androidVersion == other.androidVersion &&
        phones == other.phones &&
        valueNumber == other.valueNumber;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, activate.hashCode);
    _$hash = $jc(_$hash, iosVersion.hashCode);
    _$hash = $jc(_$hash, androidVersion.hashCode);
    _$hash = $jc(_$hash, phones.hashCode);
    _$hash = $jc(_$hash, valueNumber.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VersionConfigsResponse')
          ..add('id', id)
          ..add('type', type)
          ..add('description', description)
          ..add('activate', activate)
          ..add('iosVersion', iosVersion)
          ..add('androidVersion', androidVersion)
          ..add('phones', phones)
          ..add('valueNumber', valueNumber))
        .toString();
  }
}

class VersionConfigsResponseBuilder
    implements Builder<VersionConfigsResponse, VersionConfigsResponseBuilder> {
  _$VersionConfigsResponse? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  String? _type;
  String? get type => _$this._type;
  set type(String? type) => _$this._type = type;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  bool? _activate;
  bool? get activate => _$this._activate;
  set activate(bool? activate) => _$this._activate = activate;

  int? _iosVersion;
  int? get iosVersion => _$this._iosVersion;
  set iosVersion(int? iosVersion) => _$this._iosVersion = iosVersion;

  int? _androidVersion;
  int? get androidVersion => _$this._androidVersion;
  set androidVersion(int? androidVersion) =>
      _$this._androidVersion = androidVersion;

  String? _phones;
  String? get phones => _$this._phones;
  set phones(String? phones) => _$this._phones = phones;

  int? _valueNumber;
  int? get valueNumber => _$this._valueNumber;
  set valueNumber(int? valueNumber) => _$this._valueNumber = valueNumber;

  VersionConfigsResponseBuilder() {
    VersionConfigsResponse._defaults(this);
  }

  VersionConfigsResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _type = $v.type;
      _description = $v.description;
      _activate = $v.activate;
      _iosVersion = $v.iosVersion;
      _androidVersion = $v.androidVersion;
      _phones = $v.phones;
      _valueNumber = $v.valueNumber;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VersionConfigsResponse other) {
    _$v = other as _$VersionConfigsResponse;
  }

  @override
  void update(void Function(VersionConfigsResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VersionConfigsResponse build() => _build();

  _$VersionConfigsResponse _build() {
    final _$result = _$v ??
        _$VersionConfigsResponse._(
          id: id,
          type: type,
          description: description,
          activate: activate,
          iosVersion: iosVersion,
          androidVersion: androidVersion,
          phones: phones,
          valueNumber: valueNumber,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
