// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_type_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionTypeResponse extends TransactionTypeResponse {
  @override
  final int? id;
  @override
  final String? name;

  factory _$TransactionTypeResponse(
          [void Function(TransactionTypeResponseBuilder)? updates]) =>
      (TransactionTypeResponseBuilder()..update(updates))._build();

  _$TransactionTypeResponse._({this.id, this.name}) : super._();
  @override
  TransactionTypeResponse rebuild(
          void Function(TransactionTypeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionTypeResponseBuilder toBuilder() =>
      TransactionTypeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionTypeResponse &&
        id == other.id &&
        name == other.name;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionTypeResponse')
          ..add('id', id)
          ..add('name', name))
        .toString();
  }
}

class TransactionTypeResponseBuilder
    implements
        Builder<TransactionTypeResponse, TransactionTypeResponseBuilder> {
  _$TransactionTypeResponse? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  TransactionTypeResponseBuilder() {
    TransactionTypeResponse._defaults(this);
  }

  TransactionTypeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionTypeResponse other) {
    _$v = other as _$TransactionTypeResponse;
  }

  @override
  void update(void Function(TransactionTypeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionTypeResponse build() => _build();

  _$TransactionTypeResponse _build() {
    final _$result = _$v ??
        _$TransactionTypeResponse._(
          id: id,
          name: name,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
