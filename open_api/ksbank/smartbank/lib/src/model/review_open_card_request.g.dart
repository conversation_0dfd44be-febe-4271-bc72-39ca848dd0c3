// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_open_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewOpenCardRequest extends ReviewOpenCardRequest {
  @override
  final String? productId;

  factory _$ReviewOpenCardRequest(
          [void Function(ReviewOpenCardRequestBuilder)? updates]) =>
      (ReviewOpenCardRequestBuilder()..update(updates))._build();

  _$ReviewOpenCardRequest._({this.productId}) : super._();
  @override
  ReviewOpenCardRequest rebuild(
          void Function(ReviewOpenCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewOpenCardRequestBuilder toBuilder() =>
      ReviewOpenCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewOpenCardRequest && productId == other.productId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, productId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewOpenCardRequest')
          ..add('productId', productId))
        .toString();
  }
}

class ReviewOpenCardRequestBuilder
    implements Builder<ReviewOpenCardRequest, ReviewOpenCardRequestBuilder> {
  _$ReviewOpenCardRequest? _$v;

  String? _productId;
  String? get productId => _$this._productId;
  set productId(String? productId) => _$this._productId = productId;

  ReviewOpenCardRequestBuilder() {
    ReviewOpenCardRequest._defaults(this);
  }

  ReviewOpenCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _productId = $v.productId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewOpenCardRequest other) {
    _$v = other as _$ReviewOpenCardRequest;
  }

  @override
  void update(void Function(ReviewOpenCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewOpenCardRequest build() => _build();

  _$ReviewOpenCardRequest _build() {
    final _$result = _$v ??
        _$ReviewOpenCardRequest._(
          productId: productId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
