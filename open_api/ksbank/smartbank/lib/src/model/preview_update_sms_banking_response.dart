//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'preview_update_sms_banking_response.g.dart';

/// PreviewUpdateSmsBankingResponse
///
/// Properties:
/// * [requestId] - Mã yêu cầu
/// * [authType] - <PERSON><PERSON><PERSON> xá<PERSON> thực
@BuiltValue()
abstract class PreviewUpdateSmsBankingResponse
    implements
        Built<PreviewUpdateSmsBankingResponse,
            PreviewUpdateSmsBankingResponseBuilder> {
  /// Mã yêu cầu
  @BuiltValueField(wireName: r'requestId')
  String? get requestId;

  /// Lo<PERSON>i xác thực
  @BuiltValueField(wireName: r'authType')
  PreviewUpdateSmsBankingResponseAuthTypeEnum? get authType;
  // enum authTypeEnum {  PASSWORD,  SMS_OTP,  T_OTP,  H_OTP,  SOFT_OTP,  };

  PreviewUpdateSmsBankingResponse._();

  factory PreviewUpdateSmsBankingResponse(
          [void updates(PreviewUpdateSmsBankingResponseBuilder b)]) =
      _$PreviewUpdateSmsBankingResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PreviewUpdateSmsBankingResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PreviewUpdateSmsBankingResponse> get serializer =>
      _$PreviewUpdateSmsBankingResponseSerializer();
}

class _$PreviewUpdateSmsBankingResponseSerializer
    implements PrimitiveSerializer<PreviewUpdateSmsBankingResponse> {
  @override
  final Iterable<Type> types = const [
    PreviewUpdateSmsBankingResponse,
    _$PreviewUpdateSmsBankingResponse
  ];

  @override
  final String wireName = r'PreviewUpdateSmsBankingResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PreviewUpdateSmsBankingResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.requestId != null) {
      yield r'requestId';
      yield serializers.serialize(
        object.requestId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.authType != null) {
      yield r'authType';
      yield serializers.serialize(
        object.authType,
        specifiedType: const FullType.nullable(
            PreviewUpdateSmsBankingResponseAuthTypeEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PreviewUpdateSmsBankingResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PreviewUpdateSmsBankingResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'requestId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.requestId = valueDes;
          break;
        case r'authType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                PreviewUpdateSmsBankingResponseAuthTypeEnum),
          ) as PreviewUpdateSmsBankingResponseAuthTypeEnum?;
          if (valueDes == null) continue;
          result.authType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PreviewUpdateSmsBankingResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PreviewUpdateSmsBankingResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PreviewUpdateSmsBankingResponseAuthTypeEnum extends EnumClass {
  /// Loại xác thực
  @BuiltValueEnumConst(wireName: r'PASSWORD')
  static const PreviewUpdateSmsBankingResponseAuthTypeEnum PASSWORD =
      _$previewUpdateSmsBankingResponseAuthTypeEnum_PASSWORD;

  /// Loại xác thực
  @BuiltValueEnumConst(wireName: r'SMS_OTP')
  static const PreviewUpdateSmsBankingResponseAuthTypeEnum SMS_OTP =
      _$previewUpdateSmsBankingResponseAuthTypeEnum_SMS_OTP;

  /// Loại xác thực
  @BuiltValueEnumConst(wireName: r'T_OTP')
  static const PreviewUpdateSmsBankingResponseAuthTypeEnum T_OTP =
      _$previewUpdateSmsBankingResponseAuthTypeEnum_T_OTP;

  /// Loại xác thực
  @BuiltValueEnumConst(wireName: r'H_OTP')
  static const PreviewUpdateSmsBankingResponseAuthTypeEnum H_OTP =
      _$previewUpdateSmsBankingResponseAuthTypeEnum_H_OTP;

  /// Loại xác thực
  @BuiltValueEnumConst(wireName: r'SOFT_OTP')
  static const PreviewUpdateSmsBankingResponseAuthTypeEnum SOFT_OTP =
      _$previewUpdateSmsBankingResponseAuthTypeEnum_SOFT_OTP;

  static Serializer<PreviewUpdateSmsBankingResponseAuthTypeEnum>
      get serializer => _$previewUpdateSmsBankingResponseAuthTypeEnumSerializer;

  const PreviewUpdateSmsBankingResponseAuthTypeEnum._(String name)
      : super(name);

  static BuiltSet<PreviewUpdateSmsBankingResponseAuthTypeEnum> get values =>
      _$previewUpdateSmsBankingResponseAuthTypeEnumValues;
  static PreviewUpdateSmsBankingResponseAuthTypeEnum valueOf(String name) =>
      _$previewUpdateSmsBankingResponseAuthTypeEnumValueOf(name);
}
