// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoanResponse extends LoanResponse {
  @override
  final String? id;
  @override
  final String? accountNo;
  @override
  final String? accountName;
  @override
  final num? amount;
  @override
  final num? loanAmount;
  @override
  final String? purposeTypeName;
  @override
  final double? rate;
  @override
  final String? imageUrl;
  @override
  final String? nextPayDate;
  @override
  final double? nextPayAmount;
  @override
  final num? remainAmount;
  @override
  final String? currency;

  factory _$LoanResponse([void Function(LoanResponseBuilder)? updates]) =>
      (LoanResponseBuilder()..update(updates))._build();

  _$LoanResponse._(
      {this.id,
      this.accountNo,
      this.accountName,
      this.amount,
      this.loanAmount,
      this.purposeTypeName,
      this.rate,
      this.imageUrl,
      this.nextPayDate,
      this.nextPayAmount,
      this.remainAmount,
      this.currency})
      : super._();
  @override
  LoanResponse rebuild(void Function(LoanResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanResponseBuilder toBuilder() => LoanResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanResponse &&
        id == other.id &&
        accountNo == other.accountNo &&
        accountName == other.accountName &&
        amount == other.amount &&
        loanAmount == other.loanAmount &&
        purposeTypeName == other.purposeTypeName &&
        rate == other.rate &&
        imageUrl == other.imageUrl &&
        nextPayDate == other.nextPayDate &&
        nextPayAmount == other.nextPayAmount &&
        remainAmount == other.remainAmount &&
        currency == other.currency;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, loanAmount.hashCode);
    _$hash = $jc(_$hash, purposeTypeName.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jc(_$hash, nextPayDate.hashCode);
    _$hash = $jc(_$hash, nextPayAmount.hashCode);
    _$hash = $jc(_$hash, remainAmount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanResponse')
          ..add('id', id)
          ..add('accountNo', accountNo)
          ..add('accountName', accountName)
          ..add('amount', amount)
          ..add('loanAmount', loanAmount)
          ..add('purposeTypeName', purposeTypeName)
          ..add('rate', rate)
          ..add('imageUrl', imageUrl)
          ..add('nextPayDate', nextPayDate)
          ..add('nextPayAmount', nextPayAmount)
          ..add('remainAmount', remainAmount)
          ..add('currency', currency))
        .toString();
  }
}

class LoanResponseBuilder
    implements Builder<LoanResponse, LoanResponseBuilder> {
  _$LoanResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _loanAmount;
  num? get loanAmount => _$this._loanAmount;
  set loanAmount(num? loanAmount) => _$this._loanAmount = loanAmount;

  String? _purposeTypeName;
  String? get purposeTypeName => _$this._purposeTypeName;
  set purposeTypeName(String? purposeTypeName) =>
      _$this._purposeTypeName = purposeTypeName;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  String? _nextPayDate;
  String? get nextPayDate => _$this._nextPayDate;
  set nextPayDate(String? nextPayDate) => _$this._nextPayDate = nextPayDate;

  double? _nextPayAmount;
  double? get nextPayAmount => _$this._nextPayAmount;
  set nextPayAmount(double? nextPayAmount) =>
      _$this._nextPayAmount = nextPayAmount;

  num? _remainAmount;
  num? get remainAmount => _$this._remainAmount;
  set remainAmount(num? remainAmount) => _$this._remainAmount = remainAmount;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  LoanResponseBuilder() {
    LoanResponse._defaults(this);
  }

  LoanResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _accountNo = $v.accountNo;
      _accountName = $v.accountName;
      _amount = $v.amount;
      _loanAmount = $v.loanAmount;
      _purposeTypeName = $v.purposeTypeName;
      _rate = $v.rate;
      _imageUrl = $v.imageUrl;
      _nextPayDate = $v.nextPayDate;
      _nextPayAmount = $v.nextPayAmount;
      _remainAmount = $v.remainAmount;
      _currency = $v.currency;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanResponse other) {
    _$v = other as _$LoanResponse;
  }

  @override
  void update(void Function(LoanResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanResponse build() => _build();

  _$LoanResponse _build() {
    final _$result = _$v ??
        _$LoanResponse._(
          id: id,
          accountNo: accountNo,
          accountName: accountName,
          amount: amount,
          loanAmount: loanAmount,
          purposeTypeName: purposeTypeName,
          rate: rate,
          imageUrl: imageUrl,
          nextPayDate: nextPayDate,
          nextPayAmount: nextPayAmount,
          remainAmount: remainAmount,
          currency: currency,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
