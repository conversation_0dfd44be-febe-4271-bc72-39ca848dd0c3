// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenAccountRequest extends OpenAccountRequest {
  @override
  final String? bankCif;
  @override
  final String? branchCode;
  @override
  final String? minor;
  @override
  final String? wrnMessage;
  @override
  final String? alias;
  @override
  final String? groupCode;
  @override
  final String? agentCode;

  factory _$OpenAccountRequest(
          [void Function(OpenAccountRequestBuilder)? updates]) =>
      (OpenAccountRequestBuilder()..update(updates))._build();

  _$OpenAccountRequest._(
      {this.bankCif,
      this.branchCode,
      this.minor,
      this.wrnMessage,
      this.alias,
      this.groupCode,
      this.agentCode})
      : super._();
  @override
  OpenAccountRequest rebuild(
          void Function(OpenAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenAccountRequestBuilder toBuilder() =>
      OpenAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenAccountRequest &&
        bankCif == other.bankCif &&
        branchCode == other.branchCode &&
        minor == other.minor &&
        wrnMessage == other.wrnMessage &&
        alias == other.alias &&
        groupCode == other.groupCode &&
        agentCode == other.agentCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, branchCode.hashCode);
    _$hash = $jc(_$hash, minor.hashCode);
    _$hash = $jc(_$hash, wrnMessage.hashCode);
    _$hash = $jc(_$hash, alias.hashCode);
    _$hash = $jc(_$hash, groupCode.hashCode);
    _$hash = $jc(_$hash, agentCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenAccountRequest')
          ..add('bankCif', bankCif)
          ..add('branchCode', branchCode)
          ..add('minor', minor)
          ..add('wrnMessage', wrnMessage)
          ..add('alias', alias)
          ..add('groupCode', groupCode)
          ..add('agentCode', agentCode))
        .toString();
  }
}

class OpenAccountRequestBuilder
    implements Builder<OpenAccountRequest, OpenAccountRequestBuilder> {
  _$OpenAccountRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _branchCode;
  String? get branchCode => _$this._branchCode;
  set branchCode(String? branchCode) => _$this._branchCode = branchCode;

  String? _minor;
  String? get minor => _$this._minor;
  set minor(String? minor) => _$this._minor = minor;

  String? _wrnMessage;
  String? get wrnMessage => _$this._wrnMessage;
  set wrnMessage(String? wrnMessage) => _$this._wrnMessage = wrnMessage;

  String? _alias;
  String? get alias => _$this._alias;
  set alias(String? alias) => _$this._alias = alias;

  String? _groupCode;
  String? get groupCode => _$this._groupCode;
  set groupCode(String? groupCode) => _$this._groupCode = groupCode;

  String? _agentCode;
  String? get agentCode => _$this._agentCode;
  set agentCode(String? agentCode) => _$this._agentCode = agentCode;

  OpenAccountRequestBuilder() {
    OpenAccountRequest._defaults(this);
  }

  OpenAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _branchCode = $v.branchCode;
      _minor = $v.minor;
      _wrnMessage = $v.wrnMessage;
      _alias = $v.alias;
      _groupCode = $v.groupCode;
      _agentCode = $v.agentCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenAccountRequest other) {
    _$v = other as _$OpenAccountRequest;
  }

  @override
  void update(void Function(OpenAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenAccountRequest build() => _build();

  _$OpenAccountRequest _build() {
    final _$result = _$v ??
        _$OpenAccountRequest._(
          bankCif: bankCif,
          branchCode: branchCode,
          minor: minor,
          wrnMessage: wrnMessage,
          alias: alias,
          groupCode: groupCode,
          agentCode: agentCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
