// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_ecom_lock_credit_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEcomLockCreditCardRequest
    extends UpdateEcomLockCreditCardRequest {
  @override
  final String? cardId;
  @override
  final Flag? flag;

  factory _$UpdateEcomLockCreditCardRequest(
          [void Function(UpdateEcomLockCreditCardRequestBuilder)? updates]) =>
      (UpdateEcomLockCreditCardRequestBuilder()..update(updates))._build();

  _$UpdateEcomLockCreditCardRequest._({this.cardId, this.flag}) : super._();
  @override
  UpdateEcomLockCreditCardRequest rebuild(
          void Function(UpdateEcomLockCreditCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEcomLockCreditCardRequestBuilder toBuilder() =>
      UpdateEcomLockCreditCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEcomLockCreditCardRequest &&
        cardId == other.cardId &&
        flag == other.flag;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, flag.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEcomLockCreditCardRequest')
          ..add('cardId', cardId)
          ..add('flag', flag))
        .toString();
  }
}

class UpdateEcomLockCreditCardRequestBuilder
    implements
        Builder<UpdateEcomLockCreditCardRequest,
            UpdateEcomLockCreditCardRequestBuilder> {
  _$UpdateEcomLockCreditCardRequest? _$v;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  Flag? _flag;
  Flag? get flag => _$this._flag;
  set flag(Flag? flag) => _$this._flag = flag;

  UpdateEcomLockCreditCardRequestBuilder() {
    UpdateEcomLockCreditCardRequest._defaults(this);
  }

  UpdateEcomLockCreditCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardId = $v.cardId;
      _flag = $v.flag;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEcomLockCreditCardRequest other) {
    _$v = other as _$UpdateEcomLockCreditCardRequest;
  }

  @override
  void update(void Function(UpdateEcomLockCreditCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEcomLockCreditCardRequest build() => _build();

  _$UpdateEcomLockCreditCardRequest _build() {
    final _$result = _$v ??
        _$UpdateEcomLockCreditCardRequest._(
          cardId: cardId,
          flag: flag,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
