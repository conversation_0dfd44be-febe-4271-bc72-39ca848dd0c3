// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionResponse extends TransactionResponse {
  @override
  final int? noOfRecord;
  @override
  final BuiltList<TransactionItemMapper>? recordInfos;

  factory _$TransactionResponse(
          [void Function(TransactionResponseBuilder)? updates]) =>
      (TransactionResponseBuilder()..update(updates))._build();

  _$TransactionResponse._({this.noOfRecord, this.recordInfos}) : super._();
  @override
  TransactionResponse rebuild(
          void Function(TransactionResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionResponseBuilder toBuilder() =>
      TransactionResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionResponse &&
        noOfRecord == other.noOfRecord &&
        recordInfos == other.recordInfos;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, noOfRecord.hashCode);
    _$hash = $jc(_$hash, recordInfos.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionResponse')
          ..add('noOfRecord', noOfRecord)
          ..add('recordInfos', recordInfos))
        .toString();
  }
}

class TransactionResponseBuilder
    implements Builder<TransactionResponse, TransactionResponseBuilder> {
  _$TransactionResponse? _$v;

  int? _noOfRecord;
  int? get noOfRecord => _$this._noOfRecord;
  set noOfRecord(int? noOfRecord) => _$this._noOfRecord = noOfRecord;

  ListBuilder<TransactionItemMapper>? _recordInfos;
  ListBuilder<TransactionItemMapper> get recordInfos =>
      _$this._recordInfos ??= ListBuilder<TransactionItemMapper>();
  set recordInfos(ListBuilder<TransactionItemMapper>? recordInfos) =>
      _$this._recordInfos = recordInfos;

  TransactionResponseBuilder() {
    TransactionResponse._defaults(this);
  }

  TransactionResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _noOfRecord = $v.noOfRecord;
      _recordInfos = $v.recordInfos?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionResponse other) {
    _$v = other as _$TransactionResponse;
  }

  @override
  void update(void Function(TransactionResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionResponse build() => _build();

  _$TransactionResponse _build() {
    _$TransactionResponse _$result;
    try {
      _$result = _$v ??
          _$TransactionResponse._(
            noOfRecord: noOfRecord,
            recordInfos: _recordInfos?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'recordInfos';
        _recordInfos?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransactionResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
