// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'topup_value_lotus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TopupValueLotusResponse extends TopupValueLotusResponse {
  @override
  final num? cardValue;
  @override
  final String? cardValueCode;
  @override
  final String? cardValueName;
  @override
  final num? purchasingPrice;
  @override
  final num? referPrice;
  @override
  final bool? canPaid;
  @override
  final num? discountPercent;

  factory _$TopupValueLotusResponse(
          [void Function(TopupValueLotusResponseBuilder)? updates]) =>
      (TopupValueLotusResponseBuilder()..update(updates))._build();

  _$TopupValueLotusResponse._(
      {this.cardValue,
      this.cardValueCode,
      this.cardValueName,
      this.purchasingPrice,
      this.referPrice,
      this.canPaid,
      this.discountPercent})
      : super._();
  @override
  TopupValueLotusResponse rebuild(
          void Function(TopupValueLotusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TopupValueLotusResponseBuilder toBuilder() =>
      TopupValueLotusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TopupValueLotusResponse &&
        cardValue == other.cardValue &&
        cardValueCode == other.cardValueCode &&
        cardValueName == other.cardValueName &&
        purchasingPrice == other.purchasingPrice &&
        referPrice == other.referPrice &&
        canPaid == other.canPaid &&
        discountPercent == other.discountPercent;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jc(_$hash, cardValueCode.hashCode);
    _$hash = $jc(_$hash, cardValueName.hashCode);
    _$hash = $jc(_$hash, purchasingPrice.hashCode);
    _$hash = $jc(_$hash, referPrice.hashCode);
    _$hash = $jc(_$hash, canPaid.hashCode);
    _$hash = $jc(_$hash, discountPercent.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TopupValueLotusResponse')
          ..add('cardValue', cardValue)
          ..add('cardValueCode', cardValueCode)
          ..add('cardValueName', cardValueName)
          ..add('purchasingPrice', purchasingPrice)
          ..add('referPrice', referPrice)
          ..add('canPaid', canPaid)
          ..add('discountPercent', discountPercent))
        .toString();
  }
}

class TopupValueLotusResponseBuilder
    implements
        Builder<TopupValueLotusResponse, TopupValueLotusResponseBuilder> {
  _$TopupValueLotusResponse? _$v;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  String? _cardValueCode;
  String? get cardValueCode => _$this._cardValueCode;
  set cardValueCode(String? cardValueCode) =>
      _$this._cardValueCode = cardValueCode;

  String? _cardValueName;
  String? get cardValueName => _$this._cardValueName;
  set cardValueName(String? cardValueName) =>
      _$this._cardValueName = cardValueName;

  num? _purchasingPrice;
  num? get purchasingPrice => _$this._purchasingPrice;
  set purchasingPrice(num? purchasingPrice) =>
      _$this._purchasingPrice = purchasingPrice;

  num? _referPrice;
  num? get referPrice => _$this._referPrice;
  set referPrice(num? referPrice) => _$this._referPrice = referPrice;

  bool? _canPaid;
  bool? get canPaid => _$this._canPaid;
  set canPaid(bool? canPaid) => _$this._canPaid = canPaid;

  num? _discountPercent;
  num? get discountPercent => _$this._discountPercent;
  set discountPercent(num? discountPercent) =>
      _$this._discountPercent = discountPercent;

  TopupValueLotusResponseBuilder() {
    TopupValueLotusResponse._defaults(this);
  }

  TopupValueLotusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardValue = $v.cardValue;
      _cardValueCode = $v.cardValueCode;
      _cardValueName = $v.cardValueName;
      _purchasingPrice = $v.purchasingPrice;
      _referPrice = $v.referPrice;
      _canPaid = $v.canPaid;
      _discountPercent = $v.discountPercent;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TopupValueLotusResponse other) {
    _$v = other as _$TopupValueLotusResponse;
  }

  @override
  void update(void Function(TopupValueLotusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TopupValueLotusResponse build() => _build();

  _$TopupValueLotusResponse _build() {
    final _$result = _$v ??
        _$TopupValueLotusResponse._(
          cardValue: cardValue,
          cardValueCode: cardValueCode,
          cardValueName: cardValueName,
          purchasingPrice: purchasingPrice,
          referPrice: referPrice,
          canPaid: canPaid,
          discountPercent: discountPercent,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
