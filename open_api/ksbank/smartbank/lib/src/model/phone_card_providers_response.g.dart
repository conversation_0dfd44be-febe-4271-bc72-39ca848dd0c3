// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'phone_card_providers_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PhoneCardProvidersResponseTelephoneProviderEnum
    _$phoneCardProvidersResponseTelephoneProviderEnum_MOBIFONE =
    const PhoneCardProvidersResponseTelephoneProviderEnum._('MOBIFONE');
const PhoneCardProvidersResponseTelephoneProviderEnum
    _$phoneCardProvidersResponseTelephoneProviderEnum_VINAPHONE =
    const PhoneCardProvidersResponseTelephoneProviderEnum._('VINAPHONE');
const PhoneCardProvidersResponseTelephoneProviderEnum
    _$phoneCardProvidersResponseTelephoneProviderEnum_VIETTEL =
    const PhoneCardProvidersResponseTelephoneProviderEnum._('VIETTEL');
const PhoneCardProvidersResponseTelephoneProviderEnum
    _$phoneCardProvidersResponseTelephoneProviderEnum_VIETNAMOBILE =
    const PhoneCardProvidersResponseTelephoneProviderEnum._('VIETNAMOBILE');
const PhoneCardProvidersResponseTelephoneProviderEnum
    _$phoneCardProvidersResponseTelephoneProviderEnum_GMOBILE =
    const PhoneCardProvidersResponseTelephoneProviderEnum._('GMOBILE');

PhoneCardProvidersResponseTelephoneProviderEnum
    _$phoneCardProvidersResponseTelephoneProviderEnumValueOf(String name) {
  switch (name) {
    case 'MOBIFONE':
      return _$phoneCardProvidersResponseTelephoneProviderEnum_MOBIFONE;
    case 'VINAPHONE':
      return _$phoneCardProvidersResponseTelephoneProviderEnum_VINAPHONE;
    case 'VIETTEL':
      return _$phoneCardProvidersResponseTelephoneProviderEnum_VIETTEL;
    case 'VIETNAMOBILE':
      return _$phoneCardProvidersResponseTelephoneProviderEnum_VIETNAMOBILE;
    case 'GMOBILE':
      return _$phoneCardProvidersResponseTelephoneProviderEnum_GMOBILE;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PhoneCardProvidersResponseTelephoneProviderEnum>
    _$phoneCardProvidersResponseTelephoneProviderEnumValues = BuiltSet<
        PhoneCardProvidersResponseTelephoneProviderEnum>(const <PhoneCardProvidersResponseTelephoneProviderEnum>[
  _$phoneCardProvidersResponseTelephoneProviderEnum_MOBIFONE,
  _$phoneCardProvidersResponseTelephoneProviderEnum_VINAPHONE,
  _$phoneCardProvidersResponseTelephoneProviderEnum_VIETTEL,
  _$phoneCardProvidersResponseTelephoneProviderEnum_VIETNAMOBILE,
  _$phoneCardProvidersResponseTelephoneProviderEnum_GMOBILE,
]);

Serializer<PhoneCardProvidersResponseTelephoneProviderEnum>
    _$phoneCardProvidersResponseTelephoneProviderEnumSerializer =
    _$PhoneCardProvidersResponseTelephoneProviderEnumSerializer();

class _$PhoneCardProvidersResponseTelephoneProviderEnumSerializer
    implements
        PrimitiveSerializer<PhoneCardProvidersResponseTelephoneProviderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };

  @override
  final Iterable<Type> types = const <Type>[
    PhoneCardProvidersResponseTelephoneProviderEnum
  ];
  @override
  final String wireName = 'PhoneCardProvidersResponseTelephoneProviderEnum';

  @override
  Object serialize(Serializers serializers,
          PhoneCardProvidersResponseTelephoneProviderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PhoneCardProvidersResponseTelephoneProviderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PhoneCardProvidersResponseTelephoneProviderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PhoneCardProvidersResponse extends PhoneCardProvidersResponse {
  @override
  final PhoneCardProvidersResponseTelephoneProviderEnum? telephoneProvider;
  @override
  final String? providerName;
  @override
  final BuiltList<PhoneCardResponse>? cards;
  @override
  final String? iconUrl;

  factory _$PhoneCardProvidersResponse(
          [void Function(PhoneCardProvidersResponseBuilder)? updates]) =>
      (PhoneCardProvidersResponseBuilder()..update(updates))._build();

  _$PhoneCardProvidersResponse._(
      {this.telephoneProvider, this.providerName, this.cards, this.iconUrl})
      : super._();
  @override
  PhoneCardProvidersResponse rebuild(
          void Function(PhoneCardProvidersResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PhoneCardProvidersResponseBuilder toBuilder() =>
      PhoneCardProvidersResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PhoneCardProvidersResponse &&
        telephoneProvider == other.telephoneProvider &&
        providerName == other.providerName &&
        cards == other.cards &&
        iconUrl == other.iconUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, telephoneProvider.hashCode);
    _$hash = $jc(_$hash, providerName.hashCode);
    _$hash = $jc(_$hash, cards.hashCode);
    _$hash = $jc(_$hash, iconUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PhoneCardProvidersResponse')
          ..add('telephoneProvider', telephoneProvider)
          ..add('providerName', providerName)
          ..add('cards', cards)
          ..add('iconUrl', iconUrl))
        .toString();
  }
}

class PhoneCardProvidersResponseBuilder
    implements
        Builder<PhoneCardProvidersResponse, PhoneCardProvidersResponseBuilder> {
  _$PhoneCardProvidersResponse? _$v;

  PhoneCardProvidersResponseTelephoneProviderEnum? _telephoneProvider;
  PhoneCardProvidersResponseTelephoneProviderEnum? get telephoneProvider =>
      _$this._telephoneProvider;
  set telephoneProvider(
          PhoneCardProvidersResponseTelephoneProviderEnum? telephoneProvider) =>
      _$this._telephoneProvider = telephoneProvider;

  String? _providerName;
  String? get providerName => _$this._providerName;
  set providerName(String? providerName) => _$this._providerName = providerName;

  ListBuilder<PhoneCardResponse>? _cards;
  ListBuilder<PhoneCardResponse> get cards =>
      _$this._cards ??= ListBuilder<PhoneCardResponse>();
  set cards(ListBuilder<PhoneCardResponse>? cards) => _$this._cards = cards;

  String? _iconUrl;
  String? get iconUrl => _$this._iconUrl;
  set iconUrl(String? iconUrl) => _$this._iconUrl = iconUrl;

  PhoneCardProvidersResponseBuilder() {
    PhoneCardProvidersResponse._defaults(this);
  }

  PhoneCardProvidersResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _telephoneProvider = $v.telephoneProvider;
      _providerName = $v.providerName;
      _cards = $v.cards?.toBuilder();
      _iconUrl = $v.iconUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PhoneCardProvidersResponse other) {
    _$v = other as _$PhoneCardProvidersResponse;
  }

  @override
  void update(void Function(PhoneCardProvidersResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PhoneCardProvidersResponse build() => _build();

  _$PhoneCardProvidersResponse _build() {
    _$PhoneCardProvidersResponse _$result;
    try {
      _$result = _$v ??
          _$PhoneCardProvidersResponse._(
            telephoneProvider: telephoneProvider,
            providerName: providerName,
            cards: _cards?.build(),
            iconUrl: iconUrl,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'cards';
        _cards?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PhoneCardProvidersResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
