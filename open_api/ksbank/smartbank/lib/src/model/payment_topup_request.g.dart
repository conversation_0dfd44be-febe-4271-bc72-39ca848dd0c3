// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_topup_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PaymentTopupRequestTopupTypeEnum
    _$paymentTopupRequestTopupTypeEnum_TOPUP_PHONE =
    const PaymentTopupRequestTopupTypeEnum._('TOPUP_PHONE');
const PaymentTopupRequestTopupTypeEnum
    _$paymentTopupRequestTopupTypeEnum_TOPUP_DATA =
    const PaymentTopupRequestTopupTypeEnum._('TOPUP_DATA');
const PaymentTopupRequestTopupTypeEnum
    _$paymentTopupRequestTopupTypeEnum_PINCODE_PHONE =
    const PaymentTopupRequestTopupTypeEnum._('PINCODE_PHONE');
const PaymentTopupRequestTopupTypeEnum
    _$paymentTopupRequestTopupTypeEnum_PINCODE_DATA =
    const PaymentTopupRequestTopupTypeEnum._('PINCODE_DATA');
const PaymentTopupRequestTopupTypeEnum
    _$paymentTopupRequestTopupTypeEnum_PINCODE_GAME =
    const PaymentTopupRequestTopupTypeEnum._('PINCODE_GAME');
const PaymentTopupRequestTopupTypeEnum
    _$paymentTopupRequestTopupTypeEnum_PAY_BILL =
    const PaymentTopupRequestTopupTypeEnum._('PAY_BILL');

PaymentTopupRequestTopupTypeEnum _$paymentTopupRequestTopupTypeEnumValueOf(
    String name) {
  switch (name) {
    case 'TOPUP_PHONE':
      return _$paymentTopupRequestTopupTypeEnum_TOPUP_PHONE;
    case 'TOPUP_DATA':
      return _$paymentTopupRequestTopupTypeEnum_TOPUP_DATA;
    case 'PINCODE_PHONE':
      return _$paymentTopupRequestTopupTypeEnum_PINCODE_PHONE;
    case 'PINCODE_DATA':
      return _$paymentTopupRequestTopupTypeEnum_PINCODE_DATA;
    case 'PINCODE_GAME':
      return _$paymentTopupRequestTopupTypeEnum_PINCODE_GAME;
    case 'PAY_BILL':
      return _$paymentTopupRequestTopupTypeEnum_PAY_BILL;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PaymentTopupRequestTopupTypeEnum>
    _$paymentTopupRequestTopupTypeEnumValues = BuiltSet<
        PaymentTopupRequestTopupTypeEnum>(const <PaymentTopupRequestTopupTypeEnum>[
  _$paymentTopupRequestTopupTypeEnum_TOPUP_PHONE,
  _$paymentTopupRequestTopupTypeEnum_TOPUP_DATA,
  _$paymentTopupRequestTopupTypeEnum_PINCODE_PHONE,
  _$paymentTopupRequestTopupTypeEnum_PINCODE_DATA,
  _$paymentTopupRequestTopupTypeEnum_PINCODE_GAME,
  _$paymentTopupRequestTopupTypeEnum_PAY_BILL,
]);

Serializer<PaymentTopupRequestTopupTypeEnum>
    _$paymentTopupRequestTopupTypeEnumSerializer =
    _$PaymentTopupRequestTopupTypeEnumSerializer();

class _$PaymentTopupRequestTopupTypeEnumSerializer
    implements PrimitiveSerializer<PaymentTopupRequestTopupTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'TOPUP_PHONE': 'TOPUP_PHONE',
    'TOPUP_DATA': 'TOPUP_DATA',
    'PINCODE_PHONE': 'PINCODE_PHONE',
    'PINCODE_DATA': 'PINCODE_DATA',
    'PINCODE_GAME': 'PINCODE_GAME',
    'PAY_BILL': 'PAY_BILL',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'TOPUP_PHONE': 'TOPUP_PHONE',
    'TOPUP_DATA': 'TOPUP_DATA',
    'PINCODE_PHONE': 'PINCODE_PHONE',
    'PINCODE_DATA': 'PINCODE_DATA',
    'PINCODE_GAME': 'PINCODE_GAME',
    'PAY_BILL': 'PAY_BILL',
  };

  @override
  final Iterable<Type> types = const <Type>[PaymentTopupRequestTopupTypeEnum];
  @override
  final String wireName = 'PaymentTopupRequestTopupTypeEnum';

  @override
  Object serialize(
          Serializers serializers, PaymentTopupRequestTopupTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PaymentTopupRequestTopupTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PaymentTopupRequestTopupTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PaymentTopupRequest extends PaymentTopupRequest {
  @override
  final String? transactionNo;
  @override
  final String? phoneNo;
  @override
  final String? accountNo;
  @override
  final num? amount;
  @override
  final num? cardValue;
  @override
  final String? productCode;
  @override
  final PaymentTopupRequestTopupTypeEnum? topupType;
  @override
  final String? pinCodeName;
  @override
  final String? pinCodeDesc;
  @override
  final String? supplierName;
  @override
  final String? supplierUrl;
  @override
  final VerifySoftOtpRequest? softOtp;

  factory _$PaymentTopupRequest(
          [void Function(PaymentTopupRequestBuilder)? updates]) =>
      (PaymentTopupRequestBuilder()..update(updates))._build();

  _$PaymentTopupRequest._(
      {this.transactionNo,
      this.phoneNo,
      this.accountNo,
      this.amount,
      this.cardValue,
      this.productCode,
      this.topupType,
      this.pinCodeName,
      this.pinCodeDesc,
      this.supplierName,
      this.supplierUrl,
      this.softOtp})
      : super._();
  @override
  PaymentTopupRequest rebuild(
          void Function(PaymentTopupRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentTopupRequestBuilder toBuilder() =>
      PaymentTopupRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentTopupRequest &&
        transactionNo == other.transactionNo &&
        phoneNo == other.phoneNo &&
        accountNo == other.accountNo &&
        amount == other.amount &&
        cardValue == other.cardValue &&
        productCode == other.productCode &&
        topupType == other.topupType &&
        pinCodeName == other.pinCodeName &&
        pinCodeDesc == other.pinCodeDesc &&
        supplierName == other.supplierName &&
        supplierUrl == other.supplierUrl &&
        softOtp == other.softOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, phoneNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, topupType.hashCode);
    _$hash = $jc(_$hash, pinCodeName.hashCode);
    _$hash = $jc(_$hash, pinCodeDesc.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, supplierUrl.hashCode);
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentTopupRequest')
          ..add('transactionNo', transactionNo)
          ..add('phoneNo', phoneNo)
          ..add('accountNo', accountNo)
          ..add('amount', amount)
          ..add('cardValue', cardValue)
          ..add('productCode', productCode)
          ..add('topupType', topupType)
          ..add('pinCodeName', pinCodeName)
          ..add('pinCodeDesc', pinCodeDesc)
          ..add('supplierName', supplierName)
          ..add('supplierUrl', supplierUrl)
          ..add('softOtp', softOtp))
        .toString();
  }
}

class PaymentTopupRequestBuilder
    implements Builder<PaymentTopupRequest, PaymentTopupRequestBuilder> {
  _$PaymentTopupRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _phoneNo;
  String? get phoneNo => _$this._phoneNo;
  set phoneNo(String? phoneNo) => _$this._phoneNo = phoneNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  PaymentTopupRequestTopupTypeEnum? _topupType;
  PaymentTopupRequestTopupTypeEnum? get topupType => _$this._topupType;
  set topupType(PaymentTopupRequestTopupTypeEnum? topupType) =>
      _$this._topupType = topupType;

  String? _pinCodeName;
  String? get pinCodeName => _$this._pinCodeName;
  set pinCodeName(String? pinCodeName) => _$this._pinCodeName = pinCodeName;

  String? _pinCodeDesc;
  String? get pinCodeDesc => _$this._pinCodeDesc;
  set pinCodeDesc(String? pinCodeDesc) => _$this._pinCodeDesc = pinCodeDesc;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _supplierUrl;
  String? get supplierUrl => _$this._supplierUrl;
  set supplierUrl(String? supplierUrl) => _$this._supplierUrl = supplierUrl;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  PaymentTopupRequestBuilder() {
    PaymentTopupRequest._defaults(this);
  }

  PaymentTopupRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _phoneNo = $v.phoneNo;
      _accountNo = $v.accountNo;
      _amount = $v.amount;
      _cardValue = $v.cardValue;
      _productCode = $v.productCode;
      _topupType = $v.topupType;
      _pinCodeName = $v.pinCodeName;
      _pinCodeDesc = $v.pinCodeDesc;
      _supplierName = $v.supplierName;
      _supplierUrl = $v.supplierUrl;
      _softOtp = $v.softOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentTopupRequest other) {
    _$v = other as _$PaymentTopupRequest;
  }

  @override
  void update(void Function(PaymentTopupRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentTopupRequest build() => _build();

  _$PaymentTopupRequest _build() {
    _$PaymentTopupRequest _$result;
    try {
      _$result = _$v ??
          _$PaymentTopupRequest._(
            transactionNo: transactionNo,
            phoneNo: phoneNo,
            accountNo: accountNo,
            amount: amount,
            cardValue: cardValue,
            productCode: productCode,
            topupType: topupType,
            pinCodeName: pinCodeName,
            pinCodeDesc: pinCodeDesc,
            supplierName: supplierName,
            supplierUrl: supplierUrl,
            softOtp: _softOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PaymentTopupRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
