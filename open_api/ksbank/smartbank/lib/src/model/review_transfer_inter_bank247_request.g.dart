// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_inter_bank247_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferInterBank247Request
    extends ReviewTransferInterBank247Request {
  @override
  final int? transactionGroup;
  @override
  final String? channelCode;
  @override
  final String? cifNo;
  @override
  final String? description;
  @override
  final String? accountNoFrom;
  @override
  final String? accountNoTo;
  @override
  final int? isAccount;
  @override
  final String? targetBankCode;
  @override
  final num? amount;

  factory _$ReviewTransferInterBank247Request(
          [void Function(ReviewTransferInterBank247RequestBuilder)? updates]) =>
      (ReviewTransferInterBank247RequestBuilder()..update(updates))._build();

  _$ReviewTransferInterBank247Request._(
      {this.transactionGroup,
      this.channelCode,
      this.cifNo,
      this.description,
      this.accountNoFrom,
      this.accountNoTo,
      this.isAccount,
      this.targetBankCode,
      this.amount})
      : super._();
  @override
  ReviewTransferInterBank247Request rebuild(
          void Function(ReviewTransferInterBank247RequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferInterBank247RequestBuilder toBuilder() =>
      ReviewTransferInterBank247RequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferInterBank247Request &&
        transactionGroup == other.transactionGroup &&
        channelCode == other.channelCode &&
        cifNo == other.cifNo &&
        description == other.description &&
        accountNoFrom == other.accountNoFrom &&
        accountNoTo == other.accountNoTo &&
        isAccount == other.isAccount &&
        targetBankCode == other.targetBankCode &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, channelCode.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewTransferInterBank247Request')
          ..add('transactionGroup', transactionGroup)
          ..add('channelCode', channelCode)
          ..add('cifNo', cifNo)
          ..add('description', description)
          ..add('accountNoFrom', accountNoFrom)
          ..add('accountNoTo', accountNoTo)
          ..add('isAccount', isAccount)
          ..add('targetBankCode', targetBankCode)
          ..add('amount', amount))
        .toString();
  }
}

class ReviewTransferInterBank247RequestBuilder
    implements
        Builder<ReviewTransferInterBank247Request,
            ReviewTransferInterBank247RequestBuilder> {
  _$ReviewTransferInterBank247Request? _$v;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _channelCode;
  String? get channelCode => _$this._channelCode;
  set channelCode(String? channelCode) => _$this._channelCode = channelCode;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  ReviewTransferInterBank247RequestBuilder() {
    ReviewTransferInterBank247Request._defaults(this);
  }

  ReviewTransferInterBank247RequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionGroup = $v.transactionGroup;
      _channelCode = $v.channelCode;
      _cifNo = $v.cifNo;
      _description = $v.description;
      _accountNoFrom = $v.accountNoFrom;
      _accountNoTo = $v.accountNoTo;
      _isAccount = $v.isAccount;
      _targetBankCode = $v.targetBankCode;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferInterBank247Request other) {
    _$v = other as _$ReviewTransferInterBank247Request;
  }

  @override
  void update(
      void Function(ReviewTransferInterBank247RequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferInterBank247Request build() => _build();

  _$ReviewTransferInterBank247Request _build() {
    final _$result = _$v ??
        _$ReviewTransferInterBank247Request._(
          transactionGroup: transactionGroup,
          channelCode: channelCode,
          cifNo: cifNo,
          description: description,
          accountNoFrom: accountNoFrom,
          accountNoTo: accountNoTo,
          isAccount: isAccount,
          targetBankCode: targetBankCode,
          amount: amount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
