// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_payment_invoice_lotus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewPaymentInvoiceLotusResponse
    extends ReviewPaymentInvoiceLotusResponse {
  @override
  final String? transactionNumber;

  factory _$ReviewPaymentInvoiceLotusResponse(
          [void Function(ReviewPaymentInvoiceLotusResponseBuilder)? updates]) =>
      (ReviewPaymentInvoiceLotusResponseBuilder()..update(updates))._build();

  _$ReviewPaymentInvoiceLotusResponse._({this.transactionNumber}) : super._();
  @override
  ReviewPaymentInvoiceLotusResponse rebuild(
          void Function(ReviewPaymentInvoiceLotusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewPaymentInvoiceLotusResponseBuilder toBuilder() =>
      ReviewPaymentInvoiceLotusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewPaymentInvoiceLotusResponse &&
        transactionNumber == other.transactionNumber;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewPaymentInvoiceLotusResponse')
          ..add('transactionNumber', transactionNumber))
        .toString();
  }
}

class ReviewPaymentInvoiceLotusResponseBuilder
    implements
        Builder<ReviewPaymentInvoiceLotusResponse,
            ReviewPaymentInvoiceLotusResponseBuilder> {
  _$ReviewPaymentInvoiceLotusResponse? _$v;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  ReviewPaymentInvoiceLotusResponseBuilder() {
    ReviewPaymentInvoiceLotusResponse._defaults(this);
  }

  ReviewPaymentInvoiceLotusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNumber = $v.transactionNumber;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewPaymentInvoiceLotusResponse other) {
    _$v = other as _$ReviewPaymentInvoiceLotusResponse;
  }

  @override
  void update(
      void Function(ReviewPaymentInvoiceLotusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewPaymentInvoiceLotusResponse build() => _build();

  _$ReviewPaymentInvoiceLotusResponse _build() {
    final _$result = _$v ??
        _$ReviewPaymentInvoiceLotusResponse._(
          transactionNumber: transactionNumber,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
