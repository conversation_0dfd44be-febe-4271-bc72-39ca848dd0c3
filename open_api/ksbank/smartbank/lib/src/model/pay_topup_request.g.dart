// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pay_topup_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PayTopupRequestProviderEnum _$payTopupRequestProviderEnum_MOBIFONE =
    const PayTopupRequestProviderEnum._('MOBIFONE');
const PayTopupRequestProviderEnum _$payTopupRequestProviderEnum_VINAPHONE =
    const PayTopupRequestProviderEnum._('VINAPHONE');
const PayTopupRequestProviderEnum _$payTopupRequestProviderEnum_VIETTEL =
    const PayTopupRequestProviderEnum._('VIETTEL');
const PayTopupRequestProviderEnum _$payTopupRequestProviderEnum_VIETNAMOBILE =
    const PayTopupRequestProviderEnum._('VIETNAMOBILE');
const PayTopupRequestProviderEnum _$payTopupRequestProviderEnum_GMOBILE =
    const PayTopupRequestProviderEnum._('GMOBILE');

PayTopupRequestProviderEnum _$payTopupRequestProviderEnumValueOf(String name) {
  switch (name) {
    case 'MOBIFONE':
      return _$payTopupRequestProviderEnum_MOBIFONE;
    case 'VINAPHONE':
      return _$payTopupRequestProviderEnum_VINAPHONE;
    case 'VIETTEL':
      return _$payTopupRequestProviderEnum_VIETTEL;
    case 'VIETNAMOBILE':
      return _$payTopupRequestProviderEnum_VIETNAMOBILE;
    case 'GMOBILE':
      return _$payTopupRequestProviderEnum_GMOBILE;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PayTopupRequestProviderEnum>
    _$payTopupRequestProviderEnumValues =
    BuiltSet<PayTopupRequestProviderEnum>(const <PayTopupRequestProviderEnum>[
  _$payTopupRequestProviderEnum_MOBIFONE,
  _$payTopupRequestProviderEnum_VINAPHONE,
  _$payTopupRequestProviderEnum_VIETTEL,
  _$payTopupRequestProviderEnum_VIETNAMOBILE,
  _$payTopupRequestProviderEnum_GMOBILE,
]);

const PayTopupRequestTopupTypeEnum _$payTopupRequestTopupTypeEnum_PREPAID =
    const PayTopupRequestTopupTypeEnum._('PREPAID');
const PayTopupRequestTopupTypeEnum _$payTopupRequestTopupTypeEnum_POST_PAYMENT =
    const PayTopupRequestTopupTypeEnum._('POST_PAYMENT');

PayTopupRequestTopupTypeEnum _$payTopupRequestTopupTypeEnumValueOf(
    String name) {
  switch (name) {
    case 'PREPAID':
      return _$payTopupRequestTopupTypeEnum_PREPAID;
    case 'POST_PAYMENT':
      return _$payTopupRequestTopupTypeEnum_POST_PAYMENT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PayTopupRequestTopupTypeEnum>
    _$payTopupRequestTopupTypeEnumValues =
    BuiltSet<PayTopupRequestTopupTypeEnum>(const <PayTopupRequestTopupTypeEnum>[
  _$payTopupRequestTopupTypeEnum_PREPAID,
  _$payTopupRequestTopupTypeEnum_POST_PAYMENT,
]);

Serializer<PayTopupRequestProviderEnum>
    _$payTopupRequestProviderEnumSerializer =
    _$PayTopupRequestProviderEnumSerializer();
Serializer<PayTopupRequestTopupTypeEnum>
    _$payTopupRequestTopupTypeEnumSerializer =
    _$PayTopupRequestTopupTypeEnumSerializer();

class _$PayTopupRequestProviderEnumSerializer
    implements PrimitiveSerializer<PayTopupRequestProviderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };

  @override
  final Iterable<Type> types = const <Type>[PayTopupRequestProviderEnum];
  @override
  final String wireName = 'PayTopupRequestProviderEnum';

  @override
  Object serialize(Serializers serializers, PayTopupRequestProviderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PayTopupRequestProviderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PayTopupRequestProviderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PayTopupRequestTopupTypeEnumSerializer
    implements PrimitiveSerializer<PayTopupRequestTopupTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PREPAID': 'PREPAID',
    'POST_PAYMENT': 'POST_PAYMENT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PREPAID': 'PREPAID',
    'POST_PAYMENT': 'POST_PAYMENT',
  };

  @override
  final Iterable<Type> types = const <Type>[PayTopupRequestTopupTypeEnum];
  @override
  final String wireName = 'PayTopupRequestTopupTypeEnum';

  @override
  Object serialize(Serializers serializers, PayTopupRequestTopupTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PayTopupRequestTopupTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PayTopupRequestTopupTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PayTopupRequest extends PayTopupRequest {
  @override
  final String? transactionNo;
  @override
  final String? phoneNo;
  @override
  final PayTopupRequestProviderEnum? provider;
  @override
  final num? amount;
  @override
  final num? cardValue;
  @override
  final String? cardValueCode;
  @override
  final String? cardValueName;
  @override
  final String? bankCif;
  @override
  final String? accountNo;
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final PayTopupRequestTopupTypeEnum? topupType;

  factory _$PayTopupRequest([void Function(PayTopupRequestBuilder)? updates]) =>
      (PayTopupRequestBuilder()..update(updates))._build();

  _$PayTopupRequest._(
      {this.transactionNo,
      this.phoneNo,
      this.provider,
      this.amount,
      this.cardValue,
      this.cardValueCode,
      this.cardValueName,
      this.bankCif,
      this.accountNo,
      this.softOtp,
      this.topupType})
      : super._();
  @override
  PayTopupRequest rebuild(void Function(PayTopupRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PayTopupRequestBuilder toBuilder() => PayTopupRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PayTopupRequest &&
        transactionNo == other.transactionNo &&
        phoneNo == other.phoneNo &&
        provider == other.provider &&
        amount == other.amount &&
        cardValue == other.cardValue &&
        cardValueCode == other.cardValueCode &&
        cardValueName == other.cardValueName &&
        bankCif == other.bankCif &&
        accountNo == other.accountNo &&
        softOtp == other.softOtp &&
        topupType == other.topupType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, phoneNo.hashCode);
    _$hash = $jc(_$hash, provider.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jc(_$hash, cardValueCode.hashCode);
    _$hash = $jc(_$hash, cardValueName.hashCode);
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, topupType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PayTopupRequest')
          ..add('transactionNo', transactionNo)
          ..add('phoneNo', phoneNo)
          ..add('provider', provider)
          ..add('amount', amount)
          ..add('cardValue', cardValue)
          ..add('cardValueCode', cardValueCode)
          ..add('cardValueName', cardValueName)
          ..add('bankCif', bankCif)
          ..add('accountNo', accountNo)
          ..add('softOtp', softOtp)
          ..add('topupType', topupType))
        .toString();
  }
}

class PayTopupRequestBuilder
    implements Builder<PayTopupRequest, PayTopupRequestBuilder> {
  _$PayTopupRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _phoneNo;
  String? get phoneNo => _$this._phoneNo;
  set phoneNo(String? phoneNo) => _$this._phoneNo = phoneNo;

  PayTopupRequestProviderEnum? _provider;
  PayTopupRequestProviderEnum? get provider => _$this._provider;
  set provider(PayTopupRequestProviderEnum? provider) =>
      _$this._provider = provider;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  String? _cardValueCode;
  String? get cardValueCode => _$this._cardValueCode;
  set cardValueCode(String? cardValueCode) =>
      _$this._cardValueCode = cardValueCode;

  String? _cardValueName;
  String? get cardValueName => _$this._cardValueName;
  set cardValueName(String? cardValueName) =>
      _$this._cardValueName = cardValueName;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  PayTopupRequestTopupTypeEnum? _topupType;
  PayTopupRequestTopupTypeEnum? get topupType => _$this._topupType;
  set topupType(PayTopupRequestTopupTypeEnum? topupType) =>
      _$this._topupType = topupType;

  PayTopupRequestBuilder() {
    PayTopupRequest._defaults(this);
  }

  PayTopupRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _phoneNo = $v.phoneNo;
      _provider = $v.provider;
      _amount = $v.amount;
      _cardValue = $v.cardValue;
      _cardValueCode = $v.cardValueCode;
      _cardValueName = $v.cardValueName;
      _bankCif = $v.bankCif;
      _accountNo = $v.accountNo;
      _softOtp = $v.softOtp?.toBuilder();
      _topupType = $v.topupType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PayTopupRequest other) {
    _$v = other as _$PayTopupRequest;
  }

  @override
  void update(void Function(PayTopupRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PayTopupRequest build() => _build();

  _$PayTopupRequest _build() {
    _$PayTopupRequest _$result;
    try {
      _$result = _$v ??
          _$PayTopupRequest._(
            transactionNo: transactionNo,
            phoneNo: phoneNo,
            provider: provider,
            amount: amount,
            cardValue: cardValue,
            cardValueCode: cardValueCode,
            cardValueName: cardValueName,
            bankCif: bankCif,
            accountNo: accountNo,
            softOtp: _softOtp?.build(),
            topupType: topupType,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PayTopupRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
