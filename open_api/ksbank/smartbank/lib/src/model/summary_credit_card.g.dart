// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'summary_credit_card.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SummaryCreditCard extends SummaryCreditCard {
  @override
  final String? title;
  @override
  final int? totalCount;
  @override
  final int? totalCreditLimit;
  @override
  final int? totalOutstandingBalance;

  factory _$SummaryCreditCard(
          [void Function(SummaryCreditCardBuilder)? updates]) =>
      (SummaryCreditCardBuilder()..update(updates))._build();

  _$SummaryCreditCard._(
      {this.title,
      this.totalCount,
      this.totalCreditLimit,
      this.totalOutstandingBalance})
      : super._();
  @override
  SummaryCreditCard rebuild(void Function(SummaryCreditCardBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SummaryCreditCardBuilder toBuilder() =>
      SummaryCreditCardBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SummaryCreditCard &&
        title == other.title &&
        totalCount == other.totalCount &&
        totalCreditLimit == other.totalCreditLimit &&
        totalOutstandingBalance == other.totalOutstandingBalance;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, totalCount.hashCode);
    _$hash = $jc(_$hash, totalCreditLimit.hashCode);
    _$hash = $jc(_$hash, totalOutstandingBalance.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SummaryCreditCard')
          ..add('title', title)
          ..add('totalCount', totalCount)
          ..add('totalCreditLimit', totalCreditLimit)
          ..add('totalOutstandingBalance', totalOutstandingBalance))
        .toString();
  }
}

class SummaryCreditCardBuilder
    implements Builder<SummaryCreditCard, SummaryCreditCardBuilder> {
  _$SummaryCreditCard? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  int? _totalCount;
  int? get totalCount => _$this._totalCount;
  set totalCount(int? totalCount) => _$this._totalCount = totalCount;

  int? _totalCreditLimit;
  int? get totalCreditLimit => _$this._totalCreditLimit;
  set totalCreditLimit(int? totalCreditLimit) =>
      _$this._totalCreditLimit = totalCreditLimit;

  int? _totalOutstandingBalance;
  int? get totalOutstandingBalance => _$this._totalOutstandingBalance;
  set totalOutstandingBalance(int? totalOutstandingBalance) =>
      _$this._totalOutstandingBalance = totalOutstandingBalance;

  SummaryCreditCardBuilder() {
    SummaryCreditCard._defaults(this);
  }

  SummaryCreditCardBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _totalCount = $v.totalCount;
      _totalCreditLimit = $v.totalCreditLimit;
      _totalOutstandingBalance = $v.totalOutstandingBalance;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SummaryCreditCard other) {
    _$v = other as _$SummaryCreditCard;
  }

  @override
  void update(void Function(SummaryCreditCardBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SummaryCreditCard build() => _build();

  _$SummaryCreditCard _build() {
    final _$result = _$v ??
        _$SummaryCreditCard._(
          title: title,
          totalCount: totalCount,
          totalCreditLimit: totalCreditLimit,
          totalOutstandingBalance: totalOutstandingBalance,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
