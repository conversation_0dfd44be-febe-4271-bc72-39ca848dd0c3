// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trans_inquiry_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransInquiryDto extends TransInquiryDto {
  @override
  final String? id;
  @override
  final DateTime? transactionDate;
  @override
  final num? amount;
  @override
  final String? ccy;
  @override
  final num? inquiryAmount;
  @override
  final String? content;
  @override
  final String? groupName;
  @override
  final TransInquiryStepDto? status;

  factory _$TransInquiryDto([void Function(TransInquiryDtoBuilder)? updates]) =>
      (TransInquiryDtoBuilder()..update(updates))._build();

  _$TransInquiryDto._(
      {this.id,
      this.transactionDate,
      this.amount,
      this.ccy,
      this.inquiryAmount,
      this.content,
      this.groupName,
      this.status})
      : super._();
  @override
  TransInquiryDto rebuild(void Function(TransInquiryDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransInquiryDtoBuilder toBuilder() => TransInquiryDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransInquiryDto &&
        id == other.id &&
        transactionDate == other.transactionDate &&
        amount == other.amount &&
        ccy == other.ccy &&
        inquiryAmount == other.inquiryAmount &&
        content == other.content &&
        groupName == other.groupName &&
        status == other.status;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, ccy.hashCode);
    _$hash = $jc(_$hash, inquiryAmount.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, groupName.hashCode);
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransInquiryDto')
          ..add('id', id)
          ..add('transactionDate', transactionDate)
          ..add('amount', amount)
          ..add('ccy', ccy)
          ..add('inquiryAmount', inquiryAmount)
          ..add('content', content)
          ..add('groupName', groupName)
          ..add('status', status))
        .toString();
  }
}

class TransInquiryDtoBuilder
    implements Builder<TransInquiryDto, TransInquiryDtoBuilder> {
  _$TransInquiryDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _ccy;
  String? get ccy => _$this._ccy;
  set ccy(String? ccy) => _$this._ccy = ccy;

  num? _inquiryAmount;
  num? get inquiryAmount => _$this._inquiryAmount;
  set inquiryAmount(num? inquiryAmount) =>
      _$this._inquiryAmount = inquiryAmount;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  String? _groupName;
  String? get groupName => _$this._groupName;
  set groupName(String? groupName) => _$this._groupName = groupName;

  TransInquiryStepDtoBuilder? _status;
  TransInquiryStepDtoBuilder get status =>
      _$this._status ??= TransInquiryStepDtoBuilder();
  set status(TransInquiryStepDtoBuilder? status) => _$this._status = status;

  TransInquiryDtoBuilder() {
    TransInquiryDto._defaults(this);
  }

  TransInquiryDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _transactionDate = $v.transactionDate;
      _amount = $v.amount;
      _ccy = $v.ccy;
      _inquiryAmount = $v.inquiryAmount;
      _content = $v.content;
      _groupName = $v.groupName;
      _status = $v.status?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransInquiryDto other) {
    _$v = other as _$TransInquiryDto;
  }

  @override
  void update(void Function(TransInquiryDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransInquiryDto build() => _build();

  _$TransInquiryDto _build() {
    _$TransInquiryDto _$result;
    try {
      _$result = _$v ??
          _$TransInquiryDto._(
            id: id,
            transactionDate: transactionDate,
            amount: amount,
            ccy: ccy,
            inquiryAmount: inquiryAmount,
            content: content,
            groupName: groupName,
            status: _status?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'status';
        _status?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransInquiryDto', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
