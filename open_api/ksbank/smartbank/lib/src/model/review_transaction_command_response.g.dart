// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transaction_command_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransactionCommandResponse
    extends ReviewTransactionCommandResponse {
  @override
  final num? amount;
  @override
  final num? numberOfTransaction;

  factory _$ReviewTransactionCommandResponse(
          [void Function(ReviewTransactionCommandResponseBuilder)? updates]) =>
      (ReviewTransactionCommandResponseBuilder()..update(updates))._build();

  _$ReviewTransactionCommandResponse._({this.amount, this.numberOfTransaction})
      : super._();
  @override
  ReviewTransactionCommandResponse rebuild(
          void Function(ReviewTransactionCommandResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransactionCommandResponseBuilder toBuilder() =>
      ReviewTransactionCommandResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransactionCommandResponse &&
        amount == other.amount &&
        numberOfTransaction == other.numberOfTransaction;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, numberOfTransaction.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewTransactionCommandResponse')
          ..add('amount', amount)
          ..add('numberOfTransaction', numberOfTransaction))
        .toString();
  }
}

class ReviewTransactionCommandResponseBuilder
    implements
        Builder<ReviewTransactionCommandResponse,
            ReviewTransactionCommandResponseBuilder> {
  _$ReviewTransactionCommandResponse? _$v;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _numberOfTransaction;
  num? get numberOfTransaction => _$this._numberOfTransaction;
  set numberOfTransaction(num? numberOfTransaction) =>
      _$this._numberOfTransaction = numberOfTransaction;

  ReviewTransactionCommandResponseBuilder() {
    ReviewTransactionCommandResponse._defaults(this);
  }

  ReviewTransactionCommandResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _amount = $v.amount;
      _numberOfTransaction = $v.numberOfTransaction;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransactionCommandResponse other) {
    _$v = other as _$ReviewTransactionCommandResponse;
  }

  @override
  void update(void Function(ReviewTransactionCommandResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransactionCommandResponse build() => _build();

  _$ReviewTransactionCommandResponse _build() {
    final _$result = _$v ??
        _$ReviewTransactionCommandResponse._(
          amount: amount,
          numberOfTransaction: numberOfTransaction,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
