// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_auto_payment_card_status_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
    _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_NO_PAYMENT =
    const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum._(
        'NO_PAYMENT');
const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
    _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_MINIMUM_PAYMENT =
    const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum._(
        'MINIMUM_PAYMENT');
const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
    _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_FULL_PAYMENT =
    const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum._(
        'FULL_PAYMENT');
const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
    _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_PARTIAL_PAYMENT =
    const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum._(
        'PARTIAL_PAYMENT');

UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
    _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumValueOf(
        String name) {
  switch (name) {
    case 'NO_PAYMENT':
      return _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_NO_PAYMENT;
    case 'MINIMUM_PAYMENT':
      return _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_MINIMUM_PAYMENT;
    case 'FULL_PAYMENT':
      return _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_FULL_PAYMENT;
    case 'PARTIAL_PAYMENT':
      return _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_PARTIAL_PAYMENT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum>
    _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumValues =
    BuiltSet<
        UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum>(const <UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum>[
  _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_NO_PAYMENT,
  _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_MINIMUM_PAYMENT,
  _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_FULL_PAYMENT,
  _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_PARTIAL_PAYMENT,
]);

Serializer<UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum>
    _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumSerializer =
    _$UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumSerializer();

class _$UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumSerializer
    implements
        PrimitiveSerializer<
            UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'NO_PAYMENT': 'NO_PAYMENT',
    'MINIMUM_PAYMENT': 'MINIMUM_PAYMENT',
    'FULL_PAYMENT': 'FULL_PAYMENT',
    'PARTIAL_PAYMENT': 'PARTIAL_PAYMENT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'NO_PAYMENT': 'NO_PAYMENT',
    'MINIMUM_PAYMENT': 'MINIMUM_PAYMENT',
    'FULL_PAYMENT': 'FULL_PAYMENT',
    'PARTIAL_PAYMENT': 'PARTIAL_PAYMENT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
  ];
  @override
  final String wireName =
      'UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum';

  @override
  Object serialize(Serializers serializers,
          UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$UpdateAutoPaymentCardStatusRequest
    extends UpdateAutoPaymentCardStatusRequest {
  @override
  final String? cardNo;
  @override
  final String? cardId;
  @override
  final UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum?
      autoPayCreditCardStatus;
  @override
  final int? paymentRate;
  @override
  final String? accountNo;

  factory _$UpdateAutoPaymentCardStatusRequest(
          [void Function(UpdateAutoPaymentCardStatusRequestBuilder)?
              updates]) =>
      (UpdateAutoPaymentCardStatusRequestBuilder()..update(updates))._build();

  _$UpdateAutoPaymentCardStatusRequest._(
      {this.cardNo,
      this.cardId,
      this.autoPayCreditCardStatus,
      this.paymentRate,
      this.accountNo})
      : super._();
  @override
  UpdateAutoPaymentCardStatusRequest rebuild(
          void Function(UpdateAutoPaymentCardStatusRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateAutoPaymentCardStatusRequestBuilder toBuilder() =>
      UpdateAutoPaymentCardStatusRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateAutoPaymentCardStatusRequest &&
        cardNo == other.cardNo &&
        cardId == other.cardId &&
        autoPayCreditCardStatus == other.autoPayCreditCardStatus &&
        paymentRate == other.paymentRate &&
        accountNo == other.accountNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardNo.hashCode);
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, autoPayCreditCardStatus.hashCode);
    _$hash = $jc(_$hash, paymentRate.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateAutoPaymentCardStatusRequest')
          ..add('cardNo', cardNo)
          ..add('cardId', cardId)
          ..add('autoPayCreditCardStatus', autoPayCreditCardStatus)
          ..add('paymentRate', paymentRate)
          ..add('accountNo', accountNo))
        .toString();
  }
}

class UpdateAutoPaymentCardStatusRequestBuilder
    implements
        Builder<UpdateAutoPaymentCardStatusRequest,
            UpdateAutoPaymentCardStatusRequestBuilder> {
  _$UpdateAutoPaymentCardStatusRequest? _$v;

  String? _cardNo;
  String? get cardNo => _$this._cardNo;
  set cardNo(String? cardNo) => _$this._cardNo = cardNo;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum?
      _autoPayCreditCardStatus;
  UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum?
      get autoPayCreditCardStatus => _$this._autoPayCreditCardStatus;
  set autoPayCreditCardStatus(
          UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum?
              autoPayCreditCardStatus) =>
      _$this._autoPayCreditCardStatus = autoPayCreditCardStatus;

  int? _paymentRate;
  int? get paymentRate => _$this._paymentRate;
  set paymentRate(int? paymentRate) => _$this._paymentRate = paymentRate;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  UpdateAutoPaymentCardStatusRequestBuilder() {
    UpdateAutoPaymentCardStatusRequest._defaults(this);
  }

  UpdateAutoPaymentCardStatusRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardNo = $v.cardNo;
      _cardId = $v.cardId;
      _autoPayCreditCardStatus = $v.autoPayCreditCardStatus;
      _paymentRate = $v.paymentRate;
      _accountNo = $v.accountNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateAutoPaymentCardStatusRequest other) {
    _$v = other as _$UpdateAutoPaymentCardStatusRequest;
  }

  @override
  void update(
      void Function(UpdateAutoPaymentCardStatusRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateAutoPaymentCardStatusRequest build() => _build();

  _$UpdateAutoPaymentCardStatusRequest _build() {
    final _$result = _$v ??
        _$UpdateAutoPaymentCardStatusRequest._(
          cardNo: cardNo,
          cardId: cardId,
          autoPayCreditCardStatus: autoPayCreditCardStatus,
          paymentRate: paymentRate,
          accountNo: accountNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
