// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_vvip_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenVvipAccountResponse extends OpenVvipAccountResponse {
  @override
  final String? accountNo;
  @override
  final String? accountName;
  @override
  final String? aliasName;
  @override
  final String? createdDate;

  factory _$OpenVvipAccountResponse(
          [void Function(OpenVvipAccountResponseBuilder)? updates]) =>
      (OpenVvipAccountResponseBuilder()..update(updates))._build();

  _$OpenVvipAccountResponse._(
      {this.accountNo, this.accountName, this.aliasName, this.createdDate})
      : super._();
  @override
  OpenVvipAccountResponse rebuild(
          void Function(OpenVvipAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenVvipAccountResponseBuilder toBuilder() =>
      OpenVvipAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenVvipAccountResponse &&
        accountNo == other.accountNo &&
        accountName == other.accountName &&
        aliasName == other.aliasName &&
        createdDate == other.createdDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, createdDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenVvipAccountResponse')
          ..add('accountNo', accountNo)
          ..add('accountName', accountName)
          ..add('aliasName', aliasName)
          ..add('createdDate', createdDate))
        .toString();
  }
}

class OpenVvipAccountResponseBuilder
    implements
        Builder<OpenVvipAccountResponse, OpenVvipAccountResponseBuilder> {
  _$OpenVvipAccountResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  String? _createdDate;
  String? get createdDate => _$this._createdDate;
  set createdDate(String? createdDate) => _$this._createdDate = createdDate;

  OpenVvipAccountResponseBuilder() {
    OpenVvipAccountResponse._defaults(this);
  }

  OpenVvipAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _accountName = $v.accountName;
      _aliasName = $v.aliasName;
      _createdDate = $v.createdDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenVvipAccountResponse other) {
    _$v = other as _$OpenVvipAccountResponse;
  }

  @override
  void update(void Function(OpenVvipAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenVvipAccountResponse build() => _build();

  _$OpenVvipAccountResponse _build() {
    final _$result = _$v ??
        _$OpenVvipAccountResponse._(
          accountNo: accountNo,
          accountName: accountName,
          aliasName: aliasName,
          createdDate: createdDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
