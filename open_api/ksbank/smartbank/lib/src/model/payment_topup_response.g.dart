// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_topup_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentTopupResponse extends PaymentTopupResponse {
  @override
  final String? systemTrace;
  @override
  final String? transactionNo;
  @override
  final num? amount;
  @override
  final String? supplierCode;
  @override
  final String? supplierName;
  @override
  final String? productCode;
  @override
  final String? productName;
  @override
  final DateTime? transactionTime;
  @override
  final String? description;
  @override
  final num? discount;

  factory _$PaymentTopupResponse(
          [void Function(PaymentTopupResponseBuilder)? updates]) =>
      (PaymentTopupResponseBuilder()..update(updates))._build();

  _$PaymentTopupResponse._(
      {this.systemTrace,
      this.transactionNo,
      this.amount,
      this.supplierCode,
      this.supplierName,
      this.productCode,
      this.productName,
      this.transactionTime,
      this.description,
      this.discount})
      : super._();
  @override
  PaymentTopupResponse rebuild(
          void Function(PaymentTopupResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentTopupResponseBuilder toBuilder() =>
      PaymentTopupResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentTopupResponse &&
        systemTrace == other.systemTrace &&
        transactionNo == other.transactionNo &&
        amount == other.amount &&
        supplierCode == other.supplierCode &&
        supplierName == other.supplierName &&
        productCode == other.productCode &&
        productName == other.productName &&
        transactionTime == other.transactionTime &&
        description == other.description &&
        discount == other.discount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, systemTrace.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, productName.hashCode);
    _$hash = $jc(_$hash, transactionTime.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, discount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentTopupResponse')
          ..add('systemTrace', systemTrace)
          ..add('transactionNo', transactionNo)
          ..add('amount', amount)
          ..add('supplierCode', supplierCode)
          ..add('supplierName', supplierName)
          ..add('productCode', productCode)
          ..add('productName', productName)
          ..add('transactionTime', transactionTime)
          ..add('description', description)
          ..add('discount', discount))
        .toString();
  }
}

class PaymentTopupResponseBuilder
    implements Builder<PaymentTopupResponse, PaymentTopupResponseBuilder> {
  _$PaymentTopupResponse? _$v;

  String? _systemTrace;
  String? get systemTrace => _$this._systemTrace;
  set systemTrace(String? systemTrace) => _$this._systemTrace = systemTrace;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  String? _productName;
  String? get productName => _$this._productName;
  set productName(String? productName) => _$this._productName = productName;

  DateTime? _transactionTime;
  DateTime? get transactionTime => _$this._transactionTime;
  set transactionTime(DateTime? transactionTime) =>
      _$this._transactionTime = transactionTime;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  num? _discount;
  num? get discount => _$this._discount;
  set discount(num? discount) => _$this._discount = discount;

  PaymentTopupResponseBuilder() {
    PaymentTopupResponse._defaults(this);
  }

  PaymentTopupResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _systemTrace = $v.systemTrace;
      _transactionNo = $v.transactionNo;
      _amount = $v.amount;
      _supplierCode = $v.supplierCode;
      _supplierName = $v.supplierName;
      _productCode = $v.productCode;
      _productName = $v.productName;
      _transactionTime = $v.transactionTime;
      _description = $v.description;
      _discount = $v.discount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentTopupResponse other) {
    _$v = other as _$PaymentTopupResponse;
  }

  @override
  void update(void Function(PaymentTopupResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentTopupResponse build() => _build();

  _$PaymentTopupResponse _build() {
    final _$result = _$v ??
        _$PaymentTopupResponse._(
          systemTrace: systemTrace,
          transactionNo: transactionNo,
          amount: amount,
          supplierCode: supplierCode,
          supplierName: supplierName,
          productCode: productCode,
          productName: productName,
          transactionTime: transactionTime,
          description: description,
          discount: discount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
