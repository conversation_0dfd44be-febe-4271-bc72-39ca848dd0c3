// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'refund_schedule_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RefundScheduleResponse extends RefundScheduleResponse {
  @override
  final double? amount;
  @override
  final DateTime? refundDate;

  factory _$RefundScheduleResponse(
          [void Function(RefundScheduleResponseBuilder)? updates]) =>
      (RefundScheduleResponseBuilder()..update(updates))._build();

  _$RefundScheduleResponse._({this.amount, this.refundDate}) : super._();
  @override
  RefundScheduleResponse rebuild(
          void Function(RefundScheduleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RefundScheduleResponseBuilder toBuilder() =>
      RefundScheduleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RefundScheduleResponse &&
        amount == other.amount &&
        refundDate == other.refundDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, refundDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RefundScheduleResponse')
          ..add('amount', amount)
          ..add('refundDate', refundDate))
        .toString();
  }
}

class RefundScheduleResponseBuilder
    implements Builder<RefundScheduleResponse, RefundScheduleResponseBuilder> {
  _$RefundScheduleResponse? _$v;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  DateTime? _refundDate;
  DateTime? get refundDate => _$this._refundDate;
  set refundDate(DateTime? refundDate) => _$this._refundDate = refundDate;

  RefundScheduleResponseBuilder() {
    RefundScheduleResponse._defaults(this);
  }

  RefundScheduleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _amount = $v.amount;
      _refundDate = $v.refundDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RefundScheduleResponse other) {
    _$v = other as _$RefundScheduleResponse;
  }

  @override
  void update(void Function(RefundScheduleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RefundScheduleResponse build() => _build();

  _$RefundScheduleResponse _build() {
    final _$result = _$v ??
        _$RefundScheduleResponse._(
          amount: amount,
          refundDate: refundDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
