// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferResponse extends TransferResponse {
  @override
  final Transaction? transaction;

  factory _$TransferResponse(
          [void Function(TransferResponseBuilder)? updates]) =>
      (TransferResponseBuilder()..update(updates))._build();

  _$TransferResponse._({this.transaction}) : super._();
  @override
  TransferResponse rebuild(void Function(TransferResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferResponseBuilder toBuilder() =>
      TransferResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferResponse && transaction == other.transaction;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transaction.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferResponse')
          ..add('transaction', transaction))
        .toString();
  }
}

class TransferResponseBuilder
    implements Builder<TransferResponse, TransferResponseBuilder> {
  _$TransferResponse? _$v;

  TransactionBuilder? _transaction;
  TransactionBuilder get transaction =>
      _$this._transaction ??= TransactionBuilder();
  set transaction(TransactionBuilder? transaction) =>
      _$this._transaction = transaction;

  TransferResponseBuilder() {
    TransferResponse._defaults(this);
  }

  TransferResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transaction = $v.transaction?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferResponse other) {
    _$v = other as _$TransferResponse;
  }

  @override
  void update(void Function(TransferResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferResponse build() => _build();

  _$TransferResponse _build() {
    _$TransferResponse _$result;
    try {
      _$result = _$v ??
          _$TransferResponse._(
            transaction: _transaction?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transaction';
        _transaction?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransferResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
