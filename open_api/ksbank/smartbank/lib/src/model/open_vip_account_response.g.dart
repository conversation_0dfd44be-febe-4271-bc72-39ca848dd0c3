// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_vip_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenVipAccountResponse extends OpenVipAccountResponse {
  @override
  final String? accountNo;
  @override
  final String? accountName;
  @override
  final String? aliasName;
  @override
  final String? createdDate;
  @override
  final String? bankName;
  @override
  final num? feeAmount;

  factory _$OpenVipAccountResponse(
          [void Function(OpenVipAccountResponseBuilder)? updates]) =>
      (OpenVipAccountResponseBuilder()..update(updates))._build();

  _$OpenVipAccountResponse._(
      {this.accountNo,
      this.accountName,
      this.aliasName,
      this.createdDate,
      this.bankName,
      this.feeAmount})
      : super._();
  @override
  OpenVipAccountResponse rebuild(
          void Function(OpenVipAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenVipAccountResponseBuilder toBuilder() =>
      OpenVipAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenVipAccountResponse &&
        accountNo == other.accountNo &&
        accountName == other.accountName &&
        aliasName == other.aliasName &&
        createdDate == other.createdDate &&
        bankName == other.bankName &&
        feeAmount == other.feeAmount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, createdDate.hashCode);
    _$hash = $jc(_$hash, bankName.hashCode);
    _$hash = $jc(_$hash, feeAmount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenVipAccountResponse')
          ..add('accountNo', accountNo)
          ..add('accountName', accountName)
          ..add('aliasName', aliasName)
          ..add('createdDate', createdDate)
          ..add('bankName', bankName)
          ..add('feeAmount', feeAmount))
        .toString();
  }
}

class OpenVipAccountResponseBuilder
    implements Builder<OpenVipAccountResponse, OpenVipAccountResponseBuilder> {
  _$OpenVipAccountResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  String? _createdDate;
  String? get createdDate => _$this._createdDate;
  set createdDate(String? createdDate) => _$this._createdDate = createdDate;

  String? _bankName;
  String? get bankName => _$this._bankName;
  set bankName(String? bankName) => _$this._bankName = bankName;

  num? _feeAmount;
  num? get feeAmount => _$this._feeAmount;
  set feeAmount(num? feeAmount) => _$this._feeAmount = feeAmount;

  OpenVipAccountResponseBuilder() {
    OpenVipAccountResponse._defaults(this);
  }

  OpenVipAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _accountName = $v.accountName;
      _aliasName = $v.aliasName;
      _createdDate = $v.createdDate;
      _bankName = $v.bankName;
      _feeAmount = $v.feeAmount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenVipAccountResponse other) {
    _$v = other as _$OpenVipAccountResponse;
  }

  @override
  void update(void Function(OpenVipAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenVipAccountResponse build() => _build();

  _$OpenVipAccountResponse _build() {
    final _$result = _$v ??
        _$OpenVipAccountResponse._(
          accountNo: accountNo,
          accountName: accountName,
          aliasName: aliasName,
          createdDate: createdDate,
          bankName: bankName,
          feeAmount: feeAmount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
