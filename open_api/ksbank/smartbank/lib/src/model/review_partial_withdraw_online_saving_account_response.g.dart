// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_partial_withdraw_online_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewPartialWithdrawOnlineSavingAccountResponse
    extends ReviewPartialWithdrawOnlineSavingAccountResponse {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? accountNumber;
  @override
  final String? crAccountNo;
  @override
  final num? balance;
  @override
  final num? amount;
  @override
  final double? redemRate;
  @override
  final num? interest;
  @override
  final num? finalAmount;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewPartialWithdrawOnlineSavingAccountResponse(
          [void Function(
                  ReviewPartialWithdrawOnlineSavingAccountResponseBuilder)?
              updates]) =>
      (ReviewPartialWithdrawOnlineSavingAccountResponseBuilder()
            ..update(updates))
          ._build();

  _$ReviewPartialWithdrawOnlineSavingAccountResponse._(
      {this.bankCif,
      this.transactionNo,
      this.accountNumber,
      this.crAccountNo,
      this.balance,
      this.amount,
      this.redemRate,
      this.interest,
      this.finalAmount,
      this.transNextStep,
      this.content})
      : super._();
  @override
  ReviewPartialWithdrawOnlineSavingAccountResponse rebuild(
          void Function(ReviewPartialWithdrawOnlineSavingAccountResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewPartialWithdrawOnlineSavingAccountResponseBuilder toBuilder() =>
      ReviewPartialWithdrawOnlineSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewPartialWithdrawOnlineSavingAccountResponse &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        accountNumber == other.accountNumber &&
        crAccountNo == other.crAccountNo &&
        balance == other.balance &&
        amount == other.amount &&
        redemRate == other.redemRate &&
        interest == other.interest &&
        finalAmount == other.finalAmount &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, crAccountNo.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, redemRate.hashCode);
    _$hash = $jc(_$hash, interest.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewPartialWithdrawOnlineSavingAccountResponse')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('accountNumber', accountNumber)
          ..add('crAccountNo', crAccountNo)
          ..add('balance', balance)
          ..add('amount', amount)
          ..add('redemRate', redemRate)
          ..add('interest', interest)
          ..add('finalAmount', finalAmount)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewPartialWithdrawOnlineSavingAccountResponseBuilder
    implements
        Builder<ReviewPartialWithdrawOnlineSavingAccountResponse,
            ReviewPartialWithdrawOnlineSavingAccountResponseBuilder> {
  _$ReviewPartialWithdrawOnlineSavingAccountResponse? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _crAccountNo;
  String? get crAccountNo => _$this._crAccountNo;
  set crAccountNo(String? crAccountNo) => _$this._crAccountNo = crAccountNo;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  double? _redemRate;
  double? get redemRate => _$this._redemRate;
  set redemRate(double? redemRate) => _$this._redemRate = redemRate;

  num? _interest;
  num? get interest => _$this._interest;
  set interest(num? interest) => _$this._interest = interest;

  num? _finalAmount;
  num? get finalAmount => _$this._finalAmount;
  set finalAmount(num? finalAmount) => _$this._finalAmount = finalAmount;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewPartialWithdrawOnlineSavingAccountResponseBuilder() {
    ReviewPartialWithdrawOnlineSavingAccountResponse._defaults(this);
  }

  ReviewPartialWithdrawOnlineSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _accountNumber = $v.accountNumber;
      _crAccountNo = $v.crAccountNo;
      _balance = $v.balance;
      _amount = $v.amount;
      _redemRate = $v.redemRate;
      _interest = $v.interest;
      _finalAmount = $v.finalAmount;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewPartialWithdrawOnlineSavingAccountResponse other) {
    _$v = other as _$ReviewPartialWithdrawOnlineSavingAccountResponse;
  }

  @override
  void update(
      void Function(ReviewPartialWithdrawOnlineSavingAccountResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewPartialWithdrawOnlineSavingAccountResponse build() => _build();

  _$ReviewPartialWithdrawOnlineSavingAccountResponse _build() {
    final _$result = _$v ??
        _$ReviewPartialWithdrawOnlineSavingAccountResponse._(
          bankCif: bankCif,
          transactionNo: transactionNo,
          accountNumber: accountNumber,
          crAccountNo: crAccountNo,
          balance: balance,
          amount: amount,
          redemRate: redemRate,
          interest: interest,
          finalAmount: finalAmount,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
