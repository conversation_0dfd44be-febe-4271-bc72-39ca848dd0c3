// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'page_support_get_online_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PageSupportGetOnlineSavingAccountResponse
    extends PageSupportGetOnlineSavingAccountResponse {
  @override
  final BuiltList<GetOnlineSavingAccountResponse>? content;
  @override
  final int? pageNumber;
  @override
  final int? pageSize;
  @override
  final int? totalElements;
  @override
  final bool? last;
  @override
  final bool? first;
  @override
  final int? totalPages;

  factory _$PageSupportGetOnlineSavingAccountResponse(
          [void Function(PageSupportGetOnlineSavingAccountResponseBuilder)?
              updates]) =>
      (PageSupportGetOnlineSavingAccountResponseBuilder()..update(updates))
          ._build();

  _$PageSupportGetOnlineSavingAccountResponse._(
      {this.content,
      this.pageNumber,
      this.pageSize,
      this.totalElements,
      this.last,
      this.first,
      this.totalPages})
      : super._();
  @override
  PageSupportGetOnlineSavingAccountResponse rebuild(
          void Function(PageSupportGetOnlineSavingAccountResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PageSupportGetOnlineSavingAccountResponseBuilder toBuilder() =>
      PageSupportGetOnlineSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PageSupportGetOnlineSavingAccountResponse &&
        content == other.content &&
        pageNumber == other.pageNumber &&
        pageSize == other.pageSize &&
        totalElements == other.totalElements &&
        last == other.last &&
        first == other.first &&
        totalPages == other.totalPages;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, pageNumber.hashCode);
    _$hash = $jc(_$hash, pageSize.hashCode);
    _$hash = $jc(_$hash, totalElements.hashCode);
    _$hash = $jc(_$hash, last.hashCode);
    _$hash = $jc(_$hash, first.hashCode);
    _$hash = $jc(_$hash, totalPages.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'PageSupportGetOnlineSavingAccountResponse')
          ..add('content', content)
          ..add('pageNumber', pageNumber)
          ..add('pageSize', pageSize)
          ..add('totalElements', totalElements)
          ..add('last', last)
          ..add('first', first)
          ..add('totalPages', totalPages))
        .toString();
  }
}

class PageSupportGetOnlineSavingAccountResponseBuilder
    implements
        Builder<PageSupportGetOnlineSavingAccountResponse,
            PageSupportGetOnlineSavingAccountResponseBuilder> {
  _$PageSupportGetOnlineSavingAccountResponse? _$v;

  ListBuilder<GetOnlineSavingAccountResponse>? _content;
  ListBuilder<GetOnlineSavingAccountResponse> get content =>
      _$this._content ??= ListBuilder<GetOnlineSavingAccountResponse>();
  set content(ListBuilder<GetOnlineSavingAccountResponse>? content) =>
      _$this._content = content;

  int? _pageNumber;
  int? get pageNumber => _$this._pageNumber;
  set pageNumber(int? pageNumber) => _$this._pageNumber = pageNumber;

  int? _pageSize;
  int? get pageSize => _$this._pageSize;
  set pageSize(int? pageSize) => _$this._pageSize = pageSize;

  int? _totalElements;
  int? get totalElements => _$this._totalElements;
  set totalElements(int? totalElements) =>
      _$this._totalElements = totalElements;

  bool? _last;
  bool? get last => _$this._last;
  set last(bool? last) => _$this._last = last;

  bool? _first;
  bool? get first => _$this._first;
  set first(bool? first) => _$this._first = first;

  int? _totalPages;
  int? get totalPages => _$this._totalPages;
  set totalPages(int? totalPages) => _$this._totalPages = totalPages;

  PageSupportGetOnlineSavingAccountResponseBuilder() {
    PageSupportGetOnlineSavingAccountResponse._defaults(this);
  }

  PageSupportGetOnlineSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _content = $v.content?.toBuilder();
      _pageNumber = $v.pageNumber;
      _pageSize = $v.pageSize;
      _totalElements = $v.totalElements;
      _last = $v.last;
      _first = $v.first;
      _totalPages = $v.totalPages;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PageSupportGetOnlineSavingAccountResponse other) {
    _$v = other as _$PageSupportGetOnlineSavingAccountResponse;
  }

  @override
  void update(
      void Function(PageSupportGetOnlineSavingAccountResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  PageSupportGetOnlineSavingAccountResponse build() => _build();

  _$PageSupportGetOnlineSavingAccountResponse _build() {
    _$PageSupportGetOnlineSavingAccountResponse _$result;
    try {
      _$result = _$v ??
          _$PageSupportGetOnlineSavingAccountResponse._(
            content: _content?.build(),
            pageNumber: pageNumber,
            pageSize: pageSize,
            totalElements: totalElements,
            last: last,
            first: first,
            totalPages: totalPages,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'content';
        _content?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PageSupportGetOnlineSavingAccountResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
