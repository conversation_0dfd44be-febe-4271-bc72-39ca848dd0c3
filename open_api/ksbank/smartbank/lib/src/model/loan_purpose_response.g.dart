// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_purpose_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoanPurposeResponse extends LoanPurposeResponse {
  @override
  final int? id;
  @override
  final String? name;
  @override
  final String? imageUrl;

  factory _$LoanPurposeResponse(
          [void Function(LoanPurposeResponseBuilder)? updates]) =>
      (LoanPurposeResponseBuilder()..update(updates))._build();

  _$LoanPurposeResponse._({this.id, this.name, this.imageUrl}) : super._();
  @override
  LoanPurposeResponse rebuild(
          void Function(LoanPurposeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanPurposeResponseBuilder toBuilder() =>
      LoanPurposeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanPurposeResponse &&
        id == other.id &&
        name == other.name &&
        imageUrl == other.imageUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanPurposeResponse')
          ..add('id', id)
          ..add('name', name)
          ..add('imageUrl', imageUrl))
        .toString();
  }
}

class LoanPurposeResponseBuilder
    implements Builder<LoanPurposeResponse, LoanPurposeResponseBuilder> {
  _$LoanPurposeResponse? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  LoanPurposeResponseBuilder() {
    LoanPurposeResponse._defaults(this);
  }

  LoanPurposeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _imageUrl = $v.imageUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanPurposeResponse other) {
    _$v = other as _$LoanPurposeResponse;
  }

  @override
  void update(void Function(LoanPurposeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanPurposeResponse build() => _build();

  _$LoanPurposeResponse _build() {
    final _$result = _$v ??
        _$LoanPurposeResponse._(
          id: id,
          name: name,
          imageUrl: imageUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
