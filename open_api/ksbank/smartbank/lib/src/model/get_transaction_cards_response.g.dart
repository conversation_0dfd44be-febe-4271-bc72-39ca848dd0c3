// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_transaction_cards_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetTransactionCardsResponse extends GetTransactionCardsResponse {
  @override
  final BuiltList<TransactionCard>? transactions;

  factory _$GetTransactionCardsResponse(
          [void Function(GetTransactionCardsResponseBuilder)? updates]) =>
      (GetTransactionCardsResponseBuilder()..update(updates))._build();

  _$GetTransactionCardsResponse._({this.transactions}) : super._();
  @override
  GetTransactionCardsResponse rebuild(
          void Function(GetTransactionCardsResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetTransactionCardsResponseBuilder toBuilder() =>
      GetTransactionCardsResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetTransactionCardsResponse &&
        transactions == other.transactions;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactions.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetTransactionCardsResponse')
          ..add('transactions', transactions))
        .toString();
  }
}

class GetTransactionCardsResponseBuilder
    implements
        Builder<GetTransactionCardsResponse,
            GetTransactionCardsResponseBuilder> {
  _$GetTransactionCardsResponse? _$v;

  ListBuilder<TransactionCard>? _transactions;
  ListBuilder<TransactionCard> get transactions =>
      _$this._transactions ??= ListBuilder<TransactionCard>();
  set transactions(ListBuilder<TransactionCard>? transactions) =>
      _$this._transactions = transactions;

  GetTransactionCardsResponseBuilder() {
    GetTransactionCardsResponse._defaults(this);
  }

  GetTransactionCardsResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactions = $v.transactions?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetTransactionCardsResponse other) {
    _$v = other as _$GetTransactionCardsResponse;
  }

  @override
  void update(void Function(GetTransactionCardsResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetTransactionCardsResponse build() => _build();

  _$GetTransactionCardsResponse _build() {
    _$GetTransactionCardsResponse _$result;
    try {
      _$result = _$v ??
          _$GetTransactionCardsResponse._(
            transactions: _transactions?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transactions';
        _transactions?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'GetTransactionCardsResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
