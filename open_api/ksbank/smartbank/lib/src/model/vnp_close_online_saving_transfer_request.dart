//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:ksbank_api_smartbank/src/model/date.dart';
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'vnp_close_online_saving_transfer_request.g.dart';

/// VNPCloseOnlineSavingTransferRequest
///
/// Properties:
/// * [bankCif] - Cif no của khách hàng
/// * [transactionNo] - Số giao dịch
/// * [accountNumber] - Số tài khoản sẽ nhận được tiền làm trung gian tất toán
/// * [accountName] - Tên tài khoản
/// * [balance] - <PERSON>ố tiền
/// * [currency] - Loại tiền: VND, USD
/// * [rate] - Lãi suất
/// * [contractDate] - <PERSON><PERSON><PERSON> tài khoản được mở. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [dueDate] - Ngày tài khoản tất toán. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [finalAmount] - Số tiền sau khi tính lãi suất
/// * [destinationAccount] - Số tài khoản nhận tiền sau khi tất toán
/// * [verifySoftOtp]
/// * [benName] - Tên người nhận
/// * [benPhone] - Số điện thoại người nhận
/// * [benIdCard] - Số chứng minh thư người nhận
/// * [message]
/// * [benIssueDate]
/// * [benPlaceBy]
/// * [benAddr]
@BuiltValue()
abstract class VNPCloseOnlineSavingTransferRequest
    implements
        Built<VNPCloseOnlineSavingTransferRequest,
            VNPCloseOnlineSavingTransferRequestBuilder> {
  /// Cif no của khách hàng
  @BuiltValueField(wireName: r'bankCif')
  String? get bankCif;

  /// Số giao dịch
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số tài khoản sẽ nhận được tiền làm trung gian tất toán
  @BuiltValueField(wireName: r'accountNumber')
  String? get accountNumber;

  /// Tên tài khoản
  @BuiltValueField(wireName: r'accountName')
  String? get accountName;

  /// Số tiền
  @BuiltValueField(wireName: r'balance')
  num? get balance;

  /// Loại tiền: VND, USD
  @BuiltValueField(wireName: r'currency')
  VNPCloseOnlineSavingTransferRequestCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  /// Lãi suất
  @BuiltValueField(wireName: r'rate')
  num? get rate;

  /// Ngày tài khoản được mở. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'contractDate')
  DateTime? get contractDate;

  /// Ngày tài khoản tất toán. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'dueDate')
  DateTime? get dueDate;

  /// Số tiền sau khi tính lãi suất
  @BuiltValueField(wireName: r'finalAmount')
  num? get finalAmount;

  /// Số tài khoản nhận tiền sau khi tất toán
  @BuiltValueField(wireName: r'destinationAccount')
  String? get destinationAccount;

  @BuiltValueField(wireName: r'verifySoftOtp')
  VerifySoftOtpRequest? get verifySoftOtp;

  /// Tên người nhận
  @BuiltValueField(wireName: r'benName')
  String? get benName;

  /// Số điện thoại người nhận
  @BuiltValueField(wireName: r'benPhone')
  String? get benPhone;

  /// Số chứng minh thư người nhận
  @BuiltValueField(wireName: r'benIdCard')
  String? get benIdCard;

  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'benIssueDate')
  Date? get benIssueDate;

  @BuiltValueField(wireName: r'benPlaceBy')
  String? get benPlaceBy;

  @BuiltValueField(wireName: r'benAddr')
  String? get benAddr;

  VNPCloseOnlineSavingTransferRequest._();

  factory VNPCloseOnlineSavingTransferRequest(
          [void updates(VNPCloseOnlineSavingTransferRequestBuilder b)]) =
      _$VNPCloseOnlineSavingTransferRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(VNPCloseOnlineSavingTransferRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<VNPCloseOnlineSavingTransferRequest> get serializer =>
      _$VNPCloseOnlineSavingTransferRequestSerializer();
}

class _$VNPCloseOnlineSavingTransferRequestSerializer
    implements PrimitiveSerializer<VNPCloseOnlineSavingTransferRequest> {
  @override
  final Iterable<Type> types = const [
    VNPCloseOnlineSavingTransferRequest,
    _$VNPCloseOnlineSavingTransferRequest
  ];

  @override
  final String wireName = r'VNPCloseOnlineSavingTransferRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    VNPCloseOnlineSavingTransferRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.bankCif != null) {
      yield r'bankCif';
      yield serializers.serialize(
        object.bankCif,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNumber != null) {
      yield r'accountNumber';
      yield serializers.serialize(
        object.accountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountName != null) {
      yield r'accountName';
      yield serializers.serialize(
        object.accountName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.balance != null) {
      yield r'balance';
      yield serializers.serialize(
        object.balance,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType: const FullType.nullable(
            VNPCloseOnlineSavingTransferRequestCurrencyEnum),
      );
    }
    if (object.rate != null) {
      yield r'rate';
      yield serializers.serialize(
        object.rate,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.contractDate != null) {
      yield r'contractDate';
      yield serializers.serialize(
        object.contractDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.dueDate != null) {
      yield r'dueDate';
      yield serializers.serialize(
        object.dueDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.finalAmount != null) {
      yield r'finalAmount';
      yield serializers.serialize(
        object.finalAmount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.destinationAccount != null) {
      yield r'destinationAccount';
      yield serializers.serialize(
        object.destinationAccount,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.verifySoftOtp != null) {
      yield r'verifySoftOtp';
      yield serializers.serialize(
        object.verifySoftOtp,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
    if (object.benName != null) {
      yield r'benName';
      yield serializers.serialize(
        object.benName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.benPhone != null) {
      yield r'benPhone';
      yield serializers.serialize(
        object.benPhone,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.benIdCard != null) {
      yield r'benIdCard';
      yield serializers.serialize(
        object.benIdCard,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.message != null) {
      yield r'message';
      yield serializers.serialize(
        object.message,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.benIssueDate != null) {
      yield r'benIssueDate';
      yield serializers.serialize(
        object.benIssueDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.benPlaceBy != null) {
      yield r'benPlaceBy';
      yield serializers.serialize(
        object.benPlaceBy,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.benAddr != null) {
      yield r'benAddr';
      yield serializers.serialize(
        object.benAddr,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    VNPCloseOnlineSavingTransferRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required VNPCloseOnlineSavingTransferRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'bankCif':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCif = valueDes;
          break;
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'accountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNumber = valueDes;
          break;
        case r'accountName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountName = valueDes;
          break;
        case r'balance':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.balance = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                VNPCloseOnlineSavingTransferRequestCurrencyEnum),
          ) as VNPCloseOnlineSavingTransferRequestCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        case r'rate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.rate = valueDes;
          break;
        case r'contractDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.contractDate = valueDes;
          break;
        case r'dueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.dueDate = valueDes;
          break;
        case r'finalAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.finalAmount = valueDes;
          break;
        case r'destinationAccount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.destinationAccount = valueDes;
          break;
        case r'verifySoftOtp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.verifySoftOtp.replace(valueDes);
          break;
        case r'benName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.benName = valueDes;
          break;
        case r'benPhone':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.benPhone = valueDes;
          break;
        case r'benIdCard':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.benIdCard = valueDes;
          break;
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        case r'benIssueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.benIssueDate = valueDes;
          break;
        case r'benPlaceBy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.benPlaceBy = valueDes;
          break;
        case r'benAddr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.benAddr = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  VNPCloseOnlineSavingTransferRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = VNPCloseOnlineSavingTransferRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class VNPCloseOnlineSavingTransferRequestCurrencyEnum extends EnumClass {
  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'VND')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum VND =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_VND;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'USD')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum USD =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_USD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'ACB')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum ACB =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_ACB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'JPY')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum JPY =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_JPY;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum GOLD =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GOLD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'EUR')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum EUR =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_EUR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GBP')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum GBP =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GBP;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CHF')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum CHF =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CHF;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'AUD')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum AUD =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_AUD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CAD')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum CAD =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CAD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SGD')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum SGD =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SGD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'THB')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum THB =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_THB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NOK')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum NOK =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NOK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NZD')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum NZD =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NZD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'DKK')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum DKK =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_DKK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'HKD')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum HKD =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_HKD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SEK')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum SEK =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SEK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MYR')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum MYR =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MYR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'XAU')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum XAU =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_XAU;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MMK')
  static const VNPCloseOnlineSavingTransferRequestCurrencyEnum MMK =
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MMK;

  static Serializer<VNPCloseOnlineSavingTransferRequestCurrencyEnum>
      get serializer =>
          _$vNPCloseOnlineSavingTransferRequestCurrencyEnumSerializer;

  const VNPCloseOnlineSavingTransferRequestCurrencyEnum._(String name)
      : super(name);

  static BuiltSet<VNPCloseOnlineSavingTransferRequestCurrencyEnum> get values =>
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnumValues;
  static VNPCloseOnlineSavingTransferRequestCurrencyEnum valueOf(String name) =>
      _$vNPCloseOnlineSavingTransferRequestCurrencyEnumValueOf(name);
}
