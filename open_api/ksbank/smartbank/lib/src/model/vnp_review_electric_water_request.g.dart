// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_review_electric_water_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VnpReviewElectricWaterRequest extends VnpReviewElectricWaterRequest {
  @override
  final String? transactionNo;
  @override
  final String? serviceCode;
  @override
  final String? produceCode;
  @override
  final String? fromAcctNbr;
  @override
  final String? toAcctNbr;
  @override
  final String? amount;
  @override
  final String? fee;
  @override
  final String? descText;
  @override
  final VnpCustomerBillInfo? customer;
  @override
  final BuiltList<VnpBillInfo>? bills;
  @override
  final BuiltList<VnpServiceInfo>? services;

  factory _$VnpReviewElectricWaterRequest(
          [void Function(VnpReviewElectricWaterRequestBuilder)? updates]) =>
      (VnpReviewElectricWaterRequestBuilder()..update(updates))._build();

  _$VnpReviewElectricWaterRequest._(
      {this.transactionNo,
      this.serviceCode,
      this.produceCode,
      this.fromAcctNbr,
      this.toAcctNbr,
      this.amount,
      this.fee,
      this.descText,
      this.customer,
      this.bills,
      this.services})
      : super._();
  @override
  VnpReviewElectricWaterRequest rebuild(
          void Function(VnpReviewElectricWaterRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VnpReviewElectricWaterRequestBuilder toBuilder() =>
      VnpReviewElectricWaterRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VnpReviewElectricWaterRequest &&
        transactionNo == other.transactionNo &&
        serviceCode == other.serviceCode &&
        produceCode == other.produceCode &&
        fromAcctNbr == other.fromAcctNbr &&
        toAcctNbr == other.toAcctNbr &&
        amount == other.amount &&
        fee == other.fee &&
        descText == other.descText &&
        customer == other.customer &&
        bills == other.bills &&
        services == other.services;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, serviceCode.hashCode);
    _$hash = $jc(_$hash, produceCode.hashCode);
    _$hash = $jc(_$hash, fromAcctNbr.hashCode);
    _$hash = $jc(_$hash, toAcctNbr.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jc(_$hash, descText.hashCode);
    _$hash = $jc(_$hash, customer.hashCode);
    _$hash = $jc(_$hash, bills.hashCode);
    _$hash = $jc(_$hash, services.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VnpReviewElectricWaterRequest')
          ..add('transactionNo', transactionNo)
          ..add('serviceCode', serviceCode)
          ..add('produceCode', produceCode)
          ..add('fromAcctNbr', fromAcctNbr)
          ..add('toAcctNbr', toAcctNbr)
          ..add('amount', amount)
          ..add('fee', fee)
          ..add('descText', descText)
          ..add('customer', customer)
          ..add('bills', bills)
          ..add('services', services))
        .toString();
  }
}

class VnpReviewElectricWaterRequestBuilder
    implements
        Builder<VnpReviewElectricWaterRequest,
            VnpReviewElectricWaterRequestBuilder> {
  _$VnpReviewElectricWaterRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _serviceCode;
  String? get serviceCode => _$this._serviceCode;
  set serviceCode(String? serviceCode) => _$this._serviceCode = serviceCode;

  String? _produceCode;
  String? get produceCode => _$this._produceCode;
  set produceCode(String? produceCode) => _$this._produceCode = produceCode;

  String? _fromAcctNbr;
  String? get fromAcctNbr => _$this._fromAcctNbr;
  set fromAcctNbr(String? fromAcctNbr) => _$this._fromAcctNbr = fromAcctNbr;

  String? _toAcctNbr;
  String? get toAcctNbr => _$this._toAcctNbr;
  set toAcctNbr(String? toAcctNbr) => _$this._toAcctNbr = toAcctNbr;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _fee;
  String? get fee => _$this._fee;
  set fee(String? fee) => _$this._fee = fee;

  String? _descText;
  String? get descText => _$this._descText;
  set descText(String? descText) => _$this._descText = descText;

  VnpCustomerBillInfoBuilder? _customer;
  VnpCustomerBillInfoBuilder get customer =>
      _$this._customer ??= VnpCustomerBillInfoBuilder();
  set customer(VnpCustomerBillInfoBuilder? customer) =>
      _$this._customer = customer;

  ListBuilder<VnpBillInfo>? _bills;
  ListBuilder<VnpBillInfo> get bills =>
      _$this._bills ??= ListBuilder<VnpBillInfo>();
  set bills(ListBuilder<VnpBillInfo>? bills) => _$this._bills = bills;

  ListBuilder<VnpServiceInfo>? _services;
  ListBuilder<VnpServiceInfo> get services =>
      _$this._services ??= ListBuilder<VnpServiceInfo>();
  set services(ListBuilder<VnpServiceInfo>? services) =>
      _$this._services = services;

  VnpReviewElectricWaterRequestBuilder() {
    VnpReviewElectricWaterRequest._defaults(this);
  }

  VnpReviewElectricWaterRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _serviceCode = $v.serviceCode;
      _produceCode = $v.produceCode;
      _fromAcctNbr = $v.fromAcctNbr;
      _toAcctNbr = $v.toAcctNbr;
      _amount = $v.amount;
      _fee = $v.fee;
      _descText = $v.descText;
      _customer = $v.customer?.toBuilder();
      _bills = $v.bills?.toBuilder();
      _services = $v.services?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VnpReviewElectricWaterRequest other) {
    _$v = other as _$VnpReviewElectricWaterRequest;
  }

  @override
  void update(void Function(VnpReviewElectricWaterRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VnpReviewElectricWaterRequest build() => _build();

  _$VnpReviewElectricWaterRequest _build() {
    _$VnpReviewElectricWaterRequest _$result;
    try {
      _$result = _$v ??
          _$VnpReviewElectricWaterRequest._(
            transactionNo: transactionNo,
            serviceCode: serviceCode,
            produceCode: produceCode,
            fromAcctNbr: fromAcctNbr,
            toAcctNbr: toAcctNbr,
            amount: amount,
            fee: fee,
            descText: descText,
            customer: _customer?.build(),
            bills: _bills?.build(),
            services: _services?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'customer';
        _customer?.build();
        _$failedField = 'bills';
        _bills?.build();
        _$failedField = 'services';
        _services?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'VnpReviewElectricWaterRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
