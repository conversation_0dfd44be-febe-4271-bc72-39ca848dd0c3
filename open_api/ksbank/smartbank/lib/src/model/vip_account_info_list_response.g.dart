// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_account_info_list_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VipAccountInfoListResponse extends VipAccountInfoListResponse {
  @override
  final BuiltList<VipAccountInfoDto>? vipAccountList;

  factory _$VipAccountInfoListResponse(
          [void Function(VipAccountInfoListResponseBuilder)? updates]) =>
      (VipAccountInfoListResponseBuilder()..update(updates))._build();

  _$VipAccountInfoListResponse._({this.vipAccountList}) : super._();
  @override
  VipAccountInfoListResponse rebuild(
          void Function(VipAccountInfoListResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VipAccountInfoListResponseBuilder toBuilder() =>
      VipAccountInfoListResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VipAccountInfoListResponse &&
        vipAccountList == other.vipAccountList;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, vipAccountList.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VipAccountInfoListResponse')
          ..add('vipAccountList', vipAccountList))
        .toString();
  }
}

class VipAccountInfoListResponseBuilder
    implements
        Builder<VipAccountInfoListResponse, VipAccountInfoListResponseBuilder> {
  _$VipAccountInfoListResponse? _$v;

  ListBuilder<VipAccountInfoDto>? _vipAccountList;
  ListBuilder<VipAccountInfoDto> get vipAccountList =>
      _$this._vipAccountList ??= ListBuilder<VipAccountInfoDto>();
  set vipAccountList(ListBuilder<VipAccountInfoDto>? vipAccountList) =>
      _$this._vipAccountList = vipAccountList;

  VipAccountInfoListResponseBuilder() {
    VipAccountInfoListResponse._defaults(this);
  }

  VipAccountInfoListResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _vipAccountList = $v.vipAccountList?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VipAccountInfoListResponse other) {
    _$v = other as _$VipAccountInfoListResponse;
  }

  @override
  void update(void Function(VipAccountInfoListResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VipAccountInfoListResponse build() => _build();

  _$VipAccountInfoListResponse _build() {
    _$VipAccountInfoListResponse _$result;
    try {
      _$result = _$v ??
          _$VipAccountInfoListResponse._(
            vipAccountList: _vipAccountList?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'vipAccountList';
        _vipAccountList?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'VipAccountInfoListResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
