// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_transaction_schedule_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListTransactionScheduleResponse
    extends ListTransactionScheduleResponse {
  @override
  final BuiltList<TransferScheduleResponse>? transferScheduleList;

  factory _$ListTransactionScheduleResponse(
          [void Function(ListTransactionScheduleResponseBuilder)? updates]) =>
      (ListTransactionScheduleResponseBuilder()..update(updates))._build();

  _$ListTransactionScheduleResponse._({this.transferScheduleList}) : super._();
  @override
  ListTransactionScheduleResponse rebuild(
          void Function(ListTransactionScheduleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListTransactionScheduleResponseBuilder toBuilder() =>
      ListTransactionScheduleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListTransactionScheduleResponse &&
        transferScheduleList == other.transferScheduleList;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transferScheduleList.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListTransactionScheduleResponse')
          ..add('transferScheduleList', transferScheduleList))
        .toString();
  }
}

class ListTransactionScheduleResponseBuilder
    implements
        Builder<ListTransactionScheduleResponse,
            ListTransactionScheduleResponseBuilder> {
  _$ListTransactionScheduleResponse? _$v;

  ListBuilder<TransferScheduleResponse>? _transferScheduleList;
  ListBuilder<TransferScheduleResponse> get transferScheduleList =>
      _$this._transferScheduleList ??= ListBuilder<TransferScheduleResponse>();
  set transferScheduleList(
          ListBuilder<TransferScheduleResponse>? transferScheduleList) =>
      _$this._transferScheduleList = transferScheduleList;

  ListTransactionScheduleResponseBuilder() {
    ListTransactionScheduleResponse._defaults(this);
  }

  ListTransactionScheduleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transferScheduleList = $v.transferScheduleList?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListTransactionScheduleResponse other) {
    _$v = other as _$ListTransactionScheduleResponse;
  }

  @override
  void update(void Function(ListTransactionScheduleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListTransactionScheduleResponse build() => _build();

  _$ListTransactionScheduleResponse _build() {
    _$ListTransactionScheduleResponse _$result;
    try {
      _$result = _$v ??
          _$ListTransactionScheduleResponse._(
            transferScheduleList: _transferScheduleList?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transferScheduleList';
        _transferScheduleList?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListTransactionScheduleResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
