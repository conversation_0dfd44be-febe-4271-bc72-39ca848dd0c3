// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verify_soft_otp_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VerifySoftOtpRequest extends VerifySoftOtpRequest {
  @override
  final String? otp;

  factory _$VerifySoftOtpRequest(
          [void Function(VerifySoftOtpRequestBuilder)? updates]) =>
      (VerifySoftOtpRequestBuilder()..update(updates))._build();

  _$VerifySoftOtpRequest._({this.otp}) : super._();
  @override
  VerifySoftOtpRequest rebuild(
          void Function(VerifySoftOtpRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VerifySoftOtpRequestBuilder toBuilder() =>
      VerifySoftOtpRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VerifySoftOtpRequest && otp == other.otp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, otp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VerifySoftOtpRequest')
          ..add('otp', otp))
        .toString();
  }
}

class VerifySoftOtpRequestBuilder
    implements Builder<VerifySoftOtpRequest, VerifySoftOtpRequestBuilder> {
  _$VerifySoftOtpRequest? _$v;

  String? _otp;
  String? get otp => _$this._otp;
  set otp(String? otp) => _$this._otp = otp;

  VerifySoftOtpRequestBuilder() {
    VerifySoftOtpRequest._defaults(this);
  }

  VerifySoftOtpRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _otp = $v.otp;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VerifySoftOtpRequest other) {
    _$v = other as _$VerifySoftOtpRequest;
  }

  @override
  void update(void Function(VerifySoftOtpRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VerifySoftOtpRequest build() => _build();

  _$VerifySoftOtpRequest _build() {
    final _$result = _$v ??
        _$VerifySoftOtpRequest._(
          otp: otp,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
