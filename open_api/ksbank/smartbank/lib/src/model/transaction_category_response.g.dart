// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_category_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const TransactionCategoryResponseCashFlowEnum
    _$transactionCategoryResponseCashFlowEnum_RECEIVE =
    const TransactionCategoryResponseCashFlowEnum._('RECEIVE');
const TransactionCategoryResponseCashFlowEnum
    _$transactionCategoryResponseCashFlowEnum_SEND =
    const TransactionCategoryResponseCashFlowEnum._('SEND');

TransactionCategoryResponseCashFlowEnum
    _$transactionCategoryResponseCashFlowEnumValueOf(String name) {
  switch (name) {
    case 'RECEIVE':
      return _$transactionCategoryResponseCashFlowEnum_RECEIVE;
    case 'SEND':
      return _$transactionCategoryResponseCashFlowEnum_SEND;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<TransactionCategoryResponseCashFlowEnum>
    _$transactionCategoryResponseCashFlowEnumValues = BuiltSet<
        TransactionCategoryResponseCashFlowEnum>(const <TransactionCategoryResponseCashFlowEnum>[
  _$transactionCategoryResponseCashFlowEnum_RECEIVE,
  _$transactionCategoryResponseCashFlowEnum_SEND,
]);

Serializer<TransactionCategoryResponseCashFlowEnum>
    _$transactionCategoryResponseCashFlowEnumSerializer =
    _$TransactionCategoryResponseCashFlowEnumSerializer();

class _$TransactionCategoryResponseCashFlowEnumSerializer
    implements PrimitiveSerializer<TransactionCategoryResponseCashFlowEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'RECEIVE': 'RECEIVE',
    'SEND': 'SEND',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'RECEIVE': 'RECEIVE',
    'SEND': 'SEND',
  };

  @override
  final Iterable<Type> types = const <Type>[
    TransactionCategoryResponseCashFlowEnum
  ];
  @override
  final String wireName = 'TransactionCategoryResponseCashFlowEnum';

  @override
  Object serialize(Serializers serializers,
          TransactionCategoryResponseCashFlowEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  TransactionCategoryResponseCashFlowEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      TransactionCategoryResponseCashFlowEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$TransactionCategoryResponse extends TransactionCategoryResponse {
  @override
  final int? id;
  @override
  final String? name;
  @override
  final TransactionCategoryResponseCashFlowEnum? cashFlow;
  @override
  final String? iconUrl;

  factory _$TransactionCategoryResponse(
          [void Function(TransactionCategoryResponseBuilder)? updates]) =>
      (TransactionCategoryResponseBuilder()..update(updates))._build();

  _$TransactionCategoryResponse._(
      {this.id, this.name, this.cashFlow, this.iconUrl})
      : super._();
  @override
  TransactionCategoryResponse rebuild(
          void Function(TransactionCategoryResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionCategoryResponseBuilder toBuilder() =>
      TransactionCategoryResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionCategoryResponse &&
        id == other.id &&
        name == other.name &&
        cashFlow == other.cashFlow &&
        iconUrl == other.iconUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, cashFlow.hashCode);
    _$hash = $jc(_$hash, iconUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionCategoryResponse')
          ..add('id', id)
          ..add('name', name)
          ..add('cashFlow', cashFlow)
          ..add('iconUrl', iconUrl))
        .toString();
  }
}

class TransactionCategoryResponseBuilder
    implements
        Builder<TransactionCategoryResponse,
            TransactionCategoryResponseBuilder> {
  _$TransactionCategoryResponse? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  TransactionCategoryResponseCashFlowEnum? _cashFlow;
  TransactionCategoryResponseCashFlowEnum? get cashFlow => _$this._cashFlow;
  set cashFlow(TransactionCategoryResponseCashFlowEnum? cashFlow) =>
      _$this._cashFlow = cashFlow;

  String? _iconUrl;
  String? get iconUrl => _$this._iconUrl;
  set iconUrl(String? iconUrl) => _$this._iconUrl = iconUrl;

  TransactionCategoryResponseBuilder() {
    TransactionCategoryResponse._defaults(this);
  }

  TransactionCategoryResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _cashFlow = $v.cashFlow;
      _iconUrl = $v.iconUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionCategoryResponse other) {
    _$v = other as _$TransactionCategoryResponse;
  }

  @override
  void update(void Function(TransactionCategoryResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionCategoryResponse build() => _build();

  _$TransactionCategoryResponse _build() {
    final _$result = _$v ??
        _$TransactionCategoryResponse._(
          id: id,
          name: name,
          cashFlow: cashFlow,
          iconUrl: iconUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
