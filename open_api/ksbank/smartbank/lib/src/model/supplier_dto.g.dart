// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supplier_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SupplierDto extends SupplierDto {
  @override
  final String? supplierCode;
  @override
  final String? supplierName;
  @override
  final String? iconUrl;

  factory _$SupplierDto([void Function(SupplierDtoBuilder)? updates]) =>
      (SupplierDtoBuilder()..update(updates))._build();

  _$SupplierDto._({this.supplierCode, this.supplierName, this.iconUrl})
      : super._();
  @override
  SupplierDto rebuild(void Function(SupplierDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SupplierDtoBuilder toBuilder() => SupplierDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SupplierDto &&
        supplierCode == other.supplierCode &&
        supplierName == other.supplierName &&
        iconUrl == other.iconUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, iconUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SupplierDto')
          ..add('supplierCode', supplierCode)
          ..add('supplierName', supplierName)
          ..add('iconUrl', iconUrl))
        .toString();
  }
}

class SupplierDtoBuilder implements Builder<SupplierDto, SupplierDtoBuilder> {
  _$SupplierDto? _$v;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _iconUrl;
  String? get iconUrl => _$this._iconUrl;
  set iconUrl(String? iconUrl) => _$this._iconUrl = iconUrl;

  SupplierDtoBuilder() {
    SupplierDto._defaults(this);
  }

  SupplierDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _supplierCode = $v.supplierCode;
      _supplierName = $v.supplierName;
      _iconUrl = $v.iconUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SupplierDto other) {
    _$v = other as _$SupplierDto;
  }

  @override
  void update(void Function(SupplierDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SupplierDto build() => _build();

  _$SupplierDto _build() {
    final _$result = _$v ??
        _$SupplierDto._(
          supplierCode: supplierCode,
          supplierName: supplierName,
          iconUrl: iconUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
