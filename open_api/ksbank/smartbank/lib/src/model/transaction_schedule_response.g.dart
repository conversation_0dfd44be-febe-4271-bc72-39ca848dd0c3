// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_schedule_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionScheduleResponse extends TransactionScheduleResponse {
  @override
  final TransactionScheduleDto? transactionSchedule;

  factory _$TransactionScheduleResponse(
          [void Function(TransactionScheduleResponseBuilder)? updates]) =>
      (TransactionScheduleResponseBuilder()..update(updates))._build();

  _$TransactionScheduleResponse._({this.transactionSchedule}) : super._();
  @override
  TransactionScheduleResponse rebuild(
          void Function(TransactionScheduleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionScheduleResponseBuilder toBuilder() =>
      TransactionScheduleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionScheduleResponse &&
        transactionSchedule == other.transactionSchedule;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionSchedule.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionScheduleResponse')
          ..add('transactionSchedule', transactionSchedule))
        .toString();
  }
}

class TransactionScheduleResponseBuilder
    implements
        Builder<TransactionScheduleResponse,
            TransactionScheduleResponseBuilder> {
  _$TransactionScheduleResponse? _$v;

  TransactionScheduleDtoBuilder? _transactionSchedule;
  TransactionScheduleDtoBuilder get transactionSchedule =>
      _$this._transactionSchedule ??= TransactionScheduleDtoBuilder();
  set transactionSchedule(TransactionScheduleDtoBuilder? transactionSchedule) =>
      _$this._transactionSchedule = transactionSchedule;

  TransactionScheduleResponseBuilder() {
    TransactionScheduleResponse._defaults(this);
  }

  TransactionScheduleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionSchedule = $v.transactionSchedule?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionScheduleResponse other) {
    _$v = other as _$TransactionScheduleResponse;
  }

  @override
  void update(void Function(TransactionScheduleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionScheduleResponse build() => _build();

  _$TransactionScheduleResponse _build() {
    _$TransactionScheduleResponse _$result;
    try {
      _$result = _$v ??
          _$TransactionScheduleResponse._(
            transactionSchedule: _transactionSchedule?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transactionSchedule';
        _transactionSchedule?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransactionScheduleResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
