// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_loan_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RegisterLoanResponse extends RegisterLoanResponse {
  @override
  final String? id;
  @override
  final String? profileCode;

  factory _$RegisterLoanResponse(
          [void Function(RegisterLoanResponseBuilder)? updates]) =>
      (RegisterLoanResponseBuilder()..update(updates))._build();

  _$RegisterLoanResponse._({this.id, this.profileCode}) : super._();
  @override
  RegisterLoanResponse rebuild(
          void Function(RegisterLoanResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RegisterLoanResponseBuilder toBuilder() =>
      RegisterLoanResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RegisterLoanResponse &&
        id == other.id &&
        profileCode == other.profileCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, profileCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RegisterLoanResponse')
          ..add('id', id)
          ..add('profileCode', profileCode))
        .toString();
  }
}

class RegisterLoanResponseBuilder
    implements Builder<RegisterLoanResponse, RegisterLoanResponseBuilder> {
  _$RegisterLoanResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _profileCode;
  String? get profileCode => _$this._profileCode;
  set profileCode(String? profileCode) => _$this._profileCode = profileCode;

  RegisterLoanResponseBuilder() {
    RegisterLoanResponse._defaults(this);
  }

  RegisterLoanResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _profileCode = $v.profileCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RegisterLoanResponse other) {
    _$v = other as _$RegisterLoanResponse;
  }

  @override
  void update(void Function(RegisterLoanResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RegisterLoanResponse build() => _build();

  _$RegisterLoanResponse _build() {
    final _$result = _$v ??
        _$RegisterLoanResponse._(
          id: id,
          profileCode: profileCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
