// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_statistical.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionStatistical extends TransactionStatistical {
  @override
  final int? transactionTypeId;
  @override
  final String? transactionTypeName;
  @override
  final String? transactionTypeIconUrl;
  @override
  final int? coefficient;
  @override
  final double? percent;
  @override
  final num? amount;
  @override
  final String? currency;

  factory _$TransactionStatistical(
          [void Function(TransactionStatisticalBuilder)? updates]) =>
      (TransactionStatisticalBuilder()..update(updates))._build();

  _$TransactionStatistical._(
      {this.transactionTypeId,
      this.transactionTypeName,
      this.transactionTypeIconUrl,
      this.coefficient,
      this.percent,
      this.amount,
      this.currency})
      : super._();
  @override
  TransactionStatistical rebuild(
          void Function(TransactionStatisticalBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionStatisticalBuilder toBuilder() =>
      TransactionStatisticalBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionStatistical &&
        transactionTypeId == other.transactionTypeId &&
        transactionTypeName == other.transactionTypeName &&
        transactionTypeIconUrl == other.transactionTypeIconUrl &&
        coefficient == other.coefficient &&
        percent == other.percent &&
        amount == other.amount &&
        currency == other.currency;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionTypeId.hashCode);
    _$hash = $jc(_$hash, transactionTypeName.hashCode);
    _$hash = $jc(_$hash, transactionTypeIconUrl.hashCode);
    _$hash = $jc(_$hash, coefficient.hashCode);
    _$hash = $jc(_$hash, percent.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionStatistical')
          ..add('transactionTypeId', transactionTypeId)
          ..add('transactionTypeName', transactionTypeName)
          ..add('transactionTypeIconUrl', transactionTypeIconUrl)
          ..add('coefficient', coefficient)
          ..add('percent', percent)
          ..add('amount', amount)
          ..add('currency', currency))
        .toString();
  }
}

class TransactionStatisticalBuilder
    implements Builder<TransactionStatistical, TransactionStatisticalBuilder> {
  _$TransactionStatistical? _$v;

  int? _transactionTypeId;
  int? get transactionTypeId => _$this._transactionTypeId;
  set transactionTypeId(int? transactionTypeId) =>
      _$this._transactionTypeId = transactionTypeId;

  String? _transactionTypeName;
  String? get transactionTypeName => _$this._transactionTypeName;
  set transactionTypeName(String? transactionTypeName) =>
      _$this._transactionTypeName = transactionTypeName;

  String? _transactionTypeIconUrl;
  String? get transactionTypeIconUrl => _$this._transactionTypeIconUrl;
  set transactionTypeIconUrl(String? transactionTypeIconUrl) =>
      _$this._transactionTypeIconUrl = transactionTypeIconUrl;

  int? _coefficient;
  int? get coefficient => _$this._coefficient;
  set coefficient(int? coefficient) => _$this._coefficient = coefficient;

  double? _percent;
  double? get percent => _$this._percent;
  set percent(double? percent) => _$this._percent = percent;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  TransactionStatisticalBuilder() {
    TransactionStatistical._defaults(this);
  }

  TransactionStatisticalBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionTypeId = $v.transactionTypeId;
      _transactionTypeName = $v.transactionTypeName;
      _transactionTypeIconUrl = $v.transactionTypeIconUrl;
      _coefficient = $v.coefficient;
      _percent = $v.percent;
      _amount = $v.amount;
      _currency = $v.currency;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionStatistical other) {
    _$v = other as _$TransactionStatistical;
  }

  @override
  void update(void Function(TransactionStatisticalBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionStatistical build() => _build();

  _$TransactionStatistical _build() {
    final _$result = _$v ??
        _$TransactionStatistical._(
          transactionTypeId: transactionTypeId,
          transactionTypeName: transactionTypeName,
          transactionTypeIconUrl: transactionTypeIconUrl,
          coefficient: coefficient,
          percent: percent,
          amount: amount,
          currency: currency,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
