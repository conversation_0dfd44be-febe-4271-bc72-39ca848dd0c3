// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resolve_payment_data_app_module_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResolvePaymentDataAppModuleResponse
    extends ResolvePaymentDataAppModuleResponse {
  @override
  final String? appId;
  @override
  final String? sessionId;
  @override
  final String? successUrl;
  @override
  final String? failUrl;
  @override
  final int? amount;
  @override
  final String? orderId;
  @override
  final String? provider;
  @override
  final String? description;
  @override
  final String? accountNo;
  @override
  final String? virtualAccount;
  @override
  final String? refTransactionId;
  @override
  final String? time;

  factory _$ResolvePaymentDataAppModuleResponse(
          [void Function(ResolvePaymentDataAppModuleResponseBuilder)?
              updates]) =>
      (ResolvePaymentDataAppModuleResponseBuilder()..update(updates))._build();

  _$ResolvePaymentDataAppModuleResponse._(
      {this.appId,
      this.sessionId,
      this.successUrl,
      this.failUrl,
      this.amount,
      this.orderId,
      this.provider,
      this.description,
      this.accountNo,
      this.virtualAccount,
      this.refTransactionId,
      this.time})
      : super._();
  @override
  ResolvePaymentDataAppModuleResponse rebuild(
          void Function(ResolvePaymentDataAppModuleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResolvePaymentDataAppModuleResponseBuilder toBuilder() =>
      ResolvePaymentDataAppModuleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResolvePaymentDataAppModuleResponse &&
        appId == other.appId &&
        sessionId == other.sessionId &&
        successUrl == other.successUrl &&
        failUrl == other.failUrl &&
        amount == other.amount &&
        orderId == other.orderId &&
        provider == other.provider &&
        description == other.description &&
        accountNo == other.accountNo &&
        virtualAccount == other.virtualAccount &&
        refTransactionId == other.refTransactionId &&
        time == other.time;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, appId.hashCode);
    _$hash = $jc(_$hash, sessionId.hashCode);
    _$hash = $jc(_$hash, successUrl.hashCode);
    _$hash = $jc(_$hash, failUrl.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, orderId.hashCode);
    _$hash = $jc(_$hash, provider.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, virtualAccount.hashCode);
    _$hash = $jc(_$hash, refTransactionId.hashCode);
    _$hash = $jc(_$hash, time.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResolvePaymentDataAppModuleResponse')
          ..add('appId', appId)
          ..add('sessionId', sessionId)
          ..add('successUrl', successUrl)
          ..add('failUrl', failUrl)
          ..add('amount', amount)
          ..add('orderId', orderId)
          ..add('provider', provider)
          ..add('description', description)
          ..add('accountNo', accountNo)
          ..add('virtualAccount', virtualAccount)
          ..add('refTransactionId', refTransactionId)
          ..add('time', time))
        .toString();
  }
}

class ResolvePaymentDataAppModuleResponseBuilder
    implements
        Builder<ResolvePaymentDataAppModuleResponse,
            ResolvePaymentDataAppModuleResponseBuilder> {
  _$ResolvePaymentDataAppModuleResponse? _$v;

  String? _appId;
  String? get appId => _$this._appId;
  set appId(String? appId) => _$this._appId = appId;

  String? _sessionId;
  String? get sessionId => _$this._sessionId;
  set sessionId(String? sessionId) => _$this._sessionId = sessionId;

  String? _successUrl;
  String? get successUrl => _$this._successUrl;
  set successUrl(String? successUrl) => _$this._successUrl = successUrl;

  String? _failUrl;
  String? get failUrl => _$this._failUrl;
  set failUrl(String? failUrl) => _$this._failUrl = failUrl;

  int? _amount;
  int? get amount => _$this._amount;
  set amount(int? amount) => _$this._amount = amount;

  String? _orderId;
  String? get orderId => _$this._orderId;
  set orderId(String? orderId) => _$this._orderId = orderId;

  String? _provider;
  String? get provider => _$this._provider;
  set provider(String? provider) => _$this._provider = provider;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _virtualAccount;
  String? get virtualAccount => _$this._virtualAccount;
  set virtualAccount(String? virtualAccount) =>
      _$this._virtualAccount = virtualAccount;

  String? _refTransactionId;
  String? get refTransactionId => _$this._refTransactionId;
  set refTransactionId(String? refTransactionId) =>
      _$this._refTransactionId = refTransactionId;

  String? _time;
  String? get time => _$this._time;
  set time(String? time) => _$this._time = time;

  ResolvePaymentDataAppModuleResponseBuilder() {
    ResolvePaymentDataAppModuleResponse._defaults(this);
  }

  ResolvePaymentDataAppModuleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _appId = $v.appId;
      _sessionId = $v.sessionId;
      _successUrl = $v.successUrl;
      _failUrl = $v.failUrl;
      _amount = $v.amount;
      _orderId = $v.orderId;
      _provider = $v.provider;
      _description = $v.description;
      _accountNo = $v.accountNo;
      _virtualAccount = $v.virtualAccount;
      _refTransactionId = $v.refTransactionId;
      _time = $v.time;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResolvePaymentDataAppModuleResponse other) {
    _$v = other as _$ResolvePaymentDataAppModuleResponse;
  }

  @override
  void update(
      void Function(ResolvePaymentDataAppModuleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResolvePaymentDataAppModuleResponse build() => _build();

  _$ResolvePaymentDataAppModuleResponse _build() {
    final _$result = _$v ??
        _$ResolvePaymentDataAppModuleResponse._(
          appId: appId,
          sessionId: sessionId,
          successUrl: successUrl,
          failUrl: failUrl,
          amount: amount,
          orderId: orderId,
          provider: provider,
          description: description,
          accountNo: accountNo,
          virtualAccount: virtualAccount,
          refTransactionId: refTransactionId,
          time: time,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
