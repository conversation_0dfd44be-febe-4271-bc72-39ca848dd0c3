// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_payment_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewPaymentCardResponse extends ReviewPaymentCardResponse {
  @override
  final String? transactionNumber;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewPaymentCardResponse(
          [void Function(ReviewPaymentCardResponseBuilder)? updates]) =>
      (ReviewPaymentCardResponseBuilder()..update(updates))._build();

  _$ReviewPaymentCardResponse._(
      {this.transactionNumber, this.transNextStep, this.content})
      : super._();
  @override
  ReviewPaymentCardResponse rebuild(
          void Function(ReviewPaymentCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewPaymentCardResponseBuilder toBuilder() =>
      ReviewPaymentCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewPaymentCardResponse &&
        transactionNumber == other.transactionNumber &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewPaymentCardResponse')
          ..add('transactionNumber', transactionNumber)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewPaymentCardResponseBuilder
    implements
        Builder<ReviewPaymentCardResponse, ReviewPaymentCardResponseBuilder> {
  _$ReviewPaymentCardResponse? _$v;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewPaymentCardResponseBuilder() {
    ReviewPaymentCardResponse._defaults(this);
  }

  ReviewPaymentCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNumber = $v.transactionNumber;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewPaymentCardResponse other) {
    _$v = other as _$ReviewPaymentCardResponse;
  }

  @override
  void update(void Function(ReviewPaymentCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewPaymentCardResponse build() => _build();

  _$ReviewPaymentCardResponse _build() {
    final _$result = _$v ??
        _$ReviewPaymentCardResponse._(
          transactionNumber: transactionNumber,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
