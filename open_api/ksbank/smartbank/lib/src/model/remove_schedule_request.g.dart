// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remove_schedule_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RemoveScheduleRequest extends RemoveScheduleRequest {
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final String? scheduleId;

  factory _$RemoveScheduleRequest(
          [void Function(RemoveScheduleRequestBuilder)? updates]) =>
      (RemoveScheduleRequestBuilder()..update(updates))._build();

  _$RemoveScheduleRequest._({this.softOtp, this.scheduleId}) : super._();
  @override
  RemoveScheduleRequest rebuild(
          void Function(RemoveScheduleRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RemoveScheduleRequestBuilder toBuilder() =>
      RemoveScheduleRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RemoveScheduleRequest &&
        softOtp == other.softOtp &&
        scheduleId == other.scheduleId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, scheduleId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RemoveScheduleRequest')
          ..add('softOtp', softOtp)
          ..add('scheduleId', scheduleId))
        .toString();
  }
}

class RemoveScheduleRequestBuilder
    implements Builder<RemoveScheduleRequest, RemoveScheduleRequestBuilder> {
  _$RemoveScheduleRequest? _$v;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  String? _scheduleId;
  String? get scheduleId => _$this._scheduleId;
  set scheduleId(String? scheduleId) => _$this._scheduleId = scheduleId;

  RemoveScheduleRequestBuilder() {
    RemoveScheduleRequest._defaults(this);
  }

  RemoveScheduleRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _softOtp = $v.softOtp?.toBuilder();
      _scheduleId = $v.scheduleId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RemoveScheduleRequest other) {
    _$v = other as _$RemoveScheduleRequest;
  }

  @override
  void update(void Function(RemoveScheduleRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RemoveScheduleRequest build() => _build();

  _$RemoveScheduleRequest _build() {
    _$RemoveScheduleRequest _$result;
    try {
      _$result = _$v ??
          _$RemoveScheduleRequest._(
            softOtp: _softOtp?.build(),
            scheduleId: scheduleId,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'RemoveScheduleRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
