// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_electric_water_transfer_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VnpElectricWaterTransferResponse
    extends VnpElectricWaterTransferResponse {
  @override
  final String? responseCode;
  @override
  final String? description;
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionDate;
  @override
  final String? amount;
  @override
  final String? fee;
  @override
  final String? vatAmount;
  @override
  final String? account;
  @override
  final String? rtxnNbr;
  @override
  final DateTime? createdDate;

  factory _$VnpElectricWaterTransferResponse(
          [void Function(VnpElectricWaterTransferResponseBuilder)? updates]) =>
      (VnpElectricWaterTransferResponseBuilder()..update(updates))._build();

  _$VnpElectricWaterTransferResponse._(
      {this.responseCode,
      this.description,
      this.transactionNo,
      this.transactionDate,
      this.amount,
      this.fee,
      this.vatAmount,
      this.account,
      this.rtxnNbr,
      this.createdDate})
      : super._();
  @override
  VnpElectricWaterTransferResponse rebuild(
          void Function(VnpElectricWaterTransferResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VnpElectricWaterTransferResponseBuilder toBuilder() =>
      VnpElectricWaterTransferResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VnpElectricWaterTransferResponse &&
        responseCode == other.responseCode &&
        description == other.description &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        amount == other.amount &&
        fee == other.fee &&
        vatAmount == other.vatAmount &&
        account == other.account &&
        rtxnNbr == other.rtxnNbr &&
        createdDate == other.createdDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, responseCode.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jc(_$hash, vatAmount.hashCode);
    _$hash = $jc(_$hash, account.hashCode);
    _$hash = $jc(_$hash, rtxnNbr.hashCode);
    _$hash = $jc(_$hash, createdDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VnpElectricWaterTransferResponse')
          ..add('responseCode', responseCode)
          ..add('description', description)
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('amount', amount)
          ..add('fee', fee)
          ..add('vatAmount', vatAmount)
          ..add('account', account)
          ..add('rtxnNbr', rtxnNbr)
          ..add('createdDate', createdDate))
        .toString();
  }
}

class VnpElectricWaterTransferResponseBuilder
    implements
        Builder<VnpElectricWaterTransferResponse,
            VnpElectricWaterTransferResponseBuilder> {
  _$VnpElectricWaterTransferResponse? _$v;

  String? _responseCode;
  String? get responseCode => _$this._responseCode;
  set responseCode(String? responseCode) => _$this._responseCode = responseCode;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _fee;
  String? get fee => _$this._fee;
  set fee(String? fee) => _$this._fee = fee;

  String? _vatAmount;
  String? get vatAmount => _$this._vatAmount;
  set vatAmount(String? vatAmount) => _$this._vatAmount = vatAmount;

  String? _account;
  String? get account => _$this._account;
  set account(String? account) => _$this._account = account;

  String? _rtxnNbr;
  String? get rtxnNbr => _$this._rtxnNbr;
  set rtxnNbr(String? rtxnNbr) => _$this._rtxnNbr = rtxnNbr;

  DateTime? _createdDate;
  DateTime? get createdDate => _$this._createdDate;
  set createdDate(DateTime? createdDate) => _$this._createdDate = createdDate;

  VnpElectricWaterTransferResponseBuilder() {
    VnpElectricWaterTransferResponse._defaults(this);
  }

  VnpElectricWaterTransferResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _responseCode = $v.responseCode;
      _description = $v.description;
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _amount = $v.amount;
      _fee = $v.fee;
      _vatAmount = $v.vatAmount;
      _account = $v.account;
      _rtxnNbr = $v.rtxnNbr;
      _createdDate = $v.createdDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VnpElectricWaterTransferResponse other) {
    _$v = other as _$VnpElectricWaterTransferResponse;
  }

  @override
  void update(void Function(VnpElectricWaterTransferResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VnpElectricWaterTransferResponse build() => _build();

  _$VnpElectricWaterTransferResponse _build() {
    final _$result = _$v ??
        _$VnpElectricWaterTransferResponse._(
          responseCode: responseCode,
          description: description,
          transactionNo: transactionNo,
          transactionDate: transactionDate,
          amount: amount,
          fee: fee,
          vatAmount: vatAmount,
          account: account,
          rtxnNbr: rtxnNbr,
          createdDate: createdDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
