// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'province_data.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ProvinceData extends ProvinceData {
  @override
  final String? uuid;
  @override
  final String? gsoCode;
  @override
  final String? klbCode;
  @override
  final String? nameEn;
  @override
  final String? nameVn;
  @override
  final bool? hasBranch;

  factory _$ProvinceData([void Function(ProvinceDataBuilder)? updates]) =>
      (ProvinceDataBuilder()..update(updates))._build();

  _$ProvinceData._(
      {this.uuid,
      this.gsoCode,
      this.klbCode,
      this.nameEn,
      this.nameVn,
      this.hasBranch})
      : super._();
  @override
  ProvinceData rebuild(void Function(ProvinceDataBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ProvinceDataBuilder toBuilder() => ProvinceDataBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ProvinceData &&
        uuid == other.uuid &&
        gsoCode == other.gsoCode &&
        klbCode == other.klbCode &&
        nameEn == other.nameEn &&
        nameVn == other.nameVn &&
        hasBranch == other.hasBranch;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, uuid.hashCode);
    _$hash = $jc(_$hash, gsoCode.hashCode);
    _$hash = $jc(_$hash, klbCode.hashCode);
    _$hash = $jc(_$hash, nameEn.hashCode);
    _$hash = $jc(_$hash, nameVn.hashCode);
    _$hash = $jc(_$hash, hasBranch.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ProvinceData')
          ..add('uuid', uuid)
          ..add('gsoCode', gsoCode)
          ..add('klbCode', klbCode)
          ..add('nameEn', nameEn)
          ..add('nameVn', nameVn)
          ..add('hasBranch', hasBranch))
        .toString();
  }
}

class ProvinceDataBuilder
    implements Builder<ProvinceData, ProvinceDataBuilder> {
  _$ProvinceData? _$v;

  String? _uuid;
  String? get uuid => _$this._uuid;
  set uuid(String? uuid) => _$this._uuid = uuid;

  String? _gsoCode;
  String? get gsoCode => _$this._gsoCode;
  set gsoCode(String? gsoCode) => _$this._gsoCode = gsoCode;

  String? _klbCode;
  String? get klbCode => _$this._klbCode;
  set klbCode(String? klbCode) => _$this._klbCode = klbCode;

  String? _nameEn;
  String? get nameEn => _$this._nameEn;
  set nameEn(String? nameEn) => _$this._nameEn = nameEn;

  String? _nameVn;
  String? get nameVn => _$this._nameVn;
  set nameVn(String? nameVn) => _$this._nameVn = nameVn;

  bool? _hasBranch;
  bool? get hasBranch => _$this._hasBranch;
  set hasBranch(bool? hasBranch) => _$this._hasBranch = hasBranch;

  ProvinceDataBuilder() {
    ProvinceData._defaults(this);
  }

  ProvinceDataBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _uuid = $v.uuid;
      _gsoCode = $v.gsoCode;
      _klbCode = $v.klbCode;
      _nameEn = $v.nameEn;
      _nameVn = $v.nameVn;
      _hasBranch = $v.hasBranch;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ProvinceData other) {
    _$v = other as _$ProvinceData;
  }

  @override
  void update(void Function(ProvinceDataBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ProvinceData build() => _build();

  _$ProvinceData _build() {
    final _$result = _$v ??
        _$ProvinceData._(
          uuid: uuid,
          gsoCode: gsoCode,
          klbCode: klbCode,
          nameEn: nameEn,
          nameVn: nameVn,
          hasBranch: hasBranch,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
