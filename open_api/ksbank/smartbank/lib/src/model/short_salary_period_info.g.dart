// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'short_salary_period_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ShortSalaryPeriodInfo extends ShortSalaryPeriodInfo {
  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? startDate;
  @override
  final String? endDate;

  factory _$ShortSalaryPeriodInfo(
          [void Function(ShortSalaryPeriodInfoBuilder)? updates]) =>
      (ShortSalaryPeriodInfoBuilder()..update(updates))._build();

  _$ShortSalaryPeriodInfo._({this.id, this.name, this.startDate, this.endDate})
      : super._();
  @override
  ShortSalaryPeriodInfo rebuild(
          void Function(ShortSalaryPeriodInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ShortSalaryPeriodInfoBuilder toBuilder() =>
      ShortSalaryPeriodInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ShortSalaryPeriodInfo &&
        id == other.id &&
        name == other.name &&
        startDate == other.startDate &&
        endDate == other.endDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jc(_$hash, endDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ShortSalaryPeriodInfo')
          ..add('id', id)
          ..add('name', name)
          ..add('startDate', startDate)
          ..add('endDate', endDate))
        .toString();
  }
}

class ShortSalaryPeriodInfoBuilder
    implements Builder<ShortSalaryPeriodInfo, ShortSalaryPeriodInfoBuilder> {
  _$ShortSalaryPeriodInfo? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _startDate;
  String? get startDate => _$this._startDate;
  set startDate(String? startDate) => _$this._startDate = startDate;

  String? _endDate;
  String? get endDate => _$this._endDate;
  set endDate(String? endDate) => _$this._endDate = endDate;

  ShortSalaryPeriodInfoBuilder() {
    ShortSalaryPeriodInfo._defaults(this);
  }

  ShortSalaryPeriodInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _startDate = $v.startDate;
      _endDate = $v.endDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ShortSalaryPeriodInfo other) {
    _$v = other as _$ShortSalaryPeriodInfo;
  }

  @override
  void update(void Function(ShortSalaryPeriodInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ShortSalaryPeriodInfo build() => _build();

  _$ShortSalaryPeriodInfo _build() {
    final _$result = _$v ??
        _$ShortSalaryPeriodInfo._(
          id: id,
          name: name,
          startDate: startDate,
          endDate: endDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
