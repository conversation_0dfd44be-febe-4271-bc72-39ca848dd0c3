// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resolve_payment_data_app_module_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResolvePaymentDataAppModuleRequest
    extends ResolvePaymentDataAppModuleRequest {
  @override
  final String? clientId;
  @override
  final String? data;
  @override
  final int? timestamp;
  @override
  final String? signature;

  factory _$ResolvePaymentDataAppModuleRequest(
          [void Function(ResolvePaymentDataAppModuleRequestBuilder)?
              updates]) =>
      (ResolvePaymentDataAppModuleRequestBuilder()..update(updates))._build();

  _$ResolvePaymentDataAppModuleRequest._(
      {this.clientId, this.data, this.timestamp, this.signature})
      : super._();
  @override
  ResolvePaymentDataAppModuleRequest rebuild(
          void Function(ResolvePaymentDataAppModuleRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResolvePaymentDataAppModuleRequestBuilder toBuilder() =>
      ResolvePaymentDataAppModuleRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResolvePaymentDataAppModuleRequest &&
        clientId == other.clientId &&
        data == other.data &&
        timestamp == other.timestamp &&
        signature == other.signature;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, clientId.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, timestamp.hashCode);
    _$hash = $jc(_$hash, signature.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResolvePaymentDataAppModuleRequest')
          ..add('clientId', clientId)
          ..add('data', data)
          ..add('timestamp', timestamp)
          ..add('signature', signature))
        .toString();
  }
}

class ResolvePaymentDataAppModuleRequestBuilder
    implements
        Builder<ResolvePaymentDataAppModuleRequest,
            ResolvePaymentDataAppModuleRequestBuilder> {
  _$ResolvePaymentDataAppModuleRequest? _$v;

  String? _clientId;
  String? get clientId => _$this._clientId;
  set clientId(String? clientId) => _$this._clientId = clientId;

  String? _data;
  String? get data => _$this._data;
  set data(String? data) => _$this._data = data;

  int? _timestamp;
  int? get timestamp => _$this._timestamp;
  set timestamp(int? timestamp) => _$this._timestamp = timestamp;

  String? _signature;
  String? get signature => _$this._signature;
  set signature(String? signature) => _$this._signature = signature;

  ResolvePaymentDataAppModuleRequestBuilder() {
    ResolvePaymentDataAppModuleRequest._defaults(this);
  }

  ResolvePaymentDataAppModuleRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _clientId = $v.clientId;
      _data = $v.data;
      _timestamp = $v.timestamp;
      _signature = $v.signature;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResolvePaymentDataAppModuleRequest other) {
    _$v = other as _$ResolvePaymentDataAppModuleRequest;
  }

  @override
  void update(
      void Function(ResolvePaymentDataAppModuleRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResolvePaymentDataAppModuleRequest build() => _build();

  _$ResolvePaymentDataAppModuleRequest _build() {
    final _$result = _$v ??
        _$ResolvePaymentDataAppModuleRequest._(
          clientId: clientId,
          data: data,
          timestamp: timestamp,
          signature: signature,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
