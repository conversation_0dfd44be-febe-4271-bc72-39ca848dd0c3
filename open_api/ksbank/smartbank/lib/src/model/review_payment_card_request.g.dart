// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_payment_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewPaymentCardRequest extends ReviewPaymentCardRequest {
  @override
  final String? paymentType;
  @override
  final String? sourceAccountNo;
  @override
  final String? cifNo;
  @override
  final String? refCardId;
  @override
  final String? cardNo;
  @override
  final String? amount;
  @override
  final String? note;

  factory _$ReviewPaymentCardRequest(
          [void Function(ReviewPaymentCardRequestBuilder)? updates]) =>
      (ReviewPaymentCardRequestBuilder()..update(updates))._build();

  _$ReviewPaymentCardRequest._(
      {this.paymentType,
      this.sourceAccountNo,
      this.cifNo,
      this.refCardId,
      this.cardNo,
      this.amount,
      this.note})
      : super._();
  @override
  ReviewPaymentCardRequest rebuild(
          void Function(ReviewPaymentCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewPaymentCardRequestBuilder toBuilder() =>
      ReviewPaymentCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewPaymentCardRequest &&
        paymentType == other.paymentType &&
        sourceAccountNo == other.sourceAccountNo &&
        cifNo == other.cifNo &&
        refCardId == other.refCardId &&
        cardNo == other.cardNo &&
        amount == other.amount &&
        note == other.note;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, paymentType.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, refCardId.hashCode);
    _$hash = $jc(_$hash, cardNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, note.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewPaymentCardRequest')
          ..add('paymentType', paymentType)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('cifNo', cifNo)
          ..add('refCardId', refCardId)
          ..add('cardNo', cardNo)
          ..add('amount', amount)
          ..add('note', note))
        .toString();
  }
}

class ReviewPaymentCardRequestBuilder
    implements
        Builder<ReviewPaymentCardRequest, ReviewPaymentCardRequestBuilder> {
  _$ReviewPaymentCardRequest? _$v;

  String? _paymentType;
  String? get paymentType => _$this._paymentType;
  set paymentType(String? paymentType) => _$this._paymentType = paymentType;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _refCardId;
  String? get refCardId => _$this._refCardId;
  set refCardId(String? refCardId) => _$this._refCardId = refCardId;

  String? _cardNo;
  String? get cardNo => _$this._cardNo;
  set cardNo(String? cardNo) => _$this._cardNo = cardNo;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _note;
  String? get note => _$this._note;
  set note(String? note) => _$this._note = note;

  ReviewPaymentCardRequestBuilder() {
    ReviewPaymentCardRequest._defaults(this);
  }

  ReviewPaymentCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _paymentType = $v.paymentType;
      _sourceAccountNo = $v.sourceAccountNo;
      _cifNo = $v.cifNo;
      _refCardId = $v.refCardId;
      _cardNo = $v.cardNo;
      _amount = $v.amount;
      _note = $v.note;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewPaymentCardRequest other) {
    _$v = other as _$ReviewPaymentCardRequest;
  }

  @override
  void update(void Function(ReviewPaymentCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewPaymentCardRequest build() => _build();

  _$ReviewPaymentCardRequest _build() {
    final _$result = _$v ??
        _$ReviewPaymentCardRequest._(
          paymentType: paymentType,
          sourceAccountNo: sourceAccountNo,
          cifNo: cifNo,
          refCardId: refCardId,
          cardNo: cardNo,
          amount: amount,
          note: note,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
