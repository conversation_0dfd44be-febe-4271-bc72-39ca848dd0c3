// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_review_electric_water_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VnpReviewElectricWaterResponse extends VnpReviewElectricWaterResponse {
  @override
  final String? responseCode;
  @override
  final String? description;
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionDate;
  @override
  final String? totalAmount;
  @override
  final DateTime? createdDate;

  factory _$VnpReviewElectricWaterResponse(
          [void Function(VnpReviewElectricWaterResponseBuilder)? updates]) =>
      (VnpReviewElectricWaterResponseBuilder()..update(updates))._build();

  _$VnpReviewElectricWaterResponse._(
      {this.responseCode,
      this.description,
      this.transactionNo,
      this.transactionDate,
      this.totalAmount,
      this.createdDate})
      : super._();
  @override
  VnpReviewElectricWaterResponse rebuild(
          void Function(VnpReviewElectricWaterResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VnpReviewElectricWaterResponseBuilder toBuilder() =>
      VnpReviewElectricWaterResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VnpReviewElectricWaterResponse &&
        responseCode == other.responseCode &&
        description == other.description &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        totalAmount == other.totalAmount &&
        createdDate == other.createdDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, responseCode.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, totalAmount.hashCode);
    _$hash = $jc(_$hash, createdDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VnpReviewElectricWaterResponse')
          ..add('responseCode', responseCode)
          ..add('description', description)
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('totalAmount', totalAmount)
          ..add('createdDate', createdDate))
        .toString();
  }
}

class VnpReviewElectricWaterResponseBuilder
    implements
        Builder<VnpReviewElectricWaterResponse,
            VnpReviewElectricWaterResponseBuilder> {
  _$VnpReviewElectricWaterResponse? _$v;

  String? _responseCode;
  String? get responseCode => _$this._responseCode;
  set responseCode(String? responseCode) => _$this._responseCode = responseCode;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _totalAmount;
  String? get totalAmount => _$this._totalAmount;
  set totalAmount(String? totalAmount) => _$this._totalAmount = totalAmount;

  DateTime? _createdDate;
  DateTime? get createdDate => _$this._createdDate;
  set createdDate(DateTime? createdDate) => _$this._createdDate = createdDate;

  VnpReviewElectricWaterResponseBuilder() {
    VnpReviewElectricWaterResponse._defaults(this);
  }

  VnpReviewElectricWaterResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _responseCode = $v.responseCode;
      _description = $v.description;
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _totalAmount = $v.totalAmount;
      _createdDate = $v.createdDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VnpReviewElectricWaterResponse other) {
    _$v = other as _$VnpReviewElectricWaterResponse;
  }

  @override
  void update(void Function(VnpReviewElectricWaterResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VnpReviewElectricWaterResponse build() => _build();

  _$VnpReviewElectricWaterResponse _build() {
    final _$result = _$v ??
        _$VnpReviewElectricWaterResponse._(
          responseCode: responseCode,
          description: description,
          transactionNo: transactionNo,
          transactionDate: transactionDate,
          totalAmount: totalAmount,
          createdDate: createdDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
