// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_payment_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewPaymentRequest extends ReviewPaymentRequest {
  @override
  final String? bankCif;
  @override
  final String? accountNoFrom;
  @override
  final String? customerCode;
  @override
  final String? supplierId;
  @override
  final int? amount;
  @override
  final int? discountAmount;
  @override
  final int? chargeAmount;
  @override
  final int? vatAmount;

  factory _$ReviewPaymentRequest(
          [void Function(ReviewPaymentRequestBuilder)? updates]) =>
      (ReviewPaymentRequestBuilder()..update(updates))._build();

  _$ReviewPaymentRequest._(
      {this.bankCif,
      this.accountNoFrom,
      this.customerCode,
      this.supplierId,
      this.amount,
      this.discountAmount,
      this.chargeAmount,
      this.vatAmount})
      : super._();
  @override
  ReviewPaymentRequest rebuild(
          void Function(ReviewPaymentRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewPaymentRequestBuilder toBuilder() =>
      ReviewPaymentRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewPaymentRequest &&
        bankCif == other.bankCif &&
        accountNoFrom == other.accountNoFrom &&
        customerCode == other.customerCode &&
        supplierId == other.supplierId &&
        amount == other.amount &&
        discountAmount == other.discountAmount &&
        chargeAmount == other.chargeAmount &&
        vatAmount == other.vatAmount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, discountAmount.hashCode);
    _$hash = $jc(_$hash, chargeAmount.hashCode);
    _$hash = $jc(_$hash, vatAmount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewPaymentRequest')
          ..add('bankCif', bankCif)
          ..add('accountNoFrom', accountNoFrom)
          ..add('customerCode', customerCode)
          ..add('supplierId', supplierId)
          ..add('amount', amount)
          ..add('discountAmount', discountAmount)
          ..add('chargeAmount', chargeAmount)
          ..add('vatAmount', vatAmount))
        .toString();
  }
}

class ReviewPaymentRequestBuilder
    implements Builder<ReviewPaymentRequest, ReviewPaymentRequestBuilder> {
  _$ReviewPaymentRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  int? _amount;
  int? get amount => _$this._amount;
  set amount(int? amount) => _$this._amount = amount;

  int? _discountAmount;
  int? get discountAmount => _$this._discountAmount;
  set discountAmount(int? discountAmount) =>
      _$this._discountAmount = discountAmount;

  int? _chargeAmount;
  int? get chargeAmount => _$this._chargeAmount;
  set chargeAmount(int? chargeAmount) => _$this._chargeAmount = chargeAmount;

  int? _vatAmount;
  int? get vatAmount => _$this._vatAmount;
  set vatAmount(int? vatAmount) => _$this._vatAmount = vatAmount;

  ReviewPaymentRequestBuilder() {
    ReviewPaymentRequest._defaults(this);
  }

  ReviewPaymentRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _accountNoFrom = $v.accountNoFrom;
      _customerCode = $v.customerCode;
      _supplierId = $v.supplierId;
      _amount = $v.amount;
      _discountAmount = $v.discountAmount;
      _chargeAmount = $v.chargeAmount;
      _vatAmount = $v.vatAmount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewPaymentRequest other) {
    _$v = other as _$ReviewPaymentRequest;
  }

  @override
  void update(void Function(ReviewPaymentRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewPaymentRequest build() => _build();

  _$ReviewPaymentRequest _build() {
    final _$result = _$v ??
        _$ReviewPaymentRequest._(
          bankCif: bankCif,
          accountNoFrom: accountNoFrom,
          customerCode: customerCode,
          supplierId: supplierId,
          amount: amount,
          discountAmount: discountAmount,
          chargeAmount: chargeAmount,
          vatAmount: vatAmount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
