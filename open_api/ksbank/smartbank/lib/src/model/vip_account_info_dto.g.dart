// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_account_info_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VipAccountInfoDto extends VipAccountInfoDto {
  @override
  final String? recordID;
  @override
  final String? vipAccountNo;
  @override
  final String? vipGroup;
  @override
  final num? feeAmount;
  @override
  final String? feeAmountStr;
  @override
  final String? checkCode;
  @override
  final String? checkName;

  factory _$VipAccountInfoDto(
          [void Function(VipAccountInfoDtoBuilder)? updates]) =>
      (VipAccountInfoDtoBuilder()..update(updates))._build();

  _$VipAccountInfoDto._(
      {this.recordID,
      this.vipAccountNo,
      this.vipGroup,
      this.feeAmount,
      this.feeAmountStr,
      this.checkCode,
      this.checkName})
      : super._();
  @override
  VipAccountInfoDto rebuild(void Function(VipAccountInfoDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VipAccountInfoDtoBuilder toBuilder() =>
      VipAccountInfoDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VipAccountInfoDto &&
        recordID == other.recordID &&
        vipAccountNo == other.vipAccountNo &&
        vipGroup == other.vipGroup &&
        feeAmount == other.feeAmount &&
        feeAmountStr == other.feeAmountStr &&
        checkCode == other.checkCode &&
        checkName == other.checkName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recordID.hashCode);
    _$hash = $jc(_$hash, vipAccountNo.hashCode);
    _$hash = $jc(_$hash, vipGroup.hashCode);
    _$hash = $jc(_$hash, feeAmount.hashCode);
    _$hash = $jc(_$hash, feeAmountStr.hashCode);
    _$hash = $jc(_$hash, checkCode.hashCode);
    _$hash = $jc(_$hash, checkName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VipAccountInfoDto')
          ..add('recordID', recordID)
          ..add('vipAccountNo', vipAccountNo)
          ..add('vipGroup', vipGroup)
          ..add('feeAmount', feeAmount)
          ..add('feeAmountStr', feeAmountStr)
          ..add('checkCode', checkCode)
          ..add('checkName', checkName))
        .toString();
  }
}

class VipAccountInfoDtoBuilder
    implements Builder<VipAccountInfoDto, VipAccountInfoDtoBuilder> {
  _$VipAccountInfoDto? _$v;

  String? _recordID;
  String? get recordID => _$this._recordID;
  set recordID(String? recordID) => _$this._recordID = recordID;

  String? _vipAccountNo;
  String? get vipAccountNo => _$this._vipAccountNo;
  set vipAccountNo(String? vipAccountNo) => _$this._vipAccountNo = vipAccountNo;

  String? _vipGroup;
  String? get vipGroup => _$this._vipGroup;
  set vipGroup(String? vipGroup) => _$this._vipGroup = vipGroup;

  num? _feeAmount;
  num? get feeAmount => _$this._feeAmount;
  set feeAmount(num? feeAmount) => _$this._feeAmount = feeAmount;

  String? _feeAmountStr;
  String? get feeAmountStr => _$this._feeAmountStr;
  set feeAmountStr(String? feeAmountStr) => _$this._feeAmountStr = feeAmountStr;

  String? _checkCode;
  String? get checkCode => _$this._checkCode;
  set checkCode(String? checkCode) => _$this._checkCode = checkCode;

  String? _checkName;
  String? get checkName => _$this._checkName;
  set checkName(String? checkName) => _$this._checkName = checkName;

  VipAccountInfoDtoBuilder() {
    VipAccountInfoDto._defaults(this);
  }

  VipAccountInfoDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recordID = $v.recordID;
      _vipAccountNo = $v.vipAccountNo;
      _vipGroup = $v.vipGroup;
      _feeAmount = $v.feeAmount;
      _feeAmountStr = $v.feeAmountStr;
      _checkCode = $v.checkCode;
      _checkName = $v.checkName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VipAccountInfoDto other) {
    _$v = other as _$VipAccountInfoDto;
  }

  @override
  void update(void Function(VipAccountInfoDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VipAccountInfoDto build() => _build();

  _$VipAccountInfoDto _build() {
    final _$result = _$v ??
        _$VipAccountInfoDto._(
          recordID: recordID,
          vipAccountNo: vipAccountNo,
          vipGroup: vipGroup,
          feeAmount: feeAmount,
          feeAmountStr: feeAmountStr,
          checkCode: checkCode,
          checkName: checkName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
