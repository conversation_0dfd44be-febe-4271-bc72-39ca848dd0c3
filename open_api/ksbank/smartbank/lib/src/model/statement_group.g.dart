// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statement_group.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$StatementGroup extends StatementGroup {
  @override
  final String? name;
  @override
  final BuiltList<StatementItem>? items;

  factory _$StatementGroup([void Function(StatementGroupBuilder)? updates]) =>
      (StatementGroupBuilder()..update(updates))._build();

  _$StatementGroup._({this.name, this.items}) : super._();
  @override
  StatementGroup rebuild(void Function(StatementGroupBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  StatementGroupBuilder toBuilder() => StatementGroupBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is StatementGroup &&
        name == other.name &&
        items == other.items;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, items.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'StatementGroup')
          ..add('name', name)
          ..add('items', items))
        .toString();
  }
}

class StatementGroupBuilder
    implements Builder<StatementGroup, StatementGroupBuilder> {
  _$StatementGroup? _$v;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  ListBuilder<StatementItem>? _items;
  ListBuilder<StatementItem> get items =>
      _$this._items ??= ListBuilder<StatementItem>();
  set items(ListBuilder<StatementItem>? items) => _$this._items = items;

  StatementGroupBuilder() {
    StatementGroup._defaults(this);
  }

  StatementGroupBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _name = $v.name;
      _items = $v.items?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(StatementGroup other) {
    _$v = other as _$StatementGroup;
  }

  @override
  void update(void Function(StatementGroupBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  StatementGroup build() => _build();

  _$StatementGroup _build() {
    _$StatementGroup _$result;
    try {
      _$result = _$v ??
          _$StatementGroup._(
            name: name,
            items: _items?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'items';
        _items?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'StatementGroup', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
