// GENERATED CODE - DO NOT MODIFY BY HAND

part of 're_issue_virtual_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReIssueVirtualCardRequest extends ReIssueVirtualCardRequest {
  @override
  final String? refCardId;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;

  factory _$ReIssueVirtualCardRequest(
          [void Function(ReIssueVirtualCardRequestBuilder)? updates]) =>
      (ReIssueVirtualCardRequestBuilder()..update(updates))._build();

  _$ReIssueVirtualCardRequest._({this.refCardId, this.verifySoftOtp})
      : super._();
  @override
  ReIssueVirtualCardRequest rebuild(
          void Function(ReIssueVirtualCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReIssueVirtualCardRequestBuilder toBuilder() =>
      ReIssueVirtualCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReIssueVirtualCardRequest &&
        refCardId == other.refCardId &&
        verifySoftOtp == other.verifySoftOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, refCardId.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReIssueVirtualCardRequest')
          ..add('refCardId', refCardId)
          ..add('verifySoftOtp', verifySoftOtp))
        .toString();
  }
}

class ReIssueVirtualCardRequestBuilder
    implements
        Builder<ReIssueVirtualCardRequest, ReIssueVirtualCardRequestBuilder> {
  _$ReIssueVirtualCardRequest? _$v;

  String? _refCardId;
  String? get refCardId => _$this._refCardId;
  set refCardId(String? refCardId) => _$this._refCardId = refCardId;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  ReIssueVirtualCardRequestBuilder() {
    ReIssueVirtualCardRequest._defaults(this);
  }

  ReIssueVirtualCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _refCardId = $v.refCardId;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReIssueVirtualCardRequest other) {
    _$v = other as _$ReIssueVirtualCardRequest;
  }

  @override
  void update(void Function(ReIssueVirtualCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReIssueVirtualCardRequest build() => _build();

  _$ReIssueVirtualCardRequest _build() {
    _$ReIssueVirtualCardRequest _$result;
    try {
      _$result = _$v ??
          _$ReIssueVirtualCardRequest._(
            refCardId: refCardId,
            verifySoftOtp: _verifySoftOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ReIssueVirtualCardRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
