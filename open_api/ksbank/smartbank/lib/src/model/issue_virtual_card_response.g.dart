// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'issue_virtual_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$IssueVirtualCardResponse extends IssueVirtualCardResponse {
  @override
  final String? bankCIF;
  @override
  final String? cardId;
  @override
  final String? cardMask;
  @override
  final String? cardType;
  @override
  final String? cardHolderName;
  @override
  final String? productId;
  @override
  final String? cardExpire;

  factory _$IssueVirtualCardResponse(
          [void Function(IssueVirtualCardResponseBuilder)? updates]) =>
      (IssueVirtualCardResponseBuilder()..update(updates))._build();

  _$IssueVirtualCardResponse._(
      {this.bankCIF,
      this.cardId,
      this.cardMask,
      this.cardType,
      this.cardHolderName,
      this.productId,
      this.cardExpire})
      : super._();
  @override
  IssueVirtualCardResponse rebuild(
          void Function(IssueVirtualCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  IssueVirtualCardResponseBuilder toBuilder() =>
      IssueVirtualCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is IssueVirtualCardResponse &&
        bankCIF == other.bankCIF &&
        cardId == other.cardId &&
        cardMask == other.cardMask &&
        cardType == other.cardType &&
        cardHolderName == other.cardHolderName &&
        productId == other.productId &&
        cardExpire == other.cardExpire;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCIF.hashCode);
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, cardMask.hashCode);
    _$hash = $jc(_$hash, cardType.hashCode);
    _$hash = $jc(_$hash, cardHolderName.hashCode);
    _$hash = $jc(_$hash, productId.hashCode);
    _$hash = $jc(_$hash, cardExpire.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'IssueVirtualCardResponse')
          ..add('bankCIF', bankCIF)
          ..add('cardId', cardId)
          ..add('cardMask', cardMask)
          ..add('cardType', cardType)
          ..add('cardHolderName', cardHolderName)
          ..add('productId', productId)
          ..add('cardExpire', cardExpire))
        .toString();
  }
}

class IssueVirtualCardResponseBuilder
    implements
        Builder<IssueVirtualCardResponse, IssueVirtualCardResponseBuilder> {
  _$IssueVirtualCardResponse? _$v;

  String? _bankCIF;
  String? get bankCIF => _$this._bankCIF;
  set bankCIF(String? bankCIF) => _$this._bankCIF = bankCIF;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  String? _cardMask;
  String? get cardMask => _$this._cardMask;
  set cardMask(String? cardMask) => _$this._cardMask = cardMask;

  String? _cardType;
  String? get cardType => _$this._cardType;
  set cardType(String? cardType) => _$this._cardType = cardType;

  String? _cardHolderName;
  String? get cardHolderName => _$this._cardHolderName;
  set cardHolderName(String? cardHolderName) =>
      _$this._cardHolderName = cardHolderName;

  String? _productId;
  String? get productId => _$this._productId;
  set productId(String? productId) => _$this._productId = productId;

  String? _cardExpire;
  String? get cardExpire => _$this._cardExpire;
  set cardExpire(String? cardExpire) => _$this._cardExpire = cardExpire;

  IssueVirtualCardResponseBuilder() {
    IssueVirtualCardResponse._defaults(this);
  }

  IssueVirtualCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCIF = $v.bankCIF;
      _cardId = $v.cardId;
      _cardMask = $v.cardMask;
      _cardType = $v.cardType;
      _cardHolderName = $v.cardHolderName;
      _productId = $v.productId;
      _cardExpire = $v.cardExpire;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(IssueVirtualCardResponse other) {
    _$v = other as _$IssueVirtualCardResponse;
  }

  @override
  void update(void Function(IssueVirtualCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  IssueVirtualCardResponse build() => _build();

  _$IssueVirtualCardResponse _build() {
    final _$result = _$v ??
        _$IssueVirtualCardResponse._(
          bankCIF: bankCIF,
          cardId: cardId,
          cardMask: cardMask,
          cardType: cardType,
          cardHolderName: cardHolderName,
          productId: productId,
          cardExpire: cardExpire,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
