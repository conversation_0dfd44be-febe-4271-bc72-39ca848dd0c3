// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_service_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateServiceResponse extends UpdateServiceResponse {
  @override
  final bool? success;

  factory _$UpdateServiceResponse(
          [void Function(UpdateServiceResponseBuilder)? updates]) =>
      (UpdateServiceResponseBuilder()..update(updates))._build();

  _$UpdateServiceResponse._({this.success}) : super._();
  @override
  UpdateServiceResponse rebuild(
          void Function(UpdateServiceResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateServiceResponseBuilder toBuilder() =>
      UpdateServiceResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateServiceResponse && success == other.success;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateServiceResponse')
          ..add('success', success))
        .toString();
  }
}

class UpdateServiceResponseBuilder
    implements Builder<UpdateServiceResponse, UpdateServiceResponseBuilder> {
  _$UpdateServiceResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  UpdateServiceResponseBuilder() {
    UpdateServiceResponse._defaults(this);
  }

  UpdateServiceResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateServiceResponse other) {
    _$v = other as _$UpdateServiceResponse;
  }

  @override
  void update(void Function(UpdateServiceResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateServiceResponse build() => _build();

  _$UpdateServiceResponse _build() {
    final _$result = _$v ??
        _$UpdateServiceResponse._(
          success: success,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
