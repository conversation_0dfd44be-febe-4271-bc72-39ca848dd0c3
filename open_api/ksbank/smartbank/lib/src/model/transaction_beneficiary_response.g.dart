// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_beneficiary_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionBeneficiaryResponse extends TransactionBeneficiaryResponse {
  @override
  final String? beneficiaryName;

  factory _$TransactionBeneficiaryResponse(
          [void Function(TransactionBeneficiaryResponseBuilder)? updates]) =>
      (TransactionBeneficiaryResponseBuilder()..update(updates))._build();

  _$TransactionBeneficiaryResponse._({this.beneficiaryName}) : super._();
  @override
  TransactionBeneficiaryResponse rebuild(
          void Function(TransactionBeneficiaryResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionBeneficiaryResponseBuilder toBuilder() =>
      TransactionBeneficiaryResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionBeneficiaryResponse &&
        beneficiaryName == other.beneficiaryName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, beneficiaryName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionBeneficiaryResponse')
          ..add('beneficiaryName', beneficiaryName))
        .toString();
  }
}

class TransactionBeneficiaryResponseBuilder
    implements
        Builder<TransactionBeneficiaryResponse,
            TransactionBeneficiaryResponseBuilder> {
  _$TransactionBeneficiaryResponse? _$v;

  String? _beneficiaryName;
  String? get beneficiaryName => _$this._beneficiaryName;
  set beneficiaryName(String? beneficiaryName) =>
      _$this._beneficiaryName = beneficiaryName;

  TransactionBeneficiaryResponseBuilder() {
    TransactionBeneficiaryResponse._defaults(this);
  }

  TransactionBeneficiaryResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _beneficiaryName = $v.beneficiaryName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionBeneficiaryResponse other) {
    _$v = other as _$TransactionBeneficiaryResponse;
  }

  @override
  void update(void Function(TransactionBeneficiaryResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionBeneficiaryResponse build() => _build();

  _$TransactionBeneficiaryResponse _build() {
    final _$result = _$v ??
        _$TransactionBeneficiaryResponse._(
          beneficiaryName: beneficiaryName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
