// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_service_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VnpServiceInfo extends VnpServiceInfo {
  @override
  final String? billId;
  @override
  final String? billType;
  @override
  final String? billTypeName;
  @override
  final String? electricityCode;
  @override
  final String? incurrentDate;
  @override
  final String? amount;

  factory _$VnpServiceInfo([void Function(VnpServiceInfoBuilder)? updates]) =>
      (VnpServiceInfoBuilder()..update(updates))._build();

  _$VnpServiceInfo._(
      {this.billId,
      this.billType,
      this.billTypeName,
      this.electricityCode,
      this.incurrentDate,
      this.amount})
      : super._();
  @override
  VnpServiceInfo rebuild(void Function(VnpServiceInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VnpServiceInfoBuilder toBuilder() => VnpServiceInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VnpServiceInfo &&
        billId == other.billId &&
        billType == other.billType &&
        billTypeName == other.billTypeName &&
        electricityCode == other.electricityCode &&
        incurrentDate == other.incurrentDate &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, billId.hashCode);
    _$hash = $jc(_$hash, billType.hashCode);
    _$hash = $jc(_$hash, billTypeName.hashCode);
    _$hash = $jc(_$hash, electricityCode.hashCode);
    _$hash = $jc(_$hash, incurrentDate.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VnpServiceInfo')
          ..add('billId', billId)
          ..add('billType', billType)
          ..add('billTypeName', billTypeName)
          ..add('electricityCode', electricityCode)
          ..add('incurrentDate', incurrentDate)
          ..add('amount', amount))
        .toString();
  }
}

class VnpServiceInfoBuilder
    implements Builder<VnpServiceInfo, VnpServiceInfoBuilder> {
  _$VnpServiceInfo? _$v;

  String? _billId;
  String? get billId => _$this._billId;
  set billId(String? billId) => _$this._billId = billId;

  String? _billType;
  String? get billType => _$this._billType;
  set billType(String? billType) => _$this._billType = billType;

  String? _billTypeName;
  String? get billTypeName => _$this._billTypeName;
  set billTypeName(String? billTypeName) => _$this._billTypeName = billTypeName;

  String? _electricityCode;
  String? get electricityCode => _$this._electricityCode;
  set electricityCode(String? electricityCode) =>
      _$this._electricityCode = electricityCode;

  String? _incurrentDate;
  String? get incurrentDate => _$this._incurrentDate;
  set incurrentDate(String? incurrentDate) =>
      _$this._incurrentDate = incurrentDate;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  VnpServiceInfoBuilder() {
    VnpServiceInfo._defaults(this);
  }

  VnpServiceInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _billId = $v.billId;
      _billType = $v.billType;
      _billTypeName = $v.billTypeName;
      _electricityCode = $v.electricityCode;
      _incurrentDate = $v.incurrentDate;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VnpServiceInfo other) {
    _$v = other as _$VnpServiceInfo;
  }

  @override
  void update(void Function(VnpServiceInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VnpServiceInfo build() => _build();

  _$VnpServiceInfo _build() {
    final _$result = _$v ??
        _$VnpServiceInfo._(
          billId: billId,
          billType: billType,
          billTypeName: billTypeName,
          electricityCode: electricityCode,
          incurrentDate: incurrentDate,
          amount: amount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
