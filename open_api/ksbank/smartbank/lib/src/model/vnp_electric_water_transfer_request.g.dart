// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_electric_water_transfer_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VnpElectricWaterTransferRequest
    extends VnpElectricWaterTransferRequest {
  @override
  final String? transactionNo;
  @override
  final String? serviceCode;
  @override
  final String? produceCode;
  @override
  final String? fromAcctNbr;
  @override
  final String? toAcctNbr;
  @override
  final String? amount;
  @override
  final String? fee;
  @override
  final String? descText;

  factory _$VnpElectricWaterTransferRequest(
          [void Function(VnpElectricWaterTransferRequestBuilder)? updates]) =>
      (VnpElectricWaterTransferRequestBuilder()..update(updates))._build();

  _$VnpElectricWaterTransferRequest._(
      {this.transactionNo,
      this.serviceCode,
      this.produceCode,
      this.fromAcctNbr,
      this.toAcctNbr,
      this.amount,
      this.fee,
      this.descText})
      : super._();
  @override
  VnpElectricWaterTransferRequest rebuild(
          void Function(VnpElectricWaterTransferRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VnpElectricWaterTransferRequestBuilder toBuilder() =>
      VnpElectricWaterTransferRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VnpElectricWaterTransferRequest &&
        transactionNo == other.transactionNo &&
        serviceCode == other.serviceCode &&
        produceCode == other.produceCode &&
        fromAcctNbr == other.fromAcctNbr &&
        toAcctNbr == other.toAcctNbr &&
        amount == other.amount &&
        fee == other.fee &&
        descText == other.descText;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, serviceCode.hashCode);
    _$hash = $jc(_$hash, produceCode.hashCode);
    _$hash = $jc(_$hash, fromAcctNbr.hashCode);
    _$hash = $jc(_$hash, toAcctNbr.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jc(_$hash, descText.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VnpElectricWaterTransferRequest')
          ..add('transactionNo', transactionNo)
          ..add('serviceCode', serviceCode)
          ..add('produceCode', produceCode)
          ..add('fromAcctNbr', fromAcctNbr)
          ..add('toAcctNbr', toAcctNbr)
          ..add('amount', amount)
          ..add('fee', fee)
          ..add('descText', descText))
        .toString();
  }
}

class VnpElectricWaterTransferRequestBuilder
    implements
        Builder<VnpElectricWaterTransferRequest,
            VnpElectricWaterTransferRequestBuilder> {
  _$VnpElectricWaterTransferRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _serviceCode;
  String? get serviceCode => _$this._serviceCode;
  set serviceCode(String? serviceCode) => _$this._serviceCode = serviceCode;

  String? _produceCode;
  String? get produceCode => _$this._produceCode;
  set produceCode(String? produceCode) => _$this._produceCode = produceCode;

  String? _fromAcctNbr;
  String? get fromAcctNbr => _$this._fromAcctNbr;
  set fromAcctNbr(String? fromAcctNbr) => _$this._fromAcctNbr = fromAcctNbr;

  String? _toAcctNbr;
  String? get toAcctNbr => _$this._toAcctNbr;
  set toAcctNbr(String? toAcctNbr) => _$this._toAcctNbr = toAcctNbr;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _fee;
  String? get fee => _$this._fee;
  set fee(String? fee) => _$this._fee = fee;

  String? _descText;
  String? get descText => _$this._descText;
  set descText(String? descText) => _$this._descText = descText;

  VnpElectricWaterTransferRequestBuilder() {
    VnpElectricWaterTransferRequest._defaults(this);
  }

  VnpElectricWaterTransferRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _serviceCode = $v.serviceCode;
      _produceCode = $v.produceCode;
      _fromAcctNbr = $v.fromAcctNbr;
      _toAcctNbr = $v.toAcctNbr;
      _amount = $v.amount;
      _fee = $v.fee;
      _descText = $v.descText;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VnpElectricWaterTransferRequest other) {
    _$v = other as _$VnpElectricWaterTransferRequest;
  }

  @override
  void update(void Function(VnpElectricWaterTransferRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VnpElectricWaterTransferRequest build() => _build();

  _$VnpElectricWaterTransferRequest _build() {
    final _$result = _$v ??
        _$VnpElectricWaterTransferRequest._(
          transactionNo: transactionNo,
          serviceCode: serviceCode,
          produceCode: produceCode,
          fromAcctNbr: fromAcctNbr,
          toAcctNbr: toAcctNbr,
          amount: amount,
          fee: fee,
          descText: descText,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
