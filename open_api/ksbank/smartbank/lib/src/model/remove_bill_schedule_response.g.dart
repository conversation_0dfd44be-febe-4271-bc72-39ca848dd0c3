// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remove_bill_schedule_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RemoveBillScheduleResponse extends RemoveBillScheduleResponse {
  @override
  final String? scheduleId;

  factory _$RemoveBillScheduleResponse(
          [void Function(RemoveBillScheduleResponseBuilder)? updates]) =>
      (RemoveBillScheduleResponseBuilder()..update(updates))._build();

  _$RemoveBillScheduleResponse._({this.scheduleId}) : super._();
  @override
  RemoveBillScheduleResponse rebuild(
          void Function(RemoveBillScheduleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RemoveBillScheduleResponseBuilder toBuilder() =>
      RemoveBillScheduleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RemoveBillScheduleResponse &&
        scheduleId == other.scheduleId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, scheduleId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RemoveBillScheduleResponse')
          ..add('scheduleId', scheduleId))
        .toString();
  }
}

class RemoveBillScheduleResponseBuilder
    implements
        Builder<RemoveBillScheduleResponse, RemoveBillScheduleResponseBuilder> {
  _$RemoveBillScheduleResponse? _$v;

  String? _scheduleId;
  String? get scheduleId => _$this._scheduleId;
  set scheduleId(String? scheduleId) => _$this._scheduleId = scheduleId;

  RemoveBillScheduleResponseBuilder() {
    RemoveBillScheduleResponse._defaults(this);
  }

  RemoveBillScheduleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _scheduleId = $v.scheduleId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RemoveBillScheduleResponse other) {
    _$v = other as _$RemoveBillScheduleResponse;
  }

  @override
  void update(void Function(RemoveBillScheduleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RemoveBillScheduleResponse build() => _build();

  _$RemoveBillScheduleResponse _build() {
    final _$result = _$v ??
        _$RemoveBillScheduleResponse._(
          scheduleId: scheduleId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
