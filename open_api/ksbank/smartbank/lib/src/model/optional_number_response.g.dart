// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'optional_number_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OptionalNumberResponse extends OptionalNumberResponse {
  @override
  final BuiltList<AccountNumberInfo>? accountDtoList;

  factory _$OptionalNumberResponse(
          [void Function(OptionalNumberResponseBuilder)? updates]) =>
      (OptionalNumberResponseBuilder()..update(updates))._build();

  _$OptionalNumberResponse._({this.accountDtoList}) : super._();
  @override
  OptionalNumberResponse rebuild(
          void Function(OptionalNumberResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OptionalNumberResponseBuilder toBuilder() =>
      OptionalNumberResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OptionalNumberResponse &&
        accountDtoList == other.accountDtoList;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountDtoList.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OptionalNumberResponse')
          ..add('accountDtoList', accountDtoList))
        .toString();
  }
}

class OptionalNumberResponseBuilder
    implements Builder<OptionalNumberResponse, OptionalNumberResponseBuilder> {
  _$OptionalNumberResponse? _$v;

  ListBuilder<AccountNumberInfo>? _accountDtoList;
  ListBuilder<AccountNumberInfo> get accountDtoList =>
      _$this._accountDtoList ??= ListBuilder<AccountNumberInfo>();
  set accountDtoList(ListBuilder<AccountNumberInfo>? accountDtoList) =>
      _$this._accountDtoList = accountDtoList;

  OptionalNumberResponseBuilder() {
    OptionalNumberResponse._defaults(this);
  }

  OptionalNumberResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountDtoList = $v.accountDtoList?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OptionalNumberResponse other) {
    _$v = other as _$OptionalNumberResponse;
  }

  @override
  void update(void Function(OptionalNumberResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OptionalNumberResponse build() => _build();

  _$OptionalNumberResponse _build() {
    _$OptionalNumberResponse _$result;
    try {
      _$result = _$v ??
          _$OptionalNumberResponse._(
            accountDtoList: _accountDtoList?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'accountDtoList';
        _accountDtoList?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'OptionalNumberResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
