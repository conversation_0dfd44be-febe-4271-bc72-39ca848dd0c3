// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_inter_bank_citad_v2_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferInterBankCitadV2Request
    extends ReviewTransferInterBankCitadV2Request {
  @override
  final String? description;
  @override
  final int? transactionGroup;
  @override
  final String? accountNoFrom;
  @override
  final String? accountNoTo;
  @override
  final int? isAccount;
  @override
  final String? targetBankCode;
  @override
  final num? amount;
  @override
  final int? regionId;
  @override
  final int? bankId;
  @override
  final String? branchId;
  @override
  final String? branchName;
  @override
  final String? beneficiaryName;
  @override
  final String? idCard;
  @override
  final DateTime? issueDate;
  @override
  final String? placeBy;

  factory _$ReviewTransferInterBankCitadV2Request(
          [void Function(ReviewTransferInterBankCitadV2RequestBuilder)?
              updates]) =>
      (ReviewTransferInterBankCitadV2RequestBuilder()..update(updates))
          ._build();

  _$ReviewTransferInterBankCitadV2Request._(
      {this.description,
      this.transactionGroup,
      this.accountNoFrom,
      this.accountNoTo,
      this.isAccount,
      this.targetBankCode,
      this.amount,
      this.regionId,
      this.bankId,
      this.branchId,
      this.branchName,
      this.beneficiaryName,
      this.idCard,
      this.issueDate,
      this.placeBy})
      : super._();
  @override
  ReviewTransferInterBankCitadV2Request rebuild(
          void Function(ReviewTransferInterBankCitadV2RequestBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferInterBankCitadV2RequestBuilder toBuilder() =>
      ReviewTransferInterBankCitadV2RequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferInterBankCitadV2Request &&
        description == other.description &&
        transactionGroup == other.transactionGroup &&
        accountNoFrom == other.accountNoFrom &&
        accountNoTo == other.accountNoTo &&
        isAccount == other.isAccount &&
        targetBankCode == other.targetBankCode &&
        amount == other.amount &&
        regionId == other.regionId &&
        bankId == other.bankId &&
        branchId == other.branchId &&
        branchName == other.branchName &&
        beneficiaryName == other.beneficiaryName &&
        idCard == other.idCard &&
        issueDate == other.issueDate &&
        placeBy == other.placeBy;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, regionId.hashCode);
    _$hash = $jc(_$hash, bankId.hashCode);
    _$hash = $jc(_$hash, branchId.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, beneficiaryName.hashCode);
    _$hash = $jc(_$hash, idCard.hashCode);
    _$hash = $jc(_$hash, issueDate.hashCode);
    _$hash = $jc(_$hash, placeBy.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewTransferInterBankCitadV2Request')
          ..add('description', description)
          ..add('transactionGroup', transactionGroup)
          ..add('accountNoFrom', accountNoFrom)
          ..add('accountNoTo', accountNoTo)
          ..add('isAccount', isAccount)
          ..add('targetBankCode', targetBankCode)
          ..add('amount', amount)
          ..add('regionId', regionId)
          ..add('bankId', bankId)
          ..add('branchId', branchId)
          ..add('branchName', branchName)
          ..add('beneficiaryName', beneficiaryName)
          ..add('idCard', idCard)
          ..add('issueDate', issueDate)
          ..add('placeBy', placeBy))
        .toString();
  }
}

class ReviewTransferInterBankCitadV2RequestBuilder
    implements
        Builder<ReviewTransferInterBankCitadV2Request,
            ReviewTransferInterBankCitadV2RequestBuilder> {
  _$ReviewTransferInterBankCitadV2Request? _$v;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  int? _regionId;
  int? get regionId => _$this._regionId;
  set regionId(int? regionId) => _$this._regionId = regionId;

  int? _bankId;
  int? get bankId => _$this._bankId;
  set bankId(int? bankId) => _$this._bankId = bankId;

  String? _branchId;
  String? get branchId => _$this._branchId;
  set branchId(String? branchId) => _$this._branchId = branchId;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _beneficiaryName;
  String? get beneficiaryName => _$this._beneficiaryName;
  set beneficiaryName(String? beneficiaryName) =>
      _$this._beneficiaryName = beneficiaryName;

  String? _idCard;
  String? get idCard => _$this._idCard;
  set idCard(String? idCard) => _$this._idCard = idCard;

  DateTime? _issueDate;
  DateTime? get issueDate => _$this._issueDate;
  set issueDate(DateTime? issueDate) => _$this._issueDate = issueDate;

  String? _placeBy;
  String? get placeBy => _$this._placeBy;
  set placeBy(String? placeBy) => _$this._placeBy = placeBy;

  ReviewTransferInterBankCitadV2RequestBuilder() {
    ReviewTransferInterBankCitadV2Request._defaults(this);
  }

  ReviewTransferInterBankCitadV2RequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _description = $v.description;
      _transactionGroup = $v.transactionGroup;
      _accountNoFrom = $v.accountNoFrom;
      _accountNoTo = $v.accountNoTo;
      _isAccount = $v.isAccount;
      _targetBankCode = $v.targetBankCode;
      _amount = $v.amount;
      _regionId = $v.regionId;
      _bankId = $v.bankId;
      _branchId = $v.branchId;
      _branchName = $v.branchName;
      _beneficiaryName = $v.beneficiaryName;
      _idCard = $v.idCard;
      _issueDate = $v.issueDate;
      _placeBy = $v.placeBy;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferInterBankCitadV2Request other) {
    _$v = other as _$ReviewTransferInterBankCitadV2Request;
  }

  @override
  void update(
      void Function(ReviewTransferInterBankCitadV2RequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferInterBankCitadV2Request build() => _build();

  _$ReviewTransferInterBankCitadV2Request _build() {
    final _$result = _$v ??
        _$ReviewTransferInterBankCitadV2Request._(
          description: description,
          transactionGroup: transactionGroup,
          accountNoFrom: accountNoFrom,
          accountNoTo: accountNoTo,
          isAccount: isAccount,
          targetBankCode: targetBankCode,
          amount: amount,
          regionId: regionId,
          bankId: bankId,
          branchId: branchId,
          branchName: branchName,
          beneficiaryName: beneficiaryName,
          idCard: idCard,
          issueDate: issueDate,
          placeBy: placeBy,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
