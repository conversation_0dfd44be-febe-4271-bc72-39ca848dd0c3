//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_create_schedule_request.g.dart';

/// ReviewCreateScheduleRequest
///
/// Properties:
/// * [provider] - Nhà cung cấp dịch vụ thanh toán
/// * [supplierId] - <PERSON><PERSON> nhà cung cấp dịch vụ của hóa đơn : nước bến thành
/// * [customerCode] - <PERSON><PERSON> khách hàng theo nhà cung cấp
/// * [accountNo] - <PERSON><PERSON> tài khoản
@BuiltValue()
abstract class ReviewCreateScheduleRequest
    implements
        Built<ReviewCreateScheduleRequest, ReviewCreateScheduleRequestBuilder> {
  /// Nhà cung cấp dịch vụ thanh toán
  @BuiltValueField(wireName: r'provider')
  ReviewCreateScheduleRequestProviderEnum? get provider;
  // enum providerEnum {  PAYOO,  };

  /// Mã nhà cung cấp dịch vụ của hóa đơn : nước bến thành
  @BuiltValueField(wireName: r'supplierId')
  String? get supplierId;

  /// Mã khách hàng theo nhà cung cấp
  @BuiltValueField(wireName: r'customerCode')
  String? get customerCode;

  /// Số tài khoản
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  ReviewCreateScheduleRequest._();

  factory ReviewCreateScheduleRequest(
          [void updates(ReviewCreateScheduleRequestBuilder b)]) =
      _$ReviewCreateScheduleRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewCreateScheduleRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewCreateScheduleRequest> get serializer =>
      _$ReviewCreateScheduleRequestSerializer();
}

class _$ReviewCreateScheduleRequestSerializer
    implements PrimitiveSerializer<ReviewCreateScheduleRequest> {
  @override
  final Iterable<Type> types = const [
    ReviewCreateScheduleRequest,
    _$ReviewCreateScheduleRequest
  ];

  @override
  final String wireName = r'ReviewCreateScheduleRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewCreateScheduleRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.provider != null) {
      yield r'provider';
      yield serializers.serialize(
        object.provider,
        specifiedType:
            const FullType.nullable(ReviewCreateScheduleRequestProviderEnum),
      );
    }
    if (object.supplierId != null) {
      yield r'supplierId';
      yield serializers.serialize(
        object.supplierId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.customerCode != null) {
      yield r'customerCode';
      yield serializers.serialize(
        object.customerCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewCreateScheduleRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewCreateScheduleRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'provider':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                ReviewCreateScheduleRequestProviderEnum),
          ) as ReviewCreateScheduleRequestProviderEnum?;
          if (valueDes == null) continue;
          result.provider = valueDes;
          break;
        case r'supplierId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supplierId = valueDes;
          break;
        case r'customerCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.customerCode = valueDes;
          break;
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewCreateScheduleRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewCreateScheduleRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class ReviewCreateScheduleRequestProviderEnum extends EnumClass {
  /// Nhà cung cấp dịch vụ thanh toán
  @BuiltValueEnumConst(wireName: r'PAYOO')
  static const ReviewCreateScheduleRequestProviderEnum PAYOO =
      _$reviewCreateScheduleRequestProviderEnum_PAYOO;

  static Serializer<ReviewCreateScheduleRequestProviderEnum> get serializer =>
      _$reviewCreateScheduleRequestProviderEnumSerializer;

  const ReviewCreateScheduleRequestProviderEnum._(String name) : super(name);

  static BuiltSet<ReviewCreateScheduleRequestProviderEnum> get values =>
      _$reviewCreateScheduleRequestProviderEnumValues;
  static ReviewCreateScheduleRequestProviderEnum valueOf(String name) =>
      _$reviewCreateScheduleRequestProviderEnumValueOf(name);
}
