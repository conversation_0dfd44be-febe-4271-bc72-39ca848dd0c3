// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_schedule_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$InvoiceScheduleDto extends InvoiceScheduleDto {
  @override
  final String? id;
  @override
  final String? cifNo;
  @override
  final String? accountNo;
  @override
  final String? providerCode;
  @override
  final String? serviceId;
  @override
  final String? supplierId;
  @override
  final String? serviceCode;
  @override
  final String? serviceName;
  @override
  final String? supplierCode;
  @override
  final String? supplierName;
  @override
  final String? customerCode;
  @override
  final String? customerName;
  @override
  final String? customerAddress;
  @override
  final String? scheduleGroup;
  @override
  final String? scheduleFireAt;
  @override
  final DateTime? fromDate;
  @override
  final DateTime? toDate;
  @override
  final num? retryTimes;
  @override
  final String? allowAnotherAccounts;
  @override
  final DateTime? createdAt;
  @override
  final String? createdBy;
  @override
  final DateTime? updateAt;
  @override
  final String? updatedBy;

  factory _$InvoiceScheduleDto(
          [void Function(InvoiceScheduleDtoBuilder)? updates]) =>
      (InvoiceScheduleDtoBuilder()..update(updates))._build();

  _$InvoiceScheduleDto._(
      {this.id,
      this.cifNo,
      this.accountNo,
      this.providerCode,
      this.serviceId,
      this.supplierId,
      this.serviceCode,
      this.serviceName,
      this.supplierCode,
      this.supplierName,
      this.customerCode,
      this.customerName,
      this.customerAddress,
      this.scheduleGroup,
      this.scheduleFireAt,
      this.fromDate,
      this.toDate,
      this.retryTimes,
      this.allowAnotherAccounts,
      this.createdAt,
      this.createdBy,
      this.updateAt,
      this.updatedBy})
      : super._();
  @override
  InvoiceScheduleDto rebuild(
          void Function(InvoiceScheduleDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  InvoiceScheduleDtoBuilder toBuilder() =>
      InvoiceScheduleDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is InvoiceScheduleDto &&
        id == other.id &&
        cifNo == other.cifNo &&
        accountNo == other.accountNo &&
        providerCode == other.providerCode &&
        serviceId == other.serviceId &&
        supplierId == other.supplierId &&
        serviceCode == other.serviceCode &&
        serviceName == other.serviceName &&
        supplierCode == other.supplierCode &&
        supplierName == other.supplierName &&
        customerCode == other.customerCode &&
        customerName == other.customerName &&
        customerAddress == other.customerAddress &&
        scheduleGroup == other.scheduleGroup &&
        scheduleFireAt == other.scheduleFireAt &&
        fromDate == other.fromDate &&
        toDate == other.toDate &&
        retryTimes == other.retryTimes &&
        allowAnotherAccounts == other.allowAnotherAccounts &&
        createdAt == other.createdAt &&
        createdBy == other.createdBy &&
        updateAt == other.updateAt &&
        updatedBy == other.updatedBy;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, providerCode.hashCode);
    _$hash = $jc(_$hash, serviceId.hashCode);
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, serviceCode.hashCode);
    _$hash = $jc(_$hash, serviceName.hashCode);
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, customerAddress.hashCode);
    _$hash = $jc(_$hash, scheduleGroup.hashCode);
    _$hash = $jc(_$hash, scheduleFireAt.hashCode);
    _$hash = $jc(_$hash, fromDate.hashCode);
    _$hash = $jc(_$hash, toDate.hashCode);
    _$hash = $jc(_$hash, retryTimes.hashCode);
    _$hash = $jc(_$hash, allowAnotherAccounts.hashCode);
    _$hash = $jc(_$hash, createdAt.hashCode);
    _$hash = $jc(_$hash, createdBy.hashCode);
    _$hash = $jc(_$hash, updateAt.hashCode);
    _$hash = $jc(_$hash, updatedBy.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'InvoiceScheduleDto')
          ..add('id', id)
          ..add('cifNo', cifNo)
          ..add('accountNo', accountNo)
          ..add('providerCode', providerCode)
          ..add('serviceId', serviceId)
          ..add('supplierId', supplierId)
          ..add('serviceCode', serviceCode)
          ..add('serviceName', serviceName)
          ..add('supplierCode', supplierCode)
          ..add('supplierName', supplierName)
          ..add('customerCode', customerCode)
          ..add('customerName', customerName)
          ..add('customerAddress', customerAddress)
          ..add('scheduleGroup', scheduleGroup)
          ..add('scheduleFireAt', scheduleFireAt)
          ..add('fromDate', fromDate)
          ..add('toDate', toDate)
          ..add('retryTimes', retryTimes)
          ..add('allowAnotherAccounts', allowAnotherAccounts)
          ..add('createdAt', createdAt)
          ..add('createdBy', createdBy)
          ..add('updateAt', updateAt)
          ..add('updatedBy', updatedBy))
        .toString();
  }
}

class InvoiceScheduleDtoBuilder
    implements Builder<InvoiceScheduleDto, InvoiceScheduleDtoBuilder> {
  _$InvoiceScheduleDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _providerCode;
  String? get providerCode => _$this._providerCode;
  set providerCode(String? providerCode) => _$this._providerCode = providerCode;

  String? _serviceId;
  String? get serviceId => _$this._serviceId;
  set serviceId(String? serviceId) => _$this._serviceId = serviceId;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  String? _serviceCode;
  String? get serviceCode => _$this._serviceCode;
  set serviceCode(String? serviceCode) => _$this._serviceCode = serviceCode;

  String? _serviceName;
  String? get serviceName => _$this._serviceName;
  set serviceName(String? serviceName) => _$this._serviceName = serviceName;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _customerAddress;
  String? get customerAddress => _$this._customerAddress;
  set customerAddress(String? customerAddress) =>
      _$this._customerAddress = customerAddress;

  String? _scheduleGroup;
  String? get scheduleGroup => _$this._scheduleGroup;
  set scheduleGroup(String? scheduleGroup) =>
      _$this._scheduleGroup = scheduleGroup;

  String? _scheduleFireAt;
  String? get scheduleFireAt => _$this._scheduleFireAt;
  set scheduleFireAt(String? scheduleFireAt) =>
      _$this._scheduleFireAt = scheduleFireAt;

  DateTime? _fromDate;
  DateTime? get fromDate => _$this._fromDate;
  set fromDate(DateTime? fromDate) => _$this._fromDate = fromDate;

  DateTime? _toDate;
  DateTime? get toDate => _$this._toDate;
  set toDate(DateTime? toDate) => _$this._toDate = toDate;

  num? _retryTimes;
  num? get retryTimes => _$this._retryTimes;
  set retryTimes(num? retryTimes) => _$this._retryTimes = retryTimes;

  String? _allowAnotherAccounts;
  String? get allowAnotherAccounts => _$this._allowAnotherAccounts;
  set allowAnotherAccounts(String? allowAnotherAccounts) =>
      _$this._allowAnotherAccounts = allowAnotherAccounts;

  DateTime? _createdAt;
  DateTime? get createdAt => _$this._createdAt;
  set createdAt(DateTime? createdAt) => _$this._createdAt = createdAt;

  String? _createdBy;
  String? get createdBy => _$this._createdBy;
  set createdBy(String? createdBy) => _$this._createdBy = createdBy;

  DateTime? _updateAt;
  DateTime? get updateAt => _$this._updateAt;
  set updateAt(DateTime? updateAt) => _$this._updateAt = updateAt;

  String? _updatedBy;
  String? get updatedBy => _$this._updatedBy;
  set updatedBy(String? updatedBy) => _$this._updatedBy = updatedBy;

  InvoiceScheduleDtoBuilder() {
    InvoiceScheduleDto._defaults(this);
  }

  InvoiceScheduleDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _cifNo = $v.cifNo;
      _accountNo = $v.accountNo;
      _providerCode = $v.providerCode;
      _serviceId = $v.serviceId;
      _supplierId = $v.supplierId;
      _serviceCode = $v.serviceCode;
      _serviceName = $v.serviceName;
      _supplierCode = $v.supplierCode;
      _supplierName = $v.supplierName;
      _customerCode = $v.customerCode;
      _customerName = $v.customerName;
      _customerAddress = $v.customerAddress;
      _scheduleGroup = $v.scheduleGroup;
      _scheduleFireAt = $v.scheduleFireAt;
      _fromDate = $v.fromDate;
      _toDate = $v.toDate;
      _retryTimes = $v.retryTimes;
      _allowAnotherAccounts = $v.allowAnotherAccounts;
      _createdAt = $v.createdAt;
      _createdBy = $v.createdBy;
      _updateAt = $v.updateAt;
      _updatedBy = $v.updatedBy;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(InvoiceScheduleDto other) {
    _$v = other as _$InvoiceScheduleDto;
  }

  @override
  void update(void Function(InvoiceScheduleDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  InvoiceScheduleDto build() => _build();

  _$InvoiceScheduleDto _build() {
    final _$result = _$v ??
        _$InvoiceScheduleDto._(
          id: id,
          cifNo: cifNo,
          accountNo: accountNo,
          providerCode: providerCode,
          serviceId: serviceId,
          supplierId: supplierId,
          serviceCode: serviceCode,
          serviceName: serviceName,
          supplierCode: supplierCode,
          supplierName: supplierName,
          customerCode: customerCode,
          customerName: customerName,
          customerAddress: customerAddress,
          scheduleGroup: scheduleGroup,
          scheduleFireAt: scheduleFireAt,
          fromDate: fromDate,
          toDate: toDate,
          retryTimes: retryTimes,
          allowAnotherAccounts: allowAnotherAccounts,
          createdAt: createdAt,
          createdBy: createdBy,
          updateAt: updateAt,
          updatedBy: updatedBy,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
