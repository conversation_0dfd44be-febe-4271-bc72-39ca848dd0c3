// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_supplier_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateSupplierRequest extends UpdateSupplierRequest {
  @override
  final String? id;
  @override
  final String? code;
  @override
  final String? name;
  @override
  final String? parentId;
  @override
  final bool? active;

  factory _$UpdateSupplierRequest(
          [void Function(UpdateSupplierRequestBuilder)? updates]) =>
      (UpdateSupplierRequestBuilder()..update(updates))._build();

  _$UpdateSupplierRequest._(
      {this.id, this.code, this.name, this.parentId, this.active})
      : super._();
  @override
  UpdateSupplierRequest rebuild(
          void Function(UpdateSupplierRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateSupplierRequestBuilder toBuilder() =>
      UpdateSupplierRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateSupplierRequest &&
        id == other.id &&
        code == other.code &&
        name == other.name &&
        parentId == other.parentId &&
        active == other.active;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, active.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateSupplierRequest')
          ..add('id', id)
          ..add('code', code)
          ..add('name', name)
          ..add('parentId', parentId)
          ..add('active', active))
        .toString();
  }
}

class UpdateSupplierRequestBuilder
    implements Builder<UpdateSupplierRequest, UpdateSupplierRequestBuilder> {
  _$UpdateSupplierRequest? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  bool? _active;
  bool? get active => _$this._active;
  set active(bool? active) => _$this._active = active;

  UpdateSupplierRequestBuilder() {
    UpdateSupplierRequest._defaults(this);
  }

  UpdateSupplierRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _code = $v.code;
      _name = $v.name;
      _parentId = $v.parentId;
      _active = $v.active;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateSupplierRequest other) {
    _$v = other as _$UpdateSupplierRequest;
  }

  @override
  void update(void Function(UpdateSupplierRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateSupplierRequest build() => _build();

  _$UpdateSupplierRequest _build() {
    final _$result = _$v ??
        _$UpdateSupplierRequest._(
          id: id,
          code: code,
          name: name,
          parentId: parentId,
          active: active,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
