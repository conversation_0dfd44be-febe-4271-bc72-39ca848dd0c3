// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'region_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RegionRequest extends RegionRequest {
  @override
  final String? bankCif;

  factory _$RegionRequest([void Function(RegionRequestBuilder)? updates]) =>
      (RegionRequestBuilder()..update(updates))._build();

  _$RegionRequest._({this.bankCif}) : super._();
  @override
  RegionRequest rebuild(void Function(RegionRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RegionRequestBuilder toBuilder() => RegionRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RegionRequest && bankCif == other.bankCif;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RegionRequest')
          ..add('bankCif', bankCif))
        .toString();
  }
}

class RegionRequestBuilder
    implements Builder<RegionRequest, RegionRequestBuilder> {
  _$RegionRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  RegionRequestBuilder() {
    RegionRequest._defaults(this);
  }

  RegionRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RegionRequest other) {
    _$v = other as _$RegionRequest;
  }

  @override
  void update(void Function(RegionRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RegionRequest build() => _build();

  _$RegionRequest _build() {
    final _$result = _$v ??
        _$RegionRequest._(
          bankCif: bankCif,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
