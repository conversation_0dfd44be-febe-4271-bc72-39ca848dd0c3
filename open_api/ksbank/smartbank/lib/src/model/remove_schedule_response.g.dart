// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remove_schedule_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RemoveScheduleResponse extends RemoveScheduleResponse {
  @override
  final num? scheduleId;

  factory _$RemoveScheduleResponse(
          [void Function(RemoveScheduleResponseBuilder)? updates]) =>
      (RemoveScheduleResponseBuilder()..update(updates))._build();

  _$RemoveScheduleResponse._({this.scheduleId}) : super._();
  @override
  RemoveScheduleResponse rebuild(
          void Function(RemoveScheduleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RemoveScheduleResponseBuilder toBuilder() =>
      RemoveScheduleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RemoveScheduleResponse && scheduleId == other.scheduleId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, scheduleId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RemoveScheduleResponse')
          ..add('scheduleId', scheduleId))
        .toString();
  }
}

class RemoveScheduleResponseBuilder
    implements Builder<RemoveScheduleResponse, RemoveScheduleResponseBuilder> {
  _$RemoveScheduleResponse? _$v;

  num? _scheduleId;
  num? get scheduleId => _$this._scheduleId;
  set scheduleId(num? scheduleId) => _$this._scheduleId = scheduleId;

  RemoveScheduleResponseBuilder() {
    RemoveScheduleResponse._defaults(this);
  }

  RemoveScheduleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _scheduleId = $v.scheduleId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RemoveScheduleResponse other) {
    _$v = other as _$RemoveScheduleResponse;
  }

  @override
  void update(void Function(RemoveScheduleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RemoveScheduleResponse build() => _build();

  _$RemoveScheduleResponse _build() {
    final _$result = _$v ??
        _$RemoveScheduleResponse._(
          scheduleId: scheduleId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
