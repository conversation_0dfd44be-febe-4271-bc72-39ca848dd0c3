// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_online_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenOnlineSavingAccountResponse
    extends OpenOnlineSavingAccountResponse {
  @override
  final String? customerName;
  @override
  final String? finalAccountNumber;
  @override
  final String? transactionNo;
  @override
  final String? transactionSavingNumber;
  @override
  final String? accountNumber;
  @override
  final String? accountName;
  @override
  final String? currency;
  @override
  final num? balance;
  @override
  final num? rate;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final num? interestAmountEndOfTerm;

  factory _$OpenOnlineSavingAccountResponse(
          [void Function(OpenOnlineSavingAccountResponseBuilder)? updates]) =>
      (OpenOnlineSavingAccountResponseBuilder()..update(updates))._build();

  _$OpenOnlineSavingAccountResponse._(
      {this.customerName,
      this.finalAccountNumber,
      this.transactionNo,
      this.transactionSavingNumber,
      this.accountNumber,
      this.accountName,
      this.currency,
      this.balance,
      this.rate,
      this.contractDate,
      this.dueDate,
      this.interestAmountEndOfTerm})
      : super._();
  @override
  OpenOnlineSavingAccountResponse rebuild(
          void Function(OpenOnlineSavingAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenOnlineSavingAccountResponseBuilder toBuilder() =>
      OpenOnlineSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenOnlineSavingAccountResponse &&
        customerName == other.customerName &&
        finalAccountNumber == other.finalAccountNumber &&
        transactionNo == other.transactionNo &&
        transactionSavingNumber == other.transactionSavingNumber &&
        accountNumber == other.accountNumber &&
        accountName == other.accountName &&
        currency == other.currency &&
        balance == other.balance &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        interestAmountEndOfTerm == other.interestAmountEndOfTerm;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, finalAccountNumber.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionSavingNumber.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, interestAmountEndOfTerm.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenOnlineSavingAccountResponse')
          ..add('customerName', customerName)
          ..add('finalAccountNumber', finalAccountNumber)
          ..add('transactionNo', transactionNo)
          ..add('transactionSavingNumber', transactionSavingNumber)
          ..add('accountNumber', accountNumber)
          ..add('accountName', accountName)
          ..add('currency', currency)
          ..add('balance', balance)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('interestAmountEndOfTerm', interestAmountEndOfTerm))
        .toString();
  }
}

class OpenOnlineSavingAccountResponseBuilder
    implements
        Builder<OpenOnlineSavingAccountResponse,
            OpenOnlineSavingAccountResponseBuilder> {
  _$OpenOnlineSavingAccountResponse? _$v;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _finalAccountNumber;
  String? get finalAccountNumber => _$this._finalAccountNumber;
  set finalAccountNumber(String? finalAccountNumber) =>
      _$this._finalAccountNumber = finalAccountNumber;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _transactionSavingNumber;
  String? get transactionSavingNumber => _$this._transactionSavingNumber;
  set transactionSavingNumber(String? transactionSavingNumber) =>
      _$this._transactionSavingNumber = transactionSavingNumber;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  num? _rate;
  num? get rate => _$this._rate;
  set rate(num? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  num? _interestAmountEndOfTerm;
  num? get interestAmountEndOfTerm => _$this._interestAmountEndOfTerm;
  set interestAmountEndOfTerm(num? interestAmountEndOfTerm) =>
      _$this._interestAmountEndOfTerm = interestAmountEndOfTerm;

  OpenOnlineSavingAccountResponseBuilder() {
    OpenOnlineSavingAccountResponse._defaults(this);
  }

  OpenOnlineSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _customerName = $v.customerName;
      _finalAccountNumber = $v.finalAccountNumber;
      _transactionNo = $v.transactionNo;
      _transactionSavingNumber = $v.transactionSavingNumber;
      _accountNumber = $v.accountNumber;
      _accountName = $v.accountName;
      _currency = $v.currency;
      _balance = $v.balance;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _interestAmountEndOfTerm = $v.interestAmountEndOfTerm;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenOnlineSavingAccountResponse other) {
    _$v = other as _$OpenOnlineSavingAccountResponse;
  }

  @override
  void update(void Function(OpenOnlineSavingAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenOnlineSavingAccountResponse build() => _build();

  _$OpenOnlineSavingAccountResponse _build() {
    final _$result = _$v ??
        _$OpenOnlineSavingAccountResponse._(
          customerName: customerName,
          finalAccountNumber: finalAccountNumber,
          transactionNo: transactionNo,
          transactionSavingNumber: transactionSavingNumber,
          accountNumber: accountNumber,
          accountName: accountName,
          currency: currency,
          balance: balance,
          rate: rate,
          contractDate: contractDate,
          dueDate: dueDate,
          interestAmountEndOfTerm: interestAmountEndOfTerm,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
