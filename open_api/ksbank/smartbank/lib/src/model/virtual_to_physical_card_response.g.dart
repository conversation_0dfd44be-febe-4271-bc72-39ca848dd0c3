// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'virtual_to_physical_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VirtualToPhysicalCardResponse extends VirtualToPhysicalCardResponse {
  @override
  final String? cardId;
  @override
  final String? cardMask;
  @override
  final String? cardHolderName;
  @override
  final String? customerNumber;
  @override
  final String? addressTakeCard;

  factory _$VirtualToPhysicalCardResponse(
          [void Function(VirtualToPhysicalCardResponseBuilder)? updates]) =>
      (VirtualToPhysicalCardResponseBuilder()..update(updates))._build();

  _$VirtualToPhysicalCardResponse._(
      {this.cardId,
      this.cardMask,
      this.cardHolderName,
      this.customerNumber,
      this.addressTakeCard})
      : super._();
  @override
  VirtualToPhysicalCardResponse rebuild(
          void Function(VirtualToPhysicalCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VirtualToPhysicalCardResponseBuilder toBuilder() =>
      VirtualToPhysicalCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VirtualToPhysicalCardResponse &&
        cardId == other.cardId &&
        cardMask == other.cardMask &&
        cardHolderName == other.cardHolderName &&
        customerNumber == other.customerNumber &&
        addressTakeCard == other.addressTakeCard;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, cardMask.hashCode);
    _$hash = $jc(_$hash, cardHolderName.hashCode);
    _$hash = $jc(_$hash, customerNumber.hashCode);
    _$hash = $jc(_$hash, addressTakeCard.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VirtualToPhysicalCardResponse')
          ..add('cardId', cardId)
          ..add('cardMask', cardMask)
          ..add('cardHolderName', cardHolderName)
          ..add('customerNumber', customerNumber)
          ..add('addressTakeCard', addressTakeCard))
        .toString();
  }
}

class VirtualToPhysicalCardResponseBuilder
    implements
        Builder<VirtualToPhysicalCardResponse,
            VirtualToPhysicalCardResponseBuilder> {
  _$VirtualToPhysicalCardResponse? _$v;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  String? _cardMask;
  String? get cardMask => _$this._cardMask;
  set cardMask(String? cardMask) => _$this._cardMask = cardMask;

  String? _cardHolderName;
  String? get cardHolderName => _$this._cardHolderName;
  set cardHolderName(String? cardHolderName) =>
      _$this._cardHolderName = cardHolderName;

  String? _customerNumber;
  String? get customerNumber => _$this._customerNumber;
  set customerNumber(String? customerNumber) =>
      _$this._customerNumber = customerNumber;

  String? _addressTakeCard;
  String? get addressTakeCard => _$this._addressTakeCard;
  set addressTakeCard(String? addressTakeCard) =>
      _$this._addressTakeCard = addressTakeCard;

  VirtualToPhysicalCardResponseBuilder() {
    VirtualToPhysicalCardResponse._defaults(this);
  }

  VirtualToPhysicalCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardId = $v.cardId;
      _cardMask = $v.cardMask;
      _cardHolderName = $v.cardHolderName;
      _customerNumber = $v.customerNumber;
      _addressTakeCard = $v.addressTakeCard;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VirtualToPhysicalCardResponse other) {
    _$v = other as _$VirtualToPhysicalCardResponse;
  }

  @override
  void update(void Function(VirtualToPhysicalCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VirtualToPhysicalCardResponse build() => _build();

  _$VirtualToPhysicalCardResponse _build() {
    final _$result = _$v ??
        _$VirtualToPhysicalCardResponse._(
          cardId: cardId,
          cardMask: cardMask,
          cardHolderName: cardHolderName,
          customerNumber: customerNumber,
          addressTakeCard: addressTakeCard,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
