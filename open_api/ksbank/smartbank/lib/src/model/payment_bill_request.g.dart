// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_bill_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentBillRequest extends PaymentBillRequest {
  @override
  final String? transactionNo;
  @override
  final String? accountNo;
  @override
  final String? productCode;
  @override
  final String? customerCode;
  @override
  final BuiltList<PaymentBillDto>? bills;
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final num? amount;

  factory _$PaymentBillRequest(
          [void Function(PaymentBillRequestBuilder)? updates]) =>
      (PaymentBillRequestBuilder()..update(updates))._build();

  _$PaymentBillRequest._(
      {this.transactionNo,
      this.accountNo,
      this.productCode,
      this.customerCode,
      this.bills,
      this.softOtp,
      this.amount})
      : super._();
  @override
  PaymentBillRequest rebuild(
          void Function(PaymentBillRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentBillRequestBuilder toBuilder() =>
      PaymentBillRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentBillRequest &&
        transactionNo == other.transactionNo &&
        accountNo == other.accountNo &&
        productCode == other.productCode &&
        customerCode == other.customerCode &&
        bills == other.bills &&
        softOtp == other.softOtp &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, bills.hashCode);
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentBillRequest')
          ..add('transactionNo', transactionNo)
          ..add('accountNo', accountNo)
          ..add('productCode', productCode)
          ..add('customerCode', customerCode)
          ..add('bills', bills)
          ..add('softOtp', softOtp)
          ..add('amount', amount))
        .toString();
  }
}

class PaymentBillRequestBuilder
    implements Builder<PaymentBillRequest, PaymentBillRequestBuilder> {
  _$PaymentBillRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  ListBuilder<PaymentBillDto>? _bills;
  ListBuilder<PaymentBillDto> get bills =>
      _$this._bills ??= ListBuilder<PaymentBillDto>();
  set bills(ListBuilder<PaymentBillDto>? bills) => _$this._bills = bills;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  PaymentBillRequestBuilder() {
    PaymentBillRequest._defaults(this);
  }

  PaymentBillRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _accountNo = $v.accountNo;
      _productCode = $v.productCode;
      _customerCode = $v.customerCode;
      _bills = $v.bills?.toBuilder();
      _softOtp = $v.softOtp?.toBuilder();
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentBillRequest other) {
    _$v = other as _$PaymentBillRequest;
  }

  @override
  void update(void Function(PaymentBillRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentBillRequest build() => _build();

  _$PaymentBillRequest _build() {
    _$PaymentBillRequest _$result;
    try {
      _$result = _$v ??
          _$PaymentBillRequest._(
            transactionNo: transactionNo,
            accountNo: accountNo,
            productCode: productCode,
            customerCode: customerCode,
            bills: _bills?.build(),
            softOtp: _softOtp?.build(),
            amount: amount,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'bills';
        _bills?.build();
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PaymentBillRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
