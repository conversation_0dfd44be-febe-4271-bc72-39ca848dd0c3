// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_enterprise_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserEnterpriseInfo extends UserEnterpriseInfo {
  @override
  final String? id;
  @override
  final String? logo;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? employeeCode;
  @override
  final String? employeeName;
  @override
  final String? employeePosition;

  factory _$UserEnterpriseInfo(
          [void Function(UserEnterpriseInfoBuilder)? updates]) =>
      (UserEnterpriseInfoBuilder()..update(updates))._build();

  _$UserEnterpriseInfo._(
      {this.id,
      this.logo,
      this.name,
      this.description,
      this.employeeCode,
      this.employeeName,
      this.employeePosition})
      : super._();
  @override
  UserEnterpriseInfo rebuild(
          void Function(UserEnterpriseInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserEnterpriseInfoBuilder toBuilder() =>
      UserEnterpriseInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserEnterpriseInfo &&
        id == other.id &&
        logo == other.logo &&
        name == other.name &&
        description == other.description &&
        employeeCode == other.employeeCode &&
        employeeName == other.employeeName &&
        employeePosition == other.employeePosition;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, logo.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, employeeCode.hashCode);
    _$hash = $jc(_$hash, employeeName.hashCode);
    _$hash = $jc(_$hash, employeePosition.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserEnterpriseInfo')
          ..add('id', id)
          ..add('logo', logo)
          ..add('name', name)
          ..add('description', description)
          ..add('employeeCode', employeeCode)
          ..add('employeeName', employeeName)
          ..add('employeePosition', employeePosition))
        .toString();
  }
}

class UserEnterpriseInfoBuilder
    implements Builder<UserEnterpriseInfo, UserEnterpriseInfoBuilder> {
  _$UserEnterpriseInfo? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _logo;
  String? get logo => _$this._logo;
  set logo(String? logo) => _$this._logo = logo;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _employeeCode;
  String? get employeeCode => _$this._employeeCode;
  set employeeCode(String? employeeCode) => _$this._employeeCode = employeeCode;

  String? _employeeName;
  String? get employeeName => _$this._employeeName;
  set employeeName(String? employeeName) => _$this._employeeName = employeeName;

  String? _employeePosition;
  String? get employeePosition => _$this._employeePosition;
  set employeePosition(String? employeePosition) =>
      _$this._employeePosition = employeePosition;

  UserEnterpriseInfoBuilder() {
    UserEnterpriseInfo._defaults(this);
  }

  UserEnterpriseInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _logo = $v.logo;
      _name = $v.name;
      _description = $v.description;
      _employeeCode = $v.employeeCode;
      _employeeName = $v.employeeName;
      _employeePosition = $v.employeePosition;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserEnterpriseInfo other) {
    _$v = other as _$UserEnterpriseInfo;
  }

  @override
  void update(void Function(UserEnterpriseInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserEnterpriseInfo build() => _build();

  _$UserEnterpriseInfo _build() {
    final _$result = _$v ??
        _$UserEnterpriseInfo._(
          id: id,
          logo: logo,
          name: name,
          description: description,
          employeeCode: employeeCode,
          employeeName: employeeName,
          employeePosition: employeePosition,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
