// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_service_dto_base.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$InvoiceServiceDtoBase extends InvoiceServiceDtoBase {
  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? icon;

  factory _$InvoiceServiceDtoBase(
          [void Function(InvoiceServiceDtoBaseBuilder)? updates]) =>
      (InvoiceServiceDtoBaseBuilder()..update(updates))._build();

  _$InvoiceServiceDtoBase._({this.id, this.name, this.icon}) : super._();
  @override
  InvoiceServiceDtoBase rebuild(
          void Function(InvoiceServiceDtoBaseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  InvoiceServiceDtoBaseBuilder toBuilder() =>
      InvoiceServiceDtoBaseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is InvoiceServiceDtoBase &&
        id == other.id &&
        name == other.name &&
        icon == other.icon;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, icon.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'InvoiceServiceDtoBase')
          ..add('id', id)
          ..add('name', name)
          ..add('icon', icon))
        .toString();
  }
}

class InvoiceServiceDtoBaseBuilder
    implements Builder<InvoiceServiceDtoBase, InvoiceServiceDtoBaseBuilder> {
  _$InvoiceServiceDtoBase? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _icon;
  String? get icon => _$this._icon;
  set icon(String? icon) => _$this._icon = icon;

  InvoiceServiceDtoBaseBuilder() {
    InvoiceServiceDtoBase._defaults(this);
  }

  InvoiceServiceDtoBaseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _icon = $v.icon;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(InvoiceServiceDtoBase other) {
    _$v = other as _$InvoiceServiceDtoBase;
  }

  @override
  void update(void Function(InvoiceServiceDtoBaseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  InvoiceServiceDtoBase build() => _build();

  _$InvoiceServiceDtoBase _build() {
    final _$result = _$v ??
        _$InvoiceServiceDtoBase._(
          id: id,
          name: name,
          icon: icon,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
