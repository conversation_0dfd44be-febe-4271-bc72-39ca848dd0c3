// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_request_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferRequestDetailResponse extends TransferRequestDetailResponse {
  @override
  final String? id;
  @override
  final String? accountName;
  @override
  final String? cifNo;
  @override
  final num? amount;
  @override
  final String? description;
  @override
  final int? type;
  @override
  final String? accountNo;
  @override
  final String? bankName;
  @override
  final String? bankNameFull;
  @override
  final String? transferRequestLink;
  @override
  final String? vietQrCode;

  factory _$TransferRequestDetailResponse(
          [void Function(TransferRequestDetailResponseBuilder)? updates]) =>
      (TransferRequestDetailResponseBuilder()..update(updates))._build();

  _$TransferRequestDetailResponse._(
      {this.id,
      this.accountName,
      this.cifNo,
      this.amount,
      this.description,
      this.type,
      this.accountNo,
      this.bankName,
      this.bankNameFull,
      this.transferRequestLink,
      this.vietQrCode})
      : super._();
  @override
  TransferRequestDetailResponse rebuild(
          void Function(TransferRequestDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferRequestDetailResponseBuilder toBuilder() =>
      TransferRequestDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferRequestDetailResponse &&
        id == other.id &&
        accountName == other.accountName &&
        cifNo == other.cifNo &&
        amount == other.amount &&
        description == other.description &&
        type == other.type &&
        accountNo == other.accountNo &&
        bankName == other.bankName &&
        bankNameFull == other.bankNameFull &&
        transferRequestLink == other.transferRequestLink &&
        vietQrCode == other.vietQrCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, bankName.hashCode);
    _$hash = $jc(_$hash, bankNameFull.hashCode);
    _$hash = $jc(_$hash, transferRequestLink.hashCode);
    _$hash = $jc(_$hash, vietQrCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferRequestDetailResponse')
          ..add('id', id)
          ..add('accountName', accountName)
          ..add('cifNo', cifNo)
          ..add('amount', amount)
          ..add('description', description)
          ..add('type', type)
          ..add('accountNo', accountNo)
          ..add('bankName', bankName)
          ..add('bankNameFull', bankNameFull)
          ..add('transferRequestLink', transferRequestLink)
          ..add('vietQrCode', vietQrCode))
        .toString();
  }
}

class TransferRequestDetailResponseBuilder
    implements
        Builder<TransferRequestDetailResponse,
            TransferRequestDetailResponseBuilder> {
  _$TransferRequestDetailResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _type;
  int? get type => _$this._type;
  set type(int? type) => _$this._type = type;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _bankName;
  String? get bankName => _$this._bankName;
  set bankName(String? bankName) => _$this._bankName = bankName;

  String? _bankNameFull;
  String? get bankNameFull => _$this._bankNameFull;
  set bankNameFull(String? bankNameFull) => _$this._bankNameFull = bankNameFull;

  String? _transferRequestLink;
  String? get transferRequestLink => _$this._transferRequestLink;
  set transferRequestLink(String? transferRequestLink) =>
      _$this._transferRequestLink = transferRequestLink;

  String? _vietQrCode;
  String? get vietQrCode => _$this._vietQrCode;
  set vietQrCode(String? vietQrCode) => _$this._vietQrCode = vietQrCode;

  TransferRequestDetailResponseBuilder() {
    TransferRequestDetailResponse._defaults(this);
  }

  TransferRequestDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _accountName = $v.accountName;
      _cifNo = $v.cifNo;
      _amount = $v.amount;
      _description = $v.description;
      _type = $v.type;
      _accountNo = $v.accountNo;
      _bankName = $v.bankName;
      _bankNameFull = $v.bankNameFull;
      _transferRequestLink = $v.transferRequestLink;
      _vietQrCode = $v.vietQrCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferRequestDetailResponse other) {
    _$v = other as _$TransferRequestDetailResponse;
  }

  @override
  void update(void Function(TransferRequestDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferRequestDetailResponse build() => _build();

  _$TransferRequestDetailResponse _build() {
    final _$result = _$v ??
        _$TransferRequestDetailResponse._(
          id: id,
          accountName: accountName,
          cifNo: cifNo,
          amount: amount,
          description: description,
          type: type,
          accountNo: accountNo,
          bankName: bankName,
          bankNameFull: bankNameFull,
          transferRequestLink: transferRequestLink,
          vietQrCode: vietQrCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
