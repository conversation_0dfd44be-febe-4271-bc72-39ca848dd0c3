// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_supplier_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$InvoiceSupplierDto extends InvoiceSupplierDto {
  @override
  final String? supplierId;
  @override
  final String? serviceId;
  @override
  final String? supplierCode;
  @override
  final String? supplierName;
  @override
  final String? parentId;
  @override
  final bool? isActive;
  @override
  final DateTime? createdAt;

  factory _$InvoiceSupplierDto(
          [void Function(InvoiceSupplierDtoBuilder)? updates]) =>
      (InvoiceSupplierDtoBuilder()..update(updates))._build();

  _$InvoiceSupplierDto._(
      {this.supplierId,
      this.serviceId,
      this.supplierCode,
      this.supplierName,
      this.parentId,
      this.isActive,
      this.createdAt})
      : super._();
  @override
  InvoiceSupplierDto rebuild(
          void Function(InvoiceSupplierDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  InvoiceSupplierDtoBuilder toBuilder() =>
      InvoiceSupplierDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is InvoiceSupplierDto &&
        supplierId == other.supplierId &&
        serviceId == other.serviceId &&
        supplierCode == other.supplierCode &&
        supplierName == other.supplierName &&
        parentId == other.parentId &&
        isActive == other.isActive &&
        createdAt == other.createdAt;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, serviceId.hashCode);
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, isActive.hashCode);
    _$hash = $jc(_$hash, createdAt.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'InvoiceSupplierDto')
          ..add('supplierId', supplierId)
          ..add('serviceId', serviceId)
          ..add('supplierCode', supplierCode)
          ..add('supplierName', supplierName)
          ..add('parentId', parentId)
          ..add('isActive', isActive)
          ..add('createdAt', createdAt))
        .toString();
  }
}

class InvoiceSupplierDtoBuilder
    implements Builder<InvoiceSupplierDto, InvoiceSupplierDtoBuilder> {
  _$InvoiceSupplierDto? _$v;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  String? _serviceId;
  String? get serviceId => _$this._serviceId;
  set serviceId(String? serviceId) => _$this._serviceId = serviceId;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  bool? _isActive;
  bool? get isActive => _$this._isActive;
  set isActive(bool? isActive) => _$this._isActive = isActive;

  DateTime? _createdAt;
  DateTime? get createdAt => _$this._createdAt;
  set createdAt(DateTime? createdAt) => _$this._createdAt = createdAt;

  InvoiceSupplierDtoBuilder() {
    InvoiceSupplierDto._defaults(this);
  }

  InvoiceSupplierDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _supplierId = $v.supplierId;
      _serviceId = $v.serviceId;
      _supplierCode = $v.supplierCode;
      _supplierName = $v.supplierName;
      _parentId = $v.parentId;
      _isActive = $v.isActive;
      _createdAt = $v.createdAt;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(InvoiceSupplierDto other) {
    _$v = other as _$InvoiceSupplierDto;
  }

  @override
  void update(void Function(InvoiceSupplierDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  InvoiceSupplierDto build() => _build();

  _$InvoiceSupplierDto _build() {
    final _$result = _$v ??
        _$InvoiceSupplierDto._(
          supplierId: supplierId,
          serviceId: serviceId,
          supplierCode: supplierCode,
          supplierName: supplierName,
          parentId: parentId,
          isActive: isActive,
          createdAt: createdAt,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
