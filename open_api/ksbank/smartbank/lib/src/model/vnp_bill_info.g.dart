// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_bill_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VnpBillInfo extends VnpBillInfo {
  @override
  final String? billId;
  @override
  final String? billCode;
  @override
  final String? billType;
  @override
  final String? billTypeName;
  @override
  final String? amount;
  @override
  final String? description;
  @override
  final String? fromDate;
  @override
  final String? toDate;
  @override
  final String? price;
  @override
  final String? numberOfHouseHolds;
  @override
  final String? type;

  factory _$VnpBillInfo([void Function(VnpBillInfoBuilder)? updates]) =>
      (VnpBillInfoBuilder()..update(updates))._build();

  _$VnpBillInfo._(
      {this.billId,
      this.billCode,
      this.billType,
      this.billTypeName,
      this.amount,
      this.description,
      this.fromDate,
      this.toDate,
      this.price,
      this.numberOfHouseHolds,
      this.type})
      : super._();
  @override
  VnpBillInfo rebuild(void Function(VnpBillInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VnpBillInfoBuilder toBuilder() => VnpBillInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VnpBillInfo &&
        billId == other.billId &&
        billCode == other.billCode &&
        billType == other.billType &&
        billTypeName == other.billTypeName &&
        amount == other.amount &&
        description == other.description &&
        fromDate == other.fromDate &&
        toDate == other.toDate &&
        price == other.price &&
        numberOfHouseHolds == other.numberOfHouseHolds &&
        type == other.type;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, billId.hashCode);
    _$hash = $jc(_$hash, billCode.hashCode);
    _$hash = $jc(_$hash, billType.hashCode);
    _$hash = $jc(_$hash, billTypeName.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, fromDate.hashCode);
    _$hash = $jc(_$hash, toDate.hashCode);
    _$hash = $jc(_$hash, price.hashCode);
    _$hash = $jc(_$hash, numberOfHouseHolds.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VnpBillInfo')
          ..add('billId', billId)
          ..add('billCode', billCode)
          ..add('billType', billType)
          ..add('billTypeName', billTypeName)
          ..add('amount', amount)
          ..add('description', description)
          ..add('fromDate', fromDate)
          ..add('toDate', toDate)
          ..add('price', price)
          ..add('numberOfHouseHolds', numberOfHouseHolds)
          ..add('type', type))
        .toString();
  }
}

class VnpBillInfoBuilder implements Builder<VnpBillInfo, VnpBillInfoBuilder> {
  _$VnpBillInfo? _$v;

  String? _billId;
  String? get billId => _$this._billId;
  set billId(String? billId) => _$this._billId = billId;

  String? _billCode;
  String? get billCode => _$this._billCode;
  set billCode(String? billCode) => _$this._billCode = billCode;

  String? _billType;
  String? get billType => _$this._billType;
  set billType(String? billType) => _$this._billType = billType;

  String? _billTypeName;
  String? get billTypeName => _$this._billTypeName;
  set billTypeName(String? billTypeName) => _$this._billTypeName = billTypeName;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _fromDate;
  String? get fromDate => _$this._fromDate;
  set fromDate(String? fromDate) => _$this._fromDate = fromDate;

  String? _toDate;
  String? get toDate => _$this._toDate;
  set toDate(String? toDate) => _$this._toDate = toDate;

  String? _price;
  String? get price => _$this._price;
  set price(String? price) => _$this._price = price;

  String? _numberOfHouseHolds;
  String? get numberOfHouseHolds => _$this._numberOfHouseHolds;
  set numberOfHouseHolds(String? numberOfHouseHolds) =>
      _$this._numberOfHouseHolds = numberOfHouseHolds;

  String? _type;
  String? get type => _$this._type;
  set type(String? type) => _$this._type = type;

  VnpBillInfoBuilder() {
    VnpBillInfo._defaults(this);
  }

  VnpBillInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _billId = $v.billId;
      _billCode = $v.billCode;
      _billType = $v.billType;
      _billTypeName = $v.billTypeName;
      _amount = $v.amount;
      _description = $v.description;
      _fromDate = $v.fromDate;
      _toDate = $v.toDate;
      _price = $v.price;
      _numberOfHouseHolds = $v.numberOfHouseHolds;
      _type = $v.type;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VnpBillInfo other) {
    _$v = other as _$VnpBillInfo;
  }

  @override
  void update(void Function(VnpBillInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VnpBillInfo build() => _build();

  _$VnpBillInfo _build() {
    final _$result = _$v ??
        _$VnpBillInfo._(
          billId: billId,
          billCode: billCode,
          billType: billType,
          billTypeName: billTypeName,
          amount: amount,
          description: description,
          fromDate: fromDate,
          toDate: toDate,
          price: price,
          numberOfHouseHolds: numberOfHouseHolds,
          type: type,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
