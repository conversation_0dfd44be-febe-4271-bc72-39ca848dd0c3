// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vvip_open_method.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const VvipOpenMethod _$QR_CODE = const VvipOpenMethod._('QR_CODE');
const VvipOpenMethod _$MANUAL = const VvipOpenMethod._('MANUAL');

VvipOpenMethod _$valueOf(String name) {
  switch (name) {
    case 'QR_CODE':
      return _$QR_CODE;
    case 'MANUAL':
      return _$MANUAL;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<VvipOpenMethod> _$values =
    BuiltSet<VvipOpenMethod>(const <VvipOpenMethod>[
  _$QR_CODE,
  _$MANUAL,
]);

class _$VvipOpenMethodMeta {
  const _$VvipOpenMethodMeta();
  VvipOpenMethod get QR_CODE => _$QR_CODE;
  VvipOpenMethod get MANUAL => _$MANUAL;
  VvipOpenMethod valueOf(String name) => _$valueOf(name);
  BuiltSet<VvipOpenMethod> get values => _$values;
}

abstract class _$VvipOpenMethodMixin {
  // ignore: non_constant_identifier_names
  _$VvipOpenMethodMeta get VvipOpenMethod => const _$VvipOpenMethodMeta();
}

Serializer<VvipOpenMethod> _$vvipOpenMethodSerializer =
    _$VvipOpenMethodSerializer();

class _$VvipOpenMethodSerializer
    implements PrimitiveSerializer<VvipOpenMethod> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'QR_CODE': 'QR_CODE',
    'MANUAL': 'MANUAL',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'QR_CODE': 'QR_CODE',
    'MANUAL': 'MANUAL',
  };

  @override
  final Iterable<Type> types = const <Type>[VvipOpenMethod];
  @override
  final String wireName = 'VvipOpenMethod';

  @override
  Object serialize(Serializers serializers, VvipOpenMethod object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  VvipOpenMethod deserialize(Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      VvipOpenMethod.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
