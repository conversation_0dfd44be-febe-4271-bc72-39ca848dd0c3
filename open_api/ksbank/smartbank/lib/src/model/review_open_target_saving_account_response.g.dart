// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_open_target_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewOpenTargetSavingAccountResponse
    extends ReviewOpenTargetSavingAccountResponse {
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionDate;
  @override
  final String? sourceAccountNo;
  @override
  final double? amount;
  @override
  final String? currency;
  @override
  final String? termId;
  @override
  final String? termName;
  @override
  final double? rate;
  @override
  final DateTime? contractDate;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewOpenTargetSavingAccountResponse(
          [void Function(ReviewOpenTargetSavingAccountResponseBuilder)?
              updates]) =>
      (ReviewOpenTargetSavingAccountResponseBuilder()..update(updates))
          ._build();

  _$ReviewOpenTargetSavingAccountResponse._(
      {this.transactionNo,
      this.transactionDate,
      this.sourceAccountNo,
      this.amount,
      this.currency,
      this.termId,
      this.termName,
      this.rate,
      this.contractDate,
      this.transNextStep,
      this.content})
      : super._();
  @override
  ReviewOpenTargetSavingAccountResponse rebuild(
          void Function(ReviewOpenTargetSavingAccountResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewOpenTargetSavingAccountResponseBuilder toBuilder() =>
      ReviewOpenTargetSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewOpenTargetSavingAccountResponse &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        sourceAccountNo == other.sourceAccountNo &&
        amount == other.amount &&
        currency == other.currency &&
        termId == other.termId &&
        termName == other.termName &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, termId.hashCode);
    _$hash = $jc(_$hash, termName.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewOpenTargetSavingAccountResponse')
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('amount', amount)
          ..add('currency', currency)
          ..add('termId', termId)
          ..add('termName', termName)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewOpenTargetSavingAccountResponseBuilder
    implements
        Builder<ReviewOpenTargetSavingAccountResponse,
            ReviewOpenTargetSavingAccountResponseBuilder> {
  _$ReviewOpenTargetSavingAccountResponse? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  String? _termId;
  String? get termId => _$this._termId;
  set termId(String? termId) => _$this._termId = termId;

  String? _termName;
  String? get termName => _$this._termName;
  set termName(String? termName) => _$this._termName = termName;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewOpenTargetSavingAccountResponseBuilder() {
    ReviewOpenTargetSavingAccountResponse._defaults(this);
  }

  ReviewOpenTargetSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _sourceAccountNo = $v.sourceAccountNo;
      _amount = $v.amount;
      _currency = $v.currency;
      _termId = $v.termId;
      _termName = $v.termName;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewOpenTargetSavingAccountResponse other) {
    _$v = other as _$ReviewOpenTargetSavingAccountResponse;
  }

  @override
  void update(
      void Function(ReviewOpenTargetSavingAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewOpenTargetSavingAccountResponse build() => _build();

  _$ReviewOpenTargetSavingAccountResponse _build() {
    final _$result = _$v ??
        _$ReviewOpenTargetSavingAccountResponse._(
          transactionNo: transactionNo,
          transactionDate: transactionDate,
          sourceAccountNo: sourceAccountNo,
          amount: amount,
          currency: currency,
          termId: termId,
          termName: termName,
          rate: rate,
          contractDate: contractDate,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
