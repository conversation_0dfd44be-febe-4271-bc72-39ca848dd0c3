// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_contact_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListContactResponse extends ListContactResponse {
  @override
  final BuiltList<ContactsDto>? list;

  factory _$ListContactResponse(
          [void Function(ListContactResponseBuilder)? updates]) =>
      (ListContactResponseBuilder()..update(updates))._build();

  _$ListContactResponse._({this.list}) : super._();
  @override
  ListContactResponse rebuild(
          void Function(ListContactResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListContactResponseBuilder toBuilder() =>
      ListContactResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListContactResponse && list == other.list;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, list.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListContactResponse')
          ..add('list', list))
        .toString();
  }
}

class ListContactResponseBuilder
    implements Builder<ListContactResponse, ListContactResponseBuilder> {
  _$ListContactResponse? _$v;

  ListBuilder<ContactsDto>? _list;
  ListBuilder<ContactsDto> get list =>
      _$this._list ??= ListBuilder<ContactsDto>();
  set list(ListBuilder<ContactsDto>? list) => _$this._list = list;

  ListContactResponseBuilder() {
    ListContactResponse._defaults(this);
  }

  ListContactResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _list = $v.list?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListContactResponse other) {
    _$v = other as _$ListContactResponse;
  }

  @override
  void update(void Function(ListContactResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListContactResponse build() => _build();

  _$ListContactResponse _build() {
    _$ListContactResponse _$result;
    try {
      _$result = _$v ??
          _$ListContactResponse._(
            list: _list?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'list';
        _list?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListContactResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
