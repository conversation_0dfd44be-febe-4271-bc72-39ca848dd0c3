// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_count_pin_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResetCountPINCardRequest extends ResetCountPINCardRequest {
  @override
  final String? cardId;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;

  factory _$ResetCountPINCardRequest(
          [void Function(ResetCountPINCardRequestBuilder)? updates]) =>
      (ResetCountPINCardRequestBuilder()..update(updates))._build();

  _$ResetCountPINCardRequest._({this.cardId, this.verifySoftOtp}) : super._();
  @override
  ResetCountPINCardRequest rebuild(
          void Function(ResetCountPINCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResetCountPINCardRequestBuilder toBuilder() =>
      ResetCountPINCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResetCountPINCardRequest &&
        cardId == other.cardId &&
        verifySoftOtp == other.verifySoftOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResetCountPINCardRequest')
          ..add('cardId', cardId)
          ..add('verifySoftOtp', verifySoftOtp))
        .toString();
  }
}

class ResetCountPINCardRequestBuilder
    implements
        Builder<ResetCountPINCardRequest, ResetCountPINCardRequestBuilder> {
  _$ResetCountPINCardRequest? _$v;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  ResetCountPINCardRequestBuilder() {
    ResetCountPINCardRequest._defaults(this);
  }

  ResetCountPINCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardId = $v.cardId;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResetCountPINCardRequest other) {
    _$v = other as _$ResetCountPINCardRequest;
  }

  @override
  void update(void Function(ResetCountPINCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResetCountPINCardRequest build() => _build();

  _$ResetCountPINCardRequest _build() {
    _$ResetCountPINCardRequest _$result;
    try {
      _$result = _$v ??
          _$ResetCountPINCardRequest._(
            cardId: cardId,
            verifySoftOtp: _verifySoftOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ResetCountPINCardRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
