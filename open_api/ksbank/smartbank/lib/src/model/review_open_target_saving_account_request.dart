//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_open_target_saving_account_request.g.dart';

/// ReviewOpenTargetSavingAccountRequest
///
/// Properties:
/// * [bankCif] - Cif no của khách hàng
/// * [transactionNo] - Số giao dịch do mobile app tự sinh ra, là dãy gồm 10 chữ số
/// * [sourceAccountNo] - Số tài khoản thanh toán
/// * [amount] - Số tiền mở tài khoản
/// * [currency] - Loại tiền
@BuiltValue()
abstract class ReviewOpenTargetSavingAccountRequest
    implements
        Built<ReviewOpenTargetSavingAccountRequest,
            ReviewOpenTargetSavingAccountRequestBuilder> {
  /// Cif no của khách hàng
  @BuiltValueField(wireName: r'bankCif')
  String? get bankCif;

  /// Số giao dịch do mobile app tự sinh ra, là dãy gồm 10 chữ số
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số tài khoản thanh toán
  @BuiltValueField(wireName: r'sourceAccountNo')
  String? get sourceAccountNo;

  /// Số tiền mở tài khoản
  @BuiltValueField(wireName: r'amount')
  double? get amount;

  /// Loại tiền
  @BuiltValueField(wireName: r'currency')
  ReviewOpenTargetSavingAccountRequestCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  ReviewOpenTargetSavingAccountRequest._();

  factory ReviewOpenTargetSavingAccountRequest(
          [void updates(ReviewOpenTargetSavingAccountRequestBuilder b)]) =
      _$ReviewOpenTargetSavingAccountRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewOpenTargetSavingAccountRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewOpenTargetSavingAccountRequest> get serializer =>
      _$ReviewOpenTargetSavingAccountRequestSerializer();
}

class _$ReviewOpenTargetSavingAccountRequestSerializer
    implements PrimitiveSerializer<ReviewOpenTargetSavingAccountRequest> {
  @override
  final Iterable<Type> types = const [
    ReviewOpenTargetSavingAccountRequest,
    _$ReviewOpenTargetSavingAccountRequest
  ];

  @override
  final String wireName = r'ReviewOpenTargetSavingAccountRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewOpenTargetSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.bankCif != null) {
      yield r'bankCif';
      yield serializers.serialize(
        object.bankCif,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sourceAccountNo != null) {
      yield r'sourceAccountNo';
      yield serializers.serialize(
        object.sourceAccountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType: const FullType.nullable(
            ReviewOpenTargetSavingAccountRequestCurrencyEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewOpenTargetSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewOpenTargetSavingAccountRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'bankCif':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCif = valueDes;
          break;
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'sourceAccountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.sourceAccountNo = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                ReviewOpenTargetSavingAccountRequestCurrencyEnum),
          ) as ReviewOpenTargetSavingAccountRequestCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewOpenTargetSavingAccountRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewOpenTargetSavingAccountRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class ReviewOpenTargetSavingAccountRequestCurrencyEnum extends EnumClass {
  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'VND')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum VND =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_VND;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'USD')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum USD =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_USD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'ACB')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum ACB =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_ACB;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'JPY')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum JPY =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_JPY;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum GOLD =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GOLD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'EUR')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum EUR =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_EUR;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'GBP')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum GBP =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_GBP;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'CHF')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum CHF =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CHF;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'AUD')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum AUD =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_AUD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'CAD')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum CAD =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_CAD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'SGD')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum SGD =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SGD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'THB')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum THB =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_THB;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'NOK')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum NOK =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NOK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'NZD')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum NZD =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_NZD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'DKK')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum DKK =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_DKK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'HKD')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum HKD =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_HKD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'SEK')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum SEK =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_SEK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'MYR')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum MYR =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MYR;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'XAU')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum XAU =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_XAU;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'MMK')
  static const ReviewOpenTargetSavingAccountRequestCurrencyEnum MMK =
      _$reviewOpenTargetSavingAccountRequestCurrencyEnum_MMK;

  static Serializer<ReviewOpenTargetSavingAccountRequestCurrencyEnum>
      get serializer =>
          _$reviewOpenTargetSavingAccountRequestCurrencyEnumSerializer;

  const ReviewOpenTargetSavingAccountRequestCurrencyEnum._(String name)
      : super(name);

  static BuiltSet<ReviewOpenTargetSavingAccountRequestCurrencyEnum>
      get values => _$reviewOpenTargetSavingAccountRequestCurrencyEnumValues;
  static ReviewOpenTargetSavingAccountRequestCurrencyEnum valueOf(
          String name) =>
      _$reviewOpenTargetSavingAccountRequestCurrencyEnumValueOf(name);
}
