// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'promotion_detail.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PromotionDetail extends PromotionDetail {
  @override
  final String? title;
  @override
  final String? description;

  factory _$PromotionDetail([void Function(PromotionDetailBuilder)? updates]) =>
      (PromotionDetailBuilder()..update(updates))._build();

  _$PromotionDetail._({this.title, this.description}) : super._();
  @override
  PromotionDetail rebuild(void Function(PromotionDetailBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PromotionDetailBuilder toBuilder() => PromotionDetailBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PromotionDetail &&
        title == other.title &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PromotionDetail')
          ..add('title', title)
          ..add('description', description))
        .toString();
  }
}

class PromotionDetailBuilder
    implements Builder<PromotionDetail, PromotionDetailBuilder> {
  _$PromotionDetail? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  PromotionDetailBuilder() {
    PromotionDetail._defaults(this);
  }

  PromotionDetailBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PromotionDetail other) {
    _$v = other as _$PromotionDetail;
  }

  @override
  void update(void Function(PromotionDetailBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PromotionDetail build() => _build();

  _$PromotionDetail _build() {
    final _$result = _$v ??
        _$PromotionDetail._(
          title: title,
          description: description,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
