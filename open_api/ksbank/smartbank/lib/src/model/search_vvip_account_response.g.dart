// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_vvip_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SearchVvipAccountResponse extends SearchVvipAccountResponse {
  @override
  final String? recordID;
  @override
  final String? vipAccountNo;
  @override
  final num? feeAmount;
  @override
  final String? feeAmountStr;

  factory _$SearchVvipAccountResponse(
          [void Function(SearchVvipAccountResponseBuilder)? updates]) =>
      (SearchVvipAccountResponseBuilder()..update(updates))._build();

  _$SearchVvipAccountResponse._(
      {this.recordID, this.vipAccountNo, this.feeAmount, this.feeAmountStr})
      : super._();
  @override
  SearchVvipAccountResponse rebuild(
          void Function(SearchVvipAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SearchVvipAccountResponseBuilder toBuilder() =>
      SearchVvipAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SearchVvipAccountResponse &&
        recordID == other.recordID &&
        vipAccountNo == other.vipAccountNo &&
        feeAmount == other.feeAmount &&
        feeAmountStr == other.feeAmountStr;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, recordID.hashCode);
    _$hash = $jc(_$hash, vipAccountNo.hashCode);
    _$hash = $jc(_$hash, feeAmount.hashCode);
    _$hash = $jc(_$hash, feeAmountStr.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SearchVvipAccountResponse')
          ..add('recordID', recordID)
          ..add('vipAccountNo', vipAccountNo)
          ..add('feeAmount', feeAmount)
          ..add('feeAmountStr', feeAmountStr))
        .toString();
  }
}

class SearchVvipAccountResponseBuilder
    implements
        Builder<SearchVvipAccountResponse, SearchVvipAccountResponseBuilder> {
  _$SearchVvipAccountResponse? _$v;

  String? _recordID;
  String? get recordID => _$this._recordID;
  set recordID(String? recordID) => _$this._recordID = recordID;

  String? _vipAccountNo;
  String? get vipAccountNo => _$this._vipAccountNo;
  set vipAccountNo(String? vipAccountNo) => _$this._vipAccountNo = vipAccountNo;

  num? _feeAmount;
  num? get feeAmount => _$this._feeAmount;
  set feeAmount(num? feeAmount) => _$this._feeAmount = feeAmount;

  String? _feeAmountStr;
  String? get feeAmountStr => _$this._feeAmountStr;
  set feeAmountStr(String? feeAmountStr) => _$this._feeAmountStr = feeAmountStr;

  SearchVvipAccountResponseBuilder() {
    SearchVvipAccountResponse._defaults(this);
  }

  SearchVvipAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _recordID = $v.recordID;
      _vipAccountNo = $v.vipAccountNo;
      _feeAmount = $v.feeAmount;
      _feeAmountStr = $v.feeAmountStr;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SearchVvipAccountResponse other) {
    _$v = other as _$SearchVvipAccountResponse;
  }

  @override
  void update(void Function(SearchVvipAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SearchVvipAccountResponse build() => _build();

  _$SearchVvipAccountResponse _build() {
    final _$result = _$v ??
        _$SearchVvipAccountResponse._(
          recordID: recordID,
          vipAccountNo: vipAccountNo,
          feeAmount: feeAmount,
          feeAmountStr: feeAmountStr,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
