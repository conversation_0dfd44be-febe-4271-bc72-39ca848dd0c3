// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trans_next_step.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const TransNextStep _$NONE = const TransNextStep._('NONE');
const TransNextStep _$ENABLE_FACE_ID = const TransNextStep._('ENABLE_FACE_ID');
const TransNextStep _$SHOW_GUIDE = const TransNextStep._('SHOW_GUIDE');
const TransNextStep _$SHOW_GUIDE_GTTT_EXPIRED =
    const TransNextStep._('SHOW_GUIDE_GTTT_EXPIRED');

TransNextStep _$valueOf(String name) {
  switch (name) {
    case 'NONE':
      return _$NONE;
    case 'ENABLE_FACE_ID':
      return _$ENABLE_FACE_ID;
    case 'SHOW_GUIDE':
      return _$SHOW_GUIDE;
    case 'SHOW_GUIDE_GTTT_EXPIRED':
      return _$SHOW_GUIDE_GTTT_EXPIRED;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<TransNextStep> _$values =
    BuiltSet<TransNextStep>(const <TransNextStep>[
  _$NONE,
  _$ENABLE_FACE_ID,
  _$SHOW_GUIDE,
  _$SHOW_GUIDE_GTTT_EXPIRED,
]);

class _$TransNextStepMeta {
  const _$TransNextStepMeta();
  TransNextStep get NONE => _$NONE;
  TransNextStep get ENABLE_FACE_ID => _$ENABLE_FACE_ID;
  TransNextStep get SHOW_GUIDE => _$SHOW_GUIDE;
  TransNextStep get SHOW_GUIDE_GTTT_EXPIRED => _$SHOW_GUIDE_GTTT_EXPIRED;
  TransNextStep valueOf(String name) => _$valueOf(name);
  BuiltSet<TransNextStep> get values => _$values;
}

abstract class _$TransNextStepMixin {
  // ignore: non_constant_identifier_names
  _$TransNextStepMeta get TransNextStep => const _$TransNextStepMeta();
}

Serializer<TransNextStep> _$transNextStepSerializer =
    _$TransNextStepSerializer();

class _$TransNextStepSerializer implements PrimitiveSerializer<TransNextStep> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'NONE': 'NONE',
    'ENABLE_FACE_ID': 'ENABLE_FACE_ID',
    'SHOW_GUIDE': 'SHOW_GUIDE',
    'SHOW_GUIDE_GTTT_EXPIRED': 'SHOW_GUIDE_GTTT_EXPIRED',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'NONE': 'NONE',
    'ENABLE_FACE_ID': 'ENABLE_FACE_ID',
    'SHOW_GUIDE': 'SHOW_GUIDE',
    'SHOW_GUIDE_GTTT_EXPIRED': 'SHOW_GUIDE_GTTT_EXPIRED',
  };

  @override
  final Iterable<Type> types = const <Type>[TransNextStep];
  @override
  final String wireName = 'TransNextStep';

  @override
  Object serialize(Serializers serializers, TransNextStep object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  TransNextStep deserialize(Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      TransNextStep.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
