// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_VND =
    const LoanDetailResponseCurrencyEnum._('VND');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_USD =
    const LoanDetailResponseCurrencyEnum._('USD');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_ACB =
    const LoanDetailResponseCurrencyEnum._('ACB');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_JPY =
    const LoanDetailResponseCurrencyEnum._('JPY');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_GOLD =
    const LoanDetailResponseCurrencyEnum._('GOLD');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_EUR =
    const LoanDetailResponseCurrencyEnum._('EUR');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_GBP =
    const LoanDetailResponseCurrencyEnum._('GBP');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_CHF =
    const LoanDetailResponseCurrencyEnum._('CHF');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_AUD =
    const LoanDetailResponseCurrencyEnum._('AUD');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_CAD =
    const LoanDetailResponseCurrencyEnum._('CAD');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_SGD =
    const LoanDetailResponseCurrencyEnum._('SGD');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_THB =
    const LoanDetailResponseCurrencyEnum._('THB');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_NOK =
    const LoanDetailResponseCurrencyEnum._('NOK');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_NZD =
    const LoanDetailResponseCurrencyEnum._('NZD');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_DKK =
    const LoanDetailResponseCurrencyEnum._('DKK');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_HKD =
    const LoanDetailResponseCurrencyEnum._('HKD');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_SEK =
    const LoanDetailResponseCurrencyEnum._('SEK');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_MYR =
    const LoanDetailResponseCurrencyEnum._('MYR');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_XAU =
    const LoanDetailResponseCurrencyEnum._('XAU');
const LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnum_MMK =
    const LoanDetailResponseCurrencyEnum._('MMK');

LoanDetailResponseCurrencyEnum _$loanDetailResponseCurrencyEnumValueOf(
    String name) {
  switch (name) {
    case 'VND':
      return _$loanDetailResponseCurrencyEnum_VND;
    case 'USD':
      return _$loanDetailResponseCurrencyEnum_USD;
    case 'ACB':
      return _$loanDetailResponseCurrencyEnum_ACB;
    case 'JPY':
      return _$loanDetailResponseCurrencyEnum_JPY;
    case 'GOLD':
      return _$loanDetailResponseCurrencyEnum_GOLD;
    case 'EUR':
      return _$loanDetailResponseCurrencyEnum_EUR;
    case 'GBP':
      return _$loanDetailResponseCurrencyEnum_GBP;
    case 'CHF':
      return _$loanDetailResponseCurrencyEnum_CHF;
    case 'AUD':
      return _$loanDetailResponseCurrencyEnum_AUD;
    case 'CAD':
      return _$loanDetailResponseCurrencyEnum_CAD;
    case 'SGD':
      return _$loanDetailResponseCurrencyEnum_SGD;
    case 'THB':
      return _$loanDetailResponseCurrencyEnum_THB;
    case 'NOK':
      return _$loanDetailResponseCurrencyEnum_NOK;
    case 'NZD':
      return _$loanDetailResponseCurrencyEnum_NZD;
    case 'DKK':
      return _$loanDetailResponseCurrencyEnum_DKK;
    case 'HKD':
      return _$loanDetailResponseCurrencyEnum_HKD;
    case 'SEK':
      return _$loanDetailResponseCurrencyEnum_SEK;
    case 'MYR':
      return _$loanDetailResponseCurrencyEnum_MYR;
    case 'XAU':
      return _$loanDetailResponseCurrencyEnum_XAU;
    case 'MMK':
      return _$loanDetailResponseCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<LoanDetailResponseCurrencyEnum>
    _$loanDetailResponseCurrencyEnumValues = BuiltSet<
        LoanDetailResponseCurrencyEnum>(const <LoanDetailResponseCurrencyEnum>[
  _$loanDetailResponseCurrencyEnum_VND,
  _$loanDetailResponseCurrencyEnum_USD,
  _$loanDetailResponseCurrencyEnum_ACB,
  _$loanDetailResponseCurrencyEnum_JPY,
  _$loanDetailResponseCurrencyEnum_GOLD,
  _$loanDetailResponseCurrencyEnum_EUR,
  _$loanDetailResponseCurrencyEnum_GBP,
  _$loanDetailResponseCurrencyEnum_CHF,
  _$loanDetailResponseCurrencyEnum_AUD,
  _$loanDetailResponseCurrencyEnum_CAD,
  _$loanDetailResponseCurrencyEnum_SGD,
  _$loanDetailResponseCurrencyEnum_THB,
  _$loanDetailResponseCurrencyEnum_NOK,
  _$loanDetailResponseCurrencyEnum_NZD,
  _$loanDetailResponseCurrencyEnum_DKK,
  _$loanDetailResponseCurrencyEnum_HKD,
  _$loanDetailResponseCurrencyEnum_SEK,
  _$loanDetailResponseCurrencyEnum_MYR,
  _$loanDetailResponseCurrencyEnum_XAU,
  _$loanDetailResponseCurrencyEnum_MMK,
]);

Serializer<LoanDetailResponseCurrencyEnum>
    _$loanDetailResponseCurrencyEnumSerializer =
    _$LoanDetailResponseCurrencyEnumSerializer();

class _$LoanDetailResponseCurrencyEnumSerializer
    implements PrimitiveSerializer<LoanDetailResponseCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[LoanDetailResponseCurrencyEnum];
  @override
  final String wireName = 'LoanDetailResponseCurrencyEnum';

  @override
  Object serialize(
          Serializers serializers, LoanDetailResponseCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  LoanDetailResponseCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      LoanDetailResponseCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$LoanDetailResponse extends LoanDetailResponse {
  @override
  final String? accountNo;
  @override
  final String? accountName;
  @override
  final LoanDetailResponseCurrencyEnum? currency;
  @override
  final double? loanAmount;
  @override
  final double? paidAmount;
  @override
  final double? remainAmount;
  @override
  final DateTime? nextRefundDate;
  @override
  final double? payAmount;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final String? purposeTypeName;
  @override
  final double? rate;
  @override
  final String? imageUrl;

  factory _$LoanDetailResponse(
          [void Function(LoanDetailResponseBuilder)? updates]) =>
      (LoanDetailResponseBuilder()..update(updates))._build();

  _$LoanDetailResponse._(
      {this.accountNo,
      this.accountName,
      this.currency,
      this.loanAmount,
      this.paidAmount,
      this.remainAmount,
      this.nextRefundDate,
      this.payAmount,
      this.contractDate,
      this.dueDate,
      this.purposeTypeName,
      this.rate,
      this.imageUrl})
      : super._();
  @override
  LoanDetailResponse rebuild(
          void Function(LoanDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanDetailResponseBuilder toBuilder() =>
      LoanDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanDetailResponse &&
        accountNo == other.accountNo &&
        accountName == other.accountName &&
        currency == other.currency &&
        loanAmount == other.loanAmount &&
        paidAmount == other.paidAmount &&
        remainAmount == other.remainAmount &&
        nextRefundDate == other.nextRefundDate &&
        payAmount == other.payAmount &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        purposeTypeName == other.purposeTypeName &&
        rate == other.rate &&
        imageUrl == other.imageUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, loanAmount.hashCode);
    _$hash = $jc(_$hash, paidAmount.hashCode);
    _$hash = $jc(_$hash, remainAmount.hashCode);
    _$hash = $jc(_$hash, nextRefundDate.hashCode);
    _$hash = $jc(_$hash, payAmount.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, purposeTypeName.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanDetailResponse')
          ..add('accountNo', accountNo)
          ..add('accountName', accountName)
          ..add('currency', currency)
          ..add('loanAmount', loanAmount)
          ..add('paidAmount', paidAmount)
          ..add('remainAmount', remainAmount)
          ..add('nextRefundDate', nextRefundDate)
          ..add('payAmount', payAmount)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('purposeTypeName', purposeTypeName)
          ..add('rate', rate)
          ..add('imageUrl', imageUrl))
        .toString();
  }
}

class LoanDetailResponseBuilder
    implements Builder<LoanDetailResponse, LoanDetailResponseBuilder> {
  _$LoanDetailResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  LoanDetailResponseCurrencyEnum? _currency;
  LoanDetailResponseCurrencyEnum? get currency => _$this._currency;
  set currency(LoanDetailResponseCurrencyEnum? currency) =>
      _$this._currency = currency;

  double? _loanAmount;
  double? get loanAmount => _$this._loanAmount;
  set loanAmount(double? loanAmount) => _$this._loanAmount = loanAmount;

  double? _paidAmount;
  double? get paidAmount => _$this._paidAmount;
  set paidAmount(double? paidAmount) => _$this._paidAmount = paidAmount;

  double? _remainAmount;
  double? get remainAmount => _$this._remainAmount;
  set remainAmount(double? remainAmount) => _$this._remainAmount = remainAmount;

  DateTime? _nextRefundDate;
  DateTime? get nextRefundDate => _$this._nextRefundDate;
  set nextRefundDate(DateTime? nextRefundDate) =>
      _$this._nextRefundDate = nextRefundDate;

  double? _payAmount;
  double? get payAmount => _$this._payAmount;
  set payAmount(double? payAmount) => _$this._payAmount = payAmount;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  String? _purposeTypeName;
  String? get purposeTypeName => _$this._purposeTypeName;
  set purposeTypeName(String? purposeTypeName) =>
      _$this._purposeTypeName = purposeTypeName;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  LoanDetailResponseBuilder() {
    LoanDetailResponse._defaults(this);
  }

  LoanDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _accountName = $v.accountName;
      _currency = $v.currency;
      _loanAmount = $v.loanAmount;
      _paidAmount = $v.paidAmount;
      _remainAmount = $v.remainAmount;
      _nextRefundDate = $v.nextRefundDate;
      _payAmount = $v.payAmount;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _purposeTypeName = $v.purposeTypeName;
      _rate = $v.rate;
      _imageUrl = $v.imageUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanDetailResponse other) {
    _$v = other as _$LoanDetailResponse;
  }

  @override
  void update(void Function(LoanDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanDetailResponse build() => _build();

  _$LoanDetailResponse _build() {
    final _$result = _$v ??
        _$LoanDetailResponse._(
          accountNo: accountNo,
          accountName: accountName,
          currency: currency,
          loanAmount: loanAmount,
          paidAmount: paidAmount,
          remainAmount: remainAmount,
          nextRefundDate: nextRefundDate,
          payAmount: payAmount,
          contractDate: contractDate,
          dueDate: dueDate,
          purposeTypeName: purposeTypeName,
          rate: rate,
          imageUrl: imageUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
