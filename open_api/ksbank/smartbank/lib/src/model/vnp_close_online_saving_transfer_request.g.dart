// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_close_online_saving_transfer_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_VND =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('VND');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_USD =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('USD');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_ACB =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('ACB');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_JPY =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('JPY');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GOLD =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('GOLD');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_EUR =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('EUR');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GBP =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('GBP');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CHF =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('CHF');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_AUD =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('AUD');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CAD =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('CAD');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SGD =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('SGD');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_THB =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('THB');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NOK =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('NOK');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NZD =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('NZD');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_DKK =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('DKK');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_HKD =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('HKD');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SEK =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('SEK');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MYR =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('MYR');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_XAU =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('XAU');
const VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MMK =
    const VNPCloseOnlineSavingTransferRequestCurrencyEnum._('MMK');

VNPCloseOnlineSavingTransferRequestCurrencyEnum
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnumValueOf(String name) {
  switch (name) {
    case 'VND':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_VND;
    case 'USD':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_USD;
    case 'ACB':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_ACB;
    case 'JPY':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_JPY;
    case 'GOLD':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GOLD;
    case 'EUR':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_EUR;
    case 'GBP':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GBP;
    case 'CHF':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CHF;
    case 'AUD':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_AUD;
    case 'CAD':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CAD;
    case 'SGD':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SGD;
    case 'THB':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_THB;
    case 'NOK':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NOK;
    case 'NZD':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NZD;
    case 'DKK':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_DKK;
    case 'HKD':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_HKD;
    case 'SEK':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SEK;
    case 'MYR':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MYR;
    case 'XAU':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_XAU;
    case 'MMK':
      return _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<VNPCloseOnlineSavingTransferRequestCurrencyEnum>
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnumValues = BuiltSet<
        VNPCloseOnlineSavingTransferRequestCurrencyEnum>(const <VNPCloseOnlineSavingTransferRequestCurrencyEnum>[
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_VND,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_USD,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_ACB,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_JPY,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GOLD,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_EUR,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_GBP,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CHF,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_AUD,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_CAD,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SGD,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_THB,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NOK,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_NZD,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_DKK,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_HKD,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_SEK,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MYR,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_XAU,
  _$vNPCloseOnlineSavingTransferRequestCurrencyEnum_MMK,
]);

Serializer<VNPCloseOnlineSavingTransferRequestCurrencyEnum>
    _$vNPCloseOnlineSavingTransferRequestCurrencyEnumSerializer =
    _$VNPCloseOnlineSavingTransferRequestCurrencyEnumSerializer();

class _$VNPCloseOnlineSavingTransferRequestCurrencyEnumSerializer
    implements
        PrimitiveSerializer<VNPCloseOnlineSavingTransferRequestCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[
    VNPCloseOnlineSavingTransferRequestCurrencyEnum
  ];
  @override
  final String wireName = 'VNPCloseOnlineSavingTransferRequestCurrencyEnum';

  @override
  Object serialize(Serializers serializers,
          VNPCloseOnlineSavingTransferRequestCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  VNPCloseOnlineSavingTransferRequestCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      VNPCloseOnlineSavingTransferRequestCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$VNPCloseOnlineSavingTransferRequest
    extends VNPCloseOnlineSavingTransferRequest {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? accountNumber;
  @override
  final String? accountName;
  @override
  final num? balance;
  @override
  final VNPCloseOnlineSavingTransferRequestCurrencyEnum? currency;
  @override
  final num? rate;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final num? finalAmount;
  @override
  final String? destinationAccount;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;
  @override
  final String? benName;
  @override
  final String? benPhone;
  @override
  final String? benIdCard;
  @override
  final String? message;
  @override
  final Date? benIssueDate;
  @override
  final String? benPlaceBy;
  @override
  final String? benAddr;

  factory _$VNPCloseOnlineSavingTransferRequest(
          [void Function(VNPCloseOnlineSavingTransferRequestBuilder)?
              updates]) =>
      (VNPCloseOnlineSavingTransferRequestBuilder()..update(updates))._build();

  _$VNPCloseOnlineSavingTransferRequest._(
      {this.bankCif,
      this.transactionNo,
      this.accountNumber,
      this.accountName,
      this.balance,
      this.currency,
      this.rate,
      this.contractDate,
      this.dueDate,
      this.finalAmount,
      this.destinationAccount,
      this.verifySoftOtp,
      this.benName,
      this.benPhone,
      this.benIdCard,
      this.message,
      this.benIssueDate,
      this.benPlaceBy,
      this.benAddr})
      : super._();
  @override
  VNPCloseOnlineSavingTransferRequest rebuild(
          void Function(VNPCloseOnlineSavingTransferRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VNPCloseOnlineSavingTransferRequestBuilder toBuilder() =>
      VNPCloseOnlineSavingTransferRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VNPCloseOnlineSavingTransferRequest &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        accountNumber == other.accountNumber &&
        accountName == other.accountName &&
        balance == other.balance &&
        currency == other.currency &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        finalAmount == other.finalAmount &&
        destinationAccount == other.destinationAccount &&
        verifySoftOtp == other.verifySoftOtp &&
        benName == other.benName &&
        benPhone == other.benPhone &&
        benIdCard == other.benIdCard &&
        message == other.message &&
        benIssueDate == other.benIssueDate &&
        benPlaceBy == other.benPlaceBy &&
        benAddr == other.benAddr;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, destinationAccount.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jc(_$hash, benName.hashCode);
    _$hash = $jc(_$hash, benPhone.hashCode);
    _$hash = $jc(_$hash, benIdCard.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, benIssueDate.hashCode);
    _$hash = $jc(_$hash, benPlaceBy.hashCode);
    _$hash = $jc(_$hash, benAddr.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VNPCloseOnlineSavingTransferRequest')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('accountNumber', accountNumber)
          ..add('accountName', accountName)
          ..add('balance', balance)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('finalAmount', finalAmount)
          ..add('destinationAccount', destinationAccount)
          ..add('verifySoftOtp', verifySoftOtp)
          ..add('benName', benName)
          ..add('benPhone', benPhone)
          ..add('benIdCard', benIdCard)
          ..add('message', message)
          ..add('benIssueDate', benIssueDate)
          ..add('benPlaceBy', benPlaceBy)
          ..add('benAddr', benAddr))
        .toString();
  }
}

class VNPCloseOnlineSavingTransferRequestBuilder
    implements
        Builder<VNPCloseOnlineSavingTransferRequest,
            VNPCloseOnlineSavingTransferRequestBuilder> {
  _$VNPCloseOnlineSavingTransferRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  VNPCloseOnlineSavingTransferRequestCurrencyEnum? _currency;
  VNPCloseOnlineSavingTransferRequestCurrencyEnum? get currency =>
      _$this._currency;
  set currency(VNPCloseOnlineSavingTransferRequestCurrencyEnum? currency) =>
      _$this._currency = currency;

  num? _rate;
  num? get rate => _$this._rate;
  set rate(num? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  num? _finalAmount;
  num? get finalAmount => _$this._finalAmount;
  set finalAmount(num? finalAmount) => _$this._finalAmount = finalAmount;

  String? _destinationAccount;
  String? get destinationAccount => _$this._destinationAccount;
  set destinationAccount(String? destinationAccount) =>
      _$this._destinationAccount = destinationAccount;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  String? _benName;
  String? get benName => _$this._benName;
  set benName(String? benName) => _$this._benName = benName;

  String? _benPhone;
  String? get benPhone => _$this._benPhone;
  set benPhone(String? benPhone) => _$this._benPhone = benPhone;

  String? _benIdCard;
  String? get benIdCard => _$this._benIdCard;
  set benIdCard(String? benIdCard) => _$this._benIdCard = benIdCard;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  Date? _benIssueDate;
  Date? get benIssueDate => _$this._benIssueDate;
  set benIssueDate(Date? benIssueDate) => _$this._benIssueDate = benIssueDate;

  String? _benPlaceBy;
  String? get benPlaceBy => _$this._benPlaceBy;
  set benPlaceBy(String? benPlaceBy) => _$this._benPlaceBy = benPlaceBy;

  String? _benAddr;
  String? get benAddr => _$this._benAddr;
  set benAddr(String? benAddr) => _$this._benAddr = benAddr;

  VNPCloseOnlineSavingTransferRequestBuilder() {
    VNPCloseOnlineSavingTransferRequest._defaults(this);
  }

  VNPCloseOnlineSavingTransferRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _accountNumber = $v.accountNumber;
      _accountName = $v.accountName;
      _balance = $v.balance;
      _currency = $v.currency;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _finalAmount = $v.finalAmount;
      _destinationAccount = $v.destinationAccount;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _benName = $v.benName;
      _benPhone = $v.benPhone;
      _benIdCard = $v.benIdCard;
      _message = $v.message;
      _benIssueDate = $v.benIssueDate;
      _benPlaceBy = $v.benPlaceBy;
      _benAddr = $v.benAddr;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VNPCloseOnlineSavingTransferRequest other) {
    _$v = other as _$VNPCloseOnlineSavingTransferRequest;
  }

  @override
  void update(
      void Function(VNPCloseOnlineSavingTransferRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VNPCloseOnlineSavingTransferRequest build() => _build();

  _$VNPCloseOnlineSavingTransferRequest _build() {
    _$VNPCloseOnlineSavingTransferRequest _$result;
    try {
      _$result = _$v ??
          _$VNPCloseOnlineSavingTransferRequest._(
            bankCif: bankCif,
            transactionNo: transactionNo,
            accountNumber: accountNumber,
            accountName: accountName,
            balance: balance,
            currency: currency,
            rate: rate,
            contractDate: contractDate,
            dueDate: dueDate,
            finalAmount: finalAmount,
            destinationAccount: destinationAccount,
            verifySoftOtp: _verifySoftOtp?.build(),
            benName: benName,
            benPhone: benPhone,
            benIdCard: benIdCard,
            message: message,
            benIssueDate: benIssueDate,
            benPlaceBy: benPlaceBy,
            benAddr: benAddr,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'VNPCloseOnlineSavingTransferRequest',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
