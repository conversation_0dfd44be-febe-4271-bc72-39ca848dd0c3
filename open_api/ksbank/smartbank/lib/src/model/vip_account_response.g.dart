// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VipAccountResponse extends VipAccountResponse {
  @override
  final VipAccountInfoDto? vipAccountInfoDto;

  factory _$VipAccountResponse(
          [void Function(VipAccountResponseBuilder)? updates]) =>
      (VipAccountResponseBuilder()..update(updates))._build();

  _$VipAccountResponse._({this.vipAccountInfoDto}) : super._();
  @override
  VipAccountResponse rebuild(
          void Function(VipAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VipAccountResponseBuilder toBuilder() =>
      VipAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VipAccountResponse &&
        vipAccountInfoDto == other.vipAccountInfoDto;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, vipAccountInfoDto.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VipAccountResponse')
          ..add('vipAccountInfoDto', vipAccountInfoDto))
        .toString();
  }
}

class VipAccountResponseBuilder
    implements Builder<VipAccountResponse, VipAccountResponseBuilder> {
  _$VipAccountResponse? _$v;

  VipAccountInfoDtoBuilder? _vipAccountInfoDto;
  VipAccountInfoDtoBuilder get vipAccountInfoDto =>
      _$this._vipAccountInfoDto ??= VipAccountInfoDtoBuilder();
  set vipAccountInfoDto(VipAccountInfoDtoBuilder? vipAccountInfoDto) =>
      _$this._vipAccountInfoDto = vipAccountInfoDto;

  VipAccountResponseBuilder() {
    VipAccountResponse._defaults(this);
  }

  VipAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _vipAccountInfoDto = $v.vipAccountInfoDto?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VipAccountResponse other) {
    _$v = other as _$VipAccountResponse;
  }

  @override
  void update(void Function(VipAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VipAccountResponse build() => _build();

  _$VipAccountResponse _build() {
    _$VipAccountResponse _$result;
    try {
      _$result = _$v ??
          _$VipAccountResponse._(
            vipAccountInfoDto: _vipAccountInfoDto?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'vipAccountInfoDto';
        _vipAccountInfoDto?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'VipAccountResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
