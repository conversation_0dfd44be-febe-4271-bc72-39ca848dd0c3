// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_logwork_item.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const UserLogworkItemTypeEnum _$userLogworkItemTypeEnum_FULL_DATE =
    const UserLogworkItemTypeEnum._('FULL_DATE');
const UserLogworkItemTypeEnum _$userLogworkItemTypeEnum_NOT_FULL_DATE =
    const UserLogworkItemTypeEnum._('NOT_FULL_DATE');
const UserLogworkItemTypeEnum _$userLogworkItemTypeEnum_DATE_OFF =
    const UserLogworkItemTypeEnum._('DATE_OFF');
const UserLogworkItemTypeEnum _$userLogworkItemTypeEnum_NONE =
    const UserLogworkItemTypeEnum._('NONE');

UserLogworkItemTypeEnum _$userLogworkItemTypeEnumValueOf(String name) {
  switch (name) {
    case 'FULL_DATE':
      return _$userLogworkItemTypeEnum_FULL_DATE;
    case 'NOT_FULL_DATE':
      return _$userLogworkItemTypeEnum_NOT_FULL_DATE;
    case 'DATE_OFF':
      return _$userLogworkItemTypeEnum_DATE_OFF;
    case 'NONE':
      return _$userLogworkItemTypeEnum_NONE;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<UserLogworkItemTypeEnum> _$userLogworkItemTypeEnumValues =
    BuiltSet<UserLogworkItemTypeEnum>(const <UserLogworkItemTypeEnum>[
  _$userLogworkItemTypeEnum_FULL_DATE,
  _$userLogworkItemTypeEnum_NOT_FULL_DATE,
  _$userLogworkItemTypeEnum_DATE_OFF,
  _$userLogworkItemTypeEnum_NONE,
]);

Serializer<UserLogworkItemTypeEnum> _$userLogworkItemTypeEnumSerializer =
    _$UserLogworkItemTypeEnumSerializer();

class _$UserLogworkItemTypeEnumSerializer
    implements PrimitiveSerializer<UserLogworkItemTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'FULL_DATE': 'FULL_DATE',
    'NOT_FULL_DATE': 'NOT_FULL_DATE',
    'DATE_OFF': 'DATE_OFF',
    'NONE': 'NONE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'FULL_DATE': 'FULL_DATE',
    'NOT_FULL_DATE': 'NOT_FULL_DATE',
    'DATE_OFF': 'DATE_OFF',
    'NONE': 'NONE',
  };

  @override
  final Iterable<Type> types = const <Type>[UserLogworkItemTypeEnum];
  @override
  final String wireName = 'UserLogworkItemTypeEnum';

  @override
  Object serialize(Serializers serializers, UserLogworkItemTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  UserLogworkItemTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      UserLogworkItemTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$UserLogworkItem extends UserLogworkItem {
  @override
  final String? date;
  @override
  final double? workload;
  @override
  final UserLogworkItemTypeEnum? type;

  factory _$UserLogworkItem([void Function(UserLogworkItemBuilder)? updates]) =>
      (UserLogworkItemBuilder()..update(updates))._build();

  _$UserLogworkItem._({this.date, this.workload, this.type}) : super._();
  @override
  UserLogworkItem rebuild(void Function(UserLogworkItemBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserLogworkItemBuilder toBuilder() => UserLogworkItemBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserLogworkItem &&
        date == other.date &&
        workload == other.workload &&
        type == other.type;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, date.hashCode);
    _$hash = $jc(_$hash, workload.hashCode);
    _$hash = $jc(_$hash, type.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserLogworkItem')
          ..add('date', date)
          ..add('workload', workload)
          ..add('type', type))
        .toString();
  }
}

class UserLogworkItemBuilder
    implements Builder<UserLogworkItem, UserLogworkItemBuilder> {
  _$UserLogworkItem? _$v;

  String? _date;
  String? get date => _$this._date;
  set date(String? date) => _$this._date = date;

  double? _workload;
  double? get workload => _$this._workload;
  set workload(double? workload) => _$this._workload = workload;

  UserLogworkItemTypeEnum? _type;
  UserLogworkItemTypeEnum? get type => _$this._type;
  set type(UserLogworkItemTypeEnum? type) => _$this._type = type;

  UserLogworkItemBuilder() {
    UserLogworkItem._defaults(this);
  }

  UserLogworkItemBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _date = $v.date;
      _workload = $v.workload;
      _type = $v.type;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserLogworkItem other) {
    _$v = other as _$UserLogworkItem;
  }

  @override
  void update(void Function(UserLogworkItemBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserLogworkItem build() => _build();

  _$UserLogworkItem _build() {
    final _$result = _$v ??
        _$UserLogworkItem._(
          date: date,
          workload: workload,
          type: type,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
