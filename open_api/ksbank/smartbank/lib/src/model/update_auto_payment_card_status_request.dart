//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_auto_payment_card_status_request.g.dart';

/// UpdateAutoPaymentCardStatusRequest
///
/// Properties:
/// * [cardNo] - Số thẻ
/// * [cardId] - Id thẻ
/// * [autoPayCreditCardStatus] - Trạng thái thanh toán tự động
/// * [paymentRate] - Tỉ lệ thanh toán. Có giá trị từ 5 -> 99.  Là phần trăm thanh toán khi autoPayCreditCardStatus = PARTIAL_PAYMENT
/// * [accountNo] - Số tài khoản thanh toán dùng để trích nợ thẻ
@BuiltValue()
abstract class UpdateAutoPaymentCardStatusRequest
    implements
        Built<UpdateAutoPaymentCardStatusRequest,
            UpdateAutoPaymentCardStatusRequestBuilder> {
  /// Số thẻ
  @BuiltValueField(wireName: r'cardNo')
  String? get cardNo;

  /// Id thẻ
  @BuiltValueField(wireName: r'cardId')
  String? get cardId;

  /// Trạng thái thanh toán tự động
  @BuiltValueField(wireName: r'autoPayCreditCardStatus')
  UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum?
      get autoPayCreditCardStatus;
  // enum autoPayCreditCardStatusEnum {  NO_PAYMENT,  MINIMUM_PAYMENT,  FULL_PAYMENT,  PARTIAL_PAYMENT,  };

  /// Tỉ lệ thanh toán. Có giá trị từ 5 -> 99.  Là phần trăm thanh toán khi autoPayCreditCardStatus = PARTIAL_PAYMENT
  @BuiltValueField(wireName: r'paymentRate')
  int? get paymentRate;

  /// Số tài khoản thanh toán dùng để trích nợ thẻ
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  UpdateAutoPaymentCardStatusRequest._();

  factory UpdateAutoPaymentCardStatusRequest(
          [void updates(UpdateAutoPaymentCardStatusRequestBuilder b)]) =
      _$UpdateAutoPaymentCardStatusRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateAutoPaymentCardStatusRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateAutoPaymentCardStatusRequest> get serializer =>
      _$UpdateAutoPaymentCardStatusRequestSerializer();
}

class _$UpdateAutoPaymentCardStatusRequestSerializer
    implements PrimitiveSerializer<UpdateAutoPaymentCardStatusRequest> {
  @override
  final Iterable<Type> types = const [
    UpdateAutoPaymentCardStatusRequest,
    _$UpdateAutoPaymentCardStatusRequest
  ];

  @override
  final String wireName = r'UpdateAutoPaymentCardStatusRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateAutoPaymentCardStatusRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.cardNo != null) {
      yield r'cardNo';
      yield serializers.serialize(
        object.cardNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cardId != null) {
      yield r'cardId';
      yield serializers.serialize(
        object.cardId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.autoPayCreditCardStatus != null) {
      yield r'autoPayCreditCardStatus';
      yield serializers.serialize(
        object.autoPayCreditCardStatus,
        specifiedType: const FullType.nullable(
            UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum),
      );
    }
    if (object.paymentRate != null) {
      yield r'paymentRate';
      yield serializers.serialize(
        object.paymentRate,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateAutoPaymentCardStatusRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateAutoPaymentCardStatusRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'cardNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardNo = valueDes;
          break;
        case r'cardId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardId = valueDes;
          break;
        case r'autoPayCreditCardStatus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum),
          ) as UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum?;
          if (valueDes == null) continue;
          result.autoPayCreditCardStatus = valueDes;
          break;
        case r'paymentRate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.paymentRate = valueDes;
          break;
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateAutoPaymentCardStatusRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateAutoPaymentCardStatusRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
    extends EnumClass {
  /// Trạng thái thanh toán tự động
  @BuiltValueEnumConst(wireName: r'NO_PAYMENT')
  static const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
      NO_PAYMENT =
      _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_NO_PAYMENT;

  /// Trạng thái thanh toán tự động
  @BuiltValueEnumConst(wireName: r'MINIMUM_PAYMENT')
  static const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
      MINIMUM_PAYMENT =
      _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_MINIMUM_PAYMENT;

  /// Trạng thái thanh toán tự động
  @BuiltValueEnumConst(wireName: r'FULL_PAYMENT')
  static const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
      FULL_PAYMENT =
      _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_FULL_PAYMENT;

  /// Trạng thái thanh toán tự động
  @BuiltValueEnumConst(wireName: r'PARTIAL_PAYMENT')
  static const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum
      PARTIAL_PAYMENT =
      _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum_PARTIAL_PAYMENT;

  static Serializer<
          UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum>
      get serializer =>
          _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumSerializer;

  const UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum._(
      String name)
      : super(name);

  static BuiltSet<UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum>
      get values =>
          _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumValues;
  static UpdateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnum valueOf(
          String name) =>
      _$updateAutoPaymentCardStatusRequestAutoPayCreditCardStatusEnumValueOf(
          name);
}
