// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sms_phone.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SmsPhone extends SmsPhone {
  @override
  final String? phone;
  @override
  final BuiltList<String>? accounts;
  @override
  final int? regTdAccounts;
  @override
  final int? regLoanAccounts;

  factory _$SmsPhone([void Function(SmsPhoneBuilder)? updates]) =>
      (SmsPhoneBuilder()..update(updates))._build();

  _$SmsPhone._(
      {this.phone, this.accounts, this.regTdAccounts, this.regLoanAccounts})
      : super._();
  @override
  SmsPhone rebuild(void Function(SmsPhoneBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SmsPhoneBuilder toBuilder() => SmsPhoneBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SmsPhone &&
        phone == other.phone &&
        accounts == other.accounts &&
        regTdAccounts == other.regTdAccounts &&
        regLoanAccounts == other.regLoanAccounts;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, phone.hashCode);
    _$hash = $jc(_$hash, accounts.hashCode);
    _$hash = $jc(_$hash, regTdAccounts.hashCode);
    _$hash = $jc(_$hash, regLoanAccounts.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SmsPhone')
          ..add('phone', phone)
          ..add('accounts', accounts)
          ..add('regTdAccounts', regTdAccounts)
          ..add('regLoanAccounts', regLoanAccounts))
        .toString();
  }
}

class SmsPhoneBuilder implements Builder<SmsPhone, SmsPhoneBuilder> {
  _$SmsPhone? _$v;

  String? _phone;
  String? get phone => _$this._phone;
  set phone(String? phone) => _$this._phone = phone;

  ListBuilder<String>? _accounts;
  ListBuilder<String> get accounts =>
      _$this._accounts ??= ListBuilder<String>();
  set accounts(ListBuilder<String>? accounts) => _$this._accounts = accounts;

  int? _regTdAccounts;
  int? get regTdAccounts => _$this._regTdAccounts;
  set regTdAccounts(int? regTdAccounts) =>
      _$this._regTdAccounts = regTdAccounts;

  int? _regLoanAccounts;
  int? get regLoanAccounts => _$this._regLoanAccounts;
  set regLoanAccounts(int? regLoanAccounts) =>
      _$this._regLoanAccounts = regLoanAccounts;

  SmsPhoneBuilder() {
    SmsPhone._defaults(this);
  }

  SmsPhoneBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _phone = $v.phone;
      _accounts = $v.accounts?.toBuilder();
      _regTdAccounts = $v.regTdAccounts;
      _regLoanAccounts = $v.regLoanAccounts;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SmsPhone other) {
    _$v = other as _$SmsPhone;
  }

  @override
  void update(void Function(SmsPhoneBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SmsPhone build() => _build();

  _$SmsPhone _build() {
    _$SmsPhone _$result;
    try {
      _$result = _$v ??
          _$SmsPhone._(
            phone: phone,
            accounts: _accounts?.build(),
            regTdAccounts: regTdAccounts,
            regLoanAccounts: regLoanAccounts,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'accounts';
        _accounts?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'SmsPhone', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
