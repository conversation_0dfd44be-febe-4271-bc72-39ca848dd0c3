// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_transaction_type_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateTransactionTypeRequest extends UpdateTransactionTypeRequest {
  @override
  final int? transactionTypeId;

  factory _$UpdateTransactionTypeRequest(
          [void Function(UpdateTransactionTypeRequestBuilder)? updates]) =>
      (UpdateTransactionTypeRequestBuilder()..update(updates))._build();

  _$UpdateTransactionTypeRequest._({this.transactionTypeId}) : super._();
  @override
  UpdateTransactionTypeRequest rebuild(
          void Function(UpdateTransactionTypeRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateTransactionTypeRequestBuilder toBuilder() =>
      UpdateTransactionTypeRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateTransactionTypeRequest &&
        transactionTypeId == other.transactionTypeId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionTypeId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateTransactionTypeRequest')
          ..add('transactionTypeId', transactionTypeId))
        .toString();
  }
}

class UpdateTransactionTypeRequestBuilder
    implements
        Builder<UpdateTransactionTypeRequest,
            UpdateTransactionTypeRequestBuilder> {
  _$UpdateTransactionTypeRequest? _$v;

  int? _transactionTypeId;
  int? get transactionTypeId => _$this._transactionTypeId;
  set transactionTypeId(int? transactionTypeId) =>
      _$this._transactionTypeId = transactionTypeId;

  UpdateTransactionTypeRequestBuilder() {
    UpdateTransactionTypeRequest._defaults(this);
  }

  UpdateTransactionTypeRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionTypeId = $v.transactionTypeId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateTransactionTypeRequest other) {
    _$v = other as _$UpdateTransactionTypeRequest;
  }

  @override
  void update(void Function(UpdateTransactionTypeRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateTransactionTypeRequest build() => _build();

  _$UpdateTransactionTypeRequest _build() {
    final _$result = _$v ??
        _$UpdateTransactionTypeRequest._(
          transactionTypeId: transactionTypeId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
