// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_get_trans_inquiry_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseGetTransInquiryDetailResponse
    extends BaseResponseGetTransInquiryDetailResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final GetTransInquiryDetailResponse? data;
  @override
  final String? message;

  factory _$BaseResponseGetTransInquiryDetailResponse(
          [void Function(BaseResponseGetTransInquiryDetailResponseBuilder)?
              updates]) =>
      (BaseResponseGetTransInquiryDetailResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseGetTransInquiryDetailResponse._(
      {this.success, this.code, this.data, this.message})
      : super._();
  @override
  BaseResponseGetTransInquiryDetailResponse rebuild(
          void Function(BaseResponseGetTransInquiryDetailResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseGetTransInquiryDetailResponseBuilder toBuilder() =>
      BaseResponseGetTransInquiryDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseGetTransInquiryDetailResponse &&
        success == other.success &&
        code == other.code &&
        data == other.data &&
        message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseGetTransInquiryDetailResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('data', data)
          ..add('message', message))
        .toString();
  }
}

class BaseResponseGetTransInquiryDetailResponseBuilder
    implements
        Builder<BaseResponseGetTransInquiryDetailResponse,
            BaseResponseGetTransInquiryDetailResponseBuilder> {
  _$BaseResponseGetTransInquiryDetailResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  GetTransInquiryDetailResponseBuilder? _data;
  GetTransInquiryDetailResponseBuilder get data =>
      _$this._data ??= GetTransInquiryDetailResponseBuilder();
  set data(GetTransInquiryDetailResponseBuilder? data) => _$this._data = data;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  BaseResponseGetTransInquiryDetailResponseBuilder() {
    BaseResponseGetTransInquiryDetailResponse._defaults(this);
  }

  BaseResponseGetTransInquiryDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _data = $v.data?.toBuilder();
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseGetTransInquiryDetailResponse other) {
    _$v = other as _$BaseResponseGetTransInquiryDetailResponse;
  }

  @override
  void update(
      void Function(BaseResponseGetTransInquiryDetailResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseGetTransInquiryDetailResponse build() => _build();

  _$BaseResponseGetTransInquiryDetailResponse _build() {
    _$BaseResponseGetTransInquiryDetailResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseGetTransInquiryDetailResponse._(
            success: success,
            code: code,
            data: _data?.build(),
            message: message,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseGetTransInquiryDetailResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
