//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_saving_account_request.g.dart';

/// ReviewSavingAccountRequest
///
/// Properties:
/// * [bankCif] - Cif no của khách hàng
/// * [transactionNo] - Số giao dịch
/// * [accountNumber] - Số tài khoản
/// * [amount] - Số tiền
/// * [currency] - Loại tiền: VND, USD
/// * [termId] - Id của kì hạn
/// * [rate] - L<PERSON>i suất
/// * [finalTypeId] - Id của phương thức tất toán
/// * [finalAccountNumber] - Số tài khoản nhận tiền sau khi tất toán
/// * [balance]
@BuiltValue()
abstract class ReviewSavingAccountRequest
    implements
        Built<ReviewSavingAccountRequest, ReviewSavingAccountRequestBuilder> {
  /// Cif no của khách hàng
  @BuiltValueField(wireName: r'bankCif')
  String? get bankCif;

  /// Số giao dịch
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số tài khoản
  @BuiltValueField(wireName: r'accountNumber')
  String? get accountNumber;

  /// Số tiền
  @BuiltValueField(wireName: r'amount')
  double? get amount;

  /// Loại tiền: VND, USD
  @BuiltValueField(wireName: r'currency')
  ReviewSavingAccountRequestCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  /// Id của kì hạn
  @BuiltValueField(wireName: r'termId')
  String? get termId;

  /// Lãi suất
  @BuiltValueField(wireName: r'rate')
  double? get rate;

  /// Id của phương thức tất toán
  @BuiltValueField(wireName: r'finalTypeId')
  int? get finalTypeId;

  /// Số tài khoản nhận tiền sau khi tất toán
  @BuiltValueField(wireName: r'finalAccountNumber')
  String? get finalAccountNumber;

  @BuiltValueField(wireName: r'balance')
  num? get balance;

  ReviewSavingAccountRequest._();

  factory ReviewSavingAccountRequest(
          [void updates(ReviewSavingAccountRequestBuilder b)]) =
      _$ReviewSavingAccountRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewSavingAccountRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewSavingAccountRequest> get serializer =>
      _$ReviewSavingAccountRequestSerializer();
}

class _$ReviewSavingAccountRequestSerializer
    implements PrimitiveSerializer<ReviewSavingAccountRequest> {
  @override
  final Iterable<Type> types = const [
    ReviewSavingAccountRequest,
    _$ReviewSavingAccountRequest
  ];

  @override
  final String wireName = r'ReviewSavingAccountRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.bankCif != null) {
      yield r'bankCif';
      yield serializers.serialize(
        object.bankCif,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNumber != null) {
      yield r'accountNumber';
      yield serializers.serialize(
        object.accountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType:
            const FullType.nullable(ReviewSavingAccountRequestCurrencyEnum),
      );
    }
    if (object.termId != null) {
      yield r'termId';
      yield serializers.serialize(
        object.termId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.rate != null) {
      yield r'rate';
      yield serializers.serialize(
        object.rate,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.finalTypeId != null) {
      yield r'finalTypeId';
      yield serializers.serialize(
        object.finalTypeId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.finalAccountNumber != null) {
      yield r'finalAccountNumber';
      yield serializers.serialize(
        object.finalAccountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.balance != null) {
      yield r'balance';
      yield serializers.serialize(
        object.balance,
        specifiedType: const FullType.nullable(num),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewSavingAccountRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'bankCif':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCif = valueDes;
          break;
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'accountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNumber = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(ReviewSavingAccountRequestCurrencyEnum),
          ) as ReviewSavingAccountRequestCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        case r'termId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.termId = valueDes;
          break;
        case r'rate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.rate = valueDes;
          break;
        case r'finalTypeId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.finalTypeId = valueDes;
          break;
        case r'finalAccountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.finalAccountNumber = valueDes;
          break;
        case r'balance':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.balance = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewSavingAccountRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewSavingAccountRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class ReviewSavingAccountRequestCurrencyEnum extends EnumClass {
  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'VND')
  static const ReviewSavingAccountRequestCurrencyEnum VND =
      _$reviewSavingAccountRequestCurrencyEnum_VND;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'USD')
  static const ReviewSavingAccountRequestCurrencyEnum USD =
      _$reviewSavingAccountRequestCurrencyEnum_USD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'ACB')
  static const ReviewSavingAccountRequestCurrencyEnum ACB =
      _$reviewSavingAccountRequestCurrencyEnum_ACB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'JPY')
  static const ReviewSavingAccountRequestCurrencyEnum JPY =
      _$reviewSavingAccountRequestCurrencyEnum_JPY;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const ReviewSavingAccountRequestCurrencyEnum GOLD =
      _$reviewSavingAccountRequestCurrencyEnum_GOLD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'EUR')
  static const ReviewSavingAccountRequestCurrencyEnum EUR =
      _$reviewSavingAccountRequestCurrencyEnum_EUR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GBP')
  static const ReviewSavingAccountRequestCurrencyEnum GBP =
      _$reviewSavingAccountRequestCurrencyEnum_GBP;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CHF')
  static const ReviewSavingAccountRequestCurrencyEnum CHF =
      _$reviewSavingAccountRequestCurrencyEnum_CHF;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'AUD')
  static const ReviewSavingAccountRequestCurrencyEnum AUD =
      _$reviewSavingAccountRequestCurrencyEnum_AUD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CAD')
  static const ReviewSavingAccountRequestCurrencyEnum CAD =
      _$reviewSavingAccountRequestCurrencyEnum_CAD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SGD')
  static const ReviewSavingAccountRequestCurrencyEnum SGD =
      _$reviewSavingAccountRequestCurrencyEnum_SGD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'THB')
  static const ReviewSavingAccountRequestCurrencyEnum THB =
      _$reviewSavingAccountRequestCurrencyEnum_THB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NOK')
  static const ReviewSavingAccountRequestCurrencyEnum NOK =
      _$reviewSavingAccountRequestCurrencyEnum_NOK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NZD')
  static const ReviewSavingAccountRequestCurrencyEnum NZD =
      _$reviewSavingAccountRequestCurrencyEnum_NZD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'DKK')
  static const ReviewSavingAccountRequestCurrencyEnum DKK =
      _$reviewSavingAccountRequestCurrencyEnum_DKK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'HKD')
  static const ReviewSavingAccountRequestCurrencyEnum HKD =
      _$reviewSavingAccountRequestCurrencyEnum_HKD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SEK')
  static const ReviewSavingAccountRequestCurrencyEnum SEK =
      _$reviewSavingAccountRequestCurrencyEnum_SEK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MYR')
  static const ReviewSavingAccountRequestCurrencyEnum MYR =
      _$reviewSavingAccountRequestCurrencyEnum_MYR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'XAU')
  static const ReviewSavingAccountRequestCurrencyEnum XAU =
      _$reviewSavingAccountRequestCurrencyEnum_XAU;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MMK')
  static const ReviewSavingAccountRequestCurrencyEnum MMK =
      _$reviewSavingAccountRequestCurrencyEnum_MMK;

  static Serializer<ReviewSavingAccountRequestCurrencyEnum> get serializer =>
      _$reviewSavingAccountRequestCurrencyEnumSerializer;

  const ReviewSavingAccountRequestCurrencyEnum._(String name) : super(name);

  static BuiltSet<ReviewSavingAccountRequestCurrencyEnum> get values =>
      _$reviewSavingAccountRequestCurrencyEnumValues;
  static ReviewSavingAccountRequestCurrencyEnum valueOf(String name) =>
      _$reviewSavingAccountRequestCurrencyEnumValueOf(name);
}
