//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'payment_phone_card_request.g.dart';

/// PaymentPhoneCardRequest
///
/// Properties:
/// * [softOtp]
/// * [transactionNo] - Mã giao dịch, được trả về sau khi gọi verify face.
/// * [accountNo] - Bankinf: Số tài khoản ăn theo mã cif
/// * [cardValue] - Mệnh giá thẻ
/// * [telephoneProvider] - Nhà cung cấp
/// * [quantity] - số lượng
/// * [amount] - Số tiền cần thanh toán
@BuiltValue()
abstract class PaymentPhoneCardRequest
    implements Built<PaymentPhoneCardRequest, PaymentPhoneCardRequestBuilder> {
  @BuiltValueField(wireName: r'softOtp')
  VerifySoftOtpRequest? get softOtp;

  /// Mã giao dịch, được trả về sau khi gọi verify face.
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Bankinf: Số tài khoản ăn theo mã cif
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  /// Mệnh giá thẻ
  @BuiltValueField(wireName: r'cardValue')
  num? get cardValue;

  /// Nhà cung cấp
  @BuiltValueField(wireName: r'telephoneProvider')
  PaymentPhoneCardRequestTelephoneProviderEnum? get telephoneProvider;
  // enum telephoneProviderEnum {  MOBIFONE,  VINAPHONE,  VIETTEL,  VIETNAMOBILE,  GMOBILE,  };

  /// số lượng
  @BuiltValueField(wireName: r'quantity')
  int? get quantity;

  /// Số tiền cần thanh toán
  @BuiltValueField(wireName: r'amount')
  num? get amount;

  PaymentPhoneCardRequest._();

  factory PaymentPhoneCardRequest(
          [void updates(PaymentPhoneCardRequestBuilder b)]) =
      _$PaymentPhoneCardRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PaymentPhoneCardRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PaymentPhoneCardRequest> get serializer =>
      _$PaymentPhoneCardRequestSerializer();
}

class _$PaymentPhoneCardRequestSerializer
    implements PrimitiveSerializer<PaymentPhoneCardRequest> {
  @override
  final Iterable<Type> types = const [
    PaymentPhoneCardRequest,
    _$PaymentPhoneCardRequest
  ];

  @override
  final String wireName = r'PaymentPhoneCardRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PaymentPhoneCardRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.softOtp != null) {
      yield r'softOtp';
      yield serializers.serialize(
        object.softOtp,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cardValue != null) {
      yield r'cardValue';
      yield serializers.serialize(
        object.cardValue,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.telephoneProvider != null) {
      yield r'telephoneProvider';
      yield serializers.serialize(
        object.telephoneProvider,
        specifiedType: const FullType.nullable(
            PaymentPhoneCardRequestTelephoneProviderEnum),
      );
    }
    if (object.quantity != null) {
      yield r'quantity';
      yield serializers.serialize(
        object.quantity,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PaymentPhoneCardRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PaymentPhoneCardRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'softOtp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.softOtp.replace(valueDes);
          break;
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        case r'cardValue':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.cardValue = valueDes;
          break;
        case r'telephoneProvider':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                PaymentPhoneCardRequestTelephoneProviderEnum),
          ) as PaymentPhoneCardRequestTelephoneProviderEnum?;
          if (valueDes == null) continue;
          result.telephoneProvider = valueDes;
          break;
        case r'quantity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.quantity = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PaymentPhoneCardRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PaymentPhoneCardRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PaymentPhoneCardRequestTelephoneProviderEnum extends EnumClass {
  /// Nhà cung cấp
  @BuiltValueEnumConst(wireName: r'MOBIFONE')
  static const PaymentPhoneCardRequestTelephoneProviderEnum MOBIFONE =
      _$paymentPhoneCardRequestTelephoneProviderEnum_MOBIFONE;

  /// Nhà cung cấp
  @BuiltValueEnumConst(wireName: r'VINAPHONE')
  static const PaymentPhoneCardRequestTelephoneProviderEnum VINAPHONE =
      _$paymentPhoneCardRequestTelephoneProviderEnum_VINAPHONE;

  /// Nhà cung cấp
  @BuiltValueEnumConst(wireName: r'VIETTEL')
  static const PaymentPhoneCardRequestTelephoneProviderEnum VIETTEL =
      _$paymentPhoneCardRequestTelephoneProviderEnum_VIETTEL;

  /// Nhà cung cấp
  @BuiltValueEnumConst(wireName: r'VIETNAMOBILE')
  static const PaymentPhoneCardRequestTelephoneProviderEnum VIETNAMOBILE =
      _$paymentPhoneCardRequestTelephoneProviderEnum_VIETNAMOBILE;

  /// Nhà cung cấp
  @BuiltValueEnumConst(wireName: r'GMOBILE')
  static const PaymentPhoneCardRequestTelephoneProviderEnum GMOBILE =
      _$paymentPhoneCardRequestTelephoneProviderEnum_GMOBILE;

  static Serializer<PaymentPhoneCardRequestTelephoneProviderEnum>
      get serializer =>
          _$paymentPhoneCardRequestTelephoneProviderEnumSerializer;

  const PaymentPhoneCardRequestTelephoneProviderEnum._(String name)
      : super(name);

  static BuiltSet<PaymentPhoneCardRequestTelephoneProviderEnum> get values =>
      _$paymentPhoneCardRequestTelephoneProviderEnumValues;
  static PaymentPhoneCardRequestTelephoneProviderEnum valueOf(String name) =>
      _$paymentPhoneCardRequestTelephoneProviderEnumValueOf(name);
}
