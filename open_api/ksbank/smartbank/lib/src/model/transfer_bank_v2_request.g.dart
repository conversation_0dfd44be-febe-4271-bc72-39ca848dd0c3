// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_bank_v2_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferBankV2Request extends TransferBankV2Request {
  @override
  final String? transactionNo;
  @override
  final int? whoCharge;
  @override
  final int? chargeAmount;
  @override
  final int? isAccount;
  @override
  final String? bankCodeId;
  @override
  final String? aliasName;
  @override
  final int? is247;
  @override
  final int? isNow;
  @override
  final int? isSaveToTemplate;
  @override
  final String? description;
  @override
  final int? transactionCategoryId;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;
  @override
  final SchedulingObject? schedule;
  @override
  final int? regionId;
  @override
  final int? bankId;
  @override
  final String? branchId;
  @override
  final String? branchName;
  @override
  final String? beneficiaryName;
  @override
  final String? scheduleId;
  @override
  final String? channelCode;
  @override
  final String? idCard;
  @override
  final Date? issueDate;
  @override
  final String? placeBy;

  factory _$TransferBankV2Request(
          [void Function(TransferBankV2RequestBuilder)? updates]) =>
      (TransferBankV2RequestBuilder()..update(updates))._build();

  _$TransferBankV2Request._(
      {this.transactionNo,
      this.whoCharge,
      this.chargeAmount,
      this.isAccount,
      this.bankCodeId,
      this.aliasName,
      this.is247,
      this.isNow,
      this.isSaveToTemplate,
      this.description,
      this.transactionCategoryId,
      this.verifySoftOtp,
      this.schedule,
      this.regionId,
      this.bankId,
      this.branchId,
      this.branchName,
      this.beneficiaryName,
      this.scheduleId,
      this.channelCode,
      this.idCard,
      this.issueDate,
      this.placeBy})
      : super._();
  @override
  TransferBankV2Request rebuild(
          void Function(TransferBankV2RequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferBankV2RequestBuilder toBuilder() =>
      TransferBankV2RequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferBankV2Request &&
        transactionNo == other.transactionNo &&
        whoCharge == other.whoCharge &&
        chargeAmount == other.chargeAmount &&
        isAccount == other.isAccount &&
        bankCodeId == other.bankCodeId &&
        aliasName == other.aliasName &&
        is247 == other.is247 &&
        isNow == other.isNow &&
        isSaveToTemplate == other.isSaveToTemplate &&
        description == other.description &&
        transactionCategoryId == other.transactionCategoryId &&
        verifySoftOtp == other.verifySoftOtp &&
        schedule == other.schedule &&
        regionId == other.regionId &&
        bankId == other.bankId &&
        branchId == other.branchId &&
        branchName == other.branchName &&
        beneficiaryName == other.beneficiaryName &&
        scheduleId == other.scheduleId &&
        channelCode == other.channelCode &&
        idCard == other.idCard &&
        issueDate == other.issueDate &&
        placeBy == other.placeBy;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, whoCharge.hashCode);
    _$hash = $jc(_$hash, chargeAmount.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, bankCodeId.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, is247.hashCode);
    _$hash = $jc(_$hash, isNow.hashCode);
    _$hash = $jc(_$hash, isSaveToTemplate.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, transactionCategoryId.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jc(_$hash, schedule.hashCode);
    _$hash = $jc(_$hash, regionId.hashCode);
    _$hash = $jc(_$hash, bankId.hashCode);
    _$hash = $jc(_$hash, branchId.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, beneficiaryName.hashCode);
    _$hash = $jc(_$hash, scheduleId.hashCode);
    _$hash = $jc(_$hash, channelCode.hashCode);
    _$hash = $jc(_$hash, idCard.hashCode);
    _$hash = $jc(_$hash, issueDate.hashCode);
    _$hash = $jc(_$hash, placeBy.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferBankV2Request')
          ..add('transactionNo', transactionNo)
          ..add('whoCharge', whoCharge)
          ..add('chargeAmount', chargeAmount)
          ..add('isAccount', isAccount)
          ..add('bankCodeId', bankCodeId)
          ..add('aliasName', aliasName)
          ..add('is247', is247)
          ..add('isNow', isNow)
          ..add('isSaveToTemplate', isSaveToTemplate)
          ..add('description', description)
          ..add('transactionCategoryId', transactionCategoryId)
          ..add('verifySoftOtp', verifySoftOtp)
          ..add('schedule', schedule)
          ..add('regionId', regionId)
          ..add('bankId', bankId)
          ..add('branchId', branchId)
          ..add('branchName', branchName)
          ..add('beneficiaryName', beneficiaryName)
          ..add('scheduleId', scheduleId)
          ..add('channelCode', channelCode)
          ..add('idCard', idCard)
          ..add('issueDate', issueDate)
          ..add('placeBy', placeBy))
        .toString();
  }
}

class TransferBankV2RequestBuilder
    implements Builder<TransferBankV2Request, TransferBankV2RequestBuilder> {
  _$TransferBankV2Request? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  int? _whoCharge;
  int? get whoCharge => _$this._whoCharge;
  set whoCharge(int? whoCharge) => _$this._whoCharge = whoCharge;

  int? _chargeAmount;
  int? get chargeAmount => _$this._chargeAmount;
  set chargeAmount(int? chargeAmount) => _$this._chargeAmount = chargeAmount;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _bankCodeId;
  String? get bankCodeId => _$this._bankCodeId;
  set bankCodeId(String? bankCodeId) => _$this._bankCodeId = bankCodeId;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  int? _is247;
  int? get is247 => _$this._is247;
  set is247(int? is247) => _$this._is247 = is247;

  int? _isNow;
  int? get isNow => _$this._isNow;
  set isNow(int? isNow) => _$this._isNow = isNow;

  int? _isSaveToTemplate;
  int? get isSaveToTemplate => _$this._isSaveToTemplate;
  set isSaveToTemplate(int? isSaveToTemplate) =>
      _$this._isSaveToTemplate = isSaveToTemplate;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _transactionCategoryId;
  int? get transactionCategoryId => _$this._transactionCategoryId;
  set transactionCategoryId(int? transactionCategoryId) =>
      _$this._transactionCategoryId = transactionCategoryId;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  SchedulingObjectBuilder? _schedule;
  SchedulingObjectBuilder get schedule =>
      _$this._schedule ??= SchedulingObjectBuilder();
  set schedule(SchedulingObjectBuilder? schedule) =>
      _$this._schedule = schedule;

  int? _regionId;
  int? get regionId => _$this._regionId;
  set regionId(int? regionId) => _$this._regionId = regionId;

  int? _bankId;
  int? get bankId => _$this._bankId;
  set bankId(int? bankId) => _$this._bankId = bankId;

  String? _branchId;
  String? get branchId => _$this._branchId;
  set branchId(String? branchId) => _$this._branchId = branchId;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _beneficiaryName;
  String? get beneficiaryName => _$this._beneficiaryName;
  set beneficiaryName(String? beneficiaryName) =>
      _$this._beneficiaryName = beneficiaryName;

  String? _scheduleId;
  String? get scheduleId => _$this._scheduleId;
  set scheduleId(String? scheduleId) => _$this._scheduleId = scheduleId;

  String? _channelCode;
  String? get channelCode => _$this._channelCode;
  set channelCode(String? channelCode) => _$this._channelCode = channelCode;

  String? _idCard;
  String? get idCard => _$this._idCard;
  set idCard(String? idCard) => _$this._idCard = idCard;

  Date? _issueDate;
  Date? get issueDate => _$this._issueDate;
  set issueDate(Date? issueDate) => _$this._issueDate = issueDate;

  String? _placeBy;
  String? get placeBy => _$this._placeBy;
  set placeBy(String? placeBy) => _$this._placeBy = placeBy;

  TransferBankV2RequestBuilder() {
    TransferBankV2Request._defaults(this);
  }

  TransferBankV2RequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _whoCharge = $v.whoCharge;
      _chargeAmount = $v.chargeAmount;
      _isAccount = $v.isAccount;
      _bankCodeId = $v.bankCodeId;
      _aliasName = $v.aliasName;
      _is247 = $v.is247;
      _isNow = $v.isNow;
      _isSaveToTemplate = $v.isSaveToTemplate;
      _description = $v.description;
      _transactionCategoryId = $v.transactionCategoryId;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _schedule = $v.schedule?.toBuilder();
      _regionId = $v.regionId;
      _bankId = $v.bankId;
      _branchId = $v.branchId;
      _branchName = $v.branchName;
      _beneficiaryName = $v.beneficiaryName;
      _scheduleId = $v.scheduleId;
      _channelCode = $v.channelCode;
      _idCard = $v.idCard;
      _issueDate = $v.issueDate;
      _placeBy = $v.placeBy;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferBankV2Request other) {
    _$v = other as _$TransferBankV2Request;
  }

  @override
  void update(void Function(TransferBankV2RequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferBankV2Request build() => _build();

  _$TransferBankV2Request _build() {
    _$TransferBankV2Request _$result;
    try {
      _$result = _$v ??
          _$TransferBankV2Request._(
            transactionNo: transactionNo,
            whoCharge: whoCharge,
            chargeAmount: chargeAmount,
            isAccount: isAccount,
            bankCodeId: bankCodeId,
            aliasName: aliasName,
            is247: is247,
            isNow: isNow,
            isSaveToTemplate: isSaveToTemplate,
            description: description,
            transactionCategoryId: transactionCategoryId,
            verifySoftOtp: _verifySoftOtp?.build(),
            schedule: _schedule?.build(),
            regionId: regionId,
            bankId: bankId,
            branchId: branchId,
            branchName: branchName,
            beneficiaryName: beneficiaryName,
            scheduleId: scheduleId,
            channelCode: channelCode,
            idCard: idCard,
            issueDate: issueDate,
            placeBy: placeBy,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
        _$failedField = 'schedule';
        _schedule?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransferBankV2Request', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
