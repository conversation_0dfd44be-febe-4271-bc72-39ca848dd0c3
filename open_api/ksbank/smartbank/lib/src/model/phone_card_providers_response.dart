//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:ksbank_api_smartbank/src/model/phone_card_response.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'phone_card_providers_response.g.dart';

/// Danh sách các nhà mạng cùng mã thẻ
///
/// Properties:
/// * [telephoneProvider] - Nhà mạng
/// * [providerName] - Tên nhà mạng
/// * [cards] - Danh sách mệnh giá thẻ
/// * [iconUrl] - Đường dẫn icon của nhà mạng
@BuiltValue()
abstract class PhoneCardProvidersResponse
    implements
        Built<PhoneCardProvidersResponse, PhoneCardProvidersResponseBuilder> {
  /// Nhà mạng
  @BuiltValueField(wireName: r'telephoneProvider')
  PhoneCardProvidersResponseTelephoneProviderEnum? get telephoneProvider;
  // enum telephoneProviderEnum {  MOBIFONE,  VINAPHONE,  VIETTEL,  VIETNAMOBILE,  GMOBILE,  };

  /// Tên nhà mạng
  @BuiltValueField(wireName: r'providerName')
  String? get providerName;

  /// Danh sách mệnh giá thẻ
  @BuiltValueField(wireName: r'cards')
  BuiltList<PhoneCardResponse>? get cards;

  /// Đường dẫn icon của nhà mạng
  @BuiltValueField(wireName: r'iconUrl')
  String? get iconUrl;

  PhoneCardProvidersResponse._();

  factory PhoneCardProvidersResponse(
          [void updates(PhoneCardProvidersResponseBuilder b)]) =
      _$PhoneCardProvidersResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PhoneCardProvidersResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PhoneCardProvidersResponse> get serializer =>
      _$PhoneCardProvidersResponseSerializer();
}

class _$PhoneCardProvidersResponseSerializer
    implements PrimitiveSerializer<PhoneCardProvidersResponse> {
  @override
  final Iterable<Type> types = const [
    PhoneCardProvidersResponse,
    _$PhoneCardProvidersResponse
  ];

  @override
  final String wireName = r'PhoneCardProvidersResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PhoneCardProvidersResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.telephoneProvider != null) {
      yield r'telephoneProvider';
      yield serializers.serialize(
        object.telephoneProvider,
        specifiedType: const FullType.nullable(
            PhoneCardProvidersResponseTelephoneProviderEnum),
      );
    }
    if (object.providerName != null) {
      yield r'providerName';
      yield serializers.serialize(
        object.providerName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cards != null) {
      yield r'cards';
      yield serializers.serialize(
        object.cards,
        specifiedType:
            const FullType.nullable(BuiltList, [FullType(PhoneCardResponse)]),
      );
    }
    if (object.iconUrl != null) {
      yield r'iconUrl';
      yield serializers.serialize(
        object.iconUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PhoneCardProvidersResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PhoneCardProvidersResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'telephoneProvider':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                PhoneCardProvidersResponseTelephoneProviderEnum),
          ) as PhoneCardProvidersResponseTelephoneProviderEnum?;
          if (valueDes == null) continue;
          result.telephoneProvider = valueDes;
          break;
        case r'providerName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.providerName = valueDes;
          break;
        case r'cards':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                BuiltList, [FullType(PhoneCardResponse)]),
          ) as BuiltList<PhoneCardResponse>?;
          if (valueDes == null) continue;
          result.cards.replace(valueDes);
          break;
        case r'iconUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.iconUrl = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PhoneCardProvidersResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PhoneCardProvidersResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PhoneCardProvidersResponseTelephoneProviderEnum extends EnumClass {
  /// Nhà mạng
  @BuiltValueEnumConst(wireName: r'MOBIFONE')
  static const PhoneCardProvidersResponseTelephoneProviderEnum MOBIFONE =
      _$phoneCardProvidersResponseTelephoneProviderEnum_MOBIFONE;

  /// Nhà mạng
  @BuiltValueEnumConst(wireName: r'VINAPHONE')
  static const PhoneCardProvidersResponseTelephoneProviderEnum VINAPHONE =
      _$phoneCardProvidersResponseTelephoneProviderEnum_VINAPHONE;

  /// Nhà mạng
  @BuiltValueEnumConst(wireName: r'VIETTEL')
  static const PhoneCardProvidersResponseTelephoneProviderEnum VIETTEL =
      _$phoneCardProvidersResponseTelephoneProviderEnum_VIETTEL;

  /// Nhà mạng
  @BuiltValueEnumConst(wireName: r'VIETNAMOBILE')
  static const PhoneCardProvidersResponseTelephoneProviderEnum VIETNAMOBILE =
      _$phoneCardProvidersResponseTelephoneProviderEnum_VIETNAMOBILE;

  /// Nhà mạng
  @BuiltValueEnumConst(wireName: r'GMOBILE')
  static const PhoneCardProvidersResponseTelephoneProviderEnum GMOBILE =
      _$phoneCardProvidersResponseTelephoneProviderEnum_GMOBILE;

  static Serializer<PhoneCardProvidersResponseTelephoneProviderEnum>
      get serializer =>
          _$phoneCardProvidersResponseTelephoneProviderEnumSerializer;

  const PhoneCardProvidersResponseTelephoneProviderEnum._(String name)
      : super(name);

  static BuiltSet<PhoneCardProvidersResponseTelephoneProviderEnum> get values =>
      _$phoneCardProvidersResponseTelephoneProviderEnumValues;
  static PhoneCardProvidersResponseTelephoneProviderEnum valueOf(String name) =>
      _$phoneCardProvidersResponseTelephoneProviderEnumValueOf(name);
}
