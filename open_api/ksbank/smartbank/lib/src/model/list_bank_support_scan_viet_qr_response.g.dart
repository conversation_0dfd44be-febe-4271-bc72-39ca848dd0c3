// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_bank_support_scan_viet_qr_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListBankSupportScanVietQrResponse
    extends ListBankSupportScanVietQrResponse {
  @override
  final BuiltList<BankCodeDto>? listBanks;

  factory _$ListBankSupportScanVietQrResponse(
          [void Function(ListBankSupportScanVietQrResponseBuilder)? updates]) =>
      (ListBankSupportScanVietQrResponseBuilder()..update(updates))._build();

  _$ListBankSupportScanVietQrResponse._({this.listBanks}) : super._();
  @override
  ListBankSupportScanVietQrResponse rebuild(
          void Function(ListBankSupportScanVietQrResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListBankSupportScanVietQrResponseBuilder toBuilder() =>
      ListBankSupportScanVietQrResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListBankSupportScanVietQrResponse &&
        listBanks == other.listBanks;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, listBanks.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListBankSupportScanVietQrResponse')
          ..add('listBanks', listBanks))
        .toString();
  }
}

class ListBankSupportScanVietQrResponseBuilder
    implements
        Builder<ListBankSupportScanVietQrResponse,
            ListBankSupportScanVietQrResponseBuilder> {
  _$ListBankSupportScanVietQrResponse? _$v;

  ListBuilder<BankCodeDto>? _listBanks;
  ListBuilder<BankCodeDto> get listBanks =>
      _$this._listBanks ??= ListBuilder<BankCodeDto>();
  set listBanks(ListBuilder<BankCodeDto>? listBanks) =>
      _$this._listBanks = listBanks;

  ListBankSupportScanVietQrResponseBuilder() {
    ListBankSupportScanVietQrResponse._defaults(this);
  }

  ListBankSupportScanVietQrResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _listBanks = $v.listBanks?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListBankSupportScanVietQrResponse other) {
    _$v = other as _$ListBankSupportScanVietQrResponse;
  }

  @override
  void update(
      void Function(ListBankSupportScanVietQrResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListBankSupportScanVietQrResponse build() => _build();

  _$ListBankSupportScanVietQrResponse _build() {
    _$ListBankSupportScanVietQrResponse _$result;
    try {
      _$result = _$v ??
          _$ListBankSupportScanVietQrResponse._(
            listBanks: _listBanks?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'listBanks';
        _listBanks?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListBankSupportScanVietQrResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
