// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pre_check_reset_pin_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PreCheckResetPINCardResponse extends PreCheckResetPINCardResponse {
  @override
  final bool? valid;

  factory _$PreCheckResetPINCardResponse(
          [void Function(PreCheckResetPINCardResponseBuilder)? updates]) =>
      (PreCheckResetPINCardResponseBuilder()..update(updates))._build();

  _$PreCheckResetPINCardResponse._({this.valid}) : super._();
  @override
  PreCheckResetPINCardResponse rebuild(
          void Function(PreCheckResetPINCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PreCheckResetPINCardResponseBuilder toBuilder() =>
      PreCheckResetPINCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PreCheckResetPINCardResponse && valid == other.valid;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, valid.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PreCheckResetPINCardResponse')
          ..add('valid', valid))
        .toString();
  }
}

class PreCheckResetPINCardResponseBuilder
    implements
        Builder<PreCheckResetPINCardResponse,
            PreCheckResetPINCardResponseBuilder> {
  _$PreCheckResetPINCardResponse? _$v;

  bool? _valid;
  bool? get valid => _$this._valid;
  set valid(bool? valid) => _$this._valid = valid;

  PreCheckResetPINCardResponseBuilder() {
    PreCheckResetPINCardResponse._defaults(this);
  }

  PreCheckResetPINCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _valid = $v.valid;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PreCheckResetPINCardResponse other) {
    _$v = other as _$PreCheckResetPINCardResponse;
  }

  @override
  void update(void Function(PreCheckResetPINCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PreCheckResetPINCardResponse build() => _build();

  _$PreCheckResetPINCardResponse _build() {
    final _$result = _$v ??
        _$PreCheckResetPINCardResponse._(
          valid: valid,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
