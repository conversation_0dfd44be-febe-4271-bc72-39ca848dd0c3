// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'refund_loan_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RefundLoanRequest extends RefundLoanRequest {
  @override
  final String? bankCif;
  @override
  final String? sourceAccountNo;
  @override
  final num? amount;
  @override
  final String? accountNo;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;
  @override
  final num? balance;

  factory _$RefundLoanRequest(
          [void Function(RefundLoanRequestBuilder)? updates]) =>
      (RefundLoanRequestBuilder()..update(updates))._build();

  _$RefundLoanRequest._(
      {this.bankCif,
      this.sourceAccountNo,
      this.amount,
      this.accountNo,
      this.verifySoftOtp,
      this.balance})
      : super._();
  @override
  RefundLoanRequest rebuild(void Function(RefundLoanRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RefundLoanRequestBuilder toBuilder() =>
      RefundLoanRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RefundLoanRequest &&
        bankCif == other.bankCif &&
        sourceAccountNo == other.sourceAccountNo &&
        amount == other.amount &&
        accountNo == other.accountNo &&
        verifySoftOtp == other.verifySoftOtp &&
        balance == other.balance;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RefundLoanRequest')
          ..add('bankCif', bankCif)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('amount', amount)
          ..add('accountNo', accountNo)
          ..add('verifySoftOtp', verifySoftOtp)
          ..add('balance', balance))
        .toString();
  }
}

class RefundLoanRequestBuilder
    implements Builder<RefundLoanRequest, RefundLoanRequestBuilder> {
  _$RefundLoanRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  RefundLoanRequestBuilder() {
    RefundLoanRequest._defaults(this);
  }

  RefundLoanRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _sourceAccountNo = $v.sourceAccountNo;
      _amount = $v.amount;
      _accountNo = $v.accountNo;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _balance = $v.balance;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RefundLoanRequest other) {
    _$v = other as _$RefundLoanRequest;
  }

  @override
  void update(void Function(RefundLoanRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RefundLoanRequest build() => _build();

  _$RefundLoanRequest _build() {
    _$RefundLoanRequest _$result;
    try {
      _$result = _$v ??
          _$RefundLoanRequest._(
            bankCif: bankCif,
            sourceAccountNo: sourceAccountNo,
            amount: amount,
            accountNo: accountNo,
            verifySoftOtp: _verifySoftOtp?.build(),
            balance: balance,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'RefundLoanRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
