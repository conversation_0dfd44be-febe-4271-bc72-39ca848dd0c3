// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'issue_virtual_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$IssueVirtualCardRequest extends IssueVirtualCardRequest {
  @override
  final String? transactionNo;
  @override
  final String? productCode;
  @override
  final String? accountNo;
  @override
  final String? branchCode;
  @override
  final String? referralCode;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;

  factory _$IssueVirtualCardRequest(
          [void Function(IssueVirtualCardRequestBuilder)? updates]) =>
      (IssueVirtualCardRequestBuilder()..update(updates))._build();

  _$IssueVirtualCardRequest._(
      {this.transactionNo,
      this.productCode,
      this.accountNo,
      this.branchCode,
      this.referralCode,
      this.verifySoftOtp})
      : super._();
  @override
  IssueVirtualCardRequest rebuild(
          void Function(IssueVirtualCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  IssueVirtualCardRequestBuilder toBuilder() =>
      IssueVirtualCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is IssueVirtualCardRequest &&
        transactionNo == other.transactionNo &&
        productCode == other.productCode &&
        accountNo == other.accountNo &&
        branchCode == other.branchCode &&
        referralCode == other.referralCode &&
        verifySoftOtp == other.verifySoftOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, branchCode.hashCode);
    _$hash = $jc(_$hash, referralCode.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'IssueVirtualCardRequest')
          ..add('transactionNo', transactionNo)
          ..add('productCode', productCode)
          ..add('accountNo', accountNo)
          ..add('branchCode', branchCode)
          ..add('referralCode', referralCode)
          ..add('verifySoftOtp', verifySoftOtp))
        .toString();
  }
}

class IssueVirtualCardRequestBuilder
    implements
        Builder<IssueVirtualCardRequest, IssueVirtualCardRequestBuilder> {
  _$IssueVirtualCardRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _branchCode;
  String? get branchCode => _$this._branchCode;
  set branchCode(String? branchCode) => _$this._branchCode = branchCode;

  String? _referralCode;
  String? get referralCode => _$this._referralCode;
  set referralCode(String? referralCode) => _$this._referralCode = referralCode;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  IssueVirtualCardRequestBuilder() {
    IssueVirtualCardRequest._defaults(this);
  }

  IssueVirtualCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _productCode = $v.productCode;
      _accountNo = $v.accountNo;
      _branchCode = $v.branchCode;
      _referralCode = $v.referralCode;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(IssueVirtualCardRequest other) {
    _$v = other as _$IssueVirtualCardRequest;
  }

  @override
  void update(void Function(IssueVirtualCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  IssueVirtualCardRequest build() => _build();

  _$IssueVirtualCardRequest _build() {
    _$IssueVirtualCardRequest _$result;
    try {
      _$result = _$v ??
          _$IssueVirtualCardRequest._(
            transactionNo: transactionNo,
            productCode: productCode,
            accountNo: accountNo,
            branchCode: branchCode,
            referralCode: referralCode,
            verifySoftOtp: _verifySoftOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'IssueVirtualCardRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
