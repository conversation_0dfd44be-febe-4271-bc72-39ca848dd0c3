// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remove_bill_schedule_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RemoveBillScheduleRequest extends RemoveBillScheduleRequest {
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final String? scheduleId;

  factory _$RemoveBillScheduleRequest(
          [void Function(RemoveBillScheduleRequestBuilder)? updates]) =>
      (RemoveBillScheduleRequestBuilder()..update(updates))._build();

  _$RemoveBillScheduleRequest._({this.softOtp, this.scheduleId}) : super._();
  @override
  RemoveBillScheduleRequest rebuild(
          void Function(RemoveBillScheduleRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RemoveBillScheduleRequestBuilder toBuilder() =>
      RemoveBillScheduleRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RemoveBillScheduleRequest &&
        softOtp == other.softOtp &&
        scheduleId == other.scheduleId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, scheduleId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RemoveBillScheduleRequest')
          ..add('softOtp', softOtp)
          ..add('scheduleId', scheduleId))
        .toString();
  }
}

class RemoveBillScheduleRequestBuilder
    implements
        Builder<RemoveBillScheduleRequest, RemoveBillScheduleRequestBuilder> {
  _$RemoveBillScheduleRequest? _$v;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  String? _scheduleId;
  String? get scheduleId => _$this._scheduleId;
  set scheduleId(String? scheduleId) => _$this._scheduleId = scheduleId;

  RemoveBillScheduleRequestBuilder() {
    RemoveBillScheduleRequest._defaults(this);
  }

  RemoveBillScheduleRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _softOtp = $v.softOtp?.toBuilder();
      _scheduleId = $v.scheduleId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RemoveBillScheduleRequest other) {
    _$v = other as _$RemoveBillScheduleRequest;
  }

  @override
  void update(void Function(RemoveBillScheduleRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RemoveBillScheduleRequest build() => _build();

  _$RemoveBillScheduleRequest _build() {
    _$RemoveBillScheduleRequest _$result;
    try {
      _$result = _$v ??
          _$RemoveBillScheduleRequest._(
            softOtp: _softOtp?.build(),
            scheduleId: scheduleId,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'RemoveBillScheduleRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
