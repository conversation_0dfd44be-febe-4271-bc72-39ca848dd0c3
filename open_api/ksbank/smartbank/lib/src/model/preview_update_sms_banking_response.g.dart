// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'preview_update_sms_banking_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PreviewUpdateSmsBankingResponseAuthTypeEnum
    _$previewUpdateSmsBankingResponseAuthTypeEnum_PASSWORD =
    const PreviewUpdateSmsBankingResponseAuthTypeEnum._('PASSWORD');
const PreviewUpdateSmsBankingResponseAuthTypeEnum
    _$previewUpdateSmsBankingResponseAuthTypeEnum_SMS_OTP =
    const PreviewUpdateSmsBankingResponseAuthTypeEnum._('SMS_OTP');
const PreviewUpdateSmsBankingResponseAuthTypeEnum
    _$previewUpdateSmsBankingResponseAuthTypeEnum_T_OTP =
    const PreviewUpdateSmsBankingResponseAuthTypeEnum._('T_OTP');
const PreviewUpdateSmsBankingResponseAuthTypeEnum
    _$previewUpdateSmsBankingResponseAuthTypeEnum_H_OTP =
    const PreviewUpdateSmsBankingResponseAuthTypeEnum._('H_OTP');
const PreviewUpdateSmsBankingResponseAuthTypeEnum
    _$previewUpdateSmsBankingResponseAuthTypeEnum_SOFT_OTP =
    const PreviewUpdateSmsBankingResponseAuthTypeEnum._('SOFT_OTP');

PreviewUpdateSmsBankingResponseAuthTypeEnum
    _$previewUpdateSmsBankingResponseAuthTypeEnumValueOf(String name) {
  switch (name) {
    case 'PASSWORD':
      return _$previewUpdateSmsBankingResponseAuthTypeEnum_PASSWORD;
    case 'SMS_OTP':
      return _$previewUpdateSmsBankingResponseAuthTypeEnum_SMS_OTP;
    case 'T_OTP':
      return _$previewUpdateSmsBankingResponseAuthTypeEnum_T_OTP;
    case 'H_OTP':
      return _$previewUpdateSmsBankingResponseAuthTypeEnum_H_OTP;
    case 'SOFT_OTP':
      return _$previewUpdateSmsBankingResponseAuthTypeEnum_SOFT_OTP;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PreviewUpdateSmsBankingResponseAuthTypeEnum>
    _$previewUpdateSmsBankingResponseAuthTypeEnumValues = BuiltSet<
        PreviewUpdateSmsBankingResponseAuthTypeEnum>(const <PreviewUpdateSmsBankingResponseAuthTypeEnum>[
  _$previewUpdateSmsBankingResponseAuthTypeEnum_PASSWORD,
  _$previewUpdateSmsBankingResponseAuthTypeEnum_SMS_OTP,
  _$previewUpdateSmsBankingResponseAuthTypeEnum_T_OTP,
  _$previewUpdateSmsBankingResponseAuthTypeEnum_H_OTP,
  _$previewUpdateSmsBankingResponseAuthTypeEnum_SOFT_OTP,
]);

Serializer<PreviewUpdateSmsBankingResponseAuthTypeEnum>
    _$previewUpdateSmsBankingResponseAuthTypeEnumSerializer =
    _$PreviewUpdateSmsBankingResponseAuthTypeEnumSerializer();

class _$PreviewUpdateSmsBankingResponseAuthTypeEnumSerializer
    implements
        PrimitiveSerializer<PreviewUpdateSmsBankingResponseAuthTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PASSWORD': 'PASSWORD',
    'SMS_OTP': 'SMS_OTP',
    'T_OTP': 'T_OTP',
    'H_OTP': 'H_OTP',
    'SOFT_OTP': 'SOFT_OTP',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PASSWORD': 'PASSWORD',
    'SMS_OTP': 'SMS_OTP',
    'T_OTP': 'T_OTP',
    'H_OTP': 'H_OTP',
    'SOFT_OTP': 'SOFT_OTP',
  };

  @override
  final Iterable<Type> types = const <Type>[
    PreviewUpdateSmsBankingResponseAuthTypeEnum
  ];
  @override
  final String wireName = 'PreviewUpdateSmsBankingResponseAuthTypeEnum';

  @override
  Object serialize(Serializers serializers,
          PreviewUpdateSmsBankingResponseAuthTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PreviewUpdateSmsBankingResponseAuthTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PreviewUpdateSmsBankingResponseAuthTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PreviewUpdateSmsBankingResponse
    extends PreviewUpdateSmsBankingResponse {
  @override
  final String? requestId;
  @override
  final PreviewUpdateSmsBankingResponseAuthTypeEnum? authType;

  factory _$PreviewUpdateSmsBankingResponse(
          [void Function(PreviewUpdateSmsBankingResponseBuilder)? updates]) =>
      (PreviewUpdateSmsBankingResponseBuilder()..update(updates))._build();

  _$PreviewUpdateSmsBankingResponse._({this.requestId, this.authType})
      : super._();
  @override
  PreviewUpdateSmsBankingResponse rebuild(
          void Function(PreviewUpdateSmsBankingResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PreviewUpdateSmsBankingResponseBuilder toBuilder() =>
      PreviewUpdateSmsBankingResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PreviewUpdateSmsBankingResponse &&
        requestId == other.requestId &&
        authType == other.authType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, requestId.hashCode);
    _$hash = $jc(_$hash, authType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PreviewUpdateSmsBankingResponse')
          ..add('requestId', requestId)
          ..add('authType', authType))
        .toString();
  }
}

class PreviewUpdateSmsBankingResponseBuilder
    implements
        Builder<PreviewUpdateSmsBankingResponse,
            PreviewUpdateSmsBankingResponseBuilder> {
  _$PreviewUpdateSmsBankingResponse? _$v;

  String? _requestId;
  String? get requestId => _$this._requestId;
  set requestId(String? requestId) => _$this._requestId = requestId;

  PreviewUpdateSmsBankingResponseAuthTypeEnum? _authType;
  PreviewUpdateSmsBankingResponseAuthTypeEnum? get authType => _$this._authType;
  set authType(PreviewUpdateSmsBankingResponseAuthTypeEnum? authType) =>
      _$this._authType = authType;

  PreviewUpdateSmsBankingResponseBuilder() {
    PreviewUpdateSmsBankingResponse._defaults(this);
  }

  PreviewUpdateSmsBankingResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _requestId = $v.requestId;
      _authType = $v.authType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PreviewUpdateSmsBankingResponse other) {
    _$v = other as _$PreviewUpdateSmsBankingResponse;
  }

  @override
  void update(void Function(PreviewUpdateSmsBankingResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PreviewUpdateSmsBankingResponse build() => _build();

  _$PreviewUpdateSmsBankingResponse _build() {
    final _$result = _$v ??
        _$PreviewUpdateSmsBankingResponse._(
          requestId: requestId,
          authType: authType,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
