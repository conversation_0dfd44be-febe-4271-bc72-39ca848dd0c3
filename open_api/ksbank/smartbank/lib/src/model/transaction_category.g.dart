// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_category.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionCategory extends TransactionCategory {
  @override
  final int? id;
  @override
  final String? name;
  @override
  final String? url;
  @override
  final String? urlType;
  @override
  final int? isDefault;

  factory _$TransactionCategory(
          [void Function(TransactionCategoryBuilder)? updates]) =>
      (TransactionCategoryBuilder()..update(updates))._build();

  _$TransactionCategory._(
      {this.id, this.name, this.url, this.urlType, this.isDefault})
      : super._();
  @override
  TransactionCategory rebuild(
          void Function(TransactionCategoryBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionCategoryBuilder toBuilder() =>
      TransactionCategoryBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionCategory &&
        id == other.id &&
        name == other.name &&
        url == other.url &&
        urlType == other.urlType &&
        isDefault == other.isDefault;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, url.hashCode);
    _$hash = $jc(_$hash, urlType.hashCode);
    _$hash = $jc(_$hash, isDefault.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionCategory')
          ..add('id', id)
          ..add('name', name)
          ..add('url', url)
          ..add('urlType', urlType)
          ..add('isDefault', isDefault))
        .toString();
  }
}

class TransactionCategoryBuilder
    implements Builder<TransactionCategory, TransactionCategoryBuilder> {
  _$TransactionCategory? _$v;

  int? _id;
  int? get id => _$this._id;
  set id(int? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _url;
  String? get url => _$this._url;
  set url(String? url) => _$this._url = url;

  String? _urlType;
  String? get urlType => _$this._urlType;
  set urlType(String? urlType) => _$this._urlType = urlType;

  int? _isDefault;
  int? get isDefault => _$this._isDefault;
  set isDefault(int? isDefault) => _$this._isDefault = isDefault;

  TransactionCategoryBuilder() {
    TransactionCategory._defaults(this);
  }

  TransactionCategoryBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _url = $v.url;
      _urlType = $v.urlType;
      _isDefault = $v.isDefault;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionCategory other) {
    _$v = other as _$TransactionCategory;
  }

  @override
  void update(void Function(TransactionCategoryBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionCategory build() => _build();

  _$TransactionCategory _build() {
    final _$result = _$v ??
        _$TransactionCategory._(
          id: id,
          name: name,
          url: url,
          urlType: urlType,
          isDefault: isDefault,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
