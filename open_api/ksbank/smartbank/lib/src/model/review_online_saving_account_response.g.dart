// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_online_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewOnlineSavingAccountResponse
    extends ReviewOnlineSavingAccountResponse {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionDate;
  @override
  final String? accountNumber;
  @override
  final double? amount;
  @override
  final String? currency;
  @override
  final String? termId;
  @override
  final String? termName;
  @override
  final double? rate;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final int? finalTypeId;
  @override
  final String? finalTypeName;
  @override
  final String? finalAccountNumber;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewOnlineSavingAccountResponse(
          [void Function(ReviewOnlineSavingAccountResponseBuilder)? updates]) =>
      (ReviewOnlineSavingAccountResponseBuilder()..update(updates))._build();

  _$ReviewOnlineSavingAccountResponse._(
      {this.bankCif,
      this.transactionNo,
      this.transactionDate,
      this.accountNumber,
      this.amount,
      this.currency,
      this.termId,
      this.termName,
      this.rate,
      this.contractDate,
      this.dueDate,
      this.finalTypeId,
      this.finalTypeName,
      this.finalAccountNumber,
      this.transNextStep,
      this.content})
      : super._();
  @override
  ReviewOnlineSavingAccountResponse rebuild(
          void Function(ReviewOnlineSavingAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewOnlineSavingAccountResponseBuilder toBuilder() =>
      ReviewOnlineSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewOnlineSavingAccountResponse &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        accountNumber == other.accountNumber &&
        amount == other.amount &&
        currency == other.currency &&
        termId == other.termId &&
        termName == other.termName &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        finalTypeId == other.finalTypeId &&
        finalTypeName == other.finalTypeName &&
        finalAccountNumber == other.finalAccountNumber &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, termId.hashCode);
    _$hash = $jc(_$hash, termName.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, finalTypeId.hashCode);
    _$hash = $jc(_$hash, finalTypeName.hashCode);
    _$hash = $jc(_$hash, finalAccountNumber.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewOnlineSavingAccountResponse')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('accountNumber', accountNumber)
          ..add('amount', amount)
          ..add('currency', currency)
          ..add('termId', termId)
          ..add('termName', termName)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('finalTypeId', finalTypeId)
          ..add('finalTypeName', finalTypeName)
          ..add('finalAccountNumber', finalAccountNumber)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewOnlineSavingAccountResponseBuilder
    implements
        Builder<ReviewOnlineSavingAccountResponse,
            ReviewOnlineSavingAccountResponseBuilder> {
  _$ReviewOnlineSavingAccountResponse? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  String? _termId;
  String? get termId => _$this._termId;
  set termId(String? termId) => _$this._termId = termId;

  String? _termName;
  String? get termName => _$this._termName;
  set termName(String? termName) => _$this._termName = termName;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  int? _finalTypeId;
  int? get finalTypeId => _$this._finalTypeId;
  set finalTypeId(int? finalTypeId) => _$this._finalTypeId = finalTypeId;

  String? _finalTypeName;
  String? get finalTypeName => _$this._finalTypeName;
  set finalTypeName(String? finalTypeName) =>
      _$this._finalTypeName = finalTypeName;

  String? _finalAccountNumber;
  String? get finalAccountNumber => _$this._finalAccountNumber;
  set finalAccountNumber(String? finalAccountNumber) =>
      _$this._finalAccountNumber = finalAccountNumber;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewOnlineSavingAccountResponseBuilder() {
    ReviewOnlineSavingAccountResponse._defaults(this);
  }

  ReviewOnlineSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _accountNumber = $v.accountNumber;
      _amount = $v.amount;
      _currency = $v.currency;
      _termId = $v.termId;
      _termName = $v.termName;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _finalTypeId = $v.finalTypeId;
      _finalTypeName = $v.finalTypeName;
      _finalAccountNumber = $v.finalAccountNumber;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewOnlineSavingAccountResponse other) {
    _$v = other as _$ReviewOnlineSavingAccountResponse;
  }

  @override
  void update(
      void Function(ReviewOnlineSavingAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewOnlineSavingAccountResponse build() => _build();

  _$ReviewOnlineSavingAccountResponse _build() {
    final _$result = _$v ??
        _$ReviewOnlineSavingAccountResponse._(
          bankCif: bankCif,
          transactionNo: transactionNo,
          transactionDate: transactionDate,
          accountNumber: accountNumber,
          amount: amount,
          currency: currency,
          termId: termId,
          termName: termName,
          rate: rate,
          contractDate: contractDate,
          dueDate: dueDate,
          finalTypeId: finalTypeId,
          finalTypeName: finalTypeName,
          finalAccountNumber: finalAccountNumber,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
