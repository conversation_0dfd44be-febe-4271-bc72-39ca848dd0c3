//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'topup_value_dto.g.dart';

/// TopupValueDto
///
/// Properties:
/// * [cardValue]
/// * [cardValueCode]
/// * [cardValueName]
/// * [purchasingPrice]
/// * [referPrice]
/// * [canPaid]
/// * [discountPercent]
/// * [provider]
@BuiltValue()
abstract class TopupValueDto
    implements Built<TopupValueDto, TopupValueDtoBuilder> {
  @BuiltValueField(wireName: r'cardValue')
  num? get cardValue;

  @BuiltValueField(wireName: r'cardValueCode')
  String? get cardValueCode;

  @BuiltValueField(wireName: r'cardValueName')
  String? get cardValueName;

  @BuiltValueField(wireName: r'purchasingPrice')
  num? get purchasingPrice;

  @BuiltValueField(wireName: r'referPrice')
  num? get referPrice;

  @BuiltValueField(wireName: r'canPaid')
  bool? get canPaid;

  @BuiltValueField(wireName: r'discountPercent')
  num? get discountPercent;

  @BuiltValueField(wireName: r'provider')
  TopupValueDtoProviderEnum? get provider;
  // enum providerEnum {  MOBIFONE,  VINAPHONE,  VIETTEL,  VIETNAMOBILE,  GMOBILE,  };

  TopupValueDto._();

  factory TopupValueDto([void updates(TopupValueDtoBuilder b)]) =
      _$TopupValueDto;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TopupValueDtoBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TopupValueDto> get serializer =>
      _$TopupValueDtoSerializer();
}

class _$TopupValueDtoSerializer implements PrimitiveSerializer<TopupValueDto> {
  @override
  final Iterable<Type> types = const [TopupValueDto, _$TopupValueDto];

  @override
  final String wireName = r'TopupValueDto';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TopupValueDto object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.cardValue != null) {
      yield r'cardValue';
      yield serializers.serialize(
        object.cardValue,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.cardValueCode != null) {
      yield r'cardValueCode';
      yield serializers.serialize(
        object.cardValueCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cardValueName != null) {
      yield r'cardValueName';
      yield serializers.serialize(
        object.cardValueName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.purchasingPrice != null) {
      yield r'purchasingPrice';
      yield serializers.serialize(
        object.purchasingPrice,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.referPrice != null) {
      yield r'referPrice';
      yield serializers.serialize(
        object.referPrice,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.canPaid != null) {
      yield r'canPaid';
      yield serializers.serialize(
        object.canPaid,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.discountPercent != null) {
      yield r'discountPercent';
      yield serializers.serialize(
        object.discountPercent,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.provider != null) {
      yield r'provider';
      yield serializers.serialize(
        object.provider,
        specifiedType: const FullType.nullable(TopupValueDtoProviderEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TopupValueDto object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TopupValueDtoBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'cardValue':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.cardValue = valueDes;
          break;
        case r'cardValueCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardValueCode = valueDes;
          break;
        case r'cardValueName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardValueName = valueDes;
          break;
        case r'purchasingPrice':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.purchasingPrice = valueDes;
          break;
        case r'referPrice':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.referPrice = valueDes;
          break;
        case r'canPaid':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.canPaid = valueDes;
          break;
        case r'discountPercent':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.discountPercent = valueDes;
          break;
        case r'provider':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(TopupValueDtoProviderEnum),
          ) as TopupValueDtoProviderEnum?;
          if (valueDes == null) continue;
          result.provider = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TopupValueDto deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TopupValueDtoBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class TopupValueDtoProviderEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'MOBIFONE')
  static const TopupValueDtoProviderEnum MOBIFONE =
      _$topupValueDtoProviderEnum_MOBIFONE;
  @BuiltValueEnumConst(wireName: r'VINAPHONE')
  static const TopupValueDtoProviderEnum VINAPHONE =
      _$topupValueDtoProviderEnum_VINAPHONE;
  @BuiltValueEnumConst(wireName: r'VIETTEL')
  static const TopupValueDtoProviderEnum VIETTEL =
      _$topupValueDtoProviderEnum_VIETTEL;
  @BuiltValueEnumConst(wireName: r'VIETNAMOBILE')
  static const TopupValueDtoProviderEnum VIETNAMOBILE =
      _$topupValueDtoProviderEnum_VIETNAMOBILE;
  @BuiltValueEnumConst(wireName: r'GMOBILE')
  static const TopupValueDtoProviderEnum GMOBILE =
      _$topupValueDtoProviderEnum_GMOBILE;

  static Serializer<TopupValueDtoProviderEnum> get serializer =>
      _$topupValueDtoProviderEnumSerializer;

  const TopupValueDtoProviderEnum._(String name) : super(name);

  static BuiltSet<TopupValueDtoProviderEnum> get values =>
      _$topupValueDtoProviderEnumValues;
  static TopupValueDtoProviderEnum valueOf(String name) =>
      _$topupValueDtoProviderEnumValueOf(name);
}
