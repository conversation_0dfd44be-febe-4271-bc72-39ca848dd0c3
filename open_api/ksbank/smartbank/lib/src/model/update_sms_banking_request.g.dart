// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_sms_banking_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateSmsBankingRequest extends UpdateSmsBankingRequest {
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final String? requestId;

  factory _$UpdateSmsBankingRequest(
          [void Function(UpdateSmsBankingRequestBuilder)? updates]) =>
      (UpdateSmsBankingRequestBuilder()..update(updates))._build();

  _$UpdateSmsBankingRequest._({this.softOtp, this.requestId}) : super._();
  @override
  UpdateSmsBankingRequest rebuild(
          void Function(UpdateSmsBankingRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateSmsBankingRequestBuilder toBuilder() =>
      UpdateSmsBankingRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateSmsBankingRequest &&
        softOtp == other.softOtp &&
        requestId == other.requestId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, requestId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateSmsBankingRequest')
          ..add('softOtp', softOtp)
          ..add('requestId', requestId))
        .toString();
  }
}

class UpdateSmsBankingRequestBuilder
    implements
        Builder<UpdateSmsBankingRequest, UpdateSmsBankingRequestBuilder> {
  _$UpdateSmsBankingRequest? _$v;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  String? _requestId;
  String? get requestId => _$this._requestId;
  set requestId(String? requestId) => _$this._requestId = requestId;

  UpdateSmsBankingRequestBuilder() {
    UpdateSmsBankingRequest._defaults(this);
  }

  UpdateSmsBankingRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _softOtp = $v.softOtp?.toBuilder();
      _requestId = $v.requestId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateSmsBankingRequest other) {
    _$v = other as _$UpdateSmsBankingRequest;
  }

  @override
  void update(void Function(UpdateSmsBankingRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateSmsBankingRequest build() => _build();

  _$UpdateSmsBankingRequest _build() {
    _$UpdateSmsBankingRequest _$result;
    try {
      _$result = _$v ??
          _$UpdateSmsBankingRequest._(
            softOtp: _softOtp?.build(),
            requestId: requestId,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'UpdateSmsBankingRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
