// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_close_target_saving_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_VND =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('VND');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_USD =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('USD');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_ACB =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('ACB');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_JPY =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('JPY');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_GOLD =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('GOLD');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_EUR =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('EUR');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_GBP =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('GBP');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_CHF =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('CHF');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_AUD =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('AUD');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_CAD =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('CAD');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_SGD =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('SGD');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_THB =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('THB');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_NOK =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('NOK');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_NZD =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('NZD');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_DKK =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('DKK');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_HKD =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('HKD');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_SEK =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('SEK');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_MYR =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('MYR');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_XAU =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('XAU');
const ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnum_MMK =
    const ReviewCloseTargetSavingResponseCurrencyEnum._('MMK');

ReviewCloseTargetSavingResponseCurrencyEnum
    _$reviewCloseTargetSavingResponseCurrencyEnumValueOf(String name) {
  switch (name) {
    case 'VND':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_VND;
    case 'USD':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_USD;
    case 'ACB':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_ACB;
    case 'JPY':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_JPY;
    case 'GOLD':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_GOLD;
    case 'EUR':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_EUR;
    case 'GBP':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_GBP;
    case 'CHF':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_CHF;
    case 'AUD':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_AUD;
    case 'CAD':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_CAD;
    case 'SGD':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_SGD;
    case 'THB':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_THB;
    case 'NOK':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_NOK;
    case 'NZD':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_NZD;
    case 'DKK':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_DKK;
    case 'HKD':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_HKD;
    case 'SEK':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_SEK;
    case 'MYR':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_MYR;
    case 'XAU':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_XAU;
    case 'MMK':
      return _$reviewCloseTargetSavingResponseCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<ReviewCloseTargetSavingResponseCurrencyEnum>
    _$reviewCloseTargetSavingResponseCurrencyEnumValues = BuiltSet<
        ReviewCloseTargetSavingResponseCurrencyEnum>(const <ReviewCloseTargetSavingResponseCurrencyEnum>[
  _$reviewCloseTargetSavingResponseCurrencyEnum_VND,
  _$reviewCloseTargetSavingResponseCurrencyEnum_USD,
  _$reviewCloseTargetSavingResponseCurrencyEnum_ACB,
  _$reviewCloseTargetSavingResponseCurrencyEnum_JPY,
  _$reviewCloseTargetSavingResponseCurrencyEnum_GOLD,
  _$reviewCloseTargetSavingResponseCurrencyEnum_EUR,
  _$reviewCloseTargetSavingResponseCurrencyEnum_GBP,
  _$reviewCloseTargetSavingResponseCurrencyEnum_CHF,
  _$reviewCloseTargetSavingResponseCurrencyEnum_AUD,
  _$reviewCloseTargetSavingResponseCurrencyEnum_CAD,
  _$reviewCloseTargetSavingResponseCurrencyEnum_SGD,
  _$reviewCloseTargetSavingResponseCurrencyEnum_THB,
  _$reviewCloseTargetSavingResponseCurrencyEnum_NOK,
  _$reviewCloseTargetSavingResponseCurrencyEnum_NZD,
  _$reviewCloseTargetSavingResponseCurrencyEnum_DKK,
  _$reviewCloseTargetSavingResponseCurrencyEnum_HKD,
  _$reviewCloseTargetSavingResponseCurrencyEnum_SEK,
  _$reviewCloseTargetSavingResponseCurrencyEnum_MYR,
  _$reviewCloseTargetSavingResponseCurrencyEnum_XAU,
  _$reviewCloseTargetSavingResponseCurrencyEnum_MMK,
]);

Serializer<ReviewCloseTargetSavingResponseCurrencyEnum>
    _$reviewCloseTargetSavingResponseCurrencyEnumSerializer =
    _$ReviewCloseTargetSavingResponseCurrencyEnumSerializer();

class _$ReviewCloseTargetSavingResponseCurrencyEnumSerializer
    implements
        PrimitiveSerializer<ReviewCloseTargetSavingResponseCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[
    ReviewCloseTargetSavingResponseCurrencyEnum
  ];
  @override
  final String wireName = 'ReviewCloseTargetSavingResponseCurrencyEnum';

  @override
  Object serialize(Serializers serializers,
          ReviewCloseTargetSavingResponseCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  ReviewCloseTargetSavingResponseCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      ReviewCloseTargetSavingResponseCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$ReviewCloseTargetSavingResponse
    extends ReviewCloseTargetSavingResponse {
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionDate;
  @override
  final String? targetAccountNo;
  @override
  final String? accountName;
  @override
  final num? balance;
  @override
  final ReviewCloseTargetSavingResponseCurrencyEnum? currency;
  @override
  final double? rate;
  @override
  final num? finalAmount;
  @override
  final String? finalAccountNo;
  @override
  final DateTime? contractDate;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewCloseTargetSavingResponse(
          [void Function(ReviewCloseTargetSavingResponseBuilder)? updates]) =>
      (ReviewCloseTargetSavingResponseBuilder()..update(updates))._build();

  _$ReviewCloseTargetSavingResponse._(
      {this.transactionNo,
      this.transactionDate,
      this.targetAccountNo,
      this.accountName,
      this.balance,
      this.currency,
      this.rate,
      this.finalAmount,
      this.finalAccountNo,
      this.contractDate,
      this.transNextStep,
      this.content})
      : super._();
  @override
  ReviewCloseTargetSavingResponse rebuild(
          void Function(ReviewCloseTargetSavingResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCloseTargetSavingResponseBuilder toBuilder() =>
      ReviewCloseTargetSavingResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCloseTargetSavingResponse &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        targetAccountNo == other.targetAccountNo &&
        accountName == other.accountName &&
        balance == other.balance &&
        currency == other.currency &&
        rate == other.rate &&
        finalAmount == other.finalAmount &&
        finalAccountNo == other.finalAccountNo &&
        contractDate == other.contractDate &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, targetAccountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, finalAccountNo.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewCloseTargetSavingResponse')
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('targetAccountNo', targetAccountNo)
          ..add('accountName', accountName)
          ..add('balance', balance)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('finalAmount', finalAmount)
          ..add('finalAccountNo', finalAccountNo)
          ..add('contractDate', contractDate)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewCloseTargetSavingResponseBuilder
    implements
        Builder<ReviewCloseTargetSavingResponse,
            ReviewCloseTargetSavingResponseBuilder> {
  _$ReviewCloseTargetSavingResponse? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _targetAccountNo;
  String? get targetAccountNo => _$this._targetAccountNo;
  set targetAccountNo(String? targetAccountNo) =>
      _$this._targetAccountNo = targetAccountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  ReviewCloseTargetSavingResponseCurrencyEnum? _currency;
  ReviewCloseTargetSavingResponseCurrencyEnum? get currency => _$this._currency;
  set currency(ReviewCloseTargetSavingResponseCurrencyEnum? currency) =>
      _$this._currency = currency;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  num? _finalAmount;
  num? get finalAmount => _$this._finalAmount;
  set finalAmount(num? finalAmount) => _$this._finalAmount = finalAmount;

  String? _finalAccountNo;
  String? get finalAccountNo => _$this._finalAccountNo;
  set finalAccountNo(String? finalAccountNo) =>
      _$this._finalAccountNo = finalAccountNo;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewCloseTargetSavingResponseBuilder() {
    ReviewCloseTargetSavingResponse._defaults(this);
  }

  ReviewCloseTargetSavingResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _targetAccountNo = $v.targetAccountNo;
      _accountName = $v.accountName;
      _balance = $v.balance;
      _currency = $v.currency;
      _rate = $v.rate;
      _finalAmount = $v.finalAmount;
      _finalAccountNo = $v.finalAccountNo;
      _contractDate = $v.contractDate;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCloseTargetSavingResponse other) {
    _$v = other as _$ReviewCloseTargetSavingResponse;
  }

  @override
  void update(void Function(ReviewCloseTargetSavingResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCloseTargetSavingResponse build() => _build();

  _$ReviewCloseTargetSavingResponse _build() {
    final _$result = _$v ??
        _$ReviewCloseTargetSavingResponse._(
          transactionNo: transactionNo,
          transactionDate: transactionDate,
          targetAccountNo: targetAccountNo,
          accountName: accountName,
          balance: balance,
          currency: currency,
          rate: rate,
          finalAmount: finalAmount,
          finalAccountNo: finalAccountNo,
          contractDate: contractDate,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
