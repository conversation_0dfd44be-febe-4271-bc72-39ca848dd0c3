// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tuition_fee_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TuitionFeeDto extends TuitionFeeDto {
  @override
  final String? studentCode;
  @override
  final String? studentName;
  @override
  final String? virtualAccount;
  @override
  final int? totalAmount;
  @override
  final String? message;

  factory _$TuitionFeeDto([void Function(TuitionFeeDtoBuilder)? updates]) =>
      (TuitionFeeDtoBuilder()..update(updates))._build();

  _$TuitionFeeDto._(
      {this.studentCode,
      this.studentName,
      this.virtualAccount,
      this.totalAmount,
      this.message})
      : super._();
  @override
  TuitionFeeDto rebuild(void Function(TuitionFeeDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TuitionFeeDtoBuilder toBuilder() => TuitionFeeDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TuitionFeeDto &&
        studentCode == other.studentCode &&
        studentName == other.studentName &&
        virtualAccount == other.virtualAccount &&
        totalAmount == other.totalAmount &&
        message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, studentCode.hashCode);
    _$hash = $jc(_$hash, studentName.hashCode);
    _$hash = $jc(_$hash, virtualAccount.hashCode);
    _$hash = $jc(_$hash, totalAmount.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TuitionFeeDto')
          ..add('studentCode', studentCode)
          ..add('studentName', studentName)
          ..add('virtualAccount', virtualAccount)
          ..add('totalAmount', totalAmount)
          ..add('message', message))
        .toString();
  }
}

class TuitionFeeDtoBuilder
    implements Builder<TuitionFeeDto, TuitionFeeDtoBuilder> {
  _$TuitionFeeDto? _$v;

  String? _studentCode;
  String? get studentCode => _$this._studentCode;
  set studentCode(String? studentCode) => _$this._studentCode = studentCode;

  String? _studentName;
  String? get studentName => _$this._studentName;
  set studentName(String? studentName) => _$this._studentName = studentName;

  String? _virtualAccount;
  String? get virtualAccount => _$this._virtualAccount;
  set virtualAccount(String? virtualAccount) =>
      _$this._virtualAccount = virtualAccount;

  int? _totalAmount;
  int? get totalAmount => _$this._totalAmount;
  set totalAmount(int? totalAmount) => _$this._totalAmount = totalAmount;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  TuitionFeeDtoBuilder() {
    TuitionFeeDto._defaults(this);
  }

  TuitionFeeDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _studentCode = $v.studentCode;
      _studentName = $v.studentName;
      _virtualAccount = $v.virtualAccount;
      _totalAmount = $v.totalAmount;
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TuitionFeeDto other) {
    _$v = other as _$TuitionFeeDto;
  }

  @override
  void update(void Function(TuitionFeeDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TuitionFeeDto build() => _build();

  _$TuitionFeeDto _build() {
    final _$result = _$v ??
        _$TuitionFeeDto._(
          studentCode: studentCode,
          studentName: studentName,
          virtualAccount: virtualAccount,
          totalAmount: totalAmount,
          message: message,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
