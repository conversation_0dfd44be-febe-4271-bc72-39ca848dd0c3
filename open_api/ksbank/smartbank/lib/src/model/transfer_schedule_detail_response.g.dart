// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_schedule_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferScheduleDetailResponse extends TransferScheduleDetailResponse {
  @override
  final TransferScheduleResponse? scheduleResponse;

  factory _$TransferScheduleDetailResponse(
          [void Function(TransferScheduleDetailResponseBuilder)? updates]) =>
      (TransferScheduleDetailResponseBuilder()..update(updates))._build();

  _$TransferScheduleDetailResponse._({this.scheduleResponse}) : super._();
  @override
  TransferScheduleDetailResponse rebuild(
          void Function(TransferScheduleDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferScheduleDetailResponseBuilder toBuilder() =>
      TransferScheduleDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferScheduleDetailResponse &&
        scheduleResponse == other.scheduleResponse;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, scheduleResponse.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferScheduleDetailResponse')
          ..add('scheduleResponse', scheduleResponse))
        .toString();
  }
}

class TransferScheduleDetailResponseBuilder
    implements
        Builder<TransferScheduleDetailResponse,
            TransferScheduleDetailResponseBuilder> {
  _$TransferScheduleDetailResponse? _$v;

  TransferScheduleResponseBuilder? _scheduleResponse;
  TransferScheduleResponseBuilder get scheduleResponse =>
      _$this._scheduleResponse ??= TransferScheduleResponseBuilder();
  set scheduleResponse(TransferScheduleResponseBuilder? scheduleResponse) =>
      _$this._scheduleResponse = scheduleResponse;

  TransferScheduleDetailResponseBuilder() {
    TransferScheduleDetailResponse._defaults(this);
  }

  TransferScheduleDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _scheduleResponse = $v.scheduleResponse?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferScheduleDetailResponse other) {
    _$v = other as _$TransferScheduleDetailResponse;
  }

  @override
  void update(void Function(TransferScheduleDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferScheduleDetailResponse build() => _build();

  _$TransferScheduleDetailResponse _build() {
    _$TransferScheduleDetailResponse _$result;
    try {
      _$result = _$v ??
          _$TransferScheduleDetailResponse._(
            scheduleResponse: _scheduleResponse?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'scheduleResponse';
        _scheduleResponse?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransferScheduleDetailResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
