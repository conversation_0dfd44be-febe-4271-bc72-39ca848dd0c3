// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_payment_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewPaymentResponse extends ReviewPaymentResponse {
  @override
  final String? cifNumber;
  @override
  final String? transactionNumber;
  @override
  final String? bankSigned;
  @override
  final String? description;

  factory _$ReviewPaymentResponse(
          [void Function(ReviewPaymentResponseBuilder)? updates]) =>
      (ReviewPaymentResponseBuilder()..update(updates))._build();

  _$ReviewPaymentResponse._(
      {this.cifNumber,
      this.transactionNumber,
      this.bankSigned,
      this.description})
      : super._();
  @override
  ReviewPaymentResponse rebuild(
          void Function(ReviewPaymentResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewPaymentResponseBuilder toBuilder() =>
      ReviewPaymentResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewPaymentResponse &&
        cifNumber == other.cifNumber &&
        transactionNumber == other.transactionNumber &&
        bankSigned == other.bankSigned &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNumber.hashCode);
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, bankSigned.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewPaymentResponse')
          ..add('cifNumber', cifNumber)
          ..add('transactionNumber', transactionNumber)
          ..add('bankSigned', bankSigned)
          ..add('description', description))
        .toString();
  }
}

class ReviewPaymentResponseBuilder
    implements Builder<ReviewPaymentResponse, ReviewPaymentResponseBuilder> {
  _$ReviewPaymentResponse? _$v;

  String? _cifNumber;
  String? get cifNumber => _$this._cifNumber;
  set cifNumber(String? cifNumber) => _$this._cifNumber = cifNumber;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  String? _bankSigned;
  String? get bankSigned => _$this._bankSigned;
  set bankSigned(String? bankSigned) => _$this._bankSigned = bankSigned;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  ReviewPaymentResponseBuilder() {
    ReviewPaymentResponse._defaults(this);
  }

  ReviewPaymentResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNumber = $v.cifNumber;
      _transactionNumber = $v.transactionNumber;
      _bankSigned = $v.bankSigned;
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewPaymentResponse other) {
    _$v = other as _$ReviewPaymentResponse;
  }

  @override
  void update(void Function(ReviewPaymentResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewPaymentResponse build() => _build();

  _$ReviewPaymentResponse _build() {
    final _$result = _$v ??
        _$ReviewPaymentResponse._(
          cifNumber: cifNumber,
          transactionNumber: transactionNumber,
          bankSigned: bankSigned,
          description: description,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
