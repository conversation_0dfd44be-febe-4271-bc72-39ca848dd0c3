// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_bill_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentBillResponse extends PaymentBillResponse {
  @override
  final String? systemTrace;
  @override
  final String? transactionNo;
  @override
  final num? amount;
  @override
  final DateTime? transactionTime;
  @override
  final String? description;

  factory _$PaymentBillResponse(
          [void Function(PaymentBillResponseBuilder)? updates]) =>
      (PaymentBillResponseBuilder()..update(updates))._build();

  _$PaymentBillResponse._(
      {this.systemTrace,
      this.transactionNo,
      this.amount,
      this.transactionTime,
      this.description})
      : super._();
  @override
  PaymentBillResponse rebuild(
          void Function(PaymentBillResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentBillResponseBuilder toBuilder() =>
      PaymentBillResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentBillResponse &&
        systemTrace == other.systemTrace &&
        transactionNo == other.transactionNo &&
        amount == other.amount &&
        transactionTime == other.transactionTime &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, systemTrace.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, transactionTime.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentBillResponse')
          ..add('systemTrace', systemTrace)
          ..add('transactionNo', transactionNo)
          ..add('amount', amount)
          ..add('transactionTime', transactionTime)
          ..add('description', description))
        .toString();
  }
}

class PaymentBillResponseBuilder
    implements Builder<PaymentBillResponse, PaymentBillResponseBuilder> {
  _$PaymentBillResponse? _$v;

  String? _systemTrace;
  String? get systemTrace => _$this._systemTrace;
  set systemTrace(String? systemTrace) => _$this._systemTrace = systemTrace;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  DateTime? _transactionTime;
  DateTime? get transactionTime => _$this._transactionTime;
  set transactionTime(DateTime? transactionTime) =>
      _$this._transactionTime = transactionTime;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  PaymentBillResponseBuilder() {
    PaymentBillResponse._defaults(this);
  }

  PaymentBillResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _systemTrace = $v.systemTrace;
      _transactionNo = $v.transactionNo;
      _amount = $v.amount;
      _transactionTime = $v.transactionTime;
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentBillResponse other) {
    _$v = other as _$PaymentBillResponse;
  }

  @override
  void update(void Function(PaymentBillResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentBillResponse build() => _build();

  _$PaymentBillResponse _build() {
    final _$result = _$v ??
        _$PaymentBillResponse._(
          systemTrace: systemTrace,
          transactionNo: transactionNo,
          amount: amount,
          transactionTime: transactionTime,
          description: description,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
