// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vnp_customer_bill_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VnpCustomerBillInfo extends VnpCustomerBillInfo {
  @override
  final String? code;
  @override
  final String? name;
  @override
  final String? address;
  @override
  final String? phoneNumber;
  @override
  final String? electricityCode;
  @override
  final String? session;
  @override
  final String? numberCSMIS;
  @override
  final String? numberOfMeter;
  @override
  final String? tax;
  @override
  final String? career;
  @override
  final String? stationCode;

  factory _$VnpCustomerBillInfo(
          [void Function(VnpCustomerBillInfoBuilder)? updates]) =>
      (VnpCustomerBillInfoBuilder()..update(updates))._build();

  _$VnpCustomerBillInfo._(
      {this.code,
      this.name,
      this.address,
      this.phoneNumber,
      this.electricityCode,
      this.session,
      this.numberCSMIS,
      this.numberOfMeter,
      this.tax,
      this.career,
      this.stationCode})
      : super._();
  @override
  VnpCustomerBillInfo rebuild(
          void Function(VnpCustomerBillInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VnpCustomerBillInfoBuilder toBuilder() =>
      VnpCustomerBillInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VnpCustomerBillInfo &&
        code == other.code &&
        name == other.name &&
        address == other.address &&
        phoneNumber == other.phoneNumber &&
        electricityCode == other.electricityCode &&
        session == other.session &&
        numberCSMIS == other.numberCSMIS &&
        numberOfMeter == other.numberOfMeter &&
        tax == other.tax &&
        career == other.career &&
        stationCode == other.stationCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, electricityCode.hashCode);
    _$hash = $jc(_$hash, session.hashCode);
    _$hash = $jc(_$hash, numberCSMIS.hashCode);
    _$hash = $jc(_$hash, numberOfMeter.hashCode);
    _$hash = $jc(_$hash, tax.hashCode);
    _$hash = $jc(_$hash, career.hashCode);
    _$hash = $jc(_$hash, stationCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VnpCustomerBillInfo')
          ..add('code', code)
          ..add('name', name)
          ..add('address', address)
          ..add('phoneNumber', phoneNumber)
          ..add('electricityCode', electricityCode)
          ..add('session', session)
          ..add('numberCSMIS', numberCSMIS)
          ..add('numberOfMeter', numberOfMeter)
          ..add('tax', tax)
          ..add('career', career)
          ..add('stationCode', stationCode))
        .toString();
  }
}

class VnpCustomerBillInfoBuilder
    implements Builder<VnpCustomerBillInfo, VnpCustomerBillInfoBuilder> {
  _$VnpCustomerBillInfo? _$v;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _electricityCode;
  String? get electricityCode => _$this._electricityCode;
  set electricityCode(String? electricityCode) =>
      _$this._electricityCode = electricityCode;

  String? _session;
  String? get session => _$this._session;
  set session(String? session) => _$this._session = session;

  String? _numberCSMIS;
  String? get numberCSMIS => _$this._numberCSMIS;
  set numberCSMIS(String? numberCSMIS) => _$this._numberCSMIS = numberCSMIS;

  String? _numberOfMeter;
  String? get numberOfMeter => _$this._numberOfMeter;
  set numberOfMeter(String? numberOfMeter) =>
      _$this._numberOfMeter = numberOfMeter;

  String? _tax;
  String? get tax => _$this._tax;
  set tax(String? tax) => _$this._tax = tax;

  String? _career;
  String? get career => _$this._career;
  set career(String? career) => _$this._career = career;

  String? _stationCode;
  String? get stationCode => _$this._stationCode;
  set stationCode(String? stationCode) => _$this._stationCode = stationCode;

  VnpCustomerBillInfoBuilder() {
    VnpCustomerBillInfo._defaults(this);
  }

  VnpCustomerBillInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _code = $v.code;
      _name = $v.name;
      _address = $v.address;
      _phoneNumber = $v.phoneNumber;
      _electricityCode = $v.electricityCode;
      _session = $v.session;
      _numberCSMIS = $v.numberCSMIS;
      _numberOfMeter = $v.numberOfMeter;
      _tax = $v.tax;
      _career = $v.career;
      _stationCode = $v.stationCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VnpCustomerBillInfo other) {
    _$v = other as _$VnpCustomerBillInfo;
  }

  @override
  void update(void Function(VnpCustomerBillInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VnpCustomerBillInfo build() => _build();

  _$VnpCustomerBillInfo _build() {
    final _$result = _$v ??
        _$VnpCustomerBillInfo._(
          code: code,
          name: name,
          address: address,
          phoneNumber: phoneNumber,
          electricityCode: electricityCode,
          session: session,
          numberCSMIS: numberCSMIS,
          numberOfMeter: numberOfMeter,
          tax: tax,
          career: career,
          stationCode: stationCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
