// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentCardRequest extends PaymentCardRequest {
  @override
  final String? transactionNo;
  @override
  final String? paymentType;
  @override
  final String? sourceAccountNo;
  @override
  final String? cifNo;
  @override
  final String? refCardId;
  @override
  final String? cardNo;
  @override
  final num? amount;
  @override
  final String? note;
  @override
  final String? otpCode;

  factory _$PaymentCardRequest(
          [void Function(PaymentCardRequestBuilder)? updates]) =>
      (PaymentCardRequestBuilder()..update(updates))._build();

  _$PaymentCardRequest._(
      {this.transactionNo,
      this.paymentType,
      this.sourceAccountNo,
      this.cifNo,
      this.refCardId,
      this.cardNo,
      this.amount,
      this.note,
      this.otpCode})
      : super._();
  @override
  PaymentCardRequest rebuild(
          void Function(PaymentCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentCardRequestBuilder toBuilder() =>
      PaymentCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentCardRequest &&
        transactionNo == other.transactionNo &&
        paymentType == other.paymentType &&
        sourceAccountNo == other.sourceAccountNo &&
        cifNo == other.cifNo &&
        refCardId == other.refCardId &&
        cardNo == other.cardNo &&
        amount == other.amount &&
        note == other.note &&
        otpCode == other.otpCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, paymentType.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, refCardId.hashCode);
    _$hash = $jc(_$hash, cardNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, note.hashCode);
    _$hash = $jc(_$hash, otpCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentCardRequest')
          ..add('transactionNo', transactionNo)
          ..add('paymentType', paymentType)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('cifNo', cifNo)
          ..add('refCardId', refCardId)
          ..add('cardNo', cardNo)
          ..add('amount', amount)
          ..add('note', note)
          ..add('otpCode', otpCode))
        .toString();
  }
}

class PaymentCardRequestBuilder
    implements Builder<PaymentCardRequest, PaymentCardRequestBuilder> {
  _$PaymentCardRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _paymentType;
  String? get paymentType => _$this._paymentType;
  set paymentType(String? paymentType) => _$this._paymentType = paymentType;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _refCardId;
  String? get refCardId => _$this._refCardId;
  set refCardId(String? refCardId) => _$this._refCardId = refCardId;

  String? _cardNo;
  String? get cardNo => _$this._cardNo;
  set cardNo(String? cardNo) => _$this._cardNo = cardNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _note;
  String? get note => _$this._note;
  set note(String? note) => _$this._note = note;

  String? _otpCode;
  String? get otpCode => _$this._otpCode;
  set otpCode(String? otpCode) => _$this._otpCode = otpCode;

  PaymentCardRequestBuilder() {
    PaymentCardRequest._defaults(this);
  }

  PaymentCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _paymentType = $v.paymentType;
      _sourceAccountNo = $v.sourceAccountNo;
      _cifNo = $v.cifNo;
      _refCardId = $v.refCardId;
      _cardNo = $v.cardNo;
      _amount = $v.amount;
      _note = $v.note;
      _otpCode = $v.otpCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentCardRequest other) {
    _$v = other as _$PaymentCardRequest;
  }

  @override
  void update(void Function(PaymentCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentCardRequest build() => _build();

  _$PaymentCardRequest _build() {
    final _$result = _$v ??
        _$PaymentCardRequest._(
          transactionNo: transactionNo,
          paymentType: paymentType,
          sourceAccountNo: sourceAccountNo,
          cifNo: cifNo,
          refCardId: refCardId,
          cardNo: cardNo,
          amount: amount,
          note: note,
          otpCode: otpCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
