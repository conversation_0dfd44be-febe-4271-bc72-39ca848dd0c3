// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'topup_service_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TopupServiceDto extends TopupServiceDto {
  @override
  final String? code;
  @override
  final String? name;
  @override
  final String? iconLink;
  @override
  final bool? active;

  factory _$TopupServiceDto([void Function(TopupServiceDtoBuilder)? updates]) =>
      (TopupServiceDtoBuilder()..update(updates))._build();

  _$TopupServiceDto._({this.code, this.name, this.iconLink, this.active})
      : super._();
  @override
  TopupServiceDto rebuild(void Function(TopupServiceDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TopupServiceDtoBuilder toBuilder() => TopupServiceDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TopupServiceDto &&
        code == other.code &&
        name == other.name &&
        iconLink == other.iconLink &&
        active == other.active;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, iconLink.hashCode);
    _$hash = $jc(_$hash, active.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TopupServiceDto')
          ..add('code', code)
          ..add('name', name)
          ..add('iconLink', iconLink)
          ..add('active', active))
        .toString();
  }
}

class TopupServiceDtoBuilder
    implements Builder<TopupServiceDto, TopupServiceDtoBuilder> {
  _$TopupServiceDto? _$v;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _iconLink;
  String? get iconLink => _$this._iconLink;
  set iconLink(String? iconLink) => _$this._iconLink = iconLink;

  bool? _active;
  bool? get active => _$this._active;
  set active(bool? active) => _$this._active = active;

  TopupServiceDtoBuilder() {
    TopupServiceDto._defaults(this);
  }

  TopupServiceDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _code = $v.code;
      _name = $v.name;
      _iconLink = $v.iconLink;
      _active = $v.active;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TopupServiceDto other) {
    _$v = other as _$TopupServiceDto;
  }

  @override
  void update(void Function(TopupServiceDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TopupServiceDto build() => _build();

  _$TopupServiceDto _build() {
    final _$result = _$v ??
        _$TopupServiceDto._(
          code: code,
          name: name,
          iconLink: iconLink,
          active: active,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
