// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saving_account_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SavingAccountDetailResponse extends SavingAccountDetailResponse {
  @override
  final String? bankCif;
  @override
  final String? customerName;
  @override
  final DateTime? transactionDate;
  @override
  final String? accountNumber;
  @override
  final String? accountName;
  @override
  final String? alias;
  @override
  final String? termId;
  @override
  final String? termName;
  @override
  final String? currency;
  @override
  final double? rate;
  @override
  final num? balance;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final String? finalTypeName;
  @override
  final num? finalAmount;
  @override
  final String? finalAccountNumber;
  @override
  final String? sourceAccountNo;
  @override
  final num? interestAmount;
  @override
  final num? interestAmountEndOfTerm;
  @override
  final bool? blocked;
  @override
  final double? redemRate;
  @override
  final bool? allowPartialWithdraw;
  @override
  final num? maxWithdrawalLimit;

  factory _$SavingAccountDetailResponse(
          [void Function(SavingAccountDetailResponseBuilder)? updates]) =>
      (SavingAccountDetailResponseBuilder()..update(updates))._build();

  _$SavingAccountDetailResponse._(
      {this.bankCif,
      this.customerName,
      this.transactionDate,
      this.accountNumber,
      this.accountName,
      this.alias,
      this.termId,
      this.termName,
      this.currency,
      this.rate,
      this.balance,
      this.contractDate,
      this.dueDate,
      this.finalTypeName,
      this.finalAmount,
      this.finalAccountNumber,
      this.sourceAccountNo,
      this.interestAmount,
      this.interestAmountEndOfTerm,
      this.blocked,
      this.redemRate,
      this.allowPartialWithdraw,
      this.maxWithdrawalLimit})
      : super._();
  @override
  SavingAccountDetailResponse rebuild(
          void Function(SavingAccountDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SavingAccountDetailResponseBuilder toBuilder() =>
      SavingAccountDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SavingAccountDetailResponse &&
        bankCif == other.bankCif &&
        customerName == other.customerName &&
        transactionDate == other.transactionDate &&
        accountNumber == other.accountNumber &&
        accountName == other.accountName &&
        alias == other.alias &&
        termId == other.termId &&
        termName == other.termName &&
        currency == other.currency &&
        rate == other.rate &&
        balance == other.balance &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        finalTypeName == other.finalTypeName &&
        finalAmount == other.finalAmount &&
        finalAccountNumber == other.finalAccountNumber &&
        sourceAccountNo == other.sourceAccountNo &&
        interestAmount == other.interestAmount &&
        interestAmountEndOfTerm == other.interestAmountEndOfTerm &&
        blocked == other.blocked &&
        redemRate == other.redemRate &&
        allowPartialWithdraw == other.allowPartialWithdraw &&
        maxWithdrawalLimit == other.maxWithdrawalLimit;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, alias.hashCode);
    _$hash = $jc(_$hash, termId.hashCode);
    _$hash = $jc(_$hash, termName.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, finalTypeName.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, finalAccountNumber.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, interestAmount.hashCode);
    _$hash = $jc(_$hash, interestAmountEndOfTerm.hashCode);
    _$hash = $jc(_$hash, blocked.hashCode);
    _$hash = $jc(_$hash, redemRate.hashCode);
    _$hash = $jc(_$hash, allowPartialWithdraw.hashCode);
    _$hash = $jc(_$hash, maxWithdrawalLimit.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SavingAccountDetailResponse')
          ..add('bankCif', bankCif)
          ..add('customerName', customerName)
          ..add('transactionDate', transactionDate)
          ..add('accountNumber', accountNumber)
          ..add('accountName', accountName)
          ..add('alias', alias)
          ..add('termId', termId)
          ..add('termName', termName)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('balance', balance)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('finalTypeName', finalTypeName)
          ..add('finalAmount', finalAmount)
          ..add('finalAccountNumber', finalAccountNumber)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('interestAmount', interestAmount)
          ..add('interestAmountEndOfTerm', interestAmountEndOfTerm)
          ..add('blocked', blocked)
          ..add('redemRate', redemRate)
          ..add('allowPartialWithdraw', allowPartialWithdraw)
          ..add('maxWithdrawalLimit', maxWithdrawalLimit))
        .toString();
  }
}

class SavingAccountDetailResponseBuilder
    implements
        Builder<SavingAccountDetailResponse,
            SavingAccountDetailResponseBuilder> {
  _$SavingAccountDetailResponse? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _alias;
  String? get alias => _$this._alias;
  set alias(String? alias) => _$this._alias = alias;

  String? _termId;
  String? get termId => _$this._termId;
  set termId(String? termId) => _$this._termId = termId;

  String? _termName;
  String? get termName => _$this._termName;
  set termName(String? termName) => _$this._termName = termName;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  String? _finalTypeName;
  String? get finalTypeName => _$this._finalTypeName;
  set finalTypeName(String? finalTypeName) =>
      _$this._finalTypeName = finalTypeName;

  num? _finalAmount;
  num? get finalAmount => _$this._finalAmount;
  set finalAmount(num? finalAmount) => _$this._finalAmount = finalAmount;

  String? _finalAccountNumber;
  String? get finalAccountNumber => _$this._finalAccountNumber;
  set finalAccountNumber(String? finalAccountNumber) =>
      _$this._finalAccountNumber = finalAccountNumber;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  num? _interestAmount;
  num? get interestAmount => _$this._interestAmount;
  set interestAmount(num? interestAmount) =>
      _$this._interestAmount = interestAmount;

  num? _interestAmountEndOfTerm;
  num? get interestAmountEndOfTerm => _$this._interestAmountEndOfTerm;
  set interestAmountEndOfTerm(num? interestAmountEndOfTerm) =>
      _$this._interestAmountEndOfTerm = interestAmountEndOfTerm;

  bool? _blocked;
  bool? get blocked => _$this._blocked;
  set blocked(bool? blocked) => _$this._blocked = blocked;

  double? _redemRate;
  double? get redemRate => _$this._redemRate;
  set redemRate(double? redemRate) => _$this._redemRate = redemRate;

  bool? _allowPartialWithdraw;
  bool? get allowPartialWithdraw => _$this._allowPartialWithdraw;
  set allowPartialWithdraw(bool? allowPartialWithdraw) =>
      _$this._allowPartialWithdraw = allowPartialWithdraw;

  num? _maxWithdrawalLimit;
  num? get maxWithdrawalLimit => _$this._maxWithdrawalLimit;
  set maxWithdrawalLimit(num? maxWithdrawalLimit) =>
      _$this._maxWithdrawalLimit = maxWithdrawalLimit;

  SavingAccountDetailResponseBuilder() {
    SavingAccountDetailResponse._defaults(this);
  }

  SavingAccountDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _customerName = $v.customerName;
      _transactionDate = $v.transactionDate;
      _accountNumber = $v.accountNumber;
      _accountName = $v.accountName;
      _alias = $v.alias;
      _termId = $v.termId;
      _termName = $v.termName;
      _currency = $v.currency;
      _rate = $v.rate;
      _balance = $v.balance;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _finalTypeName = $v.finalTypeName;
      _finalAmount = $v.finalAmount;
      _finalAccountNumber = $v.finalAccountNumber;
      _sourceAccountNo = $v.sourceAccountNo;
      _interestAmount = $v.interestAmount;
      _interestAmountEndOfTerm = $v.interestAmountEndOfTerm;
      _blocked = $v.blocked;
      _redemRate = $v.redemRate;
      _allowPartialWithdraw = $v.allowPartialWithdraw;
      _maxWithdrawalLimit = $v.maxWithdrawalLimit;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SavingAccountDetailResponse other) {
    _$v = other as _$SavingAccountDetailResponse;
  }

  @override
  void update(void Function(SavingAccountDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SavingAccountDetailResponse build() => _build();

  _$SavingAccountDetailResponse _build() {
    final _$result = _$v ??
        _$SavingAccountDetailResponse._(
          bankCif: bankCif,
          customerName: customerName,
          transactionDate: transactionDate,
          accountNumber: accountNumber,
          accountName: accountName,
          alias: alias,
          termId: termId,
          termName: termName,
          currency: currency,
          rate: rate,
          balance: balance,
          contractDate: contractDate,
          dueDate: dueDate,
          finalTypeName: finalTypeName,
          finalAmount: finalAmount,
          finalAccountNumber: finalAccountNumber,
          sourceAccountNo: sourceAccountNo,
          interestAmount: interestAmount,
          interestAmountEndOfTerm: interestAmountEndOfTerm,
          blocked: blocked,
          redemRate: redemRate,
          allowPartialWithdraw: allowPartialWithdraw,
          maxWithdrawalLimit: maxWithdrawalLimit,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
