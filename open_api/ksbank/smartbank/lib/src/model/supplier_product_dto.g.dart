// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supplier_product_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SupplierProductDto extends SupplierProductDto {
  @override
  final String? productCode;
  @override
  final bool? allowManualAmount;
  @override
  final String? productName;

  factory _$SupplierProductDto(
          [void Function(SupplierProductDtoBuilder)? updates]) =>
      (SupplierProductDtoBuilder()..update(updates))._build();

  _$SupplierProductDto._(
      {this.productCode, this.allowManualAmount, this.productName})
      : super._();
  @override
  SupplierProductDto rebuild(
          void Function(SupplierProductDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SupplierProductDtoBuilder toBuilder() =>
      SupplierProductDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SupplierProductDto &&
        productCode == other.productCode &&
        allowManualAmount == other.allowManualAmount &&
        productName == other.productName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, allowManualAmount.hashCode);
    _$hash = $jc(_$hash, productName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SupplierProductDto')
          ..add('productCode', productCode)
          ..add('allowManualAmount', allowManualAmount)
          ..add('productName', productName))
        .toString();
  }
}

class SupplierProductDtoBuilder
    implements Builder<SupplierProductDto, SupplierProductDtoBuilder> {
  _$SupplierProductDto? _$v;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  bool? _allowManualAmount;
  bool? get allowManualAmount => _$this._allowManualAmount;
  set allowManualAmount(bool? allowManualAmount) =>
      _$this._allowManualAmount = allowManualAmount;

  String? _productName;
  String? get productName => _$this._productName;
  set productName(String? productName) => _$this._productName = productName;

  SupplierProductDtoBuilder() {
    SupplierProductDto._defaults(this);
  }

  SupplierProductDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _productCode = $v.productCode;
      _allowManualAmount = $v.allowManualAmount;
      _productName = $v.productName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SupplierProductDto other) {
    _$v = other as _$SupplierProductDto;
  }

  @override
  void update(void Function(SupplierProductDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SupplierProductDto build() => _build();

  _$SupplierProductDto _build() {
    final _$result = _$v ??
        _$SupplierProductDto._(
          productCode: productCode,
          allowManualAmount: allowManualAmount,
          productName: productName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
