// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_process_history_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoanProcessHistoryResponse extends LoanProcessHistoryResponse {
  @override
  final String? stepName;
  @override
  final DateTime? processDate;

  factory _$LoanProcessHistoryResponse(
          [void Function(LoanProcessHistoryResponseBuilder)? updates]) =>
      (LoanProcessHistoryResponseBuilder()..update(updates))._build();

  _$LoanProcessHistoryResponse._({this.stepName, this.processDate}) : super._();
  @override
  LoanProcessHistoryResponse rebuild(
          void Function(LoanProcessHistoryResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanProcessHistoryResponseBuilder toBuilder() =>
      LoanProcessHistoryResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanProcessHistoryResponse &&
        stepName == other.stepName &&
        processDate == other.processDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, stepName.hashCode);
    _$hash = $jc(_$hash, processDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanProcessHistoryResponse')
          ..add('stepName', stepName)
          ..add('processDate', processDate))
        .toString();
  }
}

class LoanProcessHistoryResponseBuilder
    implements
        Builder<LoanProcessHistoryResponse, LoanProcessHistoryResponseBuilder> {
  _$LoanProcessHistoryResponse? _$v;

  String? _stepName;
  String? get stepName => _$this._stepName;
  set stepName(String? stepName) => _$this._stepName = stepName;

  DateTime? _processDate;
  DateTime? get processDate => _$this._processDate;
  set processDate(DateTime? processDate) => _$this._processDate = processDate;

  LoanProcessHistoryResponseBuilder() {
    LoanProcessHistoryResponse._defaults(this);
  }

  LoanProcessHistoryResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _stepName = $v.stepName;
      _processDate = $v.processDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanProcessHistoryResponse other) {
    _$v = other as _$LoanProcessHistoryResponse;
  }

  @override
  void update(void Function(LoanProcessHistoryResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanProcessHistoryResponse build() => _build();

  _$LoanProcessHistoryResponse _build() {
    final _$result = _$v ??
        _$LoanProcessHistoryResponse._(
          stepName: stepName,
          processDate: processDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
