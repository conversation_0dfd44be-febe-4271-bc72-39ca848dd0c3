// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_pin_code_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PaymentPinCodeRequestPinCodeTypeEnum
    _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_PHONE =
    const PaymentPinCodeRequestPinCodeTypeEnum._('TOPUP_PHONE');
const PaymentPinCodeRequestPinCodeTypeEnum
    _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_DATA =
    const PaymentPinCodeRequestPinCodeTypeEnum._('TOPUP_DATA');
const PaymentPinCodeRequestPinCodeTypeEnum
    _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_PHONE =
    const PaymentPinCodeRequestPinCodeTypeEnum._('PINCODE_PHONE');
const PaymentPinCodeRequestPinCodeTypeEnum
    _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_DATA =
    const PaymentPinCodeRequestPinCodeTypeEnum._('PINCODE_DATA');
const PaymentPinCodeRequestPinCodeTypeEnum
    _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_GAME =
    const PaymentPinCodeRequestPinCodeTypeEnum._('PINCODE_GAME');
const PaymentPinCodeRequestPinCodeTypeEnum
    _$paymentPinCodeRequestPinCodeTypeEnum_PAY_BILL =
    const PaymentPinCodeRequestPinCodeTypeEnum._('PAY_BILL');

PaymentPinCodeRequestPinCodeTypeEnum
    _$paymentPinCodeRequestPinCodeTypeEnumValueOf(String name) {
  switch (name) {
    case 'TOPUP_PHONE':
      return _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_PHONE;
    case 'TOPUP_DATA':
      return _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_DATA;
    case 'PINCODE_PHONE':
      return _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_PHONE;
    case 'PINCODE_DATA':
      return _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_DATA;
    case 'PINCODE_GAME':
      return _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_GAME;
    case 'PAY_BILL':
      return _$paymentPinCodeRequestPinCodeTypeEnum_PAY_BILL;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PaymentPinCodeRequestPinCodeTypeEnum>
    _$paymentPinCodeRequestPinCodeTypeEnumValues = BuiltSet<
        PaymentPinCodeRequestPinCodeTypeEnum>(const <PaymentPinCodeRequestPinCodeTypeEnum>[
  _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_PHONE,
  _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_DATA,
  _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_PHONE,
  _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_DATA,
  _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_GAME,
  _$paymentPinCodeRequestPinCodeTypeEnum_PAY_BILL,
]);

Serializer<PaymentPinCodeRequestPinCodeTypeEnum>
    _$paymentPinCodeRequestPinCodeTypeEnumSerializer =
    _$PaymentPinCodeRequestPinCodeTypeEnumSerializer();

class _$PaymentPinCodeRequestPinCodeTypeEnumSerializer
    implements PrimitiveSerializer<PaymentPinCodeRequestPinCodeTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'TOPUP_PHONE': 'TOPUP_PHONE',
    'TOPUP_DATA': 'TOPUP_DATA',
    'PINCODE_PHONE': 'PINCODE_PHONE',
    'PINCODE_DATA': 'PINCODE_DATA',
    'PINCODE_GAME': 'PINCODE_GAME',
    'PAY_BILL': 'PAY_BILL',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'TOPUP_PHONE': 'TOPUP_PHONE',
    'TOPUP_DATA': 'TOPUP_DATA',
    'PINCODE_PHONE': 'PINCODE_PHONE',
    'PINCODE_DATA': 'PINCODE_DATA',
    'PINCODE_GAME': 'PINCODE_GAME',
    'PAY_BILL': 'PAY_BILL',
  };

  @override
  final Iterable<Type> types = const <Type>[
    PaymentPinCodeRequestPinCodeTypeEnum
  ];
  @override
  final String wireName = 'PaymentPinCodeRequestPinCodeTypeEnum';

  @override
  Object serialize(
          Serializers serializers, PaymentPinCodeRequestPinCodeTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PaymentPinCodeRequestPinCodeTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PaymentPinCodeRequestPinCodeTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PaymentPinCodeRequest extends PaymentPinCodeRequest {
  @override
  final String? transactionNo;
  @override
  final String? accountNo;
  @override
  final String? productCode;
  @override
  final num? amount;
  @override
  final num? cardValue;
  @override
  final int? quantity;
  @override
  final PaymentPinCodeRequestPinCodeTypeEnum? pinCodeType;
  @override
  final String? pinCodeName;
  @override
  final String? pinCodeDesc;
  @override
  final String? supplierName;
  @override
  final String? supplierUrl;
  @override
  final VerifySoftOtpRequest? softOtp;

  factory _$PaymentPinCodeRequest(
          [void Function(PaymentPinCodeRequestBuilder)? updates]) =>
      (PaymentPinCodeRequestBuilder()..update(updates))._build();

  _$PaymentPinCodeRequest._(
      {this.transactionNo,
      this.accountNo,
      this.productCode,
      this.amount,
      this.cardValue,
      this.quantity,
      this.pinCodeType,
      this.pinCodeName,
      this.pinCodeDesc,
      this.supplierName,
      this.supplierUrl,
      this.softOtp})
      : super._();
  @override
  PaymentPinCodeRequest rebuild(
          void Function(PaymentPinCodeRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentPinCodeRequestBuilder toBuilder() =>
      PaymentPinCodeRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentPinCodeRequest &&
        transactionNo == other.transactionNo &&
        accountNo == other.accountNo &&
        productCode == other.productCode &&
        amount == other.amount &&
        cardValue == other.cardValue &&
        quantity == other.quantity &&
        pinCodeType == other.pinCodeType &&
        pinCodeName == other.pinCodeName &&
        pinCodeDesc == other.pinCodeDesc &&
        supplierName == other.supplierName &&
        supplierUrl == other.supplierUrl &&
        softOtp == other.softOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jc(_$hash, quantity.hashCode);
    _$hash = $jc(_$hash, pinCodeType.hashCode);
    _$hash = $jc(_$hash, pinCodeName.hashCode);
    _$hash = $jc(_$hash, pinCodeDesc.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, supplierUrl.hashCode);
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentPinCodeRequest')
          ..add('transactionNo', transactionNo)
          ..add('accountNo', accountNo)
          ..add('productCode', productCode)
          ..add('amount', amount)
          ..add('cardValue', cardValue)
          ..add('quantity', quantity)
          ..add('pinCodeType', pinCodeType)
          ..add('pinCodeName', pinCodeName)
          ..add('pinCodeDesc', pinCodeDesc)
          ..add('supplierName', supplierName)
          ..add('supplierUrl', supplierUrl)
          ..add('softOtp', softOtp))
        .toString();
  }
}

class PaymentPinCodeRequestBuilder
    implements Builder<PaymentPinCodeRequest, PaymentPinCodeRequestBuilder> {
  _$PaymentPinCodeRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  int? _quantity;
  int? get quantity => _$this._quantity;
  set quantity(int? quantity) => _$this._quantity = quantity;

  PaymentPinCodeRequestPinCodeTypeEnum? _pinCodeType;
  PaymentPinCodeRequestPinCodeTypeEnum? get pinCodeType => _$this._pinCodeType;
  set pinCodeType(PaymentPinCodeRequestPinCodeTypeEnum? pinCodeType) =>
      _$this._pinCodeType = pinCodeType;

  String? _pinCodeName;
  String? get pinCodeName => _$this._pinCodeName;
  set pinCodeName(String? pinCodeName) => _$this._pinCodeName = pinCodeName;

  String? _pinCodeDesc;
  String? get pinCodeDesc => _$this._pinCodeDesc;
  set pinCodeDesc(String? pinCodeDesc) => _$this._pinCodeDesc = pinCodeDesc;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _supplierUrl;
  String? get supplierUrl => _$this._supplierUrl;
  set supplierUrl(String? supplierUrl) => _$this._supplierUrl = supplierUrl;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  PaymentPinCodeRequestBuilder() {
    PaymentPinCodeRequest._defaults(this);
  }

  PaymentPinCodeRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _accountNo = $v.accountNo;
      _productCode = $v.productCode;
      _amount = $v.amount;
      _cardValue = $v.cardValue;
      _quantity = $v.quantity;
      _pinCodeType = $v.pinCodeType;
      _pinCodeName = $v.pinCodeName;
      _pinCodeDesc = $v.pinCodeDesc;
      _supplierName = $v.supplierName;
      _supplierUrl = $v.supplierUrl;
      _softOtp = $v.softOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentPinCodeRequest other) {
    _$v = other as _$PaymentPinCodeRequest;
  }

  @override
  void update(void Function(PaymentPinCodeRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentPinCodeRequest build() => _build();

  _$PaymentPinCodeRequest _build() {
    _$PaymentPinCodeRequest _$result;
    try {
      _$result = _$v ??
          _$PaymentPinCodeRequest._(
            transactionNo: transactionNo,
            accountNo: accountNo,
            productCode: productCode,
            amount: amount,
            cardValue: cardValue,
            quantity: quantity,
            pinCodeType: pinCodeType,
            pinCodeName: pinCodeName,
            pinCodeDesc: pinCodeDesc,
            supplierName: supplierName,
            supplierUrl: supplierUrl,
            softOtp: _softOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PaymentPinCodeRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
