//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'vvip_open_method.g.dart';

class VvipOpenMethod extends EnumClass {
  /// Mở bằng quét QR hay thủ công
  @BuiltValueEnumConst(wireName: r'QR_CODE')
  static const VvipOpenMethod QR_CODE = _$QR_CODE;

  /// Mở bằng quét QR hay thủ công
  @BuiltValueEnumConst(wireName: r'MANUAL')
  static const VvipOpenMethod MANUAL = _$MANUAL;

  static Serializer<VvipOpenMethod> get serializer =>
      _$vvipOpenMethodSerializer;

  const VvipOpenMethod._(String name) : super(name);

  static BuiltSet<VvipOpenMethod> get values => _$values;
  static VvipOpenMethod valueOf(String name) => _$valueOf(name);
}

/// Optionally, enum_class can generate a mixin to go with your enum for use
/// with <PERSON><PERSON>. It exposes your enum constants as getters. So, if you mix it
/// in to your Dart component class, the values become available to the
/// corresponding Angular template.
///
/// Trigger mixin generation by writing a line like this one next to your enum.
abstract class VvipOpenMethodMixin = Object with _$VvipOpenMethodMixin;
