// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SavingAccountResponse extends SavingAccountResponse {
  @override
  final String? accountNumber;
  @override
  final int? accountType;
  @override
  final String? accountStatus;
  @override
  final String? accountName;
  @override
  final String? alias;
  @override
  final String? currency;
  @override
  final double? rate;
  @override
  final String? termName;
  @override
  final String? balance;
  @override
  final String? currentAmount;
  @override
  final String? availableAmount;
  @override
  final num? initialTdamt;
  @override
  final String? holdAmount;
  @override
  final String? imageUrl;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final int? daysLeft;
  @override
  final num? daysRateCompletion;
  @override
  final bool? onlineSaving;

  factory _$SavingAccountResponse(
          [void Function(SavingAccountResponseBuilder)? updates]) =>
      (SavingAccountResponseBuilder()..update(updates))._build();

  _$SavingAccountResponse._(
      {this.accountNumber,
      this.accountType,
      this.accountStatus,
      this.accountName,
      this.alias,
      this.currency,
      this.rate,
      this.termName,
      this.balance,
      this.currentAmount,
      this.availableAmount,
      this.initialTdamt,
      this.holdAmount,
      this.imageUrl,
      this.contractDate,
      this.dueDate,
      this.daysLeft,
      this.daysRateCompletion,
      this.onlineSaving})
      : super._();
  @override
  SavingAccountResponse rebuild(
          void Function(SavingAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SavingAccountResponseBuilder toBuilder() =>
      SavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SavingAccountResponse &&
        accountNumber == other.accountNumber &&
        accountType == other.accountType &&
        accountStatus == other.accountStatus &&
        accountName == other.accountName &&
        alias == other.alias &&
        currency == other.currency &&
        rate == other.rate &&
        termName == other.termName &&
        balance == other.balance &&
        currentAmount == other.currentAmount &&
        availableAmount == other.availableAmount &&
        initialTdamt == other.initialTdamt &&
        holdAmount == other.holdAmount &&
        imageUrl == other.imageUrl &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        daysLeft == other.daysLeft &&
        daysRateCompletion == other.daysRateCompletion &&
        onlineSaving == other.onlineSaving;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, accountType.hashCode);
    _$hash = $jc(_$hash, accountStatus.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, alias.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, termName.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, currentAmount.hashCode);
    _$hash = $jc(_$hash, availableAmount.hashCode);
    _$hash = $jc(_$hash, initialTdamt.hashCode);
    _$hash = $jc(_$hash, holdAmount.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, daysLeft.hashCode);
    _$hash = $jc(_$hash, daysRateCompletion.hashCode);
    _$hash = $jc(_$hash, onlineSaving.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SavingAccountResponse')
          ..add('accountNumber', accountNumber)
          ..add('accountType', accountType)
          ..add('accountStatus', accountStatus)
          ..add('accountName', accountName)
          ..add('alias', alias)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('termName', termName)
          ..add('balance', balance)
          ..add('currentAmount', currentAmount)
          ..add('availableAmount', availableAmount)
          ..add('initialTdamt', initialTdamt)
          ..add('holdAmount', holdAmount)
          ..add('imageUrl', imageUrl)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('daysLeft', daysLeft)
          ..add('daysRateCompletion', daysRateCompletion)
          ..add('onlineSaving', onlineSaving))
        .toString();
  }
}

class SavingAccountResponseBuilder
    implements Builder<SavingAccountResponse, SavingAccountResponseBuilder> {
  _$SavingAccountResponse? _$v;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  int? _accountType;
  int? get accountType => _$this._accountType;
  set accountType(int? accountType) => _$this._accountType = accountType;

  String? _accountStatus;
  String? get accountStatus => _$this._accountStatus;
  set accountStatus(String? accountStatus) =>
      _$this._accountStatus = accountStatus;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _alias;
  String? get alias => _$this._alias;
  set alias(String? alias) => _$this._alias = alias;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  String? _termName;
  String? get termName => _$this._termName;
  set termName(String? termName) => _$this._termName = termName;

  String? _balance;
  String? get balance => _$this._balance;
  set balance(String? balance) => _$this._balance = balance;

  String? _currentAmount;
  String? get currentAmount => _$this._currentAmount;
  set currentAmount(String? currentAmount) =>
      _$this._currentAmount = currentAmount;

  String? _availableAmount;
  String? get availableAmount => _$this._availableAmount;
  set availableAmount(String? availableAmount) =>
      _$this._availableAmount = availableAmount;

  num? _initialTdamt;
  num? get initialTdamt => _$this._initialTdamt;
  set initialTdamt(num? initialTdamt) => _$this._initialTdamt = initialTdamt;

  String? _holdAmount;
  String? get holdAmount => _$this._holdAmount;
  set holdAmount(String? holdAmount) => _$this._holdAmount = holdAmount;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  int? _daysLeft;
  int? get daysLeft => _$this._daysLeft;
  set daysLeft(int? daysLeft) => _$this._daysLeft = daysLeft;

  num? _daysRateCompletion;
  num? get daysRateCompletion => _$this._daysRateCompletion;
  set daysRateCompletion(num? daysRateCompletion) =>
      _$this._daysRateCompletion = daysRateCompletion;

  bool? _onlineSaving;
  bool? get onlineSaving => _$this._onlineSaving;
  set onlineSaving(bool? onlineSaving) => _$this._onlineSaving = onlineSaving;

  SavingAccountResponseBuilder() {
    SavingAccountResponse._defaults(this);
  }

  SavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNumber = $v.accountNumber;
      _accountType = $v.accountType;
      _accountStatus = $v.accountStatus;
      _accountName = $v.accountName;
      _alias = $v.alias;
      _currency = $v.currency;
      _rate = $v.rate;
      _termName = $v.termName;
      _balance = $v.balance;
      _currentAmount = $v.currentAmount;
      _availableAmount = $v.availableAmount;
      _initialTdamt = $v.initialTdamt;
      _holdAmount = $v.holdAmount;
      _imageUrl = $v.imageUrl;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _daysLeft = $v.daysLeft;
      _daysRateCompletion = $v.daysRateCompletion;
      _onlineSaving = $v.onlineSaving;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SavingAccountResponse other) {
    _$v = other as _$SavingAccountResponse;
  }

  @override
  void update(void Function(SavingAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SavingAccountResponse build() => _build();

  _$SavingAccountResponse _build() {
    final _$result = _$v ??
        _$SavingAccountResponse._(
          accountNumber: accountNumber,
          accountType: accountType,
          accountStatus: accountStatus,
          accountName: accountName,
          alias: alias,
          currency: currency,
          rate: rate,
          termName: termName,
          balance: balance,
          currentAmount: currentAmount,
          availableAmount: availableAmount,
          initialTdamt: initialTdamt,
          holdAmount: holdAmount,
          imageUrl: imageUrl,
          contractDate: contractDate,
          dueDate: dueDate,
          daysLeft: daysLeft,
          daysRateCompletion: daysRateCompletion,
          onlineSaving: onlineSaving,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
