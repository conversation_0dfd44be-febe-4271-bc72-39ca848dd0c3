// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_withdrawal_code_active_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListWithdrawalCodeActiveResponse
    extends ListWithdrawalCodeActiveResponse {
  @override
  final BuiltList<WithdrawalCodeResponse>? withdrawalCodes;

  factory _$ListWithdrawalCodeActiveResponse(
          [void Function(ListWithdrawalCodeActiveResponseBuilder)? updates]) =>
      (ListWithdrawalCodeActiveResponseBuilder()..update(updates))._build();

  _$ListWithdrawalCodeActiveResponse._({this.withdrawalCodes}) : super._();
  @override
  ListWithdrawalCodeActiveResponse rebuild(
          void Function(ListWithdrawalCodeActiveResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListWithdrawalCodeActiveResponseBuilder toBuilder() =>
      ListWithdrawalCodeActiveResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListWithdrawalCodeActiveResponse &&
        withdrawalCodes == other.withdrawalCodes;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, withdrawalCodes.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListWithdrawalCodeActiveResponse')
          ..add('withdrawalCodes', withdrawalCodes))
        .toString();
  }
}

class ListWithdrawalCodeActiveResponseBuilder
    implements
        Builder<ListWithdrawalCodeActiveResponse,
            ListWithdrawalCodeActiveResponseBuilder> {
  _$ListWithdrawalCodeActiveResponse? _$v;

  ListBuilder<WithdrawalCodeResponse>? _withdrawalCodes;
  ListBuilder<WithdrawalCodeResponse> get withdrawalCodes =>
      _$this._withdrawalCodes ??= ListBuilder<WithdrawalCodeResponse>();
  set withdrawalCodes(ListBuilder<WithdrawalCodeResponse>? withdrawalCodes) =>
      _$this._withdrawalCodes = withdrawalCodes;

  ListWithdrawalCodeActiveResponseBuilder() {
    ListWithdrawalCodeActiveResponse._defaults(this);
  }

  ListWithdrawalCodeActiveResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _withdrawalCodes = $v.withdrawalCodes?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListWithdrawalCodeActiveResponse other) {
    _$v = other as _$ListWithdrawalCodeActiveResponse;
  }

  @override
  void update(void Function(ListWithdrawalCodeActiveResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListWithdrawalCodeActiveResponse build() => _build();

  _$ListWithdrawalCodeActiveResponse _build() {
    _$ListWithdrawalCodeActiveResponse _$result;
    try {
      _$result = _$v ??
          _$ListWithdrawalCodeActiveResponse._(
            withdrawalCodes: _withdrawalCodes?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'withdrawalCodes';
        _withdrawalCodes?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListWithdrawalCodeActiveResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
