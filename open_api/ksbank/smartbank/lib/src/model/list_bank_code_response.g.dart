// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_bank_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListBankCodeResponse extends ListBankCodeResponse {
  @override
  final BuiltList<BankInfo>? listBanks;

  factory _$ListBankCodeResponse(
          [void Function(ListBankCodeResponseBuilder)? updates]) =>
      (ListBankCodeResponseBuilder()..update(updates))._build();

  _$ListBankCodeResponse._({this.listBanks}) : super._();
  @override
  ListBankCodeResponse rebuild(
          void Function(ListBankCodeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListBankCodeResponseBuilder toBuilder() =>
      ListBankCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListBankCodeResponse && listBanks == other.listBanks;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, listBanks.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListBankCodeResponse')
          ..add('listBanks', listBanks))
        .toString();
  }
}

class ListBankCodeResponseBuilder
    implements Builder<ListBankCodeResponse, ListBankCodeResponseBuilder> {
  _$ListBankCodeResponse? _$v;

  ListBuilder<BankInfo>? _listBanks;
  ListBuilder<BankInfo> get listBanks =>
      _$this._listBanks ??= ListBuilder<BankInfo>();
  set listBanks(ListBuilder<BankInfo>? listBanks) =>
      _$this._listBanks = listBanks;

  ListBankCodeResponseBuilder() {
    ListBankCodeResponse._defaults(this);
  }

  ListBankCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _listBanks = $v.listBanks?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListBankCodeResponse other) {
    _$v = other as _$ListBankCodeResponse;
  }

  @override
  void update(void Function(ListBankCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListBankCodeResponse build() => _build();

  _$ListBankCodeResponse _build() {
    _$ListBankCodeResponse _$result;
    try {
      _$result = _$v ??
          _$ListBankCodeResponse._(
            listBanks: _listBanks?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'listBanks';
        _listBanks?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListBankCodeResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
