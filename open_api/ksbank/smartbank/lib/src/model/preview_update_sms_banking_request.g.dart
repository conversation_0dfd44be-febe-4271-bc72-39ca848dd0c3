// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'preview_update_sms_banking_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PreviewUpdateSmsBankingRequestActionEnum
    _$previewUpdateSmsBankingRequestActionEnum_CANCEL =
    const PreviewUpdateSmsBankingRequestActionEnum._('CANCEL');
const PreviewUpdateSmsBankingRequestActionEnum
    _$previewUpdateSmsBankingRequestActionEnum_REGISTER =
    const PreviewUpdateSmsBankingRequestActionEnum._('REGISTER');
const PreviewUpdateSmsBankingRequestActionEnum
    _$previewUpdateSmsBankingRequestActionEnum_CHANGE_ACCOUNT =
    const PreviewUpdateSmsBankingRequestActionEnum._('CHANGE_ACCOUNT');

PreviewUpdateSmsBankingRequestActionEnum
    _$previewUpdateSmsBankingRequestActionEnumValueOf(String name) {
  switch (name) {
    case 'CANCEL':
      return _$previewUpdateSmsBankingRequestActionEnum_CANCEL;
    case 'REGISTER':
      return _$previewUpdateSmsBankingRequestActionEnum_REGISTER;
    case 'CHANGE_ACCOUNT':
      return _$previewUpdateSmsBankingRequestActionEnum_CHANGE_ACCOUNT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PreviewUpdateSmsBankingRequestActionEnum>
    _$previewUpdateSmsBankingRequestActionEnumValues = BuiltSet<
        PreviewUpdateSmsBankingRequestActionEnum>(const <PreviewUpdateSmsBankingRequestActionEnum>[
  _$previewUpdateSmsBankingRequestActionEnum_CANCEL,
  _$previewUpdateSmsBankingRequestActionEnum_REGISTER,
  _$previewUpdateSmsBankingRequestActionEnum_CHANGE_ACCOUNT,
]);

Serializer<PreviewUpdateSmsBankingRequestActionEnum>
    _$previewUpdateSmsBankingRequestActionEnumSerializer =
    _$PreviewUpdateSmsBankingRequestActionEnumSerializer();

class _$PreviewUpdateSmsBankingRequestActionEnumSerializer
    implements PrimitiveSerializer<PreviewUpdateSmsBankingRequestActionEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'CANCEL': 'CANCEL',
    'REGISTER': 'REGISTER',
    'CHANGE_ACCOUNT': 'CHANGE_ACCOUNT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'CANCEL': 'CANCEL',
    'REGISTER': 'REGISTER',
    'CHANGE_ACCOUNT': 'CHANGE_ACCOUNT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    PreviewUpdateSmsBankingRequestActionEnum
  ];
  @override
  final String wireName = 'PreviewUpdateSmsBankingRequestActionEnum';

  @override
  Object serialize(Serializers serializers,
          PreviewUpdateSmsBankingRequestActionEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PreviewUpdateSmsBankingRequestActionEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PreviewUpdateSmsBankingRequestActionEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PreviewUpdateSmsBankingRequest extends PreviewUpdateSmsBankingRequest {
  @override
  final PreviewUpdateSmsBankingRequestActionEnum? action;
  @override
  final String? phoneNumber;
  @override
  final String? regCasaAccounts;
  @override
  final int? regTdAccounts;
  @override
  final int? regLoanAccounts;
  @override
  final String? indexPhoneRemove;

  factory _$PreviewUpdateSmsBankingRequest(
          [void Function(PreviewUpdateSmsBankingRequestBuilder)? updates]) =>
      (PreviewUpdateSmsBankingRequestBuilder()..update(updates))._build();

  _$PreviewUpdateSmsBankingRequest._(
      {this.action,
      this.phoneNumber,
      this.regCasaAccounts,
      this.regTdAccounts,
      this.regLoanAccounts,
      this.indexPhoneRemove})
      : super._();
  @override
  PreviewUpdateSmsBankingRequest rebuild(
          void Function(PreviewUpdateSmsBankingRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PreviewUpdateSmsBankingRequestBuilder toBuilder() =>
      PreviewUpdateSmsBankingRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PreviewUpdateSmsBankingRequest &&
        action == other.action &&
        phoneNumber == other.phoneNumber &&
        regCasaAccounts == other.regCasaAccounts &&
        regTdAccounts == other.regTdAccounts &&
        regLoanAccounts == other.regLoanAccounts &&
        indexPhoneRemove == other.indexPhoneRemove;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, action.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, regCasaAccounts.hashCode);
    _$hash = $jc(_$hash, regTdAccounts.hashCode);
    _$hash = $jc(_$hash, regLoanAccounts.hashCode);
    _$hash = $jc(_$hash, indexPhoneRemove.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PreviewUpdateSmsBankingRequest')
          ..add('action', action)
          ..add('phoneNumber', phoneNumber)
          ..add('regCasaAccounts', regCasaAccounts)
          ..add('regTdAccounts', regTdAccounts)
          ..add('regLoanAccounts', regLoanAccounts)
          ..add('indexPhoneRemove', indexPhoneRemove))
        .toString();
  }
}

class PreviewUpdateSmsBankingRequestBuilder
    implements
        Builder<PreviewUpdateSmsBankingRequest,
            PreviewUpdateSmsBankingRequestBuilder> {
  _$PreviewUpdateSmsBankingRequest? _$v;

  PreviewUpdateSmsBankingRequestActionEnum? _action;
  PreviewUpdateSmsBankingRequestActionEnum? get action => _$this._action;
  set action(PreviewUpdateSmsBankingRequestActionEnum? action) =>
      _$this._action = action;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _regCasaAccounts;
  String? get regCasaAccounts => _$this._regCasaAccounts;
  set regCasaAccounts(String? regCasaAccounts) =>
      _$this._regCasaAccounts = regCasaAccounts;

  int? _regTdAccounts;
  int? get regTdAccounts => _$this._regTdAccounts;
  set regTdAccounts(int? regTdAccounts) =>
      _$this._regTdAccounts = regTdAccounts;

  int? _regLoanAccounts;
  int? get regLoanAccounts => _$this._regLoanAccounts;
  set regLoanAccounts(int? regLoanAccounts) =>
      _$this._regLoanAccounts = regLoanAccounts;

  String? _indexPhoneRemove;
  String? get indexPhoneRemove => _$this._indexPhoneRemove;
  set indexPhoneRemove(String? indexPhoneRemove) =>
      _$this._indexPhoneRemove = indexPhoneRemove;

  PreviewUpdateSmsBankingRequestBuilder() {
    PreviewUpdateSmsBankingRequest._defaults(this);
  }

  PreviewUpdateSmsBankingRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _action = $v.action;
      _phoneNumber = $v.phoneNumber;
      _regCasaAccounts = $v.regCasaAccounts;
      _regTdAccounts = $v.regTdAccounts;
      _regLoanAccounts = $v.regLoanAccounts;
      _indexPhoneRemove = $v.indexPhoneRemove;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PreviewUpdateSmsBankingRequest other) {
    _$v = other as _$PreviewUpdateSmsBankingRequest;
  }

  @override
  void update(void Function(PreviewUpdateSmsBankingRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PreviewUpdateSmsBankingRequest build() => _build();

  _$PreviewUpdateSmsBankingRequest _build() {
    final _$result = _$v ??
        _$PreviewUpdateSmsBankingRequest._(
          action: action,
          phoneNumber: phoneNumber,
          regCasaAccounts: regCasaAccounts,
          regTdAccounts: regTdAccounts,
          regLoanAccounts: regLoanAccounts,
          indexPhoneRemove: indexPhoneRemove,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
