// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_app_module_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoginAppModuleResponse extends LoginAppModuleResponse {
  @override
  final String? appId;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? provider;
  @override
  final String? merchantUrl;
  @override
  final String? sessionId;

  factory _$LoginAppModuleResponse(
          [void Function(LoginAppModuleResponseBuilder)? updates]) =>
      (LoginAppModuleResponseBuilder()..update(updates))._build();

  _$LoginAppModuleResponse._(
      {this.appId,
      this.name,
      this.description,
      this.provider,
      this.merchantUrl,
      this.sessionId})
      : super._();
  @override
  LoginAppModuleResponse rebuild(
          void Function(LoginAppModuleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginAppModuleResponseBuilder toBuilder() =>
      LoginAppModuleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoginAppModuleResponse &&
        appId == other.appId &&
        name == other.name &&
        description == other.description &&
        provider == other.provider &&
        merchantUrl == other.merchantUrl &&
        sessionId == other.sessionId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, appId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, provider.hashCode);
    _$hash = $jc(_$hash, merchantUrl.hashCode);
    _$hash = $jc(_$hash, sessionId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoginAppModuleResponse')
          ..add('appId', appId)
          ..add('name', name)
          ..add('description', description)
          ..add('provider', provider)
          ..add('merchantUrl', merchantUrl)
          ..add('sessionId', sessionId))
        .toString();
  }
}

class LoginAppModuleResponseBuilder
    implements Builder<LoginAppModuleResponse, LoginAppModuleResponseBuilder> {
  _$LoginAppModuleResponse? _$v;

  String? _appId;
  String? get appId => _$this._appId;
  set appId(String? appId) => _$this._appId = appId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _provider;
  String? get provider => _$this._provider;
  set provider(String? provider) => _$this._provider = provider;

  String? _merchantUrl;
  String? get merchantUrl => _$this._merchantUrl;
  set merchantUrl(String? merchantUrl) => _$this._merchantUrl = merchantUrl;

  String? _sessionId;
  String? get sessionId => _$this._sessionId;
  set sessionId(String? sessionId) => _$this._sessionId = sessionId;

  LoginAppModuleResponseBuilder() {
    LoginAppModuleResponse._defaults(this);
  }

  LoginAppModuleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _appId = $v.appId;
      _name = $v.name;
      _description = $v.description;
      _provider = $v.provider;
      _merchantUrl = $v.merchantUrl;
      _sessionId = $v.sessionId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoginAppModuleResponse other) {
    _$v = other as _$LoginAppModuleResponse;
  }

  @override
  void update(void Function(LoginAppModuleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoginAppModuleResponse build() => _build();

  _$LoginAppModuleResponse _build() {
    final _$result = _$v ??
        _$LoginAppModuleResponse._(
          appId: appId,
          name: name,
          description: description,
          provider: provider,
          merchantUrl: merchantUrl,
          sessionId: sessionId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
