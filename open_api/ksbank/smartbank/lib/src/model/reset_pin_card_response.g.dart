// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_pin_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResetPINCardResponse extends ResetPINCardResponse {
  @override
  final String? message;

  factory _$ResetPINCardResponse(
          [void Function(ResetPINCardResponseBuilder)? updates]) =>
      (ResetPINCardResponseBuilder()..update(updates))._build();

  _$ResetPINCardResponse._({this.message}) : super._();
  @override
  ResetPINCardResponse rebuild(
          void Function(ResetPINCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResetPINCardResponseBuilder toBuilder() =>
      ResetPINCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResetPINCardResponse && message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResetPINCardResponse')
          ..add('message', message))
        .toString();
  }
}

class ResetPINCardResponseBuilder
    implements Builder<ResetPINCardResponse, ResetPINCardResponseBuilder> {
  _$ResetPINCardResponse? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ResetPINCardResponseBuilder() {
    ResetPINCardResponse._defaults(this);
  }

  ResetPINCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResetPINCardResponse other) {
    _$v = other as _$ResetPINCardResponse;
  }

  @override
  void update(void Function(ResetPINCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResetPINCardResponse build() => _build();

  _$ResetPINCardResponse _build() {
    final _$result = _$v ??
        _$ResetPINCardResponse._(
          message: message,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
