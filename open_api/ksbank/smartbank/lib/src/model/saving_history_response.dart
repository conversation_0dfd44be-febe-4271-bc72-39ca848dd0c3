//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'saving_history_response.g.dart';

/// L<PERSON><PERSON> sử tiết kiệm
///
/// Properties:
/// * [accountNo] - Số tài khoản
/// * [savingType] - <PERSON><PERSON><PERSON> tài khoản
/// * [accountName] - Tên tài khoản
/// * [amount] - Số tiền
/// * [currency] - Lo<PERSON>i tiền
/// * [accountStatus] - Trạng thái của tài khoản
/// * [startDate] - Ng<PERSON>y bắt đầu
/// * [dueDate] - <PERSON><PERSON><PERSON> đế<PERSON> hạn
@BuiltValue()
abstract class SavingHistoryResponse
    implements Built<SavingHistoryResponse, SavingHistoryResponseBuilder> {
  /// Số tài khoản
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  /// Loại tài khoản
  @BuiltValueField(wireName: r'savingType')
  SavingHistoryResponseSavingTypeEnum? get savingType;
  // enum savingTypeEnum {  ONLINE_SAVING,  TARGET_SAVING,  OFFLINE_SAVING,  };

  /// Tên tài khoản
  @BuiltValueField(wireName: r'accountName')
  String? get accountName;

  /// Số tiền
  @BuiltValueField(wireName: r'amount')
  double? get amount;

  /// Loại tiền
  @BuiltValueField(wireName: r'currency')
  SavingHistoryResponseCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  /// Trạng thái của tài khoản
  @BuiltValueField(wireName: r'accountStatus')
  int? get accountStatus;

  /// Ngày bắt đầu
  @BuiltValueField(wireName: r'startDate')
  DateTime? get startDate;

  /// Ngày đến hạn
  @BuiltValueField(wireName: r'dueDate')
  DateTime? get dueDate;

  SavingHistoryResponse._();

  factory SavingHistoryResponse(
      [void updates(SavingHistoryResponseBuilder b)]) = _$SavingHistoryResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SavingHistoryResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SavingHistoryResponse> get serializer =>
      _$SavingHistoryResponseSerializer();
}

class _$SavingHistoryResponseSerializer
    implements PrimitiveSerializer<SavingHistoryResponse> {
  @override
  final Iterable<Type> types = const [
    SavingHistoryResponse,
    _$SavingHistoryResponse
  ];

  @override
  final String wireName = r'SavingHistoryResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SavingHistoryResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.savingType != null) {
      yield r'savingType';
      yield serializers.serialize(
        object.savingType,
        specifiedType:
            const FullType.nullable(SavingHistoryResponseSavingTypeEnum),
      );
    }
    if (object.accountName != null) {
      yield r'accountName';
      yield serializers.serialize(
        object.accountName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType:
            const FullType.nullable(SavingHistoryResponseCurrencyEnum),
      );
    }
    if (object.accountStatus != null) {
      yield r'accountStatus';
      yield serializers.serialize(
        object.accountStatus,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.startDate != null) {
      yield r'startDate';
      yield serializers.serialize(
        object.startDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.dueDate != null) {
      yield r'dueDate';
      yield serializers.serialize(
        object.dueDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    SavingHistoryResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SavingHistoryResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        case r'savingType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(SavingHistoryResponseSavingTypeEnum),
          ) as SavingHistoryResponseSavingTypeEnum?;
          if (valueDes == null) continue;
          result.savingType = valueDes;
          break;
        case r'accountName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountName = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(SavingHistoryResponseCurrencyEnum),
          ) as SavingHistoryResponseCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        case r'accountStatus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.accountStatus = valueDes;
          break;
        case r'startDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.startDate = valueDes;
          break;
        case r'dueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.dueDate = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SavingHistoryResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SavingHistoryResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class SavingHistoryResponseSavingTypeEnum extends EnumClass {
  /// Loại tài khoản
  @BuiltValueEnumConst(wireName: r'ONLINE_SAVING')
  static const SavingHistoryResponseSavingTypeEnum ONLINE_SAVING =
      _$savingHistoryResponseSavingTypeEnum_ONLINE_SAVING;

  /// Loại tài khoản
  @BuiltValueEnumConst(wireName: r'TARGET_SAVING')
  static const SavingHistoryResponseSavingTypeEnum TARGET_SAVING =
      _$savingHistoryResponseSavingTypeEnum_TARGET_SAVING;

  /// Loại tài khoản
  @BuiltValueEnumConst(wireName: r'OFFLINE_SAVING')
  static const SavingHistoryResponseSavingTypeEnum OFFLINE_SAVING =
      _$savingHistoryResponseSavingTypeEnum_OFFLINE_SAVING;

  static Serializer<SavingHistoryResponseSavingTypeEnum> get serializer =>
      _$savingHistoryResponseSavingTypeEnumSerializer;

  const SavingHistoryResponseSavingTypeEnum._(String name) : super(name);

  static BuiltSet<SavingHistoryResponseSavingTypeEnum> get values =>
      _$savingHistoryResponseSavingTypeEnumValues;
  static SavingHistoryResponseSavingTypeEnum valueOf(String name) =>
      _$savingHistoryResponseSavingTypeEnumValueOf(name);
}

class SavingHistoryResponseCurrencyEnum extends EnumClass {
  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'VND')
  static const SavingHistoryResponseCurrencyEnum VND =
      _$savingHistoryResponseCurrencyEnum_VND;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'USD')
  static const SavingHistoryResponseCurrencyEnum USD =
      _$savingHistoryResponseCurrencyEnum_USD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'ACB')
  static const SavingHistoryResponseCurrencyEnum ACB =
      _$savingHistoryResponseCurrencyEnum_ACB;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'JPY')
  static const SavingHistoryResponseCurrencyEnum JPY =
      _$savingHistoryResponseCurrencyEnum_JPY;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const SavingHistoryResponseCurrencyEnum GOLD =
      _$savingHistoryResponseCurrencyEnum_GOLD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'EUR')
  static const SavingHistoryResponseCurrencyEnum EUR =
      _$savingHistoryResponseCurrencyEnum_EUR;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'GBP')
  static const SavingHistoryResponseCurrencyEnum GBP =
      _$savingHistoryResponseCurrencyEnum_GBP;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'CHF')
  static const SavingHistoryResponseCurrencyEnum CHF =
      _$savingHistoryResponseCurrencyEnum_CHF;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'AUD')
  static const SavingHistoryResponseCurrencyEnum AUD =
      _$savingHistoryResponseCurrencyEnum_AUD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'CAD')
  static const SavingHistoryResponseCurrencyEnum CAD =
      _$savingHistoryResponseCurrencyEnum_CAD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'SGD')
  static const SavingHistoryResponseCurrencyEnum SGD =
      _$savingHistoryResponseCurrencyEnum_SGD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'THB')
  static const SavingHistoryResponseCurrencyEnum THB =
      _$savingHistoryResponseCurrencyEnum_THB;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'NOK')
  static const SavingHistoryResponseCurrencyEnum NOK =
      _$savingHistoryResponseCurrencyEnum_NOK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'NZD')
  static const SavingHistoryResponseCurrencyEnum NZD =
      _$savingHistoryResponseCurrencyEnum_NZD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'DKK')
  static const SavingHistoryResponseCurrencyEnum DKK =
      _$savingHistoryResponseCurrencyEnum_DKK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'HKD')
  static const SavingHistoryResponseCurrencyEnum HKD =
      _$savingHistoryResponseCurrencyEnum_HKD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'SEK')
  static const SavingHistoryResponseCurrencyEnum SEK =
      _$savingHistoryResponseCurrencyEnum_SEK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'MYR')
  static const SavingHistoryResponseCurrencyEnum MYR =
      _$savingHistoryResponseCurrencyEnum_MYR;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'XAU')
  static const SavingHistoryResponseCurrencyEnum XAU =
      _$savingHistoryResponseCurrencyEnum_XAU;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'MMK')
  static const SavingHistoryResponseCurrencyEnum MMK =
      _$savingHistoryResponseCurrencyEnum_MMK;

  static Serializer<SavingHistoryResponseCurrencyEnum> get serializer =>
      _$savingHistoryResponseCurrencyEnumSerializer;

  const SavingHistoryResponseCurrencyEnum._(String name) : super(name);

  static BuiltSet<SavingHistoryResponseCurrencyEnum> get values =>
      _$savingHistoryResponseCurrencyEnumValues;
  static SavingHistoryResponseCurrencyEnum valueOf(String name) =>
      _$savingHistoryResponseCurrencyEnumValueOf(name);
}
