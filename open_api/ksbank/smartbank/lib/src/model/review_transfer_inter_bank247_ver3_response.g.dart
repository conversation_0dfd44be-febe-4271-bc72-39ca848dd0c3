// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_inter_bank247_ver3_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferInterBank247Ver3Response
    extends ReviewTransferInterBank247Ver3Response {
  @override
  final String? cifNumber;
  @override
  final String? transactionNumber;
  @override
  final String? transactionName;
  @override
  final int? transactionGroup;
  @override
  final String? sourceAccountNumber;
  @override
  final String? targetAccountNumber;
  @override
  final String? targetAccountName;
  @override
  final ReviewAdvanceTransactionResponse? advanceTransaction;
  @override
  final num? amount;
  @override
  final int? fee;
  @override
  final int? tax;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;
  @override
  final int? targetAccountType;

  factory _$ReviewTransferInterBank247Ver3Response(
          [void Function(ReviewTransferInterBank247Ver3ResponseBuilder)?
              updates]) =>
      (ReviewTransferInterBank247Ver3ResponseBuilder()..update(updates))
          ._build();

  _$ReviewTransferInterBank247Ver3Response._(
      {this.cifNumber,
      this.transactionNumber,
      this.transactionName,
      this.transactionGroup,
      this.sourceAccountNumber,
      this.targetAccountNumber,
      this.targetAccountName,
      this.advanceTransaction,
      this.amount,
      this.fee,
      this.tax,
      this.transNextStep,
      this.content,
      this.targetAccountType})
      : super._();
  @override
  ReviewTransferInterBank247Ver3Response rebuild(
          void Function(ReviewTransferInterBank247Ver3ResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferInterBank247Ver3ResponseBuilder toBuilder() =>
      ReviewTransferInterBank247Ver3ResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferInterBank247Ver3Response &&
        cifNumber == other.cifNumber &&
        transactionNumber == other.transactionNumber &&
        transactionName == other.transactionName &&
        transactionGroup == other.transactionGroup &&
        sourceAccountNumber == other.sourceAccountNumber &&
        targetAccountNumber == other.targetAccountNumber &&
        targetAccountName == other.targetAccountName &&
        advanceTransaction == other.advanceTransaction &&
        amount == other.amount &&
        fee == other.fee &&
        tax == other.tax &&
        transNextStep == other.transNextStep &&
        content == other.content &&
        targetAccountType == other.targetAccountType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNumber.hashCode);
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, transactionName.hashCode);
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, sourceAccountNumber.hashCode);
    _$hash = $jc(_$hash, targetAccountNumber.hashCode);
    _$hash = $jc(_$hash, targetAccountName.hashCode);
    _$hash = $jc(_$hash, advanceTransaction.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jc(_$hash, tax.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, targetAccountType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewTransferInterBank247Ver3Response')
          ..add('cifNumber', cifNumber)
          ..add('transactionNumber', transactionNumber)
          ..add('transactionName', transactionName)
          ..add('transactionGroup', transactionGroup)
          ..add('sourceAccountNumber', sourceAccountNumber)
          ..add('targetAccountNumber', targetAccountNumber)
          ..add('targetAccountName', targetAccountName)
          ..add('advanceTransaction', advanceTransaction)
          ..add('amount', amount)
          ..add('fee', fee)
          ..add('tax', tax)
          ..add('transNextStep', transNextStep)
          ..add('content', content)
          ..add('targetAccountType', targetAccountType))
        .toString();
  }
}

class ReviewTransferInterBank247Ver3ResponseBuilder
    implements
        Builder<ReviewTransferInterBank247Ver3Response,
            ReviewTransferInterBank247Ver3ResponseBuilder> {
  _$ReviewTransferInterBank247Ver3Response? _$v;

  String? _cifNumber;
  String? get cifNumber => _$this._cifNumber;
  set cifNumber(String? cifNumber) => _$this._cifNumber = cifNumber;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  String? _transactionName;
  String? get transactionName => _$this._transactionName;
  set transactionName(String? transactionName) =>
      _$this._transactionName = transactionName;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _sourceAccountNumber;
  String? get sourceAccountNumber => _$this._sourceAccountNumber;
  set sourceAccountNumber(String? sourceAccountNumber) =>
      _$this._sourceAccountNumber = sourceAccountNumber;

  String? _targetAccountNumber;
  String? get targetAccountNumber => _$this._targetAccountNumber;
  set targetAccountNumber(String? targetAccountNumber) =>
      _$this._targetAccountNumber = targetAccountNumber;

  String? _targetAccountName;
  String? get targetAccountName => _$this._targetAccountName;
  set targetAccountName(String? targetAccountName) =>
      _$this._targetAccountName = targetAccountName;

  ReviewAdvanceTransactionResponseBuilder? _advanceTransaction;
  ReviewAdvanceTransactionResponseBuilder get advanceTransaction =>
      _$this._advanceTransaction ??= ReviewAdvanceTransactionResponseBuilder();
  set advanceTransaction(
          ReviewAdvanceTransactionResponseBuilder? advanceTransaction) =>
      _$this._advanceTransaction = advanceTransaction;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  int? _fee;
  int? get fee => _$this._fee;
  set fee(int? fee) => _$this._fee = fee;

  int? _tax;
  int? get tax => _$this._tax;
  set tax(int? tax) => _$this._tax = tax;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  int? _targetAccountType;
  int? get targetAccountType => _$this._targetAccountType;
  set targetAccountType(int? targetAccountType) =>
      _$this._targetAccountType = targetAccountType;

  ReviewTransferInterBank247Ver3ResponseBuilder() {
    ReviewTransferInterBank247Ver3Response._defaults(this);
  }

  ReviewTransferInterBank247Ver3ResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNumber = $v.cifNumber;
      _transactionNumber = $v.transactionNumber;
      _transactionName = $v.transactionName;
      _transactionGroup = $v.transactionGroup;
      _sourceAccountNumber = $v.sourceAccountNumber;
      _targetAccountNumber = $v.targetAccountNumber;
      _targetAccountName = $v.targetAccountName;
      _advanceTransaction = $v.advanceTransaction?.toBuilder();
      _amount = $v.amount;
      _fee = $v.fee;
      _tax = $v.tax;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _targetAccountType = $v.targetAccountType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferInterBank247Ver3Response other) {
    _$v = other as _$ReviewTransferInterBank247Ver3Response;
  }

  @override
  void update(
      void Function(ReviewTransferInterBank247Ver3ResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferInterBank247Ver3Response build() => _build();

  _$ReviewTransferInterBank247Ver3Response _build() {
    _$ReviewTransferInterBank247Ver3Response _$result;
    try {
      _$result = _$v ??
          _$ReviewTransferInterBank247Ver3Response._(
            cifNumber: cifNumber,
            transactionNumber: transactionNumber,
            transactionName: transactionName,
            transactionGroup: transactionGroup,
            sourceAccountNumber: sourceAccountNumber,
            targetAccountNumber: targetAccountNumber,
            targetAccountName: targetAccountName,
            advanceTransaction: _advanceTransaction?.build(),
            amount: amount,
            fee: fee,
            tax: tax,
            transNextStep: transNextStep,
            content: content,
            targetAccountType: targetAccountType,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'advanceTransaction';
        _advanceTransaction?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ReviewTransferInterBank247Ver3Response',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
