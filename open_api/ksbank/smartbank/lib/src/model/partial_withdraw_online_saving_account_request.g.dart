// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'partial_withdraw_online_saving_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PartialWithdrawOnlineSavingAccountRequest
    extends PartialWithdrawOnlineSavingAccountRequest {
  @override
  final String? bankCif;
  @override
  final String? accountNumber;
  @override
  final num? amount;
  @override
  final num? finalAmount;
  @override
  final String? narrative;
  @override
  final String? crAccountNo;
  @override
  final num? maxWithdrawalLimit;
  @override
  final String? transactionNo;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;

  factory _$PartialWithdrawOnlineSavingAccountRequest(
          [void Function(PartialWithdrawOnlineSavingAccountRequestBuilder)?
              updates]) =>
      (PartialWithdrawOnlineSavingAccountRequestBuilder()..update(updates))
          ._build();

  _$PartialWithdrawOnlineSavingAccountRequest._(
      {this.bankCif,
      this.accountNumber,
      this.amount,
      this.finalAmount,
      this.narrative,
      this.crAccountNo,
      this.maxWithdrawalLimit,
      this.transactionNo,
      this.verifySoftOtp})
      : super._();
  @override
  PartialWithdrawOnlineSavingAccountRequest rebuild(
          void Function(PartialWithdrawOnlineSavingAccountRequestBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PartialWithdrawOnlineSavingAccountRequestBuilder toBuilder() =>
      PartialWithdrawOnlineSavingAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PartialWithdrawOnlineSavingAccountRequest &&
        bankCif == other.bankCif &&
        accountNumber == other.accountNumber &&
        amount == other.amount &&
        finalAmount == other.finalAmount &&
        narrative == other.narrative &&
        crAccountNo == other.crAccountNo &&
        maxWithdrawalLimit == other.maxWithdrawalLimit &&
        transactionNo == other.transactionNo &&
        verifySoftOtp == other.verifySoftOtp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, narrative.hashCode);
    _$hash = $jc(_$hash, crAccountNo.hashCode);
    _$hash = $jc(_$hash, maxWithdrawalLimit.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'PartialWithdrawOnlineSavingAccountRequest')
          ..add('bankCif', bankCif)
          ..add('accountNumber', accountNumber)
          ..add('amount', amount)
          ..add('finalAmount', finalAmount)
          ..add('narrative', narrative)
          ..add('crAccountNo', crAccountNo)
          ..add('maxWithdrawalLimit', maxWithdrawalLimit)
          ..add('transactionNo', transactionNo)
          ..add('verifySoftOtp', verifySoftOtp))
        .toString();
  }
}

class PartialWithdrawOnlineSavingAccountRequestBuilder
    implements
        Builder<PartialWithdrawOnlineSavingAccountRequest,
            PartialWithdrawOnlineSavingAccountRequestBuilder> {
  _$PartialWithdrawOnlineSavingAccountRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _finalAmount;
  num? get finalAmount => _$this._finalAmount;
  set finalAmount(num? finalAmount) => _$this._finalAmount = finalAmount;

  String? _narrative;
  String? get narrative => _$this._narrative;
  set narrative(String? narrative) => _$this._narrative = narrative;

  String? _crAccountNo;
  String? get crAccountNo => _$this._crAccountNo;
  set crAccountNo(String? crAccountNo) => _$this._crAccountNo = crAccountNo;

  num? _maxWithdrawalLimit;
  num? get maxWithdrawalLimit => _$this._maxWithdrawalLimit;
  set maxWithdrawalLimit(num? maxWithdrawalLimit) =>
      _$this._maxWithdrawalLimit = maxWithdrawalLimit;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  PartialWithdrawOnlineSavingAccountRequestBuilder() {
    PartialWithdrawOnlineSavingAccountRequest._defaults(this);
  }

  PartialWithdrawOnlineSavingAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _accountNumber = $v.accountNumber;
      _amount = $v.amount;
      _finalAmount = $v.finalAmount;
      _narrative = $v.narrative;
      _crAccountNo = $v.crAccountNo;
      _maxWithdrawalLimit = $v.maxWithdrawalLimit;
      _transactionNo = $v.transactionNo;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PartialWithdrawOnlineSavingAccountRequest other) {
    _$v = other as _$PartialWithdrawOnlineSavingAccountRequest;
  }

  @override
  void update(
      void Function(PartialWithdrawOnlineSavingAccountRequestBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  PartialWithdrawOnlineSavingAccountRequest build() => _build();

  _$PartialWithdrawOnlineSavingAccountRequest _build() {
    _$PartialWithdrawOnlineSavingAccountRequest _$result;
    try {
      _$result = _$v ??
          _$PartialWithdrawOnlineSavingAccountRequest._(
            bankCif: bankCif,
            accountNumber: accountNumber,
            amount: amount,
            finalAmount: finalAmount,
            narrative: narrative,
            crAccountNo: crAccountNo,
            maxWithdrawalLimit: maxWithdrawalLimit,
            transactionNo: transactionNo,
            verifySoftOtp: _verifySoftOtp?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PartialWithdrawOnlineSavingAccountRequest',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
