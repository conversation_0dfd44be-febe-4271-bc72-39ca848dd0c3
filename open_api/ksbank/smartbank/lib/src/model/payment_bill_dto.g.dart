// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_bill_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentBillDto extends PaymentBillDto {
  @override
  final String? billId;

  factory _$PaymentBillDto([void Function(PaymentBillDtoBuilder)? updates]) =>
      (PaymentBillDtoBuilder()..update(updates))._build();

  _$PaymentBillDto._({this.billId}) : super._();
  @override
  PaymentBillDto rebuild(void Function(PaymentBillDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentBillDtoBuilder toBuilder() => PaymentBillDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentBillDto && billId == other.billId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, billId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentBillDto')
          ..add('billId', billId))
        .toString();
  }
}

class PaymentBillDtoBuilder
    implements Builder<PaymentBillDto, PaymentBillDtoBuilder> {
  _$PaymentBillDto? _$v;

  String? _billId;
  String? get billId => _$this._billId;
  set billId(String? billId) => _$this._billId = billId;

  PaymentBillDtoBuilder() {
    PaymentBillDto._defaults(this);
  }

  PaymentBillDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _billId = $v.billId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentBillDto other) {
    _$v = other as _$PaymentBillDto;
  }

  @override
  void update(void Function(PaymentBillDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentBillDto build() => _build();

  _$PaymentBillDto _build() {
    final _$result = _$v ??
        _$PaymentBillDto._(
          billId: billId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
