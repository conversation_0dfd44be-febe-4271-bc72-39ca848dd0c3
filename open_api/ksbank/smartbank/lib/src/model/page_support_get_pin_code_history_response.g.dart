// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'page_support_get_pin_code_history_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PageSupportGetPinCodeHistoryResponse
    extends PageSupportGetPinCodeHistoryResponse {
  @override
  final BuiltList<GetPinCodeHistoryResponse>? content;
  @override
  final int? pageNumber;
  @override
  final int? pageSize;
  @override
  final int? totalElements;
  @override
  final bool? last;
  @override
  final bool? first;
  @override
  final int? totalPages;

  factory _$PageSupportGetPinCodeHistoryResponse(
          [void Function(PageSupportGetPinCodeHistoryResponseBuilder)?
              updates]) =>
      (PageSupportGetPinCodeHistoryResponseBuilder()..update(updates))._build();

  _$PageSupportGetPinCodeHistoryResponse._(
      {this.content,
      this.pageNumber,
      this.pageSize,
      this.totalElements,
      this.last,
      this.first,
      this.totalPages})
      : super._();
  @override
  PageSupportGetPinCodeHistoryResponse rebuild(
          void Function(PageSupportGetPinCodeHistoryResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PageSupportGetPinCodeHistoryResponseBuilder toBuilder() =>
      PageSupportGetPinCodeHistoryResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PageSupportGetPinCodeHistoryResponse &&
        content == other.content &&
        pageNumber == other.pageNumber &&
        pageSize == other.pageSize &&
        totalElements == other.totalElements &&
        last == other.last &&
        first == other.first &&
        totalPages == other.totalPages;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, pageNumber.hashCode);
    _$hash = $jc(_$hash, pageSize.hashCode);
    _$hash = $jc(_$hash, totalElements.hashCode);
    _$hash = $jc(_$hash, last.hashCode);
    _$hash = $jc(_$hash, first.hashCode);
    _$hash = $jc(_$hash, totalPages.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PageSupportGetPinCodeHistoryResponse')
          ..add('content', content)
          ..add('pageNumber', pageNumber)
          ..add('pageSize', pageSize)
          ..add('totalElements', totalElements)
          ..add('last', last)
          ..add('first', first)
          ..add('totalPages', totalPages))
        .toString();
  }
}

class PageSupportGetPinCodeHistoryResponseBuilder
    implements
        Builder<PageSupportGetPinCodeHistoryResponse,
            PageSupportGetPinCodeHistoryResponseBuilder> {
  _$PageSupportGetPinCodeHistoryResponse? _$v;

  ListBuilder<GetPinCodeHistoryResponse>? _content;
  ListBuilder<GetPinCodeHistoryResponse> get content =>
      _$this._content ??= ListBuilder<GetPinCodeHistoryResponse>();
  set content(ListBuilder<GetPinCodeHistoryResponse>? content) =>
      _$this._content = content;

  int? _pageNumber;
  int? get pageNumber => _$this._pageNumber;
  set pageNumber(int? pageNumber) => _$this._pageNumber = pageNumber;

  int? _pageSize;
  int? get pageSize => _$this._pageSize;
  set pageSize(int? pageSize) => _$this._pageSize = pageSize;

  int? _totalElements;
  int? get totalElements => _$this._totalElements;
  set totalElements(int? totalElements) =>
      _$this._totalElements = totalElements;

  bool? _last;
  bool? get last => _$this._last;
  set last(bool? last) => _$this._last = last;

  bool? _first;
  bool? get first => _$this._first;
  set first(bool? first) => _$this._first = first;

  int? _totalPages;
  int? get totalPages => _$this._totalPages;
  set totalPages(int? totalPages) => _$this._totalPages = totalPages;

  PageSupportGetPinCodeHistoryResponseBuilder() {
    PageSupportGetPinCodeHistoryResponse._defaults(this);
  }

  PageSupportGetPinCodeHistoryResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _content = $v.content?.toBuilder();
      _pageNumber = $v.pageNumber;
      _pageSize = $v.pageSize;
      _totalElements = $v.totalElements;
      _last = $v.last;
      _first = $v.first;
      _totalPages = $v.totalPages;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PageSupportGetPinCodeHistoryResponse other) {
    _$v = other as _$PageSupportGetPinCodeHistoryResponse;
  }

  @override
  void update(
      void Function(PageSupportGetPinCodeHistoryResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PageSupportGetPinCodeHistoryResponse build() => _build();

  _$PageSupportGetPinCodeHistoryResponse _build() {
    _$PageSupportGetPinCodeHistoryResponse _$result;
    try {
      _$result = _$v ??
          _$PageSupportGetPinCodeHistoryResponse._(
            content: _content?.build(),
            pageNumber: pageNumber,
            pageSize: pageSize,
            totalElements: totalElements,
            last: last,
            first: first,
            totalPages: totalPages,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'content';
        _content?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PageSupportGetPinCodeHistoryResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
