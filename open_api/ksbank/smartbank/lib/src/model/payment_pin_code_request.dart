//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'payment_pin_code_request.g.dart';

/// PaymentPinCodeRequest
///
/// Properties:
/// * [transactionNo] - Mã giao dịch, được trả về khi gọi api review hoặc check limt
/// * [accountNo] - Số tài khoản thực hiện thanh toán
/// * [productCode] - <PERSON><PERSON> sản phẩm cần thanh toán
/// * [amount] - Số tiền thực hiện thanh toán
/// * [cardValue] - Mệnh giá.
/// * [quantity] - Số lượng
/// * [pinCodeType] - Pin Code Type
/// * [pinCodeName] - Tên của pin code (Dành cho mục đích ghi log)
/// * [pinCodeDesc] - <PERSON>ô tả pin code (Dành cho mục đích ghi log)
/// * [supplierName] - Tên nhà cung cấp (Dành cho mục đích ghi log)
/// * [supplierUrl] - Icon nhà mạng (Dành cho mục đích ghi log)
/// * [softOtp]
@BuiltValue()
abstract class PaymentPinCodeRequest
    implements Built<PaymentPinCodeRequest, PaymentPinCodeRequestBuilder> {
  /// Mã giao dịch, được trả về khi gọi api review hoặc check limt
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số tài khoản thực hiện thanh toán
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  /// Mã sản phẩm cần thanh toán
  @BuiltValueField(wireName: r'productCode')
  String? get productCode;

  /// Số tiền thực hiện thanh toán
  @BuiltValueField(wireName: r'amount')
  num? get amount;

  /// Mệnh giá.
  @BuiltValueField(wireName: r'cardValue')
  num? get cardValue;

  /// Số lượng
  @BuiltValueField(wireName: r'quantity')
  int? get quantity;

  /// Pin Code Type
  @BuiltValueField(wireName: r'pinCodeType')
  PaymentPinCodeRequestPinCodeTypeEnum? get pinCodeType;
  // enum pinCodeTypeEnum {  TOPUP_PHONE,  TOPUP_DATA,  PINCODE_PHONE,  PINCODE_DATA,  PINCODE_GAME,  PAY_BILL,  };

  /// Tên của pin code (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'pinCodeName')
  String? get pinCodeName;

  /// Mô tả pin code (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'pinCodeDesc')
  String? get pinCodeDesc;

  /// Tên nhà cung cấp (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'supplierName')
  String? get supplierName;

  /// Icon nhà mạng (Dành cho mục đích ghi log)
  @BuiltValueField(wireName: r'supplierUrl')
  String? get supplierUrl;

  @BuiltValueField(wireName: r'softOtp')
  VerifySoftOtpRequest? get softOtp;

  PaymentPinCodeRequest._();

  factory PaymentPinCodeRequest(
      [void updates(PaymentPinCodeRequestBuilder b)]) = _$PaymentPinCodeRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PaymentPinCodeRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PaymentPinCodeRequest> get serializer =>
      _$PaymentPinCodeRequestSerializer();
}

class _$PaymentPinCodeRequestSerializer
    implements PrimitiveSerializer<PaymentPinCodeRequest> {
  @override
  final Iterable<Type> types = const [
    PaymentPinCodeRequest,
    _$PaymentPinCodeRequest
  ];

  @override
  final String wireName = r'PaymentPinCodeRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PaymentPinCodeRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.productCode != null) {
      yield r'productCode';
      yield serializers.serialize(
        object.productCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.cardValue != null) {
      yield r'cardValue';
      yield serializers.serialize(
        object.cardValue,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.quantity != null) {
      yield r'quantity';
      yield serializers.serialize(
        object.quantity,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.pinCodeType != null) {
      yield r'pinCodeType';
      yield serializers.serialize(
        object.pinCodeType,
        specifiedType:
            const FullType.nullable(PaymentPinCodeRequestPinCodeTypeEnum),
      );
    }
    if (object.pinCodeName != null) {
      yield r'pinCodeName';
      yield serializers.serialize(
        object.pinCodeName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.pinCodeDesc != null) {
      yield r'pinCodeDesc';
      yield serializers.serialize(
        object.pinCodeDesc,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supplierName != null) {
      yield r'supplierName';
      yield serializers.serialize(
        object.supplierName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.supplierUrl != null) {
      yield r'supplierUrl';
      yield serializers.serialize(
        object.supplierUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.softOtp != null) {
      yield r'softOtp';
      yield serializers.serialize(
        object.softOtp,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PaymentPinCodeRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PaymentPinCodeRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        case r'productCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.productCode = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'cardValue':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.cardValue = valueDes;
          break;
        case r'quantity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.quantity = valueDes;
          break;
        case r'pinCodeType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(PaymentPinCodeRequestPinCodeTypeEnum),
          ) as PaymentPinCodeRequestPinCodeTypeEnum?;
          if (valueDes == null) continue;
          result.pinCodeType = valueDes;
          break;
        case r'pinCodeName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.pinCodeName = valueDes;
          break;
        case r'pinCodeDesc':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.pinCodeDesc = valueDes;
          break;
        case r'supplierName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supplierName = valueDes;
          break;
        case r'supplierUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.supplierUrl = valueDes;
          break;
        case r'softOtp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.softOtp.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PaymentPinCodeRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PaymentPinCodeRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PaymentPinCodeRequestPinCodeTypeEnum extends EnumClass {
  /// Pin Code Type
  @BuiltValueEnumConst(wireName: r'TOPUP_PHONE')
  static const PaymentPinCodeRequestPinCodeTypeEnum TOPUP_PHONE =
      _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_PHONE;

  /// Pin Code Type
  @BuiltValueEnumConst(wireName: r'TOPUP_DATA')
  static const PaymentPinCodeRequestPinCodeTypeEnum TOPUP_DATA =
      _$paymentPinCodeRequestPinCodeTypeEnum_TOPUP_DATA;

  /// Pin Code Type
  @BuiltValueEnumConst(wireName: r'PINCODE_PHONE')
  static const PaymentPinCodeRequestPinCodeTypeEnum PINCODE_PHONE =
      _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_PHONE;

  /// Pin Code Type
  @BuiltValueEnumConst(wireName: r'PINCODE_DATA')
  static const PaymentPinCodeRequestPinCodeTypeEnum PINCODE_DATA =
      _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_DATA;

  /// Pin Code Type
  @BuiltValueEnumConst(wireName: r'PINCODE_GAME')
  static const PaymentPinCodeRequestPinCodeTypeEnum PINCODE_GAME =
      _$paymentPinCodeRequestPinCodeTypeEnum_PINCODE_GAME;

  /// Pin Code Type
  @BuiltValueEnumConst(wireName: r'PAY_BILL')
  static const PaymentPinCodeRequestPinCodeTypeEnum PAY_BILL =
      _$paymentPinCodeRequestPinCodeTypeEnum_PAY_BILL;

  static Serializer<PaymentPinCodeRequestPinCodeTypeEnum> get serializer =>
      _$paymentPinCodeRequestPinCodeTypeEnumSerializer;

  const PaymentPinCodeRequestPinCodeTypeEnum._(String name) : super(name);

  static BuiltSet<PaymentPinCodeRequestPinCodeTypeEnum> get values =>
      _$paymentPinCodeRequestPinCodeTypeEnumValues;
  static PaymentPinCodeRequestPinCodeTypeEnum valueOf(String name) =>
      _$paymentPinCodeRequestPinCodeTypeEnumValueOf(name);
}
