// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'registered_loan_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RegisteredLoanDetailResponse extends RegisteredLoanDetailResponse {
  @override
  final String? id;
  @override
  final String? profileCode;
  @override
  final String? cifNo;
  @override
  final String? loanProfileId;
  @override
  final String? purposeName;
  @override
  final int? loanMonth;
  @override
  final num? loanAmount;
  @override
  final String? city;
  @override
  final String? district;
  @override
  final BuiltList<String>? loanCollateralImageUrls;
  @override
  final DateTime? registerDate;

  factory _$RegisteredLoanDetailResponse(
          [void Function(RegisteredLoanDetailResponseBuilder)? updates]) =>
      (RegisteredLoanDetailResponseBuilder()..update(updates))._build();

  _$RegisteredLoanDetailResponse._(
      {this.id,
      this.profileCode,
      this.cifNo,
      this.loanProfileId,
      this.purposeName,
      this.loanMonth,
      this.loanAmount,
      this.city,
      this.district,
      this.loanCollateralImageUrls,
      this.registerDate})
      : super._();
  @override
  RegisteredLoanDetailResponse rebuild(
          void Function(RegisteredLoanDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RegisteredLoanDetailResponseBuilder toBuilder() =>
      RegisteredLoanDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RegisteredLoanDetailResponse &&
        id == other.id &&
        profileCode == other.profileCode &&
        cifNo == other.cifNo &&
        loanProfileId == other.loanProfileId &&
        purposeName == other.purposeName &&
        loanMonth == other.loanMonth &&
        loanAmount == other.loanAmount &&
        city == other.city &&
        district == other.district &&
        loanCollateralImageUrls == other.loanCollateralImageUrls &&
        registerDate == other.registerDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, profileCode.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, loanProfileId.hashCode);
    _$hash = $jc(_$hash, purposeName.hashCode);
    _$hash = $jc(_$hash, loanMonth.hashCode);
    _$hash = $jc(_$hash, loanAmount.hashCode);
    _$hash = $jc(_$hash, city.hashCode);
    _$hash = $jc(_$hash, district.hashCode);
    _$hash = $jc(_$hash, loanCollateralImageUrls.hashCode);
    _$hash = $jc(_$hash, registerDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RegisteredLoanDetailResponse')
          ..add('id', id)
          ..add('profileCode', profileCode)
          ..add('cifNo', cifNo)
          ..add('loanProfileId', loanProfileId)
          ..add('purposeName', purposeName)
          ..add('loanMonth', loanMonth)
          ..add('loanAmount', loanAmount)
          ..add('city', city)
          ..add('district', district)
          ..add('loanCollateralImageUrls', loanCollateralImageUrls)
          ..add('registerDate', registerDate))
        .toString();
  }
}

class RegisteredLoanDetailResponseBuilder
    implements
        Builder<RegisteredLoanDetailResponse,
            RegisteredLoanDetailResponseBuilder> {
  _$RegisteredLoanDetailResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _profileCode;
  String? get profileCode => _$this._profileCode;
  set profileCode(String? profileCode) => _$this._profileCode = profileCode;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _loanProfileId;
  String? get loanProfileId => _$this._loanProfileId;
  set loanProfileId(String? loanProfileId) =>
      _$this._loanProfileId = loanProfileId;

  String? _purposeName;
  String? get purposeName => _$this._purposeName;
  set purposeName(String? purposeName) => _$this._purposeName = purposeName;

  int? _loanMonth;
  int? get loanMonth => _$this._loanMonth;
  set loanMonth(int? loanMonth) => _$this._loanMonth = loanMonth;

  num? _loanAmount;
  num? get loanAmount => _$this._loanAmount;
  set loanAmount(num? loanAmount) => _$this._loanAmount = loanAmount;

  String? _city;
  String? get city => _$this._city;
  set city(String? city) => _$this._city = city;

  String? _district;
  String? get district => _$this._district;
  set district(String? district) => _$this._district = district;

  ListBuilder<String>? _loanCollateralImageUrls;
  ListBuilder<String> get loanCollateralImageUrls =>
      _$this._loanCollateralImageUrls ??= ListBuilder<String>();
  set loanCollateralImageUrls(ListBuilder<String>? loanCollateralImageUrls) =>
      _$this._loanCollateralImageUrls = loanCollateralImageUrls;

  DateTime? _registerDate;
  DateTime? get registerDate => _$this._registerDate;
  set registerDate(DateTime? registerDate) =>
      _$this._registerDate = registerDate;

  RegisteredLoanDetailResponseBuilder() {
    RegisteredLoanDetailResponse._defaults(this);
  }

  RegisteredLoanDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _profileCode = $v.profileCode;
      _cifNo = $v.cifNo;
      _loanProfileId = $v.loanProfileId;
      _purposeName = $v.purposeName;
      _loanMonth = $v.loanMonth;
      _loanAmount = $v.loanAmount;
      _city = $v.city;
      _district = $v.district;
      _loanCollateralImageUrls = $v.loanCollateralImageUrls?.toBuilder();
      _registerDate = $v.registerDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RegisteredLoanDetailResponse other) {
    _$v = other as _$RegisteredLoanDetailResponse;
  }

  @override
  void update(void Function(RegisteredLoanDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RegisteredLoanDetailResponse build() => _build();

  _$RegisteredLoanDetailResponse _build() {
    _$RegisteredLoanDetailResponse _$result;
    try {
      _$result = _$v ??
          _$RegisteredLoanDetailResponse._(
            id: id,
            profileCode: profileCode,
            cifNo: cifNo,
            loanProfileId: loanProfileId,
            purposeName: purposeName,
            loanMonth: loanMonth,
            loanAmount: loanAmount,
            city: city,
            district: district,
            loanCollateralImageUrls: _loanCollateralImageUrls?.build(),
            registerDate: registerDate,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'loanCollateralImageUrls';
        _loanCollateralImageUrls?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'RegisteredLoanDetailResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
