// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provider_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ProviderDto extends ProviderDto {
  @override
  final String? providerCode;
  @override
  final String? providerName;

  factory _$ProviderDto([void Function(ProviderDtoBuilder)? updates]) =>
      (ProviderDtoBuilder()..update(updates))._build();

  _$ProviderDto._({this.providerCode, this.providerName}) : super._();
  @override
  ProviderDto rebuild(void Function(ProviderDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ProviderDtoBuilder toBuilder() => ProviderDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ProviderDto &&
        providerCode == other.providerCode &&
        providerName == other.providerName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, providerCode.hashCode);
    _$hash = $jc(_$hash, providerName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ProviderDto')
          ..add('providerCode', providerCode)
          ..add('providerName', providerName))
        .toString();
  }
}

class ProviderDtoBuilder implements Builder<ProviderDto, ProviderDtoBuilder> {
  _$ProviderDto? _$v;

  String? _providerCode;
  String? get providerCode => _$this._providerCode;
  set providerCode(String? providerCode) => _$this._providerCode = providerCode;

  String? _providerName;
  String? get providerName => _$this._providerName;
  set providerName(String? providerName) => _$this._providerName = providerName;

  ProviderDtoBuilder() {
    ProviderDto._defaults(this);
  }

  ProviderDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _providerCode = $v.providerCode;
      _providerName = $v.providerName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ProviderDto other) {
    _$v = other as _$ProviderDto;
  }

  @override
  void update(void Function(ProviderDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ProviderDto build() => _build();

  _$ProviderDto _build() {
    final _$result = _$v ??
        _$ProviderDto._(
          providerCode: providerCode,
          providerName: providerName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
