// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lock_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LockCardRequest extends LockCardRequest {
  @override
  final String? cifNo;
  @override
  final String? cardNo;
  @override
  final String? productTypeCode;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;
  @override
  final String? refCardId;

  factory _$LockCardRequest([void Function(LockCardRequestBuilder)? updates]) =>
      (LockCardRequestBuilder()..update(updates))._build();

  _$LockCardRequest._(
      {this.cifNo,
      this.cardNo,
      this.productTypeCode,
      this.verifySoftOtp,
      this.refCardId})
      : super._();
  @override
  LockCardRequest rebuild(void Function(LockCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LockCardRequestBuilder toBuilder() => LockCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LockCardRequest &&
        cifNo == other.cifNo &&
        cardNo == other.cardNo &&
        productTypeCode == other.productTypeCode &&
        verifySoftOtp == other.verifySoftOtp &&
        refCardId == other.refCardId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, cardNo.hashCode);
    _$hash = $jc(_$hash, productTypeCode.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jc(_$hash, refCardId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LockCardRequest')
          ..add('cifNo', cifNo)
          ..add('cardNo', cardNo)
          ..add('productTypeCode', productTypeCode)
          ..add('verifySoftOtp', verifySoftOtp)
          ..add('refCardId', refCardId))
        .toString();
  }
}

class LockCardRequestBuilder
    implements Builder<LockCardRequest, LockCardRequestBuilder> {
  _$LockCardRequest? _$v;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _cardNo;
  String? get cardNo => _$this._cardNo;
  set cardNo(String? cardNo) => _$this._cardNo = cardNo;

  String? _productTypeCode;
  String? get productTypeCode => _$this._productTypeCode;
  set productTypeCode(String? productTypeCode) =>
      _$this._productTypeCode = productTypeCode;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  String? _refCardId;
  String? get refCardId => _$this._refCardId;
  set refCardId(String? refCardId) => _$this._refCardId = refCardId;

  LockCardRequestBuilder() {
    LockCardRequest._defaults(this);
  }

  LockCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNo = $v.cifNo;
      _cardNo = $v.cardNo;
      _productTypeCode = $v.productTypeCode;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _refCardId = $v.refCardId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LockCardRequest other) {
    _$v = other as _$LockCardRequest;
  }

  @override
  void update(void Function(LockCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LockCardRequest build() => _build();

  _$LockCardRequest _build() {
    _$LockCardRequest _$result;
    try {
      _$result = _$v ??
          _$LockCardRequest._(
            cifNo: cifNo,
            cardNo: cardNo,
            productTypeCode: productTypeCode,
            verifySoftOtp: _verifySoftOtp?.build(),
            refCardId: refCardId,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'LockCardRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
