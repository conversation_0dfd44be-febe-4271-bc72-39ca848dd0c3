// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_loan_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RegisterLoanRequest extends RegisterLoanRequest {
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final int? purposeId;
  @override
  final num? amount;
  @override
  final int? period;
  @override
  final String? collateral;
  @override
  final BuiltList<String>? collateralImageUrls;
  @override
  final String? city;
  @override
  final String? district;
  @override
  final String? relationship;
  @override
  final String? residence;
  @override
  final String? income;
  @override
  final num? anotherLoanAmount;

  factory _$RegisterLoanRequest(
          [void Function(RegisterLoanRequestBuilder)? updates]) =>
      (RegisterLoanRequestBuilder()..update(updates))._build();

  _$RegisterLoanRequest._(
      {this.softOtp,
      this.purposeId,
      this.amount,
      this.period,
      this.collateral,
      this.collateralImageUrls,
      this.city,
      this.district,
      this.relationship,
      this.residence,
      this.income,
      this.anotherLoanAmount})
      : super._();
  @override
  RegisterLoanRequest rebuild(
          void Function(RegisterLoanRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RegisterLoanRequestBuilder toBuilder() =>
      RegisterLoanRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RegisterLoanRequest &&
        softOtp == other.softOtp &&
        purposeId == other.purposeId &&
        amount == other.amount &&
        period == other.period &&
        collateral == other.collateral &&
        collateralImageUrls == other.collateralImageUrls &&
        city == other.city &&
        district == other.district &&
        relationship == other.relationship &&
        residence == other.residence &&
        income == other.income &&
        anotherLoanAmount == other.anotherLoanAmount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, purposeId.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, period.hashCode);
    _$hash = $jc(_$hash, collateral.hashCode);
    _$hash = $jc(_$hash, collateralImageUrls.hashCode);
    _$hash = $jc(_$hash, city.hashCode);
    _$hash = $jc(_$hash, district.hashCode);
    _$hash = $jc(_$hash, relationship.hashCode);
    _$hash = $jc(_$hash, residence.hashCode);
    _$hash = $jc(_$hash, income.hashCode);
    _$hash = $jc(_$hash, anotherLoanAmount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RegisterLoanRequest')
          ..add('softOtp', softOtp)
          ..add('purposeId', purposeId)
          ..add('amount', amount)
          ..add('period', period)
          ..add('collateral', collateral)
          ..add('collateralImageUrls', collateralImageUrls)
          ..add('city', city)
          ..add('district', district)
          ..add('relationship', relationship)
          ..add('residence', residence)
          ..add('income', income)
          ..add('anotherLoanAmount', anotherLoanAmount))
        .toString();
  }
}

class RegisterLoanRequestBuilder
    implements Builder<RegisterLoanRequest, RegisterLoanRequestBuilder> {
  _$RegisterLoanRequest? _$v;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  int? _purposeId;
  int? get purposeId => _$this._purposeId;
  set purposeId(int? purposeId) => _$this._purposeId = purposeId;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  int? _period;
  int? get period => _$this._period;
  set period(int? period) => _$this._period = period;

  String? _collateral;
  String? get collateral => _$this._collateral;
  set collateral(String? collateral) => _$this._collateral = collateral;

  ListBuilder<String>? _collateralImageUrls;
  ListBuilder<String> get collateralImageUrls =>
      _$this._collateralImageUrls ??= ListBuilder<String>();
  set collateralImageUrls(ListBuilder<String>? collateralImageUrls) =>
      _$this._collateralImageUrls = collateralImageUrls;

  String? _city;
  String? get city => _$this._city;
  set city(String? city) => _$this._city = city;

  String? _district;
  String? get district => _$this._district;
  set district(String? district) => _$this._district = district;

  String? _relationship;
  String? get relationship => _$this._relationship;
  set relationship(String? relationship) => _$this._relationship = relationship;

  String? _residence;
  String? get residence => _$this._residence;
  set residence(String? residence) => _$this._residence = residence;

  String? _income;
  String? get income => _$this._income;
  set income(String? income) => _$this._income = income;

  num? _anotherLoanAmount;
  num? get anotherLoanAmount => _$this._anotherLoanAmount;
  set anotherLoanAmount(num? anotherLoanAmount) =>
      _$this._anotherLoanAmount = anotherLoanAmount;

  RegisterLoanRequestBuilder() {
    RegisterLoanRequest._defaults(this);
  }

  RegisterLoanRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _softOtp = $v.softOtp?.toBuilder();
      _purposeId = $v.purposeId;
      _amount = $v.amount;
      _period = $v.period;
      _collateral = $v.collateral;
      _collateralImageUrls = $v.collateralImageUrls?.toBuilder();
      _city = $v.city;
      _district = $v.district;
      _relationship = $v.relationship;
      _residence = $v.residence;
      _income = $v.income;
      _anotherLoanAmount = $v.anotherLoanAmount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RegisterLoanRequest other) {
    _$v = other as _$RegisterLoanRequest;
  }

  @override
  void update(void Function(RegisterLoanRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RegisterLoanRequest build() => _build();

  _$RegisterLoanRequest _build() {
    _$RegisterLoanRequest _$result;
    try {
      _$result = _$v ??
          _$RegisterLoanRequest._(
            softOtp: _softOtp?.build(),
            purposeId: purposeId,
            amount: amount,
            period: period,
            collateral: collateral,
            collateralImageUrls: _collateralImageUrls?.build(),
            city: city,
            district: district,
            relationship: relationship,
            residence: residence,
            income: income,
            anotherLoanAmount: anotherLoanAmount,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();

        _$failedField = 'collateralImageUrls';
        _collateralImageUrls?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'RegisterLoanRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
