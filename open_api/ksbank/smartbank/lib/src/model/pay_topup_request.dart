//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'pay_topup_request.g.dart';

/// PayTopupRequest
///
/// Properties:
/// * [transactionNo] - Mã giao dịch, được trả về sau khi gọi verify face.
/// * [phoneNo] - Số điện thoại
/// * [provider] - Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
/// * [amount] - Số tiền cần thanh toán
/// * [cardValue] - Mệnh giá thẻ
/// * [cardValueCode] - <PERSON><PERSON> nhà cung cấp (Mobile Phone)
/// * [cardValueName] - Tên mô tả hạn mức thẻ
/// * [bankCif] - Bankinf: Mã cif của khách hàng thuộc 1 ngân hàng cụ thể
/// * [accountNo] - Bankinf: Số tài khoản ăn theo mã cif
/// * [softOtp]
/// * [topupType]
@BuiltValue()
abstract class PayTopupRequest
    implements Built<PayTopupRequest, PayTopupRequestBuilder> {
  /// Mã giao dịch, được trả về sau khi gọi verify face.
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số điện thoại
  @BuiltValueField(wireName: r'phoneNo')
  String? get phoneNo;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueField(wireName: r'provider')
  PayTopupRequestProviderEnum? get provider;
  // enum providerEnum {  MOBIFONE,  VINAPHONE,  VIETTEL,  VIETNAMOBILE,  GMOBILE,  };

  /// Số tiền cần thanh toán
  @BuiltValueField(wireName: r'amount')
  num? get amount;

  /// Mệnh giá thẻ
  @BuiltValueField(wireName: r'cardValue')
  num? get cardValue;

  /// Mã nhà cung cấp (Mobile Phone)
  @BuiltValueField(wireName: r'cardValueCode')
  String? get cardValueCode;

  /// Tên mô tả hạn mức thẻ
  @BuiltValueField(wireName: r'cardValueName')
  String? get cardValueName;

  /// Bankinf: Mã cif của khách hàng thuộc 1 ngân hàng cụ thể
  @BuiltValueField(wireName: r'bankCif')
  String? get bankCif;

  /// Bankinf: Số tài khoản ăn theo mã cif
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  @BuiltValueField(wireName: r'softOtp')
  VerifySoftOtpRequest? get softOtp;

  @BuiltValueField(wireName: r'topupType')
  PayTopupRequestTopupTypeEnum? get topupType;
  // enum topupTypeEnum {  PREPAID,  POST_PAYMENT,  };

  PayTopupRequest._();

  factory PayTopupRequest([void updates(PayTopupRequestBuilder b)]) =
      _$PayTopupRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PayTopupRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PayTopupRequest> get serializer =>
      _$PayTopupRequestSerializer();
}

class _$PayTopupRequestSerializer
    implements PrimitiveSerializer<PayTopupRequest> {
  @override
  final Iterable<Type> types = const [PayTopupRequest, _$PayTopupRequest];

  @override
  final String wireName = r'PayTopupRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PayTopupRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.phoneNo != null) {
      yield r'phoneNo';
      yield serializers.serialize(
        object.phoneNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.provider != null) {
      yield r'provider';
      yield serializers.serialize(
        object.provider,
        specifiedType: const FullType.nullable(PayTopupRequestProviderEnum),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.cardValue != null) {
      yield r'cardValue';
      yield serializers.serialize(
        object.cardValue,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.cardValueCode != null) {
      yield r'cardValueCode';
      yield serializers.serialize(
        object.cardValueCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cardValueName != null) {
      yield r'cardValueName';
      yield serializers.serialize(
        object.cardValueName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.bankCif != null) {
      yield r'bankCif';
      yield serializers.serialize(
        object.bankCif,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.softOtp != null) {
      yield r'softOtp';
      yield serializers.serialize(
        object.softOtp,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
    if (object.topupType != null) {
      yield r'topupType';
      yield serializers.serialize(
        object.topupType,
        specifiedType: const FullType.nullable(PayTopupRequestTopupTypeEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PayTopupRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PayTopupRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'phoneNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNo = valueDes;
          break;
        case r'provider':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(PayTopupRequestProviderEnum),
          ) as PayTopupRequestProviderEnum?;
          if (valueDes == null) continue;
          result.provider = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'cardValue':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.cardValue = valueDes;
          break;
        case r'cardValueCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardValueCode = valueDes;
          break;
        case r'cardValueName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.cardValueName = valueDes;
          break;
        case r'bankCif':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCif = valueDes;
          break;
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        case r'softOtp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.softOtp.replace(valueDes);
          break;
        case r'topupType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(PayTopupRequestTopupTypeEnum),
          ) as PayTopupRequestTopupTypeEnum?;
          if (valueDes == null) continue;
          result.topupType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PayTopupRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PayTopupRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PayTopupRequestProviderEnum extends EnumClass {
  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'MOBIFONE')
  static const PayTopupRequestProviderEnum MOBIFONE =
      _$payTopupRequestProviderEnum_MOBIFONE;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'VINAPHONE')
  static const PayTopupRequestProviderEnum VINAPHONE =
      _$payTopupRequestProviderEnum_VINAPHONE;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'VIETTEL')
  static const PayTopupRequestProviderEnum VIETTEL =
      _$payTopupRequestProviderEnum_VIETTEL;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'VIETNAMOBILE')
  static const PayTopupRequestProviderEnum VIETNAMOBILE =
      _$payTopupRequestProviderEnum_VIETNAMOBILE;

  /// Nhà mạng tương nộp (Không có thì sẽ mặc định lấy theo đầu số)
  @BuiltValueEnumConst(wireName: r'GMOBILE')
  static const PayTopupRequestProviderEnum GMOBILE =
      _$payTopupRequestProviderEnum_GMOBILE;

  static Serializer<PayTopupRequestProviderEnum> get serializer =>
      _$payTopupRequestProviderEnumSerializer;

  const PayTopupRequestProviderEnum._(String name) : super(name);

  static BuiltSet<PayTopupRequestProviderEnum> get values =>
      _$payTopupRequestProviderEnumValues;
  static PayTopupRequestProviderEnum valueOf(String name) =>
      _$payTopupRequestProviderEnumValueOf(name);
}

class PayTopupRequestTopupTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PREPAID')
  static const PayTopupRequestTopupTypeEnum PREPAID =
      _$payTopupRequestTopupTypeEnum_PREPAID;
  @BuiltValueEnumConst(wireName: r'POST_PAYMENT')
  static const PayTopupRequestTopupTypeEnum POST_PAYMENT =
      _$payTopupRequestTopupTypeEnum_POST_PAYMENT;

  static Serializer<PayTopupRequestTopupTypeEnum> get serializer =>
      _$payTopupRequestTopupTypeEnumSerializer;

  const PayTopupRequestTopupTypeEnum._(String name) : super(name);

  static BuiltSet<PayTopupRequestTopupTypeEnum> get values =>
      _$payTopupRequestTopupTypeEnumValues;
  static PayTopupRequestTopupTypeEnum valueOf(String name) =>
      _$payTopupRequestTopupTypeEnumValueOf(name);
}
