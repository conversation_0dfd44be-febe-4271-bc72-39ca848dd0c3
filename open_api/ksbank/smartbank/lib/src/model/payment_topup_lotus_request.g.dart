// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_topup_lotus_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PaymentTopupLotusRequestTelecomProviderEnum
    _$paymentTopupLotusRequestTelecomProviderEnum_MOBIFONE =
    const PaymentTopupLotusRequestTelecomProviderEnum._('MOBIFONE');
const PaymentTopupLotusRequestTelecomProviderEnum
    _$paymentTopupLotusRequestTelecomProviderEnum_VINAPHONE =
    const PaymentTopupLotusRequestTelecomProviderEnum._('VINAPHONE');
const PaymentTopupLotusRequestTelecomProviderEnum
    _$paymentTopupLotusRequestTelecomProviderEnum_VIETTEL =
    const PaymentTopupLotusRequestTelecomProviderEnum._('VIETTEL');
const PaymentTopupLotusRequestTelecomProviderEnum
    _$paymentTopupLotusRequestTelecomProviderEnum_VIETNAMOBILE =
    const PaymentTopupLotusRequestTelecomProviderEnum._('VIETNAMOBILE');
const PaymentTopupLotusRequestTelecomProviderEnum
    _$paymentTopupLotusRequestTelecomProviderEnum_GMOBILE =
    const PaymentTopupLotusRequestTelecomProviderEnum._('GMOBILE');

PaymentTopupLotusRequestTelecomProviderEnum
    _$paymentTopupLotusRequestTelecomProviderEnumValueOf(String name) {
  switch (name) {
    case 'MOBIFONE':
      return _$paymentTopupLotusRequestTelecomProviderEnum_MOBIFONE;
    case 'VINAPHONE':
      return _$paymentTopupLotusRequestTelecomProviderEnum_VINAPHONE;
    case 'VIETTEL':
      return _$paymentTopupLotusRequestTelecomProviderEnum_VIETTEL;
    case 'VIETNAMOBILE':
      return _$paymentTopupLotusRequestTelecomProviderEnum_VIETNAMOBILE;
    case 'GMOBILE':
      return _$paymentTopupLotusRequestTelecomProviderEnum_GMOBILE;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PaymentTopupLotusRequestTelecomProviderEnum>
    _$paymentTopupLotusRequestTelecomProviderEnumValues = BuiltSet<
        PaymentTopupLotusRequestTelecomProviderEnum>(const <PaymentTopupLotusRequestTelecomProviderEnum>[
  _$paymentTopupLotusRequestTelecomProviderEnum_MOBIFONE,
  _$paymentTopupLotusRequestTelecomProviderEnum_VINAPHONE,
  _$paymentTopupLotusRequestTelecomProviderEnum_VIETTEL,
  _$paymentTopupLotusRequestTelecomProviderEnum_VIETNAMOBILE,
  _$paymentTopupLotusRequestTelecomProviderEnum_GMOBILE,
]);

const PaymentTopupLotusRequestTopupTypeEnum
    _$paymentTopupLotusRequestTopupTypeEnum_PREPAID =
    const PaymentTopupLotusRequestTopupTypeEnum._('PREPAID');
const PaymentTopupLotusRequestTopupTypeEnum
    _$paymentTopupLotusRequestTopupTypeEnum_POST_PAYMENT =
    const PaymentTopupLotusRequestTopupTypeEnum._('POST_PAYMENT');

PaymentTopupLotusRequestTopupTypeEnum
    _$paymentTopupLotusRequestTopupTypeEnumValueOf(String name) {
  switch (name) {
    case 'PREPAID':
      return _$paymentTopupLotusRequestTopupTypeEnum_PREPAID;
    case 'POST_PAYMENT':
      return _$paymentTopupLotusRequestTopupTypeEnum_POST_PAYMENT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PaymentTopupLotusRequestTopupTypeEnum>
    _$paymentTopupLotusRequestTopupTypeEnumValues = BuiltSet<
        PaymentTopupLotusRequestTopupTypeEnum>(const <PaymentTopupLotusRequestTopupTypeEnum>[
  _$paymentTopupLotusRequestTopupTypeEnum_PREPAID,
  _$paymentTopupLotusRequestTopupTypeEnum_POST_PAYMENT,
]);

Serializer<PaymentTopupLotusRequestTelecomProviderEnum>
    _$paymentTopupLotusRequestTelecomProviderEnumSerializer =
    _$PaymentTopupLotusRequestTelecomProviderEnumSerializer();
Serializer<PaymentTopupLotusRequestTopupTypeEnum>
    _$paymentTopupLotusRequestTopupTypeEnumSerializer =
    _$PaymentTopupLotusRequestTopupTypeEnumSerializer();

class _$PaymentTopupLotusRequestTelecomProviderEnumSerializer
    implements
        PrimitiveSerializer<PaymentTopupLotusRequestTelecomProviderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };

  @override
  final Iterable<Type> types = const <Type>[
    PaymentTopupLotusRequestTelecomProviderEnum
  ];
  @override
  final String wireName = 'PaymentTopupLotusRequestTelecomProviderEnum';

  @override
  Object serialize(Serializers serializers,
          PaymentTopupLotusRequestTelecomProviderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PaymentTopupLotusRequestTelecomProviderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PaymentTopupLotusRequestTelecomProviderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PaymentTopupLotusRequestTopupTypeEnumSerializer
    implements PrimitiveSerializer<PaymentTopupLotusRequestTopupTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PREPAID': 'PREPAID',
    'POST_PAYMENT': 'POST_PAYMENT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PREPAID': 'PREPAID',
    'POST_PAYMENT': 'POST_PAYMENT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    PaymentTopupLotusRequestTopupTypeEnum
  ];
  @override
  final String wireName = 'PaymentTopupLotusRequestTopupTypeEnum';

  @override
  Object serialize(
          Serializers serializers, PaymentTopupLotusRequestTopupTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PaymentTopupLotusRequestTopupTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PaymentTopupLotusRequestTopupTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PaymentTopupLotusRequest extends PaymentTopupLotusRequest {
  @override
  final String? phoneNumber;
  @override
  final PaymentTopupLotusRequestTelecomProviderEnum? telecomProvider;
  @override
  final num? amount;
  @override
  final num? cardValue;
  @override
  final String? cardValueCode;
  @override
  final String? cardValueName;
  @override
  final PaymentTopupLotusRequestTopupTypeEnum? topupType;
  @override
  final String? cifNo;
  @override
  final String? accountNo;

  factory _$PaymentTopupLotusRequest(
          [void Function(PaymentTopupLotusRequestBuilder)? updates]) =>
      (PaymentTopupLotusRequestBuilder()..update(updates))._build();

  _$PaymentTopupLotusRequest._(
      {this.phoneNumber,
      this.telecomProvider,
      this.amount,
      this.cardValue,
      this.cardValueCode,
      this.cardValueName,
      this.topupType,
      this.cifNo,
      this.accountNo})
      : super._();
  @override
  PaymentTopupLotusRequest rebuild(
          void Function(PaymentTopupLotusRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentTopupLotusRequestBuilder toBuilder() =>
      PaymentTopupLotusRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentTopupLotusRequest &&
        phoneNumber == other.phoneNumber &&
        telecomProvider == other.telecomProvider &&
        amount == other.amount &&
        cardValue == other.cardValue &&
        cardValueCode == other.cardValueCode &&
        cardValueName == other.cardValueName &&
        topupType == other.topupType &&
        cifNo == other.cifNo &&
        accountNo == other.accountNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, telecomProvider.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jc(_$hash, cardValueCode.hashCode);
    _$hash = $jc(_$hash, cardValueName.hashCode);
    _$hash = $jc(_$hash, topupType.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentTopupLotusRequest')
          ..add('phoneNumber', phoneNumber)
          ..add('telecomProvider', telecomProvider)
          ..add('amount', amount)
          ..add('cardValue', cardValue)
          ..add('cardValueCode', cardValueCode)
          ..add('cardValueName', cardValueName)
          ..add('topupType', topupType)
          ..add('cifNo', cifNo)
          ..add('accountNo', accountNo))
        .toString();
  }
}

class PaymentTopupLotusRequestBuilder
    implements
        Builder<PaymentTopupLotusRequest, PaymentTopupLotusRequestBuilder> {
  _$PaymentTopupLotusRequest? _$v;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  PaymentTopupLotusRequestTelecomProviderEnum? _telecomProvider;
  PaymentTopupLotusRequestTelecomProviderEnum? get telecomProvider =>
      _$this._telecomProvider;
  set telecomProvider(
          PaymentTopupLotusRequestTelecomProviderEnum? telecomProvider) =>
      _$this._telecomProvider = telecomProvider;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  String? _cardValueCode;
  String? get cardValueCode => _$this._cardValueCode;
  set cardValueCode(String? cardValueCode) =>
      _$this._cardValueCode = cardValueCode;

  String? _cardValueName;
  String? get cardValueName => _$this._cardValueName;
  set cardValueName(String? cardValueName) =>
      _$this._cardValueName = cardValueName;

  PaymentTopupLotusRequestTopupTypeEnum? _topupType;
  PaymentTopupLotusRequestTopupTypeEnum? get topupType => _$this._topupType;
  set topupType(PaymentTopupLotusRequestTopupTypeEnum? topupType) =>
      _$this._topupType = topupType;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  PaymentTopupLotusRequestBuilder() {
    PaymentTopupLotusRequest._defaults(this);
  }

  PaymentTopupLotusRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _phoneNumber = $v.phoneNumber;
      _telecomProvider = $v.telecomProvider;
      _amount = $v.amount;
      _cardValue = $v.cardValue;
      _cardValueCode = $v.cardValueCode;
      _cardValueName = $v.cardValueName;
      _topupType = $v.topupType;
      _cifNo = $v.cifNo;
      _accountNo = $v.accountNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentTopupLotusRequest other) {
    _$v = other as _$PaymentTopupLotusRequest;
  }

  @override
  void update(void Function(PaymentTopupLotusRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentTopupLotusRequest build() => _build();

  _$PaymentTopupLotusRequest _build() {
    final _$result = _$v ??
        _$PaymentTopupLotusRequest._(
          phoneNumber: phoneNumber,
          telecomProvider: telecomProvider,
          amount: amount,
          cardValue: cardValue,
          cardValueCode: cardValueCode,
          cardValueName: cardValueName,
          topupType: topupType,
          cifNo: cifNo,
          accountNo: accountNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
