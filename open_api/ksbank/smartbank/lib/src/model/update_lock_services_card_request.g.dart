// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_lock_services_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateLockServicesCardRequest extends UpdateLockServicesCardRequest {
  @override
  final String? cardId;
  @override
  final CardServiceType? cardServiceType;
  @override
  final Flag? flag;

  factory _$UpdateLockServicesCardRequest(
          [void Function(UpdateLockServicesCardRequestBuilder)? updates]) =>
      (UpdateLockServicesCardRequestBuilder()..update(updates))._build();

  _$UpdateLockServicesCardRequest._(
      {this.cardId, this.cardServiceType, this.flag})
      : super._();
  @override
  UpdateLockServicesCardRequest rebuild(
          void Function(UpdateLockServicesCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateLockServicesCardRequestBuilder toBuilder() =>
      UpdateLockServicesCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateLockServicesCardRequest &&
        cardId == other.cardId &&
        cardServiceType == other.cardServiceType &&
        flag == other.flag;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, cardServiceType.hashCode);
    _$hash = $jc(_$hash, flag.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateLockServicesCardRequest')
          ..add('cardId', cardId)
          ..add('cardServiceType', cardServiceType)
          ..add('flag', flag))
        .toString();
  }
}

class UpdateLockServicesCardRequestBuilder
    implements
        Builder<UpdateLockServicesCardRequest,
            UpdateLockServicesCardRequestBuilder> {
  _$UpdateLockServicesCardRequest? _$v;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  CardServiceType? _cardServiceType;
  CardServiceType? get cardServiceType => _$this._cardServiceType;
  set cardServiceType(CardServiceType? cardServiceType) =>
      _$this._cardServiceType = cardServiceType;

  Flag? _flag;
  Flag? get flag => _$this._flag;
  set flag(Flag? flag) => _$this._flag = flag;

  UpdateLockServicesCardRequestBuilder() {
    UpdateLockServicesCardRequest._defaults(this);
  }

  UpdateLockServicesCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardId = $v.cardId;
      _cardServiceType = $v.cardServiceType;
      _flag = $v.flag;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateLockServicesCardRequest other) {
    _$v = other as _$UpdateLockServicesCardRequest;
  }

  @override
  void update(void Function(UpdateLockServicesCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateLockServicesCardRequest build() => _build();

  _$UpdateLockServicesCardRequest _build() {
    final _$result = _$v ??
        _$UpdateLockServicesCardRequest._(
          cardId: cardId,
          cardServiceType: cardServiceType,
          flag: flag,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
