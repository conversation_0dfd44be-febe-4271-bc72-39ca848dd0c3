// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_customer_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateCustomerRequest extends UpdateCustomerRequest {
  @override
  final String? userId;
  @override
  final String? cifNumber;
  @override
  final String? mobilephone;
  @override
  final String? email;

  factory _$UpdateCustomerRequest(
          [void Function(UpdateCustomerRequestBuilder)? updates]) =>
      (UpdateCustomerRequestBuilder()..update(updates))._build();

  _$UpdateCustomerRequest._(
      {this.userId, this.cifNumber, this.mobilephone, this.email})
      : super._();
  @override
  UpdateCustomerRequest rebuild(
          void Function(UpdateCustomerRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateCustomerRequestBuilder toBuilder() =>
      UpdateCustomerRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateCustomerRequest &&
        userId == other.userId &&
        cifNumber == other.cifNumber &&
        mobilephone == other.mobilephone &&
        email == other.email;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, cifNumber.hashCode);
    _$hash = $jc(_$hash, mobilephone.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateCustomerRequest')
          ..add('userId', userId)
          ..add('cifNumber', cifNumber)
          ..add('mobilephone', mobilephone)
          ..add('email', email))
        .toString();
  }
}

class UpdateCustomerRequestBuilder
    implements Builder<UpdateCustomerRequest, UpdateCustomerRequestBuilder> {
  _$UpdateCustomerRequest? _$v;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  String? _cifNumber;
  String? get cifNumber => _$this._cifNumber;
  set cifNumber(String? cifNumber) => _$this._cifNumber = cifNumber;

  String? _mobilephone;
  String? get mobilephone => _$this._mobilephone;
  set mobilephone(String? mobilephone) => _$this._mobilephone = mobilephone;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  UpdateCustomerRequestBuilder() {
    UpdateCustomerRequest._defaults(this);
  }

  UpdateCustomerRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _cifNumber = $v.cifNumber;
      _mobilephone = $v.mobilephone;
      _email = $v.email;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateCustomerRequest other) {
    _$v = other as _$UpdateCustomerRequest;
  }

  @override
  void update(void Function(UpdateCustomerRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateCustomerRequest build() => _build();

  _$UpdateCustomerRequest _build() {
    final _$result = _$v ??
        _$UpdateCustomerRequest._(
          userId: userId,
          cifNumber: cifNumber,
          mobilephone: mobilephone,
          email: email,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
