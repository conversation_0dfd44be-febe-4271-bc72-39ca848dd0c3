// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_close_online_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewCloseOnlineSavingAccountResponse
    extends ReviewCloseOnlineSavingAccountResponse {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionDate;
  @override
  final String? accountNumber;
  @override
  final String? accountName;
  @override
  final double? balance;
  @override
  final String? termName;
  @override
  final String? finalTypeName;
  @override
  final String? currency;
  @override
  final double? rate;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final double? finalAmount;
  @override
  final String? destinationAccount;
  @override
  final double? interestAmount;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewCloseOnlineSavingAccountResponse(
          [void Function(ReviewCloseOnlineSavingAccountResponseBuilder)?
              updates]) =>
      (ReviewCloseOnlineSavingAccountResponseBuilder()..update(updates))
          ._build();

  _$ReviewCloseOnlineSavingAccountResponse._(
      {this.bankCif,
      this.transactionNo,
      this.transactionDate,
      this.accountNumber,
      this.accountName,
      this.balance,
      this.termName,
      this.finalTypeName,
      this.currency,
      this.rate,
      this.contractDate,
      this.dueDate,
      this.finalAmount,
      this.destinationAccount,
      this.interestAmount,
      this.transNextStep,
      this.content})
      : super._();
  @override
  ReviewCloseOnlineSavingAccountResponse rebuild(
          void Function(ReviewCloseOnlineSavingAccountResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCloseOnlineSavingAccountResponseBuilder toBuilder() =>
      ReviewCloseOnlineSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCloseOnlineSavingAccountResponse &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        accountNumber == other.accountNumber &&
        accountName == other.accountName &&
        balance == other.balance &&
        termName == other.termName &&
        finalTypeName == other.finalTypeName &&
        currency == other.currency &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        finalAmount == other.finalAmount &&
        destinationAccount == other.destinationAccount &&
        interestAmount == other.interestAmount &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, termName.hashCode);
    _$hash = $jc(_$hash, finalTypeName.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, destinationAccount.hashCode);
    _$hash = $jc(_$hash, interestAmount.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewCloseOnlineSavingAccountResponse')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('accountNumber', accountNumber)
          ..add('accountName', accountName)
          ..add('balance', balance)
          ..add('termName', termName)
          ..add('finalTypeName', finalTypeName)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('finalAmount', finalAmount)
          ..add('destinationAccount', destinationAccount)
          ..add('interestAmount', interestAmount)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewCloseOnlineSavingAccountResponseBuilder
    implements
        Builder<ReviewCloseOnlineSavingAccountResponse,
            ReviewCloseOnlineSavingAccountResponseBuilder> {
  _$ReviewCloseOnlineSavingAccountResponse? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  double? _balance;
  double? get balance => _$this._balance;
  set balance(double? balance) => _$this._balance = balance;

  String? _termName;
  String? get termName => _$this._termName;
  set termName(String? termName) => _$this._termName = termName;

  String? _finalTypeName;
  String? get finalTypeName => _$this._finalTypeName;
  set finalTypeName(String? finalTypeName) =>
      _$this._finalTypeName = finalTypeName;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  double? _finalAmount;
  double? get finalAmount => _$this._finalAmount;
  set finalAmount(double? finalAmount) => _$this._finalAmount = finalAmount;

  String? _destinationAccount;
  String? get destinationAccount => _$this._destinationAccount;
  set destinationAccount(String? destinationAccount) =>
      _$this._destinationAccount = destinationAccount;

  double? _interestAmount;
  double? get interestAmount => _$this._interestAmount;
  set interestAmount(double? interestAmount) =>
      _$this._interestAmount = interestAmount;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewCloseOnlineSavingAccountResponseBuilder() {
    ReviewCloseOnlineSavingAccountResponse._defaults(this);
  }

  ReviewCloseOnlineSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _accountNumber = $v.accountNumber;
      _accountName = $v.accountName;
      _balance = $v.balance;
      _termName = $v.termName;
      _finalTypeName = $v.finalTypeName;
      _currency = $v.currency;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _finalAmount = $v.finalAmount;
      _destinationAccount = $v.destinationAccount;
      _interestAmount = $v.interestAmount;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCloseOnlineSavingAccountResponse other) {
    _$v = other as _$ReviewCloseOnlineSavingAccountResponse;
  }

  @override
  void update(
      void Function(ReviewCloseOnlineSavingAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCloseOnlineSavingAccountResponse build() => _build();

  _$ReviewCloseOnlineSavingAccountResponse _build() {
    final _$result = _$v ??
        _$ReviewCloseOnlineSavingAccountResponse._(
          bankCif: bankCif,
          transactionNo: transactionNo,
          transactionDate: transactionDate,
          accountNumber: accountNumber,
          accountName: accountName,
          balance: balance,
          termName: termName,
          finalTypeName: finalTypeName,
          currency: currency,
          rate: rate,
          contractDate: contractDate,
          dueDate: dueDate,
          finalAmount: finalAmount,
          destinationAccount: destinationAccount,
          interestAmount: interestAmount,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
