// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_profit_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoanProfitResponse extends LoanProfitResponse {
  @override
  final int? month;
  @override
  final num? paymentAmount;

  factory _$LoanProfitResponse(
          [void Function(LoanProfitResponseBuilder)? updates]) =>
      (LoanProfitResponseBuilder()..update(updates))._build();

  _$LoanProfitResponse._({this.month, this.paymentAmount}) : super._();
  @override
  LoanProfitResponse rebuild(
          void Function(LoanProfitResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanProfitResponseBuilder toBuilder() =>
      LoanProfitResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanProfitResponse &&
        month == other.month &&
        paymentAmount == other.paymentAmount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, month.hashCode);
    _$hash = $jc(_$hash, paymentAmount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanProfitResponse')
          ..add('month', month)
          ..add('paymentAmount', paymentAmount))
        .toString();
  }
}

class LoanProfitResponseBuilder
    implements Builder<LoanProfitResponse, LoanProfitResponseBuilder> {
  _$LoanProfitResponse? _$v;

  int? _month;
  int? get month => _$this._month;
  set month(int? month) => _$this._month = month;

  num? _paymentAmount;
  num? get paymentAmount => _$this._paymentAmount;
  set paymentAmount(num? paymentAmount) =>
      _$this._paymentAmount = paymentAmount;

  LoanProfitResponseBuilder() {
    LoanProfitResponse._defaults(this);
  }

  LoanProfitResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _month = $v.month;
      _paymentAmount = $v.paymentAmount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanProfitResponse other) {
    _$v = other as _$LoanProfitResponse;
  }

  @override
  void update(void Function(LoanProfitResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanProfitResponse build() => _build();

  _$LoanProfitResponse _build() {
    final _$result = _$v ??
        _$LoanProfitResponse._(
          month: month,
          paymentAmount: paymentAmount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
