// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'target_saving_trans_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TargetSavingTransResponse extends TargetSavingTransResponse {
  @override
  final String? accountNo;
  @override
  final String? transactionNo;
  @override
  final double? balance;
  @override
  final String? customerName;
  @override
  final DateTime? contractDate;

  factory _$TargetSavingTransResponse(
          [void Function(TargetSavingTransResponseBuilder)? updates]) =>
      (TargetSavingTransResponseBuilder()..update(updates))._build();

  _$TargetSavingTransResponse._(
      {this.accountNo,
      this.transactionNo,
      this.balance,
      this.customerName,
      this.contractDate})
      : super._();
  @override
  TargetSavingTransResponse rebuild(
          void Function(TargetSavingTransResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TargetSavingTransResponseBuilder toBuilder() =>
      TargetSavingTransResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TargetSavingTransResponse &&
        accountNo == other.accountNo &&
        transactionNo == other.transactionNo &&
        balance == other.balance &&
        customerName == other.customerName &&
        contractDate == other.contractDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TargetSavingTransResponse')
          ..add('accountNo', accountNo)
          ..add('transactionNo', transactionNo)
          ..add('balance', balance)
          ..add('customerName', customerName)
          ..add('contractDate', contractDate))
        .toString();
  }
}

class TargetSavingTransResponseBuilder
    implements
        Builder<TargetSavingTransResponse, TargetSavingTransResponseBuilder> {
  _$TargetSavingTransResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  double? _balance;
  double? get balance => _$this._balance;
  set balance(double? balance) => _$this._balance = balance;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  TargetSavingTransResponseBuilder() {
    TargetSavingTransResponse._defaults(this);
  }

  TargetSavingTransResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _transactionNo = $v.transactionNo;
      _balance = $v.balance;
      _customerName = $v.customerName;
      _contractDate = $v.contractDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TargetSavingTransResponse other) {
    _$v = other as _$TargetSavingTransResponse;
  }

  @override
  void update(void Function(TargetSavingTransResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TargetSavingTransResponse build() => _build();

  _$TargetSavingTransResponse _build() {
    final _$result = _$v ??
        _$TargetSavingTransResponse._(
          accountNo: accountNo,
          transactionNo: transactionNo,
          balance: balance,
          customerName: customerName,
          contractDate: contractDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
