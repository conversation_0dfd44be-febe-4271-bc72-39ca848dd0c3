// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pay_topup_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PayTopupResponse extends PayTopupResponse {
  @override
  final String? orderNo;
  @override
  final String? systemTrace;
  @override
  final String? accountNo;
  @override
  final String? customerCode;
  @override
  final String? supplierCode;
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionTime;
  @override
  final num? amount;
  @override
  final num? value;

  factory _$PayTopupResponse(
          [void Function(PayTopupResponseBuilder)? updates]) =>
      (PayTopupResponseBuilder()..update(updates))._build();

  _$PayTopupResponse._(
      {this.orderNo,
      this.systemTrace,
      this.accountNo,
      this.customerCode,
      this.supplierCode,
      this.transactionNo,
      this.transactionTime,
      this.amount,
      this.value})
      : super._();
  @override
  PayTopupResponse rebuild(void Function(PayTopupResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PayTopupResponseBuilder toBuilder() =>
      PayTopupResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PayTopupResponse &&
        orderNo == other.orderNo &&
        systemTrace == other.systemTrace &&
        accountNo == other.accountNo &&
        customerCode == other.customerCode &&
        supplierCode == other.supplierCode &&
        transactionNo == other.transactionNo &&
        transactionTime == other.transactionTime &&
        amount == other.amount &&
        value == other.value;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, orderNo.hashCode);
    _$hash = $jc(_$hash, systemTrace.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionTime.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, value.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PayTopupResponse')
          ..add('orderNo', orderNo)
          ..add('systemTrace', systemTrace)
          ..add('accountNo', accountNo)
          ..add('customerCode', customerCode)
          ..add('supplierCode', supplierCode)
          ..add('transactionNo', transactionNo)
          ..add('transactionTime', transactionTime)
          ..add('amount', amount)
          ..add('value', value))
        .toString();
  }
}

class PayTopupResponseBuilder
    implements Builder<PayTopupResponse, PayTopupResponseBuilder> {
  _$PayTopupResponse? _$v;

  String? _orderNo;
  String? get orderNo => _$this._orderNo;
  set orderNo(String? orderNo) => _$this._orderNo = orderNo;

  String? _systemTrace;
  String? get systemTrace => _$this._systemTrace;
  set systemTrace(String? systemTrace) => _$this._systemTrace = systemTrace;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionTime;
  DateTime? get transactionTime => _$this._transactionTime;
  set transactionTime(DateTime? transactionTime) =>
      _$this._transactionTime = transactionTime;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _value;
  num? get value => _$this._value;
  set value(num? value) => _$this._value = value;

  PayTopupResponseBuilder() {
    PayTopupResponse._defaults(this);
  }

  PayTopupResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _orderNo = $v.orderNo;
      _systemTrace = $v.systemTrace;
      _accountNo = $v.accountNo;
      _customerCode = $v.customerCode;
      _supplierCode = $v.supplierCode;
      _transactionNo = $v.transactionNo;
      _transactionTime = $v.transactionTime;
      _amount = $v.amount;
      _value = $v.value;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PayTopupResponse other) {
    _$v = other as _$PayTopupResponse;
  }

  @override
  void update(void Function(PayTopupResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PayTopupResponse build() => _build();

  _$PayTopupResponse _build() {
    final _$result = _$v ??
        _$PayTopupResponse._(
          orderNo: orderNo,
          systemTrace: systemTrace,
          accountNo: accountNo,
          customerCode: customerCode,
          supplierCode: supplierCode,
          transactionNo: transactionNo,
          transactionTime: transactionTime,
          amount: amount,
          value: value,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
