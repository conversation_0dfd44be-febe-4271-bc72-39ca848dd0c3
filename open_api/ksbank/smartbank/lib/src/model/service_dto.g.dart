// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ServiceDto extends ServiceDto {
  @override
  final String? serviceCode;
  @override
  final String? serviceName;
  @override
  final String? iconUrl;

  factory _$ServiceDto([void Function(ServiceDtoBuilder)? updates]) =>
      (ServiceDtoBuilder()..update(updates))._build();

  _$ServiceDto._({this.serviceCode, this.serviceName, this.iconUrl})
      : super._();
  @override
  ServiceDto rebuild(void Function(ServiceDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ServiceDtoBuilder toBuilder() => ServiceDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ServiceDto &&
        serviceCode == other.serviceCode &&
        serviceName == other.serviceName &&
        iconUrl == other.iconUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, serviceCode.hashCode);
    _$hash = $jc(_$hash, serviceName.hashCode);
    _$hash = $jc(_$hash, iconUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ServiceDto')
          ..add('serviceCode', serviceCode)
          ..add('serviceName', serviceName)
          ..add('iconUrl', iconUrl))
        .toString();
  }
}

class ServiceDtoBuilder implements Builder<ServiceDto, ServiceDtoBuilder> {
  _$ServiceDto? _$v;

  String? _serviceCode;
  String? get serviceCode => _$this._serviceCode;
  set serviceCode(String? serviceCode) => _$this._serviceCode = serviceCode;

  String? _serviceName;
  String? get serviceName => _$this._serviceName;
  set serviceName(String? serviceName) => _$this._serviceName = serviceName;

  String? _iconUrl;
  String? get iconUrl => _$this._iconUrl;
  set iconUrl(String? iconUrl) => _$this._iconUrl = iconUrl;

  ServiceDtoBuilder() {
    ServiceDto._defaults(this);
  }

  ServiceDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _serviceCode = $v.serviceCode;
      _serviceName = $v.serviceName;
      _iconUrl = $v.iconUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ServiceDto other) {
    _$v = other as _$ServiceDto;
  }

  @override
  void update(void Function(ServiceDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ServiceDto build() => _build();

  _$ServiceDto _build() {
    final _$result = _$v ??
        _$ServiceDto._(
          serviceCode: serviceCode,
          serviceName: serviceName,
          iconUrl: iconUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
