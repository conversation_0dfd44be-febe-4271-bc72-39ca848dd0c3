// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_open_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewOpenCardResponse extends ReviewOpenCardResponse {
  @override
  final String? transactionNo;
  @override
  final TransNextStep? nextStep;

  factory _$ReviewOpenCardResponse(
          [void Function(ReviewOpenCardResponseBuilder)? updates]) =>
      (ReviewOpenCardResponseBuilder()..update(updates))._build();

  _$ReviewOpenCardResponse._({this.transactionNo, this.nextStep}) : super._();
  @override
  ReviewOpenCardResponse rebuild(
          void Function(ReviewOpenCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewOpenCardResponseBuilder toBuilder() =>
      ReviewOpenCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewOpenCardResponse &&
        transactionNo == other.transactionNo &&
        nextStep == other.nextStep;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, nextStep.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewOpenCardResponse')
          ..add('transactionNo', transactionNo)
          ..add('nextStep', nextStep))
        .toString();
  }
}

class ReviewOpenCardResponseBuilder
    implements Builder<ReviewOpenCardResponse, ReviewOpenCardResponseBuilder> {
  _$ReviewOpenCardResponse? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  TransNextStep? _nextStep;
  TransNextStep? get nextStep => _$this._nextStep;
  set nextStep(TransNextStep? nextStep) => _$this._nextStep = nextStep;

  ReviewOpenCardResponseBuilder() {
    ReviewOpenCardResponse._defaults(this);
  }

  ReviewOpenCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _nextStep = $v.nextStep;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewOpenCardResponse other) {
    _$v = other as _$ReviewOpenCardResponse;
  }

  @override
  void update(void Function(ReviewOpenCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewOpenCardResponse build() => _build();

  _$ReviewOpenCardResponse _build() {
    final _$result = _$v ??
        _$ReviewOpenCardResponse._(
          transactionNo: transactionNo,
          nextStep: nextStep,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
