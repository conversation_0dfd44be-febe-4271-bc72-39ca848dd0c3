// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_account_v1_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$VipAccountV1Response extends VipAccountV1Response {
  @override
  final String? vipAccountNo;
  @override
  final String? vipGroup;
  @override
  final num? feeAmount;
  @override
  final num? feeVat;
  @override
  final num? feeAmountIncludeVat;
  @override
  final String? feeAmountStr;
  @override
  final String? feeAmountIncludeVatStr;
  @override
  final String? checkCode;
  @override
  final String? checkName;

  factory _$VipAccountV1Response(
          [void Function(VipAccountV1ResponseBuilder)? updates]) =>
      (VipAccountV1ResponseBuilder()..update(updates))._build();

  _$VipAccountV1Response._(
      {this.vipAccountNo,
      this.vipGroup,
      this.feeAmount,
      this.feeVat,
      this.feeAmountIncludeVat,
      this.feeAmountStr,
      this.feeAmountIncludeVatStr,
      this.checkCode,
      this.checkName})
      : super._();
  @override
  VipAccountV1Response rebuild(
          void Function(VipAccountV1ResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VipAccountV1ResponseBuilder toBuilder() =>
      VipAccountV1ResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VipAccountV1Response &&
        vipAccountNo == other.vipAccountNo &&
        vipGroup == other.vipGroup &&
        feeAmount == other.feeAmount &&
        feeVat == other.feeVat &&
        feeAmountIncludeVat == other.feeAmountIncludeVat &&
        feeAmountStr == other.feeAmountStr &&
        feeAmountIncludeVatStr == other.feeAmountIncludeVatStr &&
        checkCode == other.checkCode &&
        checkName == other.checkName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, vipAccountNo.hashCode);
    _$hash = $jc(_$hash, vipGroup.hashCode);
    _$hash = $jc(_$hash, feeAmount.hashCode);
    _$hash = $jc(_$hash, feeVat.hashCode);
    _$hash = $jc(_$hash, feeAmountIncludeVat.hashCode);
    _$hash = $jc(_$hash, feeAmountStr.hashCode);
    _$hash = $jc(_$hash, feeAmountIncludeVatStr.hashCode);
    _$hash = $jc(_$hash, checkCode.hashCode);
    _$hash = $jc(_$hash, checkName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VipAccountV1Response')
          ..add('vipAccountNo', vipAccountNo)
          ..add('vipGroup', vipGroup)
          ..add('feeAmount', feeAmount)
          ..add('feeVat', feeVat)
          ..add('feeAmountIncludeVat', feeAmountIncludeVat)
          ..add('feeAmountStr', feeAmountStr)
          ..add('feeAmountIncludeVatStr', feeAmountIncludeVatStr)
          ..add('checkCode', checkCode)
          ..add('checkName', checkName))
        .toString();
  }
}

class VipAccountV1ResponseBuilder
    implements Builder<VipAccountV1Response, VipAccountV1ResponseBuilder> {
  _$VipAccountV1Response? _$v;

  String? _vipAccountNo;
  String? get vipAccountNo => _$this._vipAccountNo;
  set vipAccountNo(String? vipAccountNo) => _$this._vipAccountNo = vipAccountNo;

  String? _vipGroup;
  String? get vipGroup => _$this._vipGroup;
  set vipGroup(String? vipGroup) => _$this._vipGroup = vipGroup;

  num? _feeAmount;
  num? get feeAmount => _$this._feeAmount;
  set feeAmount(num? feeAmount) => _$this._feeAmount = feeAmount;

  num? _feeVat;
  num? get feeVat => _$this._feeVat;
  set feeVat(num? feeVat) => _$this._feeVat = feeVat;

  num? _feeAmountIncludeVat;
  num? get feeAmountIncludeVat => _$this._feeAmountIncludeVat;
  set feeAmountIncludeVat(num? feeAmountIncludeVat) =>
      _$this._feeAmountIncludeVat = feeAmountIncludeVat;

  String? _feeAmountStr;
  String? get feeAmountStr => _$this._feeAmountStr;
  set feeAmountStr(String? feeAmountStr) => _$this._feeAmountStr = feeAmountStr;

  String? _feeAmountIncludeVatStr;
  String? get feeAmountIncludeVatStr => _$this._feeAmountIncludeVatStr;
  set feeAmountIncludeVatStr(String? feeAmountIncludeVatStr) =>
      _$this._feeAmountIncludeVatStr = feeAmountIncludeVatStr;

  String? _checkCode;
  String? get checkCode => _$this._checkCode;
  set checkCode(String? checkCode) => _$this._checkCode = checkCode;

  String? _checkName;
  String? get checkName => _$this._checkName;
  set checkName(String? checkName) => _$this._checkName = checkName;

  VipAccountV1ResponseBuilder() {
    VipAccountV1Response._defaults(this);
  }

  VipAccountV1ResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _vipAccountNo = $v.vipAccountNo;
      _vipGroup = $v.vipGroup;
      _feeAmount = $v.feeAmount;
      _feeVat = $v.feeVat;
      _feeAmountIncludeVat = $v.feeAmountIncludeVat;
      _feeAmountStr = $v.feeAmountStr;
      _feeAmountIncludeVatStr = $v.feeAmountIncludeVatStr;
      _checkCode = $v.checkCode;
      _checkName = $v.checkName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VipAccountV1Response other) {
    _$v = other as _$VipAccountV1Response;
  }

  @override
  void update(void Function(VipAccountV1ResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VipAccountV1Response build() => _build();

  _$VipAccountV1Response _build() {
    final _$result = _$v ??
        _$VipAccountV1Response._(
          vipAccountNo: vipAccountNo,
          vipGroup: vipGroup,
          feeAmount: feeAmount,
          feeVat: feeVat,
          feeAmountIncludeVat: feeAmountIncludeVat,
          feeAmountStr: feeAmountStr,
          feeAmountIncludeVatStr: feeAmountIncludeVatStr,
          checkCode: checkCode,
          checkName: checkName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
