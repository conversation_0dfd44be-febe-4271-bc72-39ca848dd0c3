// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'region.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Region extends Region {
  @override
  final int? regionID;
  @override
  final String? regionName;

  factory _$Region([void Function(RegionBuilder)? updates]) =>
      (RegionBuilder()..update(updates))._build();

  _$Region._({this.regionID, this.regionName}) : super._();
  @override
  Region rebuild(void Function(RegionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RegionBuilder toBuilder() => RegionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Region &&
        regionID == other.regionID &&
        regionName == other.regionName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, regionID.hashCode);
    _$hash = $jc(_$hash, regionName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Region')
          ..add('regionID', regionID)
          ..add('regionName', regionName))
        .toString();
  }
}

class RegionBuilder implements Builder<Region, RegionBuilder> {
  _$Region? _$v;

  int? _regionID;
  int? get regionID => _$this._regionID;
  set regionID(int? regionID) => _$this._regionID = regionID;

  String? _regionName;
  String? get regionName => _$this._regionName;
  set regionName(String? regionName) => _$this._regionName = regionName;

  RegionBuilder() {
    Region._defaults(this);
  }

  RegionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _regionID = $v.regionID;
      _regionName = $v.regionName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Region other) {
    _$v = other as _$Region;
  }

  @override
  void update(void Function(RegionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Region build() => _build();

  _$Region _build() {
    final _$result = _$v ??
        _$Region._(
          regionID: regionID,
          regionName: regionName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
