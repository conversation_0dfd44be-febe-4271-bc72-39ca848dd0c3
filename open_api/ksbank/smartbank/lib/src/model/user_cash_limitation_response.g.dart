// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_cash_limitation_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UserCashLimitationResponse extends UserCashLimitationResponse {
  @override
  final String? notice;
  @override
  final BuiltList<CashLimitationResponse>? cashLimitations;

  factory _$UserCashLimitationResponse(
          [void Function(UserCashLimitationResponseBuilder)? updates]) =>
      (UserCashLimitationResponseBuilder()..update(updates))._build();

  _$UserCashLimitationResponse._({this.notice, this.cashLimitations})
      : super._();
  @override
  UserCashLimitationResponse rebuild(
          void Function(UserCashLimitationResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UserCashLimitationResponseBuilder toBuilder() =>
      UserCashLimitationResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserCashLimitationResponse &&
        notice == other.notice &&
        cashLimitations == other.cashLimitations;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, notice.hashCode);
    _$hash = $jc(_$hash, cashLimitations.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UserCashLimitationResponse')
          ..add('notice', notice)
          ..add('cashLimitations', cashLimitations))
        .toString();
  }
}

class UserCashLimitationResponseBuilder
    implements
        Builder<UserCashLimitationResponse, UserCashLimitationResponseBuilder> {
  _$UserCashLimitationResponse? _$v;

  String? _notice;
  String? get notice => _$this._notice;
  set notice(String? notice) => _$this._notice = notice;

  ListBuilder<CashLimitationResponse>? _cashLimitations;
  ListBuilder<CashLimitationResponse> get cashLimitations =>
      _$this._cashLimitations ??= ListBuilder<CashLimitationResponse>();
  set cashLimitations(ListBuilder<CashLimitationResponse>? cashLimitations) =>
      _$this._cashLimitations = cashLimitations;

  UserCashLimitationResponseBuilder() {
    UserCashLimitationResponse._defaults(this);
  }

  UserCashLimitationResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _notice = $v.notice;
      _cashLimitations = $v.cashLimitations?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UserCashLimitationResponse other) {
    _$v = other as _$UserCashLimitationResponse;
  }

  @override
  void update(void Function(UserCashLimitationResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UserCashLimitationResponse build() => _build();

  _$UserCashLimitationResponse _build() {
    _$UserCashLimitationResponse _$result;
    try {
      _$result = _$v ??
          _$UserCashLimitationResponse._(
            notice: notice,
            cashLimitations: _cashLimitations?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'cashLimitations';
        _cashLimitations?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'UserCashLimitationResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
