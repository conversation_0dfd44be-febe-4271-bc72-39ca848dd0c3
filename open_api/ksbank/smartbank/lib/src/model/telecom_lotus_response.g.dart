// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'telecom_lotus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TelecomLotusResponse extends TelecomLotusResponse {
  @override
  final String? telecomCode;
  @override
  final String? telecomName;
  @override
  final String? icon;

  factory _$TelecomLotusResponse(
          [void Function(TelecomLotusResponseBuilder)? updates]) =>
      (TelecomLotusResponseBuilder()..update(updates))._build();

  _$TelecomLotusResponse._({this.telecomCode, this.telecomName, this.icon})
      : super._();
  @override
  TelecomLotusResponse rebuild(
          void Function(TelecomLotusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TelecomLotusResponseBuilder toBuilder() =>
      TelecomLotusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TelecomLotusResponse &&
        telecomCode == other.telecomCode &&
        telecomName == other.telecomName &&
        icon == other.icon;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, telecomCode.hashCode);
    _$hash = $jc(_$hash, telecomName.hashCode);
    _$hash = $jc(_$hash, icon.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TelecomLotusResponse')
          ..add('telecomCode', telecomCode)
          ..add('telecomName', telecomName)
          ..add('icon', icon))
        .toString();
  }
}

class TelecomLotusResponseBuilder
    implements Builder<TelecomLotusResponse, TelecomLotusResponseBuilder> {
  _$TelecomLotusResponse? _$v;

  String? _telecomCode;
  String? get telecomCode => _$this._telecomCode;
  set telecomCode(String? telecomCode) => _$this._telecomCode = telecomCode;

  String? _telecomName;
  String? get telecomName => _$this._telecomName;
  set telecomName(String? telecomName) => _$this._telecomName = telecomName;

  String? _icon;
  String? get icon => _$this._icon;
  set icon(String? icon) => _$this._icon = icon;

  TelecomLotusResponseBuilder() {
    TelecomLotusResponse._defaults(this);
  }

  TelecomLotusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _telecomCode = $v.telecomCode;
      _telecomName = $v.telecomName;
      _icon = $v.icon;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TelecomLotusResponse other) {
    _$v = other as _$TelecomLotusResponse;
  }

  @override
  void update(void Function(TelecomLotusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TelecomLotusResponse build() => _build();

  _$TelecomLotusResponse _build() {
    final _$result = _$v ??
        _$TelecomLotusResponse._(
          telecomCode: telecomCode,
          telecomName: telecomName,
          icon: icon,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
