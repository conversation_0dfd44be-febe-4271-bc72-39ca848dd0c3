// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'share_bills_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ShareBillsRequest extends ShareBillsRequest {
  @override
  final String? transactionId;
  @override
  final String? beneficiaryAccountNo;
  @override
  final BuiltList<SharedPerson>? sharedPeople;
  @override
  final String? description;

  factory _$ShareBillsRequest(
          [void Function(ShareBillsRequestBuilder)? updates]) =>
      (ShareBillsRequestBuilder()..update(updates))._build();

  _$ShareBillsRequest._(
      {this.transactionId,
      this.beneficiaryAccountNo,
      this.sharedPeople,
      this.description})
      : super._();
  @override
  ShareBillsRequest rebuild(void Function(ShareBillsRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ShareBillsRequestBuilder toBuilder() =>
      ShareBillsRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ShareBillsRequest &&
        transactionId == other.transactionId &&
        beneficiaryAccountNo == other.beneficiaryAccountNo &&
        sharedPeople == other.sharedPeople &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionId.hashCode);
    _$hash = $jc(_$hash, beneficiaryAccountNo.hashCode);
    _$hash = $jc(_$hash, sharedPeople.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ShareBillsRequest')
          ..add('transactionId', transactionId)
          ..add('beneficiaryAccountNo', beneficiaryAccountNo)
          ..add('sharedPeople', sharedPeople)
          ..add('description', description))
        .toString();
  }
}

class ShareBillsRequestBuilder
    implements Builder<ShareBillsRequest, ShareBillsRequestBuilder> {
  _$ShareBillsRequest? _$v;

  String? _transactionId;
  String? get transactionId => _$this._transactionId;
  set transactionId(String? transactionId) =>
      _$this._transactionId = transactionId;

  String? _beneficiaryAccountNo;
  String? get beneficiaryAccountNo => _$this._beneficiaryAccountNo;
  set beneficiaryAccountNo(String? beneficiaryAccountNo) =>
      _$this._beneficiaryAccountNo = beneficiaryAccountNo;

  ListBuilder<SharedPerson>? _sharedPeople;
  ListBuilder<SharedPerson> get sharedPeople =>
      _$this._sharedPeople ??= ListBuilder<SharedPerson>();
  set sharedPeople(ListBuilder<SharedPerson>? sharedPeople) =>
      _$this._sharedPeople = sharedPeople;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  ShareBillsRequestBuilder() {
    ShareBillsRequest._defaults(this);
  }

  ShareBillsRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionId = $v.transactionId;
      _beneficiaryAccountNo = $v.beneficiaryAccountNo;
      _sharedPeople = $v.sharedPeople?.toBuilder();
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ShareBillsRequest other) {
    _$v = other as _$ShareBillsRequest;
  }

  @override
  void update(void Function(ShareBillsRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ShareBillsRequest build() => _build();

  _$ShareBillsRequest _build() {
    _$ShareBillsRequest _$result;
    try {
      _$result = _$v ??
          _$ShareBillsRequest._(
            transactionId: transactionId,
            beneficiaryAccountNo: beneficiaryAccountNo,
            sharedPeople: _sharedPeople?.build(),
            description: description,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'sharedPeople';
        _sharedPeople?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ShareBillsRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
