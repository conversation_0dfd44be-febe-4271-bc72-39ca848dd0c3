// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_customer_logging_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateCustomerLoggingRequest extends UpdateCustomerLoggingRequest {
  @override
  final BuiltSet<String>? customerLogging;

  factory _$UpdateCustomerLoggingRequest(
          [void Function(UpdateCustomerLoggingRequestBuilder)? updates]) =>
      (UpdateCustomerLoggingRequestBuilder()..update(updates))._build();

  _$UpdateCustomerLoggingRequest._({this.customerLogging}) : super._();
  @override
  UpdateCustomerLoggingRequest rebuild(
          void Function(UpdateCustomerLoggingRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateCustomerLoggingRequestBuilder toBuilder() =>
      UpdateCustomerLoggingRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateCustomerLoggingRequest &&
        customerLogging == other.customerLogging;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, customerLogging.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateCustomerLoggingRequest')
          ..add('customerLogging', customerLogging))
        .toString();
  }
}

class UpdateCustomerLoggingRequestBuilder
    implements
        Builder<UpdateCustomerLoggingRequest,
            UpdateCustomerLoggingRequestBuilder> {
  _$UpdateCustomerLoggingRequest? _$v;

  SetBuilder<String>? _customerLogging;
  SetBuilder<String> get customerLogging =>
      _$this._customerLogging ??= SetBuilder<String>();
  set customerLogging(SetBuilder<String>? customerLogging) =>
      _$this._customerLogging = customerLogging;

  UpdateCustomerLoggingRequestBuilder() {
    UpdateCustomerLoggingRequest._defaults(this);
  }

  UpdateCustomerLoggingRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _customerLogging = $v.customerLogging?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateCustomerLoggingRequest other) {
    _$v = other as _$UpdateCustomerLoggingRequest;
  }

  @override
  void update(void Function(UpdateCustomerLoggingRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateCustomerLoggingRequest build() => _build();

  _$UpdateCustomerLoggingRequest _build() {
    _$UpdateCustomerLoggingRequest _$result;
    try {
      _$result = _$v ??
          _$UpdateCustomerLoggingRequest._(
            customerLogging: _customerLogging?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'customerLogging';
        _customerLogging?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'UpdateCustomerLoggingRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
