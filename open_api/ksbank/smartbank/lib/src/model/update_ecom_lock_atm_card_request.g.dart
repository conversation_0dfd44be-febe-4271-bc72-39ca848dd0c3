// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_ecom_lock_atm_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateEcomLockATMCardRequest extends UpdateEcomLockATMCardRequest {
  @override
  final String? cardId;
  @override
  final Flag? flag;

  factory _$UpdateEcomLockATMCardRequest(
          [void Function(UpdateEcomLockATMCardRequestBuilder)? updates]) =>
      (UpdateEcomLockATMCardRequestBuilder()..update(updates))._build();

  _$UpdateEcomLockATMCardRequest._({this.cardId, this.flag}) : super._();
  @override
  UpdateEcomLockATMCardRequest rebuild(
          void Function(UpdateEcomLockATMCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateEcomLockATMCardRequestBuilder toBuilder() =>
      UpdateEcomLockATMCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateEcomLockATMCardRequest &&
        cardId == other.cardId &&
        flag == other.flag;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, flag.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateEcomLockATMCardRequest')
          ..add('cardId', cardId)
          ..add('flag', flag))
        .toString();
  }
}

class UpdateEcomLockATMCardRequestBuilder
    implements
        Builder<UpdateEcomLockATMCardRequest,
            UpdateEcomLockATMCardRequestBuilder> {
  _$UpdateEcomLockATMCardRequest? _$v;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  Flag? _flag;
  Flag? get flag => _$this._flag;
  set flag(Flag? flag) => _$this._flag = flag;

  UpdateEcomLockATMCardRequestBuilder() {
    UpdateEcomLockATMCardRequest._defaults(this);
  }

  UpdateEcomLockATMCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardId = $v.cardId;
      _flag = $v.flag;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateEcomLockATMCardRequest other) {
    _$v = other as _$UpdateEcomLockATMCardRequest;
  }

  @override
  void update(void Function(UpdateEcomLockATMCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateEcomLockATMCardRequest build() => _build();

  _$UpdateEcomLockATMCardRequest _build() {
    final _$result = _$v ??
        _$UpdateEcomLockATMCardRequest._(
          cardId: cardId,
          flag: flag,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
