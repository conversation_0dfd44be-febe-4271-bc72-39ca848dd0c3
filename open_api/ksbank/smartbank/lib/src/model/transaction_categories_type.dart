//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'transaction_categories_type.g.dart';

class TransactionCategoriesType extends EnumClass {
  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'SAVINGS')
  static const TransactionCategoriesType SAVINGS = _$SAVINGS;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'CREATE_TARGET_SAVINGS_ACCOUNT')
  static const TransactionCategoriesType CREATE_TARGET_SAVINGS_ACCOUNT =
      _$CREATE_TARGET_SAVINGS_ACCOUNT;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'DEPOSIT_TARGET_SAVING')
  static const TransactionCategoriesType DEPOSIT_TARGET_SAVING =
      _$DEPOSIT_TARGET_SAVING;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'WITHDRAWAL_TARGET_SAVING')
  static const TransactionCategoriesType WITHDRAWAL_TARGET_SAVING =
      _$WITHDRAWAL_TARGET_SAVING;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'CLOSE_TARGET_SAVING')
  static const TransactionCategoriesType CLOSE_TARGET_SAVING =
      _$CLOSE_TARGET_SAVING;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'CLOSE_SAVING')
  static const TransactionCategoriesType CLOSE_SAVING = _$CLOSE_SAVING;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'LOAN_CREDIT')
  static const TransactionCategoriesType LOAN_CREDIT = _$LOAN_CREDIT;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'TRANSFER_INTERNAL')
  static const TransactionCategoriesType TRANSFER_INTERNAL =
      _$TRANSFER_INTERNAL;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'TRANSFER_INTERNAL_SAME_CIF')
  static const TransactionCategoriesType TRANSFER_INTERNAL_SAME_CIF =
      _$TRANSFER_INTERNAL_SAME_CIF;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'TRANSFER_INTERNAL_DIFF_CIF')
  static const TransactionCategoriesType TRANSFER_INTERNAL_DIFF_CIF =
      _$TRANSFER_INTERNAL_DIFF_CIF;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'TRANSFER_EXTERNAL_VIA_NAPAS')
  static const TransactionCategoriesType TRANSFER_EXTERNAL_VIA_NAPAS =
      _$TRANSFER_EXTERNAL_VIA_NAPAS;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'TRANSFER_EXTERNAL_VIA_CITAD')
  static const TransactionCategoriesType TRANSFER_EXTERNAL_VIA_CITAD =
      _$TRANSFER_EXTERNAL_VIA_CITAD;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'PAY_BILLS')
  static const TransactionCategoriesType PAY_BILLS = _$PAY_BILLS;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'PAY_BILLS_FIRE')
  static const TransactionCategoriesType PAY_BILLS_FIRE = _$PAY_BILLS_FIRE;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'PAY_TOPUP')
  static const TransactionCategoriesType PAY_TOPUP = _$PAY_TOPUP;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'PAY_CREDIT_CARD')
  static const TransactionCategoriesType PAY_CREDIT_CARD = _$PAY_CREDIT_CARD;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'OPEN_VIP_ACCOUNT')
  static const TransactionCategoriesType OPEN_VIP_ACCOUNT = _$OPEN_VIP_ACCOUNT;

  /// Loại giao dịch
  @BuiltValueEnumConst(wireName: r'REGISTER_PACKAGE_MYSHOP')
  static const TransactionCategoriesType REGISTER_PACKAGE_MYSHOP =
      _$REGISTER_PACKAGE_MYSHOP;

  static Serializer<TransactionCategoriesType> get serializer =>
      _$transactionCategoriesTypeSerializer;

  const TransactionCategoriesType._(String name) : super(name);

  static BuiltSet<TransactionCategoriesType> get values => _$values;
  static TransactionCategoriesType valueOf(String name) => _$valueOf(name);
}

/// Optionally, enum_class can generate a mixin to go with your enum for use
/// with Angular. It exposes your enum constants as getters. So, if you mix it
/// in to your Dart component class, the values become available to the
/// corresponding Angular template.
///
/// Trigger mixin generation by writing a line like this one next to your enum.
abstract class TransactionCategoriesTypeMixin = Object
    with _$TransactionCategoriesTypeMixin;
