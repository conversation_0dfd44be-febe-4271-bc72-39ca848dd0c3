// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_create_transfer_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewCreateTransferRequest extends ReviewCreateTransferRequest {
  @override
  final String? refNo;
  @override
  final String? fromAcctNbr;
  @override
  final num? amount;
  @override
  final String? descText;
  @override
  final String? authenMode;
  @override
  final String? benName;
  @override
  final String? benPhone;
  @override
  final String? benIdCard;
  @override
  final String? benIssueDate;
  @override
  final String? benPlaceBy;
  @override
  final String? benAddr;

  factory _$ReviewCreateTransferRequest(
          [void Function(ReviewCreateTransferRequestBuilder)? updates]) =>
      (ReviewCreateTransferRequestBuilder()..update(updates))._build();

  _$ReviewCreateTransferRequest._(
      {this.refNo,
      this.fromAcctNbr,
      this.amount,
      this.descText,
      this.authenMode,
      this.benName,
      this.benPhone,
      this.benIdCard,
      this.benIssueDate,
      this.benPlaceBy,
      this.benAddr})
      : super._();
  @override
  ReviewCreateTransferRequest rebuild(
          void Function(ReviewCreateTransferRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCreateTransferRequestBuilder toBuilder() =>
      ReviewCreateTransferRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCreateTransferRequest &&
        refNo == other.refNo &&
        fromAcctNbr == other.fromAcctNbr &&
        amount == other.amount &&
        descText == other.descText &&
        authenMode == other.authenMode &&
        benName == other.benName &&
        benPhone == other.benPhone &&
        benIdCard == other.benIdCard &&
        benIssueDate == other.benIssueDate &&
        benPlaceBy == other.benPlaceBy &&
        benAddr == other.benAddr;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, refNo.hashCode);
    _$hash = $jc(_$hash, fromAcctNbr.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, descText.hashCode);
    _$hash = $jc(_$hash, authenMode.hashCode);
    _$hash = $jc(_$hash, benName.hashCode);
    _$hash = $jc(_$hash, benPhone.hashCode);
    _$hash = $jc(_$hash, benIdCard.hashCode);
    _$hash = $jc(_$hash, benIssueDate.hashCode);
    _$hash = $jc(_$hash, benPlaceBy.hashCode);
    _$hash = $jc(_$hash, benAddr.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewCreateTransferRequest')
          ..add('refNo', refNo)
          ..add('fromAcctNbr', fromAcctNbr)
          ..add('amount', amount)
          ..add('descText', descText)
          ..add('authenMode', authenMode)
          ..add('benName', benName)
          ..add('benPhone', benPhone)
          ..add('benIdCard', benIdCard)
          ..add('benIssueDate', benIssueDate)
          ..add('benPlaceBy', benPlaceBy)
          ..add('benAddr', benAddr))
        .toString();
  }
}

class ReviewCreateTransferRequestBuilder
    implements
        Builder<ReviewCreateTransferRequest,
            ReviewCreateTransferRequestBuilder> {
  _$ReviewCreateTransferRequest? _$v;

  String? _refNo;
  String? get refNo => _$this._refNo;
  set refNo(String? refNo) => _$this._refNo = refNo;

  String? _fromAcctNbr;
  String? get fromAcctNbr => _$this._fromAcctNbr;
  set fromAcctNbr(String? fromAcctNbr) => _$this._fromAcctNbr = fromAcctNbr;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _descText;
  String? get descText => _$this._descText;
  set descText(String? descText) => _$this._descText = descText;

  String? _authenMode;
  String? get authenMode => _$this._authenMode;
  set authenMode(String? authenMode) => _$this._authenMode = authenMode;

  String? _benName;
  String? get benName => _$this._benName;
  set benName(String? benName) => _$this._benName = benName;

  String? _benPhone;
  String? get benPhone => _$this._benPhone;
  set benPhone(String? benPhone) => _$this._benPhone = benPhone;

  String? _benIdCard;
  String? get benIdCard => _$this._benIdCard;
  set benIdCard(String? benIdCard) => _$this._benIdCard = benIdCard;

  String? _benIssueDate;
  String? get benIssueDate => _$this._benIssueDate;
  set benIssueDate(String? benIssueDate) => _$this._benIssueDate = benIssueDate;

  String? _benPlaceBy;
  String? get benPlaceBy => _$this._benPlaceBy;
  set benPlaceBy(String? benPlaceBy) => _$this._benPlaceBy = benPlaceBy;

  String? _benAddr;
  String? get benAddr => _$this._benAddr;
  set benAddr(String? benAddr) => _$this._benAddr = benAddr;

  ReviewCreateTransferRequestBuilder() {
    ReviewCreateTransferRequest._defaults(this);
  }

  ReviewCreateTransferRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _refNo = $v.refNo;
      _fromAcctNbr = $v.fromAcctNbr;
      _amount = $v.amount;
      _descText = $v.descText;
      _authenMode = $v.authenMode;
      _benName = $v.benName;
      _benPhone = $v.benPhone;
      _benIdCard = $v.benIdCard;
      _benIssueDate = $v.benIssueDate;
      _benPlaceBy = $v.benPlaceBy;
      _benAddr = $v.benAddr;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCreateTransferRequest other) {
    _$v = other as _$ReviewCreateTransferRequest;
  }

  @override
  void update(void Function(ReviewCreateTransferRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCreateTransferRequest build() => _build();

  _$ReviewCreateTransferRequest _build() {
    final _$result = _$v ??
        _$ReviewCreateTransferRequest._(
          refNo: refNo,
          fromAcctNbr: fromAcctNbr,
          amount: amount,
          descText: descText,
          authenMode: authenMode,
          benName: benName,
          benPhone: benPhone,
          benIdCard: benIdCard,
          benIssueDate: benIssueDate,
          benPlaceBy: benPlaceBy,
          benAddr: benAddr,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
