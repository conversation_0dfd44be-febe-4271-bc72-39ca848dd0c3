// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_statistical_item_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionStatisticalItemDto extends TransactionStatisticalItemDto {
  @override
  final String? transactionNo;
  @override
  final int? transactionTypeId;
  @override
  final String? transactionTypeName;
  @override
  final String? transactionTypeIconUrl;
  @override
  final String? description;
  @override
  final int? coefficient;
  @override
  final String? currency;
  @override
  final String? currencyName;
  @override
  final String? amountStr;
  @override
  final num? amount;
  @override
  final DateTime? transactionAt;

  factory _$TransactionStatisticalItemDto(
          [void Function(TransactionStatisticalItemDtoBuilder)? updates]) =>
      (TransactionStatisticalItemDtoBuilder()..update(updates))._build();

  _$TransactionStatisticalItemDto._(
      {this.transactionNo,
      this.transactionTypeId,
      this.transactionTypeName,
      this.transactionTypeIconUrl,
      this.description,
      this.coefficient,
      this.currency,
      this.currencyName,
      this.amountStr,
      this.amount,
      this.transactionAt})
      : super._();
  @override
  TransactionStatisticalItemDto rebuild(
          void Function(TransactionStatisticalItemDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionStatisticalItemDtoBuilder toBuilder() =>
      TransactionStatisticalItemDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionStatisticalItemDto &&
        transactionNo == other.transactionNo &&
        transactionTypeId == other.transactionTypeId &&
        transactionTypeName == other.transactionTypeName &&
        transactionTypeIconUrl == other.transactionTypeIconUrl &&
        description == other.description &&
        coefficient == other.coefficient &&
        currency == other.currency &&
        currencyName == other.currencyName &&
        amountStr == other.amountStr &&
        amount == other.amount &&
        transactionAt == other.transactionAt;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionTypeId.hashCode);
    _$hash = $jc(_$hash, transactionTypeName.hashCode);
    _$hash = $jc(_$hash, transactionTypeIconUrl.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, coefficient.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, currencyName.hashCode);
    _$hash = $jc(_$hash, amountStr.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, transactionAt.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionStatisticalItemDto')
          ..add('transactionNo', transactionNo)
          ..add('transactionTypeId', transactionTypeId)
          ..add('transactionTypeName', transactionTypeName)
          ..add('transactionTypeIconUrl', transactionTypeIconUrl)
          ..add('description', description)
          ..add('coefficient', coefficient)
          ..add('currency', currency)
          ..add('currencyName', currencyName)
          ..add('amountStr', amountStr)
          ..add('amount', amount)
          ..add('transactionAt', transactionAt))
        .toString();
  }
}

class TransactionStatisticalItemDtoBuilder
    implements
        Builder<TransactionStatisticalItemDto,
            TransactionStatisticalItemDtoBuilder> {
  _$TransactionStatisticalItemDto? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  int? _transactionTypeId;
  int? get transactionTypeId => _$this._transactionTypeId;
  set transactionTypeId(int? transactionTypeId) =>
      _$this._transactionTypeId = transactionTypeId;

  String? _transactionTypeName;
  String? get transactionTypeName => _$this._transactionTypeName;
  set transactionTypeName(String? transactionTypeName) =>
      _$this._transactionTypeName = transactionTypeName;

  String? _transactionTypeIconUrl;
  String? get transactionTypeIconUrl => _$this._transactionTypeIconUrl;
  set transactionTypeIconUrl(String? transactionTypeIconUrl) =>
      _$this._transactionTypeIconUrl = transactionTypeIconUrl;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _coefficient;
  int? get coefficient => _$this._coefficient;
  set coefficient(int? coefficient) => _$this._coefficient = coefficient;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  String? _currencyName;
  String? get currencyName => _$this._currencyName;
  set currencyName(String? currencyName) => _$this._currencyName = currencyName;

  String? _amountStr;
  String? get amountStr => _$this._amountStr;
  set amountStr(String? amountStr) => _$this._amountStr = amountStr;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  DateTime? _transactionAt;
  DateTime? get transactionAt => _$this._transactionAt;
  set transactionAt(DateTime? transactionAt) =>
      _$this._transactionAt = transactionAt;

  TransactionStatisticalItemDtoBuilder() {
    TransactionStatisticalItemDto._defaults(this);
  }

  TransactionStatisticalItemDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _transactionTypeId = $v.transactionTypeId;
      _transactionTypeName = $v.transactionTypeName;
      _transactionTypeIconUrl = $v.transactionTypeIconUrl;
      _description = $v.description;
      _coefficient = $v.coefficient;
      _currency = $v.currency;
      _currencyName = $v.currencyName;
      _amountStr = $v.amountStr;
      _amount = $v.amount;
      _transactionAt = $v.transactionAt;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionStatisticalItemDto other) {
    _$v = other as _$TransactionStatisticalItemDto;
  }

  @override
  void update(void Function(TransactionStatisticalItemDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionStatisticalItemDto build() => _build();

  _$TransactionStatisticalItemDto _build() {
    final _$result = _$v ??
        _$TransactionStatisticalItemDto._(
          transactionNo: transactionNo,
          transactionTypeId: transactionTypeId,
          transactionTypeName: transactionTypeName,
          transactionTypeIconUrl: transactionTypeIconUrl,
          description: description,
          coefficient: coefficient,
          currency: currency,
          currencyName: currencyName,
          amountStr: amountStr,
          amount: amount,
          transactionAt: transactionAt,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
