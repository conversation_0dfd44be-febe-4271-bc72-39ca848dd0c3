// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_phone_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const PaymentPhoneCardRequestTelephoneProviderEnum
    _$paymentPhoneCardRequestTelephoneProviderEnum_MOBIFONE =
    const PaymentPhoneCardRequestTelephoneProviderEnum._('MOBIFONE');
const PaymentPhoneCardRequestTelephoneProviderEnum
    _$paymentPhoneCardRequestTelephoneProviderEnum_VINAPHONE =
    const PaymentPhoneCardRequestTelephoneProviderEnum._('VINAPHONE');
const PaymentPhoneCardRequestTelephoneProviderEnum
    _$paymentPhoneCardRequestTelephoneProviderEnum_VIETTEL =
    const PaymentPhoneCardRequestTelephoneProviderEnum._('VIETTEL');
const PaymentPhoneCardRequestTelephoneProviderEnum
    _$paymentPhoneCardRequestTelephoneProviderEnum_VIETNAMOBILE =
    const PaymentPhoneCardRequestTelephoneProviderEnum._('VIETNAMOBILE');
const PaymentPhoneCardRequestTelephoneProviderEnum
    _$paymentPhoneCardRequestTelephoneProviderEnum_GMOBILE =
    const PaymentPhoneCardRequestTelephoneProviderEnum._('GMOBILE');

PaymentPhoneCardRequestTelephoneProviderEnum
    _$paymentPhoneCardRequestTelephoneProviderEnumValueOf(String name) {
  switch (name) {
    case 'MOBIFONE':
      return _$paymentPhoneCardRequestTelephoneProviderEnum_MOBIFONE;
    case 'VINAPHONE':
      return _$paymentPhoneCardRequestTelephoneProviderEnum_VINAPHONE;
    case 'VIETTEL':
      return _$paymentPhoneCardRequestTelephoneProviderEnum_VIETTEL;
    case 'VIETNAMOBILE':
      return _$paymentPhoneCardRequestTelephoneProviderEnum_VIETNAMOBILE;
    case 'GMOBILE':
      return _$paymentPhoneCardRequestTelephoneProviderEnum_GMOBILE;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<PaymentPhoneCardRequestTelephoneProviderEnum>
    _$paymentPhoneCardRequestTelephoneProviderEnumValues = BuiltSet<
        PaymentPhoneCardRequestTelephoneProviderEnum>(const <PaymentPhoneCardRequestTelephoneProviderEnum>[
  _$paymentPhoneCardRequestTelephoneProviderEnum_MOBIFONE,
  _$paymentPhoneCardRequestTelephoneProviderEnum_VINAPHONE,
  _$paymentPhoneCardRequestTelephoneProviderEnum_VIETTEL,
  _$paymentPhoneCardRequestTelephoneProviderEnum_VIETNAMOBILE,
  _$paymentPhoneCardRequestTelephoneProviderEnum_GMOBILE,
]);

Serializer<PaymentPhoneCardRequestTelephoneProviderEnum>
    _$paymentPhoneCardRequestTelephoneProviderEnumSerializer =
    _$PaymentPhoneCardRequestTelephoneProviderEnumSerializer();

class _$PaymentPhoneCardRequestTelephoneProviderEnumSerializer
    implements
        PrimitiveSerializer<PaymentPhoneCardRequestTelephoneProviderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'MOBIFONE': 'MOBIFONE',
    'VINAPHONE': 'VINAPHONE',
    'VIETTEL': 'VIETTEL',
    'VIETNAMOBILE': 'VIETNAMOBILE',
    'GMOBILE': 'GMOBILE',
  };

  @override
  final Iterable<Type> types = const <Type>[
    PaymentPhoneCardRequestTelephoneProviderEnum
  ];
  @override
  final String wireName = 'PaymentPhoneCardRequestTelephoneProviderEnum';

  @override
  Object serialize(Serializers serializers,
          PaymentPhoneCardRequestTelephoneProviderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  PaymentPhoneCardRequestTelephoneProviderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      PaymentPhoneCardRequestTelephoneProviderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$PaymentPhoneCardRequest extends PaymentPhoneCardRequest {
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final String? transactionNo;
  @override
  final String? accountNo;
  @override
  final num? cardValue;
  @override
  final PaymentPhoneCardRequestTelephoneProviderEnum? telephoneProvider;
  @override
  final int? quantity;
  @override
  final num? amount;

  factory _$PaymentPhoneCardRequest(
          [void Function(PaymentPhoneCardRequestBuilder)? updates]) =>
      (PaymentPhoneCardRequestBuilder()..update(updates))._build();

  _$PaymentPhoneCardRequest._(
      {this.softOtp,
      this.transactionNo,
      this.accountNo,
      this.cardValue,
      this.telephoneProvider,
      this.quantity,
      this.amount})
      : super._();
  @override
  PaymentPhoneCardRequest rebuild(
          void Function(PaymentPhoneCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentPhoneCardRequestBuilder toBuilder() =>
      PaymentPhoneCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentPhoneCardRequest &&
        softOtp == other.softOtp &&
        transactionNo == other.transactionNo &&
        accountNo == other.accountNo &&
        cardValue == other.cardValue &&
        telephoneProvider == other.telephoneProvider &&
        quantity == other.quantity &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jc(_$hash, telephoneProvider.hashCode);
    _$hash = $jc(_$hash, quantity.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentPhoneCardRequest')
          ..add('softOtp', softOtp)
          ..add('transactionNo', transactionNo)
          ..add('accountNo', accountNo)
          ..add('cardValue', cardValue)
          ..add('telephoneProvider', telephoneProvider)
          ..add('quantity', quantity)
          ..add('amount', amount))
        .toString();
  }
}

class PaymentPhoneCardRequestBuilder
    implements
        Builder<PaymentPhoneCardRequest, PaymentPhoneCardRequestBuilder> {
  _$PaymentPhoneCardRequest? _$v;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  PaymentPhoneCardRequestTelephoneProviderEnum? _telephoneProvider;
  PaymentPhoneCardRequestTelephoneProviderEnum? get telephoneProvider =>
      _$this._telephoneProvider;
  set telephoneProvider(
          PaymentPhoneCardRequestTelephoneProviderEnum? telephoneProvider) =>
      _$this._telephoneProvider = telephoneProvider;

  int? _quantity;
  int? get quantity => _$this._quantity;
  set quantity(int? quantity) => _$this._quantity = quantity;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  PaymentPhoneCardRequestBuilder() {
    PaymentPhoneCardRequest._defaults(this);
  }

  PaymentPhoneCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _softOtp = $v.softOtp?.toBuilder();
      _transactionNo = $v.transactionNo;
      _accountNo = $v.accountNo;
      _cardValue = $v.cardValue;
      _telephoneProvider = $v.telephoneProvider;
      _quantity = $v.quantity;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentPhoneCardRequest other) {
    _$v = other as _$PaymentPhoneCardRequest;
  }

  @override
  void update(void Function(PaymentPhoneCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentPhoneCardRequest build() => _build();

  _$PaymentPhoneCardRequest _build() {
    _$PaymentPhoneCardRequest _$result;
    try {
      _$result = _$v ??
          _$PaymentPhoneCardRequest._(
            softOtp: _softOtp?.build(),
            transactionNo: transactionNo,
            accountNo: accountNo,
            cardValue: cardValue,
            telephoneProvider: telephoneProvider,
            quantity: quantity,
            amount: amount,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PaymentPhoneCardRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
