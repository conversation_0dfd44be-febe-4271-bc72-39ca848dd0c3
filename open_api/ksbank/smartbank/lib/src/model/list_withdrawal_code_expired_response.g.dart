// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_withdrawal_code_expired_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListWithdrawalCodeExpiredResponse
    extends ListWithdrawalCodeExpiredResponse {
  @override
  final BuiltList<WithdrawalCodeResponse>? withdrawalCodes;

  factory _$ListWithdrawalCodeExpiredResponse(
          [void Function(ListWithdrawalCodeExpiredResponseBuilder)? updates]) =>
      (ListWithdrawalCodeExpiredResponseBuilder()..update(updates))._build();

  _$ListWithdrawalCodeExpiredResponse._({this.withdrawalCodes}) : super._();
  @override
  ListWithdrawalCodeExpiredResponse rebuild(
          void Function(ListWithdrawalCodeExpiredResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListWithdrawalCodeExpiredResponseBuilder toBuilder() =>
      ListWithdrawalCodeExpiredResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListWithdrawalCodeExpiredResponse &&
        withdrawalCodes == other.withdrawalCodes;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, withdrawalCodes.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListWithdrawalCodeExpiredResponse')
          ..add('withdrawalCodes', withdrawalCodes))
        .toString();
  }
}

class ListWithdrawalCodeExpiredResponseBuilder
    implements
        Builder<ListWithdrawalCodeExpiredResponse,
            ListWithdrawalCodeExpiredResponseBuilder> {
  _$ListWithdrawalCodeExpiredResponse? _$v;

  ListBuilder<WithdrawalCodeResponse>? _withdrawalCodes;
  ListBuilder<WithdrawalCodeResponse> get withdrawalCodes =>
      _$this._withdrawalCodes ??= ListBuilder<WithdrawalCodeResponse>();
  set withdrawalCodes(ListBuilder<WithdrawalCodeResponse>? withdrawalCodes) =>
      _$this._withdrawalCodes = withdrawalCodes;

  ListWithdrawalCodeExpiredResponseBuilder() {
    ListWithdrawalCodeExpiredResponse._defaults(this);
  }

  ListWithdrawalCodeExpiredResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _withdrawalCodes = $v.withdrawalCodes?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListWithdrawalCodeExpiredResponse other) {
    _$v = other as _$ListWithdrawalCodeExpiredResponse;
  }

  @override
  void update(
      void Function(ListWithdrawalCodeExpiredResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListWithdrawalCodeExpiredResponse build() => _build();

  _$ListWithdrawalCodeExpiredResponse _build() {
    _$ListWithdrawalCodeExpiredResponse _$result;
    try {
      _$result = _$v ??
          _$ListWithdrawalCodeExpiredResponse._(
            withdrawalCodes: _withdrawalCodes?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'withdrawalCodes';
        _withdrawalCodes?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ListWithdrawalCodeExpiredResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
