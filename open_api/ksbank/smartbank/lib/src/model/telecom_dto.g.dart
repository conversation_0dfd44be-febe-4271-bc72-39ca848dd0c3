// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'telecom_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TelecomDto extends TelecomDto {
  @override
  final String? teleCode;
  @override
  final String? teleName;
  @override
  final String? icon;

  factory _$TelecomDto([void Function(TelecomDtoBuilder)? updates]) =>
      (TelecomDtoBuilder()..update(updates))._build();

  _$TelecomDto._({this.teleCode, this.teleName, this.icon}) : super._();
  @override
  TelecomDto rebuild(void Function(TelecomDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TelecomDtoBuilder toBuilder() => TelecomDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TelecomDto &&
        teleCode == other.teleCode &&
        teleName == other.teleName &&
        icon == other.icon;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, teleCode.hashCode);
    _$hash = $jc(_$hash, teleName.hashCode);
    _$hash = $jc(_$hash, icon.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TelecomDto')
          ..add('teleCode', teleCode)
          ..add('teleName', teleName)
          ..add('icon', icon))
        .toString();
  }
}

class TelecomDtoBuilder implements Builder<TelecomDto, TelecomDtoBuilder> {
  _$TelecomDto? _$v;

  String? _teleCode;
  String? get teleCode => _$this._teleCode;
  set teleCode(String? teleCode) => _$this._teleCode = teleCode;

  String? _teleName;
  String? get teleName => _$this._teleName;
  set teleName(String? teleName) => _$this._teleName = teleName;

  String? _icon;
  String? get icon => _$this._icon;
  set icon(String? icon) => _$this._icon = icon;

  TelecomDtoBuilder() {
    TelecomDto._defaults(this);
  }

  TelecomDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _teleCode = $v.teleCode;
      _teleName = $v.teleName;
      _icon = $v.icon;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TelecomDto other) {
    _$v = other as _$TelecomDto;
  }

  @override
  void update(void Function(TelecomDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TelecomDto build() => _build();

  _$TelecomDto _build() {
    final _$result = _$v ??
        _$TelecomDto._(
          teleCode: teleCode,
          teleName: teleName,
          icon: icon,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
