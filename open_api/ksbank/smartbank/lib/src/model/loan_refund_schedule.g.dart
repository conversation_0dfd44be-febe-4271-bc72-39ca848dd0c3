// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_refund_schedule.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoanRefundSchedule extends LoanRefundSchedule {
  @override
  final DateTime? createdAt;
  @override
  final String? createdBy;
  @override
  final DateTime? updatedAt;
  @override
  final String? updatedBy;
  @override
  final String? id;
  @override
  final String? loanId;
  @override
  final double? amount;
  @override
  final DateTime? refundDate;

  factory _$LoanRefundSchedule(
          [void Function(LoanRefundScheduleBuilder)? updates]) =>
      (LoanRefundScheduleBuilder()..update(updates))._build();

  _$LoanRefundSchedule._(
      {this.createdAt,
      this.createdBy,
      this.updatedAt,
      this.updatedBy,
      this.id,
      this.loanId,
      this.amount,
      this.refundDate})
      : super._();
  @override
  LoanRefundSchedule rebuild(
          void Function(LoanRefundScheduleBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanRefundScheduleBuilder toBuilder() =>
      LoanRefundScheduleBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanRefundSchedule &&
        createdAt == other.createdAt &&
        createdBy == other.createdBy &&
        updatedAt == other.updatedAt &&
        updatedBy == other.updatedBy &&
        id == other.id &&
        loanId == other.loanId &&
        amount == other.amount &&
        refundDate == other.refundDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, createdAt.hashCode);
    _$hash = $jc(_$hash, createdBy.hashCode);
    _$hash = $jc(_$hash, updatedAt.hashCode);
    _$hash = $jc(_$hash, updatedBy.hashCode);
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, loanId.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, refundDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanRefundSchedule')
          ..add('createdAt', createdAt)
          ..add('createdBy', createdBy)
          ..add('updatedAt', updatedAt)
          ..add('updatedBy', updatedBy)
          ..add('id', id)
          ..add('loanId', loanId)
          ..add('amount', amount)
          ..add('refundDate', refundDate))
        .toString();
  }
}

class LoanRefundScheduleBuilder
    implements Builder<LoanRefundSchedule, LoanRefundScheduleBuilder> {
  _$LoanRefundSchedule? _$v;

  DateTime? _createdAt;
  DateTime? get createdAt => _$this._createdAt;
  set createdAt(DateTime? createdAt) => _$this._createdAt = createdAt;

  String? _createdBy;
  String? get createdBy => _$this._createdBy;
  set createdBy(String? createdBy) => _$this._createdBy = createdBy;

  DateTime? _updatedAt;
  DateTime? get updatedAt => _$this._updatedAt;
  set updatedAt(DateTime? updatedAt) => _$this._updatedAt = updatedAt;

  String? _updatedBy;
  String? get updatedBy => _$this._updatedBy;
  set updatedBy(String? updatedBy) => _$this._updatedBy = updatedBy;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _loanId;
  String? get loanId => _$this._loanId;
  set loanId(String? loanId) => _$this._loanId = loanId;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  DateTime? _refundDate;
  DateTime? get refundDate => _$this._refundDate;
  set refundDate(DateTime? refundDate) => _$this._refundDate = refundDate;

  LoanRefundScheduleBuilder() {
    LoanRefundSchedule._defaults(this);
  }

  LoanRefundScheduleBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _createdAt = $v.createdAt;
      _createdBy = $v.createdBy;
      _updatedAt = $v.updatedAt;
      _updatedBy = $v.updatedBy;
      _id = $v.id;
      _loanId = $v.loanId;
      _amount = $v.amount;
      _refundDate = $v.refundDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanRefundSchedule other) {
    _$v = other as _$LoanRefundSchedule;
  }

  @override
  void update(void Function(LoanRefundScheduleBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanRefundSchedule build() => _build();

  _$LoanRefundSchedule _build() {
    final _$result = _$v ??
        _$LoanRefundSchedule._(
          createdAt: createdAt,
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          id: id,
          loanId: loanId,
          amount: amount,
          refundDate: refundDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
