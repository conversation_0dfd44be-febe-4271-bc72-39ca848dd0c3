// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lounge_items.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoungeItems extends LoungeItems {
  @override
  final String? id;
  @override
  final String? airportName;
  @override
  final String? loungeName;
  @override
  final String? address;
  @override
  final String? childPolicy;

  factory _$LoungeItems([void Function(LoungeItemsBuilder)? updates]) =>
      (LoungeItemsBuilder()..update(updates))._build();

  _$LoungeItems._(
      {this.id,
      this.airportName,
      this.loungeName,
      this.address,
      this.childPolicy})
      : super._();
  @override
  LoungeItems rebuild(void Function(LoungeItemsBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoungeItemsBuilder toBuilder() => LoungeItemsBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoungeItems &&
        id == other.id &&
        airportName == other.airportName &&
        loungeName == other.loungeName &&
        address == other.address &&
        childPolicy == other.childPolicy;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, airportName.hashCode);
    _$hash = $jc(_$hash, loungeName.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, childPolicy.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoungeItems')
          ..add('id', id)
          ..add('airportName', airportName)
          ..add('loungeName', loungeName)
          ..add('address', address)
          ..add('childPolicy', childPolicy))
        .toString();
  }
}

class LoungeItemsBuilder implements Builder<LoungeItems, LoungeItemsBuilder> {
  _$LoungeItems? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _airportName;
  String? get airportName => _$this._airportName;
  set airportName(String? airportName) => _$this._airportName = airportName;

  String? _loungeName;
  String? get loungeName => _$this._loungeName;
  set loungeName(String? loungeName) => _$this._loungeName = loungeName;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  String? _childPolicy;
  String? get childPolicy => _$this._childPolicy;
  set childPolicy(String? childPolicy) => _$this._childPolicy = childPolicy;

  LoungeItemsBuilder() {
    LoungeItems._defaults(this);
  }

  LoungeItemsBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _airportName = $v.airportName;
      _loungeName = $v.loungeName;
      _address = $v.address;
      _childPolicy = $v.childPolicy;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoungeItems other) {
    _$v = other as _$LoungeItems;
  }

  @override
  void update(void Function(LoungeItemsBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoungeItems build() => _build();

  _$LoungeItems _build() {
    final _$result = _$v ??
        _$LoungeItems._(
          id: id,
          airportName: airportName,
          loungeName: loungeName,
          address: address,
          childPolicy: childPolicy,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
