// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_categories_type.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const TransactionCategoriesType _$SAVINGS =
    const TransactionCategoriesType._('SAVINGS');
const TransactionCategoriesType _$CREATE_TARGET_SAVINGS_ACCOUNT =
    const TransactionCategoriesType._('CREATE_TARGET_SAVINGS_ACCOUNT');
const TransactionCategoriesType _$DEPOSIT_TARGET_SAVING =
    const TransactionCategoriesType._('DEPOSIT_TARGET_SAVING');
const TransactionCategoriesType _$WITHDRAWAL_TARGET_SAVING =
    const TransactionCategoriesType._('WITHDRAWAL_TARGET_SAVING');
const TransactionCategoriesType _$CLOSE_TARGET_SAVING =
    const TransactionCategoriesType._('CLOSE_TARGET_SAVING');
const TransactionCategoriesType _$CLOSE_SAVING =
    const TransactionCategoriesType._('CLOSE_SAVING');
const TransactionCategoriesType _$LOAN_CREDIT =
    const TransactionCategoriesType._('LOAN_CREDIT');
const TransactionCategoriesType _$TRANSFER_INTERNAL =
    const TransactionCategoriesType._('TRANSFER_INTERNAL');
const TransactionCategoriesType _$TRANSFER_INTERNAL_SAME_CIF =
    const TransactionCategoriesType._('TRANSFER_INTERNAL_SAME_CIF');
const TransactionCategoriesType _$TRANSFER_INTERNAL_DIFF_CIF =
    const TransactionCategoriesType._('TRANSFER_INTERNAL_DIFF_CIF');
const TransactionCategoriesType _$TRANSFER_EXTERNAL_VIA_NAPAS =
    const TransactionCategoriesType._('TRANSFER_EXTERNAL_VIA_NAPAS');
const TransactionCategoriesType _$TRANSFER_EXTERNAL_VIA_CITAD =
    const TransactionCategoriesType._('TRANSFER_EXTERNAL_VIA_CITAD');
const TransactionCategoriesType _$PAY_BILLS =
    const TransactionCategoriesType._('PAY_BILLS');
const TransactionCategoriesType _$PAY_BILLS_FIRE =
    const TransactionCategoriesType._('PAY_BILLS_FIRE');
const TransactionCategoriesType _$PAY_TOPUP =
    const TransactionCategoriesType._('PAY_TOPUP');
const TransactionCategoriesType _$PAY_CREDIT_CARD =
    const TransactionCategoriesType._('PAY_CREDIT_CARD');
const TransactionCategoriesType _$OPEN_VIP_ACCOUNT =
    const TransactionCategoriesType._('OPEN_VIP_ACCOUNT');
const TransactionCategoriesType _$REGISTER_PACKAGE_MYSHOP =
    const TransactionCategoriesType._('REGISTER_PACKAGE_MYSHOP');

TransactionCategoriesType _$valueOf(String name) {
  switch (name) {
    case 'SAVINGS':
      return _$SAVINGS;
    case 'CREATE_TARGET_SAVINGS_ACCOUNT':
      return _$CREATE_TARGET_SAVINGS_ACCOUNT;
    case 'DEPOSIT_TARGET_SAVING':
      return _$DEPOSIT_TARGET_SAVING;
    case 'WITHDRAWAL_TARGET_SAVING':
      return _$WITHDRAWAL_TARGET_SAVING;
    case 'CLOSE_TARGET_SAVING':
      return _$CLOSE_TARGET_SAVING;
    case 'CLOSE_SAVING':
      return _$CLOSE_SAVING;
    case 'LOAN_CREDIT':
      return _$LOAN_CREDIT;
    case 'TRANSFER_INTERNAL':
      return _$TRANSFER_INTERNAL;
    case 'TRANSFER_INTERNAL_SAME_CIF':
      return _$TRANSFER_INTERNAL_SAME_CIF;
    case 'TRANSFER_INTERNAL_DIFF_CIF':
      return _$TRANSFER_INTERNAL_DIFF_CIF;
    case 'TRANSFER_EXTERNAL_VIA_NAPAS':
      return _$TRANSFER_EXTERNAL_VIA_NAPAS;
    case 'TRANSFER_EXTERNAL_VIA_CITAD':
      return _$TRANSFER_EXTERNAL_VIA_CITAD;
    case 'PAY_BILLS':
      return _$PAY_BILLS;
    case 'PAY_BILLS_FIRE':
      return _$PAY_BILLS_FIRE;
    case 'PAY_TOPUP':
      return _$PAY_TOPUP;
    case 'PAY_CREDIT_CARD':
      return _$PAY_CREDIT_CARD;
    case 'OPEN_VIP_ACCOUNT':
      return _$OPEN_VIP_ACCOUNT;
    case 'REGISTER_PACKAGE_MYSHOP':
      return _$REGISTER_PACKAGE_MYSHOP;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<TransactionCategoriesType> _$values =
    BuiltSet<TransactionCategoriesType>(const <TransactionCategoriesType>[
  _$SAVINGS,
  _$CREATE_TARGET_SAVINGS_ACCOUNT,
  _$DEPOSIT_TARGET_SAVING,
  _$WITHDRAWAL_TARGET_SAVING,
  _$CLOSE_TARGET_SAVING,
  _$CLOSE_SAVING,
  _$LOAN_CREDIT,
  _$TRANSFER_INTERNAL,
  _$TRANSFER_INTERNAL_SAME_CIF,
  _$TRANSFER_INTERNAL_DIFF_CIF,
  _$TRANSFER_EXTERNAL_VIA_NAPAS,
  _$TRANSFER_EXTERNAL_VIA_CITAD,
  _$PAY_BILLS,
  _$PAY_BILLS_FIRE,
  _$PAY_TOPUP,
  _$PAY_CREDIT_CARD,
  _$OPEN_VIP_ACCOUNT,
  _$REGISTER_PACKAGE_MYSHOP,
]);

class _$TransactionCategoriesTypeMeta {
  const _$TransactionCategoriesTypeMeta();
  TransactionCategoriesType get SAVINGS => _$SAVINGS;
  TransactionCategoriesType get CREATE_TARGET_SAVINGS_ACCOUNT =>
      _$CREATE_TARGET_SAVINGS_ACCOUNT;
  TransactionCategoriesType get DEPOSIT_TARGET_SAVING =>
      _$DEPOSIT_TARGET_SAVING;
  TransactionCategoriesType get WITHDRAWAL_TARGET_SAVING =>
      _$WITHDRAWAL_TARGET_SAVING;
  TransactionCategoriesType get CLOSE_TARGET_SAVING => _$CLOSE_TARGET_SAVING;
  TransactionCategoriesType get CLOSE_SAVING => _$CLOSE_SAVING;
  TransactionCategoriesType get LOAN_CREDIT => _$LOAN_CREDIT;
  TransactionCategoriesType get TRANSFER_INTERNAL => _$TRANSFER_INTERNAL;
  TransactionCategoriesType get TRANSFER_INTERNAL_SAME_CIF =>
      _$TRANSFER_INTERNAL_SAME_CIF;
  TransactionCategoriesType get TRANSFER_INTERNAL_DIFF_CIF =>
      _$TRANSFER_INTERNAL_DIFF_CIF;
  TransactionCategoriesType get TRANSFER_EXTERNAL_VIA_NAPAS =>
      _$TRANSFER_EXTERNAL_VIA_NAPAS;
  TransactionCategoriesType get TRANSFER_EXTERNAL_VIA_CITAD =>
      _$TRANSFER_EXTERNAL_VIA_CITAD;
  TransactionCategoriesType get PAY_BILLS => _$PAY_BILLS;
  TransactionCategoriesType get PAY_BILLS_FIRE => _$PAY_BILLS_FIRE;
  TransactionCategoriesType get PAY_TOPUP => _$PAY_TOPUP;
  TransactionCategoriesType get PAY_CREDIT_CARD => _$PAY_CREDIT_CARD;
  TransactionCategoriesType get OPEN_VIP_ACCOUNT => _$OPEN_VIP_ACCOUNT;
  TransactionCategoriesType get REGISTER_PACKAGE_MYSHOP =>
      _$REGISTER_PACKAGE_MYSHOP;
  TransactionCategoriesType valueOf(String name) => _$valueOf(name);
  BuiltSet<TransactionCategoriesType> get values => _$values;
}

abstract class _$TransactionCategoriesTypeMixin {
  // ignore: non_constant_identifier_names
  _$TransactionCategoriesTypeMeta get TransactionCategoriesType =>
      const _$TransactionCategoriesTypeMeta();
}

Serializer<TransactionCategoriesType> _$transactionCategoriesTypeSerializer =
    _$TransactionCategoriesTypeSerializer();

class _$TransactionCategoriesTypeSerializer
    implements PrimitiveSerializer<TransactionCategoriesType> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'SAVINGS': 'SAVINGS',
    'CREATE_TARGET_SAVINGS_ACCOUNT': 'CREATE_TARGET_SAVINGS_ACCOUNT',
    'DEPOSIT_TARGET_SAVING': 'DEPOSIT_TARGET_SAVING',
    'WITHDRAWAL_TARGET_SAVING': 'WITHDRAWAL_TARGET_SAVING',
    'CLOSE_TARGET_SAVING': 'CLOSE_TARGET_SAVING',
    'CLOSE_SAVING': 'CLOSE_SAVING',
    'LOAN_CREDIT': 'LOAN_CREDIT',
    'TRANSFER_INTERNAL': 'TRANSFER_INTERNAL',
    'TRANSFER_INTERNAL_SAME_CIF': 'TRANSFER_INTERNAL_SAME_CIF',
    'TRANSFER_INTERNAL_DIFF_CIF': 'TRANSFER_INTERNAL_DIFF_CIF',
    'TRANSFER_EXTERNAL_VIA_NAPAS': 'TRANSFER_EXTERNAL_VIA_NAPAS',
    'TRANSFER_EXTERNAL_VIA_CITAD': 'TRANSFER_EXTERNAL_VIA_CITAD',
    'PAY_BILLS': 'PAY_BILLS',
    'PAY_BILLS_FIRE': 'PAY_BILLS_FIRE',
    'PAY_TOPUP': 'PAY_TOPUP',
    'PAY_CREDIT_CARD': 'PAY_CREDIT_CARD',
    'OPEN_VIP_ACCOUNT': 'OPEN_VIP_ACCOUNT',
    'REGISTER_PACKAGE_MYSHOP': 'REGISTER_PACKAGE_MYSHOP',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'SAVINGS': 'SAVINGS',
    'CREATE_TARGET_SAVINGS_ACCOUNT': 'CREATE_TARGET_SAVINGS_ACCOUNT',
    'DEPOSIT_TARGET_SAVING': 'DEPOSIT_TARGET_SAVING',
    'WITHDRAWAL_TARGET_SAVING': 'WITHDRAWAL_TARGET_SAVING',
    'CLOSE_TARGET_SAVING': 'CLOSE_TARGET_SAVING',
    'CLOSE_SAVING': 'CLOSE_SAVING',
    'LOAN_CREDIT': 'LOAN_CREDIT',
    'TRANSFER_INTERNAL': 'TRANSFER_INTERNAL',
    'TRANSFER_INTERNAL_SAME_CIF': 'TRANSFER_INTERNAL_SAME_CIF',
    'TRANSFER_INTERNAL_DIFF_CIF': 'TRANSFER_INTERNAL_DIFF_CIF',
    'TRANSFER_EXTERNAL_VIA_NAPAS': 'TRANSFER_EXTERNAL_VIA_NAPAS',
    'TRANSFER_EXTERNAL_VIA_CITAD': 'TRANSFER_EXTERNAL_VIA_CITAD',
    'PAY_BILLS': 'PAY_BILLS',
    'PAY_BILLS_FIRE': 'PAY_BILLS_FIRE',
    'PAY_TOPUP': 'PAY_TOPUP',
    'PAY_CREDIT_CARD': 'PAY_CREDIT_CARD',
    'OPEN_VIP_ACCOUNT': 'OPEN_VIP_ACCOUNT',
    'REGISTER_PACKAGE_MYSHOP': 'REGISTER_PACKAGE_MYSHOP',
  };

  @override
  final Iterable<Type> types = const <Type>[TransactionCategoriesType];
  @override
  final String wireName = 'TransactionCategoriesType';

  @override
  Object serialize(Serializers serializers, TransactionCategoriesType object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  TransactionCategoriesType deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      TransactionCategoriesType.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
