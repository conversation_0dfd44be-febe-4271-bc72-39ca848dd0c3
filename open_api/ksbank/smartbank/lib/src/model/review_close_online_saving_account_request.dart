//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_close_online_saving_account_request.g.dart';

/// ReviewCloseOnlineSavingAccountRequest
///
/// Properties:
/// * [bankCif] - Cif no của khách hàng
/// * [transactionNo] - Số giao dịch
/// * [accountNumber] - S<PERSON> tài khoản
/// * [accountName] - Tên tài khoản
/// * [balance] - Số tiền
/// * [currency] - Loại tiền: VND, USD
/// * [rate] - Lãi suất
/// * [contractDate] - <PERSON><PERSON>y tài khoản được mở. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [dueDate] - <PERSON><PERSON>y tài khoản tất toán. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [finalAmount] - Số tiền sau khi tính lãi suất
/// * [destinationAccount] - Số tài khoản nhận tiền sau khi tất toán
@BuiltValue()
abstract class ReviewCloseOnlineSavingAccountRequest
    implements
        Built<ReviewCloseOnlineSavingAccountRequest,
            ReviewCloseOnlineSavingAccountRequestBuilder> {
  /// Cif no của khách hàng
  @BuiltValueField(wireName: r'bankCif')
  String? get bankCif;

  /// Số giao dịch
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số tài khoản
  @BuiltValueField(wireName: r'accountNumber')
  String? get accountNumber;

  /// Tên tài khoản
  @BuiltValueField(wireName: r'accountName')
  String? get accountName;

  /// Số tiền
  @BuiltValueField(wireName: r'balance')
  double? get balance;

  /// Loại tiền: VND, USD
  @BuiltValueField(wireName: r'currency')
  ReviewCloseOnlineSavingAccountRequestCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  /// Lãi suất
  @BuiltValueField(wireName: r'rate')
  double? get rate;

  /// Ngày tài khoản được mở. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'contractDate')
  DateTime? get contractDate;

  /// Ngày tài khoản tất toán. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'dueDate')
  DateTime? get dueDate;

  /// Số tiền sau khi tính lãi suất
  @BuiltValueField(wireName: r'finalAmount')
  double? get finalAmount;

  /// Số tài khoản nhận tiền sau khi tất toán
  @BuiltValueField(wireName: r'destinationAccount')
  String? get destinationAccount;

  ReviewCloseOnlineSavingAccountRequest._();

  factory ReviewCloseOnlineSavingAccountRequest(
          [void updates(ReviewCloseOnlineSavingAccountRequestBuilder b)]) =
      _$ReviewCloseOnlineSavingAccountRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewCloseOnlineSavingAccountRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewCloseOnlineSavingAccountRequest> get serializer =>
      _$ReviewCloseOnlineSavingAccountRequestSerializer();
}

class _$ReviewCloseOnlineSavingAccountRequestSerializer
    implements PrimitiveSerializer<ReviewCloseOnlineSavingAccountRequest> {
  @override
  final Iterable<Type> types = const [
    ReviewCloseOnlineSavingAccountRequest,
    _$ReviewCloseOnlineSavingAccountRequest
  ];

  @override
  final String wireName = r'ReviewCloseOnlineSavingAccountRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewCloseOnlineSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.bankCif != null) {
      yield r'bankCif';
      yield serializers.serialize(
        object.bankCif,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNumber != null) {
      yield r'accountNumber';
      yield serializers.serialize(
        object.accountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountName != null) {
      yield r'accountName';
      yield serializers.serialize(
        object.accountName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.balance != null) {
      yield r'balance';
      yield serializers.serialize(
        object.balance,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType: const FullType.nullable(
            ReviewCloseOnlineSavingAccountRequestCurrencyEnum),
      );
    }
    if (object.rate != null) {
      yield r'rate';
      yield serializers.serialize(
        object.rate,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.contractDate != null) {
      yield r'contractDate';
      yield serializers.serialize(
        object.contractDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.dueDate != null) {
      yield r'dueDate';
      yield serializers.serialize(
        object.dueDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.finalAmount != null) {
      yield r'finalAmount';
      yield serializers.serialize(
        object.finalAmount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.destinationAccount != null) {
      yield r'destinationAccount';
      yield serializers.serialize(
        object.destinationAccount,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewCloseOnlineSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewCloseOnlineSavingAccountRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'bankCif':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCif = valueDes;
          break;
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'accountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNumber = valueDes;
          break;
        case r'accountName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountName = valueDes;
          break;
        case r'balance':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.balance = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                ReviewCloseOnlineSavingAccountRequestCurrencyEnum),
          ) as ReviewCloseOnlineSavingAccountRequestCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        case r'rate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.rate = valueDes;
          break;
        case r'contractDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.contractDate = valueDes;
          break;
        case r'dueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.dueDate = valueDes;
          break;
        case r'finalAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.finalAmount = valueDes;
          break;
        case r'destinationAccount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.destinationAccount = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewCloseOnlineSavingAccountRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewCloseOnlineSavingAccountRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class ReviewCloseOnlineSavingAccountRequestCurrencyEnum extends EnumClass {
  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'VND')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum VND =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_VND;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'USD')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum USD =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_USD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'ACB')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum ACB =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_ACB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'JPY')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum JPY =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_JPY;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum GOLD =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GOLD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'EUR')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum EUR =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_EUR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GBP')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum GBP =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GBP;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CHF')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum CHF =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CHF;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'AUD')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum AUD =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_AUD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CAD')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum CAD =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CAD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SGD')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum SGD =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SGD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'THB')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum THB =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_THB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NOK')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum NOK =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NOK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NZD')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum NZD =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NZD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'DKK')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum DKK =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_DKK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'HKD')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum HKD =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_HKD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SEK')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum SEK =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SEK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MYR')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum MYR =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MYR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'XAU')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum XAU =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_XAU;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MMK')
  static const ReviewCloseOnlineSavingAccountRequestCurrencyEnum MMK =
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MMK;

  static Serializer<ReviewCloseOnlineSavingAccountRequestCurrencyEnum>
      get serializer =>
          _$reviewCloseOnlineSavingAccountRequestCurrencyEnumSerializer;

  const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._(String name)
      : super(name);

  static BuiltSet<ReviewCloseOnlineSavingAccountRequestCurrencyEnum>
      get values => _$reviewCloseOnlineSavingAccountRequestCurrencyEnumValues;
  static ReviewCloseOnlineSavingAccountRequestCurrencyEnum valueOf(
          String name) =>
      _$reviewCloseOnlineSavingAccountRequestCurrencyEnumValueOf(name);
}
