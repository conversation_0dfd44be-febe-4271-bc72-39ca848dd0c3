// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_create_schedule_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const ReviewCreateScheduleRequestProviderEnum
    _$reviewCreateScheduleRequestProviderEnum_PAYOO =
    const ReviewCreateScheduleRequestProviderEnum._('PAYOO');

ReviewCreateScheduleRequestProviderEnum
    _$reviewCreateScheduleRequestProviderEnumValueOf(String name) {
  switch (name) {
    case 'PAYOO':
      return _$reviewCreateScheduleRequestProviderEnum_PAYOO;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<ReviewCreateScheduleRequestProviderEnum>
    _$reviewCreateScheduleRequestProviderEnumValues = BuiltSet<
        ReviewCreateScheduleRequestProviderEnum>(const <ReviewCreateScheduleRequestProviderEnum>[
  _$reviewCreateScheduleRequestProviderEnum_PAYOO,
]);

Serializer<ReviewCreateScheduleRequestProviderEnum>
    _$reviewCreateScheduleRequestProviderEnumSerializer =
    _$ReviewCreateScheduleRequestProviderEnumSerializer();

class _$ReviewCreateScheduleRequestProviderEnumSerializer
    implements PrimitiveSerializer<ReviewCreateScheduleRequestProviderEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PAYOO': 'PAYOO',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PAYOO': 'PAYOO',
  };

  @override
  final Iterable<Type> types = const <Type>[
    ReviewCreateScheduleRequestProviderEnum
  ];
  @override
  final String wireName = 'ReviewCreateScheduleRequestProviderEnum';

  @override
  Object serialize(Serializers serializers,
          ReviewCreateScheduleRequestProviderEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  ReviewCreateScheduleRequestProviderEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      ReviewCreateScheduleRequestProviderEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$ReviewCreateScheduleRequest extends ReviewCreateScheduleRequest {
  @override
  final ReviewCreateScheduleRequestProviderEnum? provider;
  @override
  final String? supplierId;
  @override
  final String? customerCode;
  @override
  final String? accountNo;

  factory _$ReviewCreateScheduleRequest(
          [void Function(ReviewCreateScheduleRequestBuilder)? updates]) =>
      (ReviewCreateScheduleRequestBuilder()..update(updates))._build();

  _$ReviewCreateScheduleRequest._(
      {this.provider, this.supplierId, this.customerCode, this.accountNo})
      : super._();
  @override
  ReviewCreateScheduleRequest rebuild(
          void Function(ReviewCreateScheduleRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCreateScheduleRequestBuilder toBuilder() =>
      ReviewCreateScheduleRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCreateScheduleRequest &&
        provider == other.provider &&
        supplierId == other.supplierId &&
        customerCode == other.customerCode &&
        accountNo == other.accountNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, provider.hashCode);
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewCreateScheduleRequest')
          ..add('provider', provider)
          ..add('supplierId', supplierId)
          ..add('customerCode', customerCode)
          ..add('accountNo', accountNo))
        .toString();
  }
}

class ReviewCreateScheduleRequestBuilder
    implements
        Builder<ReviewCreateScheduleRequest,
            ReviewCreateScheduleRequestBuilder> {
  _$ReviewCreateScheduleRequest? _$v;

  ReviewCreateScheduleRequestProviderEnum? _provider;
  ReviewCreateScheduleRequestProviderEnum? get provider => _$this._provider;
  set provider(ReviewCreateScheduleRequestProviderEnum? provider) =>
      _$this._provider = provider;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  ReviewCreateScheduleRequestBuilder() {
    ReviewCreateScheduleRequest._defaults(this);
  }

  ReviewCreateScheduleRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _provider = $v.provider;
      _supplierId = $v.supplierId;
      _customerCode = $v.customerCode;
      _accountNo = $v.accountNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCreateScheduleRequest other) {
    _$v = other as _$ReviewCreateScheduleRequest;
  }

  @override
  void update(void Function(ReviewCreateScheduleRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCreateScheduleRequest build() => _build();

  _$ReviewCreateScheduleRequest _build() {
    final _$result = _$v ??
        _$ReviewCreateScheduleRequest._(
          provider: provider,
          supplierId: supplierId,
          customerCode: customerCode,
          accountNo: accountNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
