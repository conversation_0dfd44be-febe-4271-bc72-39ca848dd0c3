//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/verify_soft_otp_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'open_online_saving_account_request.g.dart';

/// OpenOnlineSavingAccountRequest
///
/// Properties:
/// * [bankCif] - Cif no của khách hàng
/// * [transactionNo] - Số giao dịch , số này mobile app tự sinh, có độ dài là 10
/// * [accountNumber] - Số tài khoản
/// * [amount] - Số tiền
/// * [currency] - Loại tiền: VND, USD
/// * [termId] - Id của kì hạn
/// * [rate] - <PERSON><PERSON><PERSON> suất
/// * [contractDate] - <PERSON><PERSON>y tài khoản được mở. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [dueDate] - Ngày tài khoản tất toán. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [finalTypeId] - Id của phương thức tất toán
/// * [finalTypeName] - Tên của phương thức tất toán
/// * [finalAccountNumber] - Số tài khoản nhận tiền sau khi tất toán
/// * [interestAmountEndOfTerm] - Số tiền lãi cuối kỳ
/// * [referralCode] - Mã giới thiệu (nếu có)
/// * [verifySoftOtpData]
@BuiltValue()
abstract class OpenOnlineSavingAccountRequest
    implements
        Built<OpenOnlineSavingAccountRequest,
            OpenOnlineSavingAccountRequestBuilder> {
  /// Cif no của khách hàng
  @BuiltValueField(wireName: r'bankCif')
  String? get bankCif;

  /// Số giao dịch , số này mobile app tự sinh, có độ dài là 10
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Số tài khoản
  @BuiltValueField(wireName: r'accountNumber')
  String? get accountNumber;

  /// Số tiền
  @BuiltValueField(wireName: r'amount')
  double? get amount;

  /// Loại tiền: VND, USD
  @BuiltValueField(wireName: r'currency')
  OpenOnlineSavingAccountRequestCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  /// Id của kì hạn
  @BuiltValueField(wireName: r'termId')
  String? get termId;

  /// Lãi suất
  @BuiltValueField(wireName: r'rate')
  double? get rate;

  /// Ngày tài khoản được mở. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'contractDate')
  DateTime? get contractDate;

  /// Ngày tài khoản tất toán. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'dueDate')
  DateTime? get dueDate;

  /// Id của phương thức tất toán
  @BuiltValueField(wireName: r'finalTypeId')
  int? get finalTypeId;

  /// Tên của phương thức tất toán
  @BuiltValueField(wireName: r'finalTypeName')
  String? get finalTypeName;

  /// Số tài khoản nhận tiền sau khi tất toán
  @BuiltValueField(wireName: r'finalAccountNumber')
  String? get finalAccountNumber;

  /// Số tiền lãi cuối kỳ
  @BuiltValueField(wireName: r'interestAmountEndOfTerm')
  num? get interestAmountEndOfTerm;

  /// Mã giới thiệu (nếu có)
  @BuiltValueField(wireName: r'referralCode')
  String? get referralCode;

  @BuiltValueField(wireName: r'verifySoftOtpData')
  VerifySoftOtpRequest? get verifySoftOtpData;

  OpenOnlineSavingAccountRequest._();

  factory OpenOnlineSavingAccountRequest(
          [void updates(OpenOnlineSavingAccountRequestBuilder b)]) =
      _$OpenOnlineSavingAccountRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(OpenOnlineSavingAccountRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<OpenOnlineSavingAccountRequest> get serializer =>
      _$OpenOnlineSavingAccountRequestSerializer();
}

class _$OpenOnlineSavingAccountRequestSerializer
    implements PrimitiveSerializer<OpenOnlineSavingAccountRequest> {
  @override
  final Iterable<Type> types = const [
    OpenOnlineSavingAccountRequest,
    _$OpenOnlineSavingAccountRequest
  ];

  @override
  final String wireName = r'OpenOnlineSavingAccountRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    OpenOnlineSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.bankCif != null) {
      yield r'bankCif';
      yield serializers.serialize(
        object.bankCif,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountNumber != null) {
      yield r'accountNumber';
      yield serializers.serialize(
        object.accountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.amount != null) {
      yield r'amount';
      yield serializers.serialize(
        object.amount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType:
            const FullType.nullable(OpenOnlineSavingAccountRequestCurrencyEnum),
      );
    }
    if (object.termId != null) {
      yield r'termId';
      yield serializers.serialize(
        object.termId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.rate != null) {
      yield r'rate';
      yield serializers.serialize(
        object.rate,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.contractDate != null) {
      yield r'contractDate';
      yield serializers.serialize(
        object.contractDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.dueDate != null) {
      yield r'dueDate';
      yield serializers.serialize(
        object.dueDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.finalTypeId != null) {
      yield r'finalTypeId';
      yield serializers.serialize(
        object.finalTypeId,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.finalTypeName != null) {
      yield r'finalTypeName';
      yield serializers.serialize(
        object.finalTypeName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.finalAccountNumber != null) {
      yield r'finalAccountNumber';
      yield serializers.serialize(
        object.finalAccountNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.interestAmountEndOfTerm != null) {
      yield r'interestAmountEndOfTerm';
      yield serializers.serialize(
        object.interestAmountEndOfTerm,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.referralCode != null) {
      yield r'referralCode';
      yield serializers.serialize(
        object.referralCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.verifySoftOtpData != null) {
      yield r'verifySoftOtpData';
      yield serializers.serialize(
        object.verifySoftOtpData,
        specifiedType: const FullType.nullable(VerifySoftOtpRequest),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    OpenOnlineSavingAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required OpenOnlineSavingAccountRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'bankCif':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bankCif = valueDes;
          break;
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'accountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNumber = valueDes;
          break;
        case r'amount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.amount = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                OpenOnlineSavingAccountRequestCurrencyEnum),
          ) as OpenOnlineSavingAccountRequestCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        case r'termId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.termId = valueDes;
          break;
        case r'rate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.rate = valueDes;
          break;
        case r'contractDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.contractDate = valueDes;
          break;
        case r'dueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.dueDate = valueDes;
          break;
        case r'finalTypeId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.finalTypeId = valueDes;
          break;
        case r'finalTypeName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.finalTypeName = valueDes;
          break;
        case r'finalAccountNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.finalAccountNumber = valueDes;
          break;
        case r'interestAmountEndOfTerm':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.interestAmountEndOfTerm = valueDes;
          break;
        case r'referralCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.referralCode = valueDes;
          break;
        case r'verifySoftOtpData':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(VerifySoftOtpRequest),
          ) as VerifySoftOtpRequest?;
          if (valueDes == null) continue;
          result.verifySoftOtpData.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  OpenOnlineSavingAccountRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = OpenOnlineSavingAccountRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class OpenOnlineSavingAccountRequestCurrencyEnum extends EnumClass {
  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'VND')
  static const OpenOnlineSavingAccountRequestCurrencyEnum VND =
      _$openOnlineSavingAccountRequestCurrencyEnum_VND;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'USD')
  static const OpenOnlineSavingAccountRequestCurrencyEnum USD =
      _$openOnlineSavingAccountRequestCurrencyEnum_USD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'ACB')
  static const OpenOnlineSavingAccountRequestCurrencyEnum ACB =
      _$openOnlineSavingAccountRequestCurrencyEnum_ACB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'JPY')
  static const OpenOnlineSavingAccountRequestCurrencyEnum JPY =
      _$openOnlineSavingAccountRequestCurrencyEnum_JPY;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const OpenOnlineSavingAccountRequestCurrencyEnum GOLD =
      _$openOnlineSavingAccountRequestCurrencyEnum_GOLD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'EUR')
  static const OpenOnlineSavingAccountRequestCurrencyEnum EUR =
      _$openOnlineSavingAccountRequestCurrencyEnum_EUR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'GBP')
  static const OpenOnlineSavingAccountRequestCurrencyEnum GBP =
      _$openOnlineSavingAccountRequestCurrencyEnum_GBP;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CHF')
  static const OpenOnlineSavingAccountRequestCurrencyEnum CHF =
      _$openOnlineSavingAccountRequestCurrencyEnum_CHF;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'AUD')
  static const OpenOnlineSavingAccountRequestCurrencyEnum AUD =
      _$openOnlineSavingAccountRequestCurrencyEnum_AUD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'CAD')
  static const OpenOnlineSavingAccountRequestCurrencyEnum CAD =
      _$openOnlineSavingAccountRequestCurrencyEnum_CAD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SGD')
  static const OpenOnlineSavingAccountRequestCurrencyEnum SGD =
      _$openOnlineSavingAccountRequestCurrencyEnum_SGD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'THB')
  static const OpenOnlineSavingAccountRequestCurrencyEnum THB =
      _$openOnlineSavingAccountRequestCurrencyEnum_THB;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NOK')
  static const OpenOnlineSavingAccountRequestCurrencyEnum NOK =
      _$openOnlineSavingAccountRequestCurrencyEnum_NOK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'NZD')
  static const OpenOnlineSavingAccountRequestCurrencyEnum NZD =
      _$openOnlineSavingAccountRequestCurrencyEnum_NZD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'DKK')
  static const OpenOnlineSavingAccountRequestCurrencyEnum DKK =
      _$openOnlineSavingAccountRequestCurrencyEnum_DKK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'HKD')
  static const OpenOnlineSavingAccountRequestCurrencyEnum HKD =
      _$openOnlineSavingAccountRequestCurrencyEnum_HKD;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'SEK')
  static const OpenOnlineSavingAccountRequestCurrencyEnum SEK =
      _$openOnlineSavingAccountRequestCurrencyEnum_SEK;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MYR')
  static const OpenOnlineSavingAccountRequestCurrencyEnum MYR =
      _$openOnlineSavingAccountRequestCurrencyEnum_MYR;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'XAU')
  static const OpenOnlineSavingAccountRequestCurrencyEnum XAU =
      _$openOnlineSavingAccountRequestCurrencyEnum_XAU;

  /// Loại tiền: VND, USD
  @BuiltValueEnumConst(wireName: r'MMK')
  static const OpenOnlineSavingAccountRequestCurrencyEnum MMK =
      _$openOnlineSavingAccountRequestCurrencyEnum_MMK;

  static Serializer<OpenOnlineSavingAccountRequestCurrencyEnum>
      get serializer => _$openOnlineSavingAccountRequestCurrencyEnumSerializer;

  const OpenOnlineSavingAccountRequestCurrencyEnum._(String name) : super(name);

  static BuiltSet<OpenOnlineSavingAccountRequestCurrencyEnum> get values =>
      _$openOnlineSavingAccountRequestCurrencyEnumValues;
  static OpenOnlineSavingAccountRequestCurrencyEnum valueOf(String name) =>
      _$openOnlineSavingAccountRequestCurrencyEnumValueOf(name);
}
