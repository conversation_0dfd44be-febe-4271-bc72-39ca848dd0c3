// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_pin_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentPinCodeResponse extends PaymentPinCodeResponse {
  @override
  final String? systemTrace;
  @override
  final String? transactionNo;
  @override
  final num? amount;
  @override
  final BuiltList<PinCodePaidDto>? cards;
  @override
  final String? description;

  factory _$PaymentPinCodeResponse(
          [void Function(PaymentPinCodeResponseBuilder)? updates]) =>
      (PaymentPinCodeResponseBuilder()..update(updates))._build();

  _$PaymentPinCodeResponse._(
      {this.systemTrace,
      this.transactionNo,
      this.amount,
      this.cards,
      this.description})
      : super._();
  @override
  PaymentPinCodeResponse rebuild(
          void Function(PaymentPinCodeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentPinCodeResponseBuilder toBuilder() =>
      PaymentPinCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentPinCodeResponse &&
        systemTrace == other.systemTrace &&
        transactionNo == other.transactionNo &&
        amount == other.amount &&
        cards == other.cards &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, systemTrace.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, cards.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentPinCodeResponse')
          ..add('systemTrace', systemTrace)
          ..add('transactionNo', transactionNo)
          ..add('amount', amount)
          ..add('cards', cards)
          ..add('description', description))
        .toString();
  }
}

class PaymentPinCodeResponseBuilder
    implements Builder<PaymentPinCodeResponse, PaymentPinCodeResponseBuilder> {
  _$PaymentPinCodeResponse? _$v;

  String? _systemTrace;
  String? get systemTrace => _$this._systemTrace;
  set systemTrace(String? systemTrace) => _$this._systemTrace = systemTrace;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  ListBuilder<PinCodePaidDto>? _cards;
  ListBuilder<PinCodePaidDto> get cards =>
      _$this._cards ??= ListBuilder<PinCodePaidDto>();
  set cards(ListBuilder<PinCodePaidDto>? cards) => _$this._cards = cards;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  PaymentPinCodeResponseBuilder() {
    PaymentPinCodeResponse._defaults(this);
  }

  PaymentPinCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _systemTrace = $v.systemTrace;
      _transactionNo = $v.transactionNo;
      _amount = $v.amount;
      _cards = $v.cards?.toBuilder();
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentPinCodeResponse other) {
    _$v = other as _$PaymentPinCodeResponse;
  }

  @override
  void update(void Function(PaymentPinCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentPinCodeResponse build() => _build();

  _$PaymentPinCodeResponse _build() {
    _$PaymentPinCodeResponse _$result;
    try {
      _$result = _$v ??
          _$PaymentPinCodeResponse._(
            systemTrace: systemTrace,
            transactionNo: transactionNo,
            amount: amount,
            cards: _cards?.build(),
            description: description,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'cards';
        _cards?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PaymentPinCodeResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
