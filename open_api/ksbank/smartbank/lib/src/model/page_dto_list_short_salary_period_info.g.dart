// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'page_dto_list_short_salary_period_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PageDtoListShortSalaryPeriodInfo
    extends PageDtoListShortSalaryPeriodInfo {
  @override
  final int? page;
  @override
  final int? size;
  @override
  final int? totalElements;
  @override
  final int? totalPages;
  @override
  final BuiltList<ShortSalaryPeriodInfo>? data;

  factory _$PageDtoListShortSalaryPeriodInfo(
          [void Function(PageDtoListShortSalaryPeriodInfoBuilder)? updates]) =>
      (PageDtoListShortSalaryPeriodInfoBuilder()..update(updates))._build();

  _$PageDtoListShortSalaryPeriodInfo._(
      {this.page, this.size, this.totalElements, this.totalPages, this.data})
      : super._();
  @override
  PageDtoListShortSalaryPeriodInfo rebuild(
          void Function(PageDtoListShortSalaryPeriodInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PageDtoListShortSalaryPeriodInfoBuilder toBuilder() =>
      PageDtoListShortSalaryPeriodInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PageDtoListShortSalaryPeriodInfo &&
        page == other.page &&
        size == other.size &&
        totalElements == other.totalElements &&
        totalPages == other.totalPages &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, page.hashCode);
    _$hash = $jc(_$hash, size.hashCode);
    _$hash = $jc(_$hash, totalElements.hashCode);
    _$hash = $jc(_$hash, totalPages.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PageDtoListShortSalaryPeriodInfo')
          ..add('page', page)
          ..add('size', size)
          ..add('totalElements', totalElements)
          ..add('totalPages', totalPages)
          ..add('data', data))
        .toString();
  }
}

class PageDtoListShortSalaryPeriodInfoBuilder
    implements
        Builder<PageDtoListShortSalaryPeriodInfo,
            PageDtoListShortSalaryPeriodInfoBuilder> {
  _$PageDtoListShortSalaryPeriodInfo? _$v;

  int? _page;
  int? get page => _$this._page;
  set page(int? page) => _$this._page = page;

  int? _size;
  int? get size => _$this._size;
  set size(int? size) => _$this._size = size;

  int? _totalElements;
  int? get totalElements => _$this._totalElements;
  set totalElements(int? totalElements) =>
      _$this._totalElements = totalElements;

  int? _totalPages;
  int? get totalPages => _$this._totalPages;
  set totalPages(int? totalPages) => _$this._totalPages = totalPages;

  ListBuilder<ShortSalaryPeriodInfo>? _data;
  ListBuilder<ShortSalaryPeriodInfo> get data =>
      _$this._data ??= ListBuilder<ShortSalaryPeriodInfo>();
  set data(ListBuilder<ShortSalaryPeriodInfo>? data) => _$this._data = data;

  PageDtoListShortSalaryPeriodInfoBuilder() {
    PageDtoListShortSalaryPeriodInfo._defaults(this);
  }

  PageDtoListShortSalaryPeriodInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _page = $v.page;
      _size = $v.size;
      _totalElements = $v.totalElements;
      _totalPages = $v.totalPages;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PageDtoListShortSalaryPeriodInfo other) {
    _$v = other as _$PageDtoListShortSalaryPeriodInfo;
  }

  @override
  void update(void Function(PageDtoListShortSalaryPeriodInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PageDtoListShortSalaryPeriodInfo build() => _build();

  _$PageDtoListShortSalaryPeriodInfo _build() {
    _$PageDtoListShortSalaryPeriodInfo _$result;
    try {
      _$result = _$v ??
          _$PageDtoListShortSalaryPeriodInfo._(
            page: page,
            size: size,
            totalElements: totalElements,
            totalPages: totalPages,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PageDtoListShortSalaryPeriodInfo', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
