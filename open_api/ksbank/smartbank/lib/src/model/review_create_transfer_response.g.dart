// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_create_transfer_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewCreateTransferResponse extends ReviewCreateTransferResponse {
  @override
  final String? responseId;
  @override
  final String? requestOrgId;
  @override
  final String? status;
  @override
  final String? datetime;
  @override
  final String? errorCode;
  @override
  final String? errorDescription;
  @override
  final String? signature;
  @override
  final String? transactionNo;
  @override
  final String? transactionDate;
  @override
  final String? responseCode;
  @override
  final String? description;
  @override
  final String? amount;
  @override
  final String? customerName;
  @override
  final String? accountName;
  @override
  final String? bankSigned;
  @override
  final String? action;
  @override
  final num? chargeAmount;
  @override
  final num? vatAmount;
  @override
  final String? transactionNumber;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewCreateTransferResponse(
          [void Function(ReviewCreateTransferResponseBuilder)? updates]) =>
      (ReviewCreateTransferResponseBuilder()..update(updates))._build();

  _$ReviewCreateTransferResponse._(
      {this.responseId,
      this.requestOrgId,
      this.status,
      this.datetime,
      this.errorCode,
      this.errorDescription,
      this.signature,
      this.transactionNo,
      this.transactionDate,
      this.responseCode,
      this.description,
      this.amount,
      this.customerName,
      this.accountName,
      this.bankSigned,
      this.action,
      this.chargeAmount,
      this.vatAmount,
      this.transactionNumber,
      this.transNextStep,
      this.content})
      : super._();
  @override
  ReviewCreateTransferResponse rebuild(
          void Function(ReviewCreateTransferResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCreateTransferResponseBuilder toBuilder() =>
      ReviewCreateTransferResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCreateTransferResponse &&
        responseId == other.responseId &&
        requestOrgId == other.requestOrgId &&
        status == other.status &&
        datetime == other.datetime &&
        errorCode == other.errorCode &&
        errorDescription == other.errorDescription &&
        signature == other.signature &&
        transactionNo == other.transactionNo &&
        transactionDate == other.transactionDate &&
        responseCode == other.responseCode &&
        description == other.description &&
        amount == other.amount &&
        customerName == other.customerName &&
        accountName == other.accountName &&
        bankSigned == other.bankSigned &&
        action == other.action &&
        chargeAmount == other.chargeAmount &&
        vatAmount == other.vatAmount &&
        transactionNumber == other.transactionNumber &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, responseId.hashCode);
    _$hash = $jc(_$hash, requestOrgId.hashCode);
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jc(_$hash, datetime.hashCode);
    _$hash = $jc(_$hash, errorCode.hashCode);
    _$hash = $jc(_$hash, errorDescription.hashCode);
    _$hash = $jc(_$hash, signature.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, responseCode.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, bankSigned.hashCode);
    _$hash = $jc(_$hash, action.hashCode);
    _$hash = $jc(_$hash, chargeAmount.hashCode);
    _$hash = $jc(_$hash, vatAmount.hashCode);
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewCreateTransferResponse')
          ..add('responseId', responseId)
          ..add('requestOrgId', requestOrgId)
          ..add('status', status)
          ..add('datetime', datetime)
          ..add('errorCode', errorCode)
          ..add('errorDescription', errorDescription)
          ..add('signature', signature)
          ..add('transactionNo', transactionNo)
          ..add('transactionDate', transactionDate)
          ..add('responseCode', responseCode)
          ..add('description', description)
          ..add('amount', amount)
          ..add('customerName', customerName)
          ..add('accountName', accountName)
          ..add('bankSigned', bankSigned)
          ..add('action', action)
          ..add('chargeAmount', chargeAmount)
          ..add('vatAmount', vatAmount)
          ..add('transactionNumber', transactionNumber)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewCreateTransferResponseBuilder
    implements
        Builder<ReviewCreateTransferResponse,
            ReviewCreateTransferResponseBuilder> {
  _$ReviewCreateTransferResponse? _$v;

  String? _responseId;
  String? get responseId => _$this._responseId;
  set responseId(String? responseId) => _$this._responseId = responseId;

  String? _requestOrgId;
  String? get requestOrgId => _$this._requestOrgId;
  set requestOrgId(String? requestOrgId) => _$this._requestOrgId = requestOrgId;

  String? _status;
  String? get status => _$this._status;
  set status(String? status) => _$this._status = status;

  String? _datetime;
  String? get datetime => _$this._datetime;
  set datetime(String? datetime) => _$this._datetime = datetime;

  String? _errorCode;
  String? get errorCode => _$this._errorCode;
  set errorCode(String? errorCode) => _$this._errorCode = errorCode;

  String? _errorDescription;
  String? get errorDescription => _$this._errorDescription;
  set errorDescription(String? errorDescription) =>
      _$this._errorDescription = errorDescription;

  String? _signature;
  String? get signature => _$this._signature;
  set signature(String? signature) => _$this._signature = signature;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _transactionDate;
  String? get transactionDate => _$this._transactionDate;
  set transactionDate(String? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _responseCode;
  String? get responseCode => _$this._responseCode;
  set responseCode(String? responseCode) => _$this._responseCode = responseCode;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _amount;
  String? get amount => _$this._amount;
  set amount(String? amount) => _$this._amount = amount;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _bankSigned;
  String? get bankSigned => _$this._bankSigned;
  set bankSigned(String? bankSigned) => _$this._bankSigned = bankSigned;

  String? _action;
  String? get action => _$this._action;
  set action(String? action) => _$this._action = action;

  num? _chargeAmount;
  num? get chargeAmount => _$this._chargeAmount;
  set chargeAmount(num? chargeAmount) => _$this._chargeAmount = chargeAmount;

  num? _vatAmount;
  num? get vatAmount => _$this._vatAmount;
  set vatAmount(num? vatAmount) => _$this._vatAmount = vatAmount;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewCreateTransferResponseBuilder() {
    ReviewCreateTransferResponse._defaults(this);
  }

  ReviewCreateTransferResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _responseId = $v.responseId;
      _requestOrgId = $v.requestOrgId;
      _status = $v.status;
      _datetime = $v.datetime;
      _errorCode = $v.errorCode;
      _errorDescription = $v.errorDescription;
      _signature = $v.signature;
      _transactionNo = $v.transactionNo;
      _transactionDate = $v.transactionDate;
      _responseCode = $v.responseCode;
      _description = $v.description;
      _amount = $v.amount;
      _customerName = $v.customerName;
      _accountName = $v.accountName;
      _bankSigned = $v.bankSigned;
      _action = $v.action;
      _chargeAmount = $v.chargeAmount;
      _vatAmount = $v.vatAmount;
      _transactionNumber = $v.transactionNumber;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCreateTransferResponse other) {
    _$v = other as _$ReviewCreateTransferResponse;
  }

  @override
  void update(void Function(ReviewCreateTransferResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCreateTransferResponse build() => _build();

  _$ReviewCreateTransferResponse _build() {
    final _$result = _$v ??
        _$ReviewCreateTransferResponse._(
          responseId: responseId,
          requestOrgId: requestOrgId,
          status: status,
          datetime: datetime,
          errorCode: errorCode,
          errorDescription: errorDescription,
          signature: signature,
          transactionNo: transactionNo,
          transactionDate: transactionDate,
          responseCode: responseCode,
          description: description,
          amount: amount,
          customerName: customerName,
          accountName: accountName,
          bankSigned: bankSigned,
          action: action,
          chargeAmount: chargeAmount,
          vatAmount: vatAmount,
          transactionNumber: transactionNumber,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
