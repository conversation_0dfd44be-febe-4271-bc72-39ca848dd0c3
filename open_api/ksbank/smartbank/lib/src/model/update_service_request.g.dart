// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_service_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateServiceRequest extends UpdateServiceRequest {
  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? icon;
  @override
  final String? code;
  @override
  final bool? active;
  @override
  final bool? isTele;

  factory _$UpdateServiceRequest(
          [void Function(UpdateServiceRequestBuilder)? updates]) =>
      (UpdateServiceRequestBuilder()..update(updates))._build();

  _$UpdateServiceRequest._(
      {this.id, this.name, this.icon, this.code, this.active, this.isTele})
      : super._();
  @override
  UpdateServiceRequest rebuild(
          void Function(UpdateServiceRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateServiceRequestBuilder toBuilder() =>
      UpdateServiceRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateServiceRequest &&
        id == other.id &&
        name == other.name &&
        icon == other.icon &&
        code == other.code &&
        active == other.active &&
        isTele == other.isTele;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, icon.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, active.hashCode);
    _$hash = $jc(_$hash, isTele.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateServiceRequest')
          ..add('id', id)
          ..add('name', name)
          ..add('icon', icon)
          ..add('code', code)
          ..add('active', active)
          ..add('isTele', isTele))
        .toString();
  }
}

class UpdateServiceRequestBuilder
    implements Builder<UpdateServiceRequest, UpdateServiceRequestBuilder> {
  _$UpdateServiceRequest? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _icon;
  String? get icon => _$this._icon;
  set icon(String? icon) => _$this._icon = icon;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  bool? _active;
  bool? get active => _$this._active;
  set active(bool? active) => _$this._active = active;

  bool? _isTele;
  bool? get isTele => _$this._isTele;
  set isTele(bool? isTele) => _$this._isTele = isTele;

  UpdateServiceRequestBuilder() {
    UpdateServiceRequest._defaults(this);
  }

  UpdateServiceRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _icon = $v.icon;
      _code = $v.code;
      _active = $v.active;
      _isTele = $v.isTele;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateServiceRequest other) {
    _$v = other as _$UpdateServiceRequest;
  }

  @override
  void update(void Function(UpdateServiceRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateServiceRequest build() => _build();

  _$UpdateServiceRequest _build() {
    final _$result = _$v ??
        _$UpdateServiceRequest._(
          id: id,
          name: name,
          icon: icon,
          code: code,
          active: active,
          isTele: isTele,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
