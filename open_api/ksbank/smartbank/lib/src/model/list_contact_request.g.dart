// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_contact_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ListContactRequest extends ListContactRequest {
  @override
  final String? cifNo;

  factory _$ListContactRequest(
          [void Function(ListContactRequestBuilder)? updates]) =>
      (ListContactRequestBuilder()..update(updates))._build();

  _$ListContactRequest._({this.cifNo}) : super._();
  @override
  ListContactRequest rebuild(
          void Function(ListContactRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ListContactRequestBuilder toBuilder() =>
      ListContactRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ListContactRequest && cifNo == other.cifNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ListContactRequest')
          ..add('cifNo', cifNo))
        .toString();
  }
}

class ListContactRequestBuilder
    implements Builder<ListContactRequest, ListContactRequestBuilder> {
  _$ListContactRequest? _$v;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  ListContactRequestBuilder() {
    ListContactRequest._defaults(this);
  }

  ListContactRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNo = $v.cifNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ListContactRequest other) {
    _$v = other as _$ListContactRequest;
  }

  @override
  void update(void Function(ListContactRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ListContactRequest build() => _build();

  _$ListContactRequest _build() {
    final _$result = _$v ??
        _$ListContactRequest._(
          cifNo: cifNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
