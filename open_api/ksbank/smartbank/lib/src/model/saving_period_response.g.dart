// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saving_period_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SavingPeriodResponse extends SavingPeriodResponse {
  @override
  final String? id;
  @override
  final String? name;
  @override
  final double? rate;

  factory _$SavingPeriodResponse(
          [void Function(SavingPeriodResponseBuilder)? updates]) =>
      (SavingPeriodResponseBuilder()..update(updates))._build();

  _$SavingPeriodResponse._({this.id, this.name, this.rate}) : super._();
  @override
  SavingPeriodResponse rebuild(
          void Function(SavingPeriodResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SavingPeriodResponseBuilder toBuilder() =>
      SavingPeriodResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SavingPeriodResponse &&
        id == other.id &&
        name == other.name &&
        rate == other.rate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SavingPeriodResponse')
          ..add('id', id)
          ..add('name', name)
          ..add('rate', rate))
        .toString();
  }
}

class SavingPeriodResponseBuilder
    implements Builder<SavingPeriodResponse, SavingPeriodResponseBuilder> {
  _$SavingPeriodResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  SavingPeriodResponseBuilder() {
    SavingPeriodResponse._defaults(this);
  }

  SavingPeriodResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _rate = $v.rate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SavingPeriodResponse other) {
    _$v = other as _$SavingPeriodResponse;
  }

  @override
  void update(void Function(SavingPeriodResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SavingPeriodResponse build() => _build();

  _$SavingPeriodResponse _build() {
    final _$result = _$v ??
        _$SavingPeriodResponse._(
          id: id,
          name: name,
          rate: rate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
