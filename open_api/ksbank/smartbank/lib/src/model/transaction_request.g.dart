// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionRequest extends TransactionRequest {
  @override
  final String? bankCif;
  @override
  final String? accountNo;
  @override
  final String? accountType;
  @override
  final String? productTypeCode;
  @override
  final String? refCardId;
  @override
  final String? cardNo;
  @override
  final String? fromDate;
  @override
  final String? toDate;

  factory _$TransactionRequest(
          [void Function(TransactionRequestBuilder)? updates]) =>
      (TransactionRequestBuilder()..update(updates))._build();

  _$TransactionRequest._(
      {this.bankCif,
      this.accountNo,
      this.accountType,
      this.productTypeCode,
      this.refCardId,
      this.cardNo,
      this.fromDate,
      this.toDate})
      : super._();
  @override
  TransactionRequest rebuild(
          void Function(TransactionRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionRequestBuilder toBuilder() =>
      TransactionRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionRequest &&
        bankCif == other.bankCif &&
        accountNo == other.accountNo &&
        accountType == other.accountType &&
        productTypeCode == other.productTypeCode &&
        refCardId == other.refCardId &&
        cardNo == other.cardNo &&
        fromDate == other.fromDate &&
        toDate == other.toDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountType.hashCode);
    _$hash = $jc(_$hash, productTypeCode.hashCode);
    _$hash = $jc(_$hash, refCardId.hashCode);
    _$hash = $jc(_$hash, cardNo.hashCode);
    _$hash = $jc(_$hash, fromDate.hashCode);
    _$hash = $jc(_$hash, toDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionRequest')
          ..add('bankCif', bankCif)
          ..add('accountNo', accountNo)
          ..add('accountType', accountType)
          ..add('productTypeCode', productTypeCode)
          ..add('refCardId', refCardId)
          ..add('cardNo', cardNo)
          ..add('fromDate', fromDate)
          ..add('toDate', toDate))
        .toString();
  }
}

class TransactionRequestBuilder
    implements Builder<TransactionRequest, TransactionRequestBuilder> {
  _$TransactionRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountType;
  String? get accountType => _$this._accountType;
  set accountType(String? accountType) => _$this._accountType = accountType;

  String? _productTypeCode;
  String? get productTypeCode => _$this._productTypeCode;
  set productTypeCode(String? productTypeCode) =>
      _$this._productTypeCode = productTypeCode;

  String? _refCardId;
  String? get refCardId => _$this._refCardId;
  set refCardId(String? refCardId) => _$this._refCardId = refCardId;

  String? _cardNo;
  String? get cardNo => _$this._cardNo;
  set cardNo(String? cardNo) => _$this._cardNo = cardNo;

  String? _fromDate;
  String? get fromDate => _$this._fromDate;
  set fromDate(String? fromDate) => _$this._fromDate = fromDate;

  String? _toDate;
  String? get toDate => _$this._toDate;
  set toDate(String? toDate) => _$this._toDate = toDate;

  TransactionRequestBuilder() {
    TransactionRequest._defaults(this);
  }

  TransactionRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _accountNo = $v.accountNo;
      _accountType = $v.accountType;
      _productTypeCode = $v.productTypeCode;
      _refCardId = $v.refCardId;
      _cardNo = $v.cardNo;
      _fromDate = $v.fromDate;
      _toDate = $v.toDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionRequest other) {
    _$v = other as _$TransactionRequest;
  }

  @override
  void update(void Function(TransactionRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionRequest build() => _build();

  _$TransactionRequest _build() {
    final _$result = _$v ??
        _$TransactionRequest._(
          bankCif: bankCif,
          accountNo: accountNo,
          accountType: accountType,
          productTypeCode: productTypeCode,
          refCardId: refCardId,
          cardNo: cardNo,
          fromDate: fromDate,
          toDate: toDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
