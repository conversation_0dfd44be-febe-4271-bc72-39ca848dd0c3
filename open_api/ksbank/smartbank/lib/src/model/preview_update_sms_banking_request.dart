//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'preview_update_sms_banking_request.g.dart';

/// PreviewUpdateSmsBankingRequest
///
/// Properties:
/// * [action] - Đăng ký mới hay hủy dịch vụ ?
/// * [phoneNumber] - Số điện thoại nhận SMS biến động số dư
/// * [regCasaAccounts] - Danh sách tài khoản thanh toán nhận biến động (ngăn cách bằng dấu phẩy)
/// * [regTdAccounts] - Cờ check đăng ký nhận SMS cho tài khoản tiết kiệm (0 - Không đăng ký, 1 - <PERSON><PERSON><PERSON> ký)
/// * [regLoanAccounts] - C<PERSON> check đăng ký nhận SMS cho tài khoản vay (0 - Kh<PERSON>ng đăng ký, 1 - <PERSON><PERSON><PERSON> ký)
/// * [indexPhoneRemove] - Index số điện thoại muốn hủy đăng ký SMS (ngăn cách bằng dấu phẩy)
@BuiltValue()
abstract class PreviewUpdateSmsBankingRequest
    implements
        Built<PreviewUpdateSmsBankingRequest,
            PreviewUpdateSmsBankingRequestBuilder> {
  /// Đăng ký mới hay hủy dịch vụ ?
  @BuiltValueField(wireName: r'action')
  PreviewUpdateSmsBankingRequestActionEnum? get action;
  // enum actionEnum {  CANCEL,  REGISTER,  CHANGE_ACCOUNT,  };

  /// Số điện thoại nhận SMS biến động số dư
  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  /// Danh sách tài khoản thanh toán nhận biến động (ngăn cách bằng dấu phẩy)
  @BuiltValueField(wireName: r'regCasaAccounts')
  String? get regCasaAccounts;

  /// Cờ check đăng ký nhận SMS cho tài khoản tiết kiệm (0 - Không đăng ký, 1 - Đăng ký)
  @BuiltValueField(wireName: r'regTdAccounts')
  int? get regTdAccounts;

  /// Cờ check đăng ký nhận SMS cho tài khoản vay (0 - Không đăng ký, 1 - Đăng ký)
  @BuiltValueField(wireName: r'regLoanAccounts')
  int? get regLoanAccounts;

  /// Index số điện thoại muốn hủy đăng ký SMS (ngăn cách bằng dấu phẩy)
  @BuiltValueField(wireName: r'indexPhoneRemove')
  String? get indexPhoneRemove;

  PreviewUpdateSmsBankingRequest._();

  factory PreviewUpdateSmsBankingRequest(
          [void updates(PreviewUpdateSmsBankingRequestBuilder b)]) =
      _$PreviewUpdateSmsBankingRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(PreviewUpdateSmsBankingRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<PreviewUpdateSmsBankingRequest> get serializer =>
      _$PreviewUpdateSmsBankingRequestSerializer();
}

class _$PreviewUpdateSmsBankingRequestSerializer
    implements PrimitiveSerializer<PreviewUpdateSmsBankingRequest> {
  @override
  final Iterable<Type> types = const [
    PreviewUpdateSmsBankingRequest,
    _$PreviewUpdateSmsBankingRequest
  ];

  @override
  final String wireName = r'PreviewUpdateSmsBankingRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    PreviewUpdateSmsBankingRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.action != null) {
      yield r'action';
      yield serializers.serialize(
        object.action,
        specifiedType:
            const FullType.nullable(PreviewUpdateSmsBankingRequestActionEnum),
      );
    }
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.regCasaAccounts != null) {
      yield r'regCasaAccounts';
      yield serializers.serialize(
        object.regCasaAccounts,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.regTdAccounts != null) {
      yield r'regTdAccounts';
      yield serializers.serialize(
        object.regTdAccounts,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.regLoanAccounts != null) {
      yield r'regLoanAccounts';
      yield serializers.serialize(
        object.regLoanAccounts,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.indexPhoneRemove != null) {
      yield r'indexPhoneRemove';
      yield serializers.serialize(
        object.indexPhoneRemove,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    PreviewUpdateSmsBankingRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required PreviewUpdateSmsBankingRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'action':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                PreviewUpdateSmsBankingRequestActionEnum),
          ) as PreviewUpdateSmsBankingRequestActionEnum?;
          if (valueDes == null) continue;
          result.action = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNumber = valueDes;
          break;
        case r'regCasaAccounts':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.regCasaAccounts = valueDes;
          break;
        case r'regTdAccounts':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.regTdAccounts = valueDes;
          break;
        case r'regLoanAccounts':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.regLoanAccounts = valueDes;
          break;
        case r'indexPhoneRemove':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.indexPhoneRemove = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  PreviewUpdateSmsBankingRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = PreviewUpdateSmsBankingRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class PreviewUpdateSmsBankingRequestActionEnum extends EnumClass {
  /// Đăng ký mới hay hủy dịch vụ ?
  @BuiltValueEnumConst(wireName: r'CANCEL')
  static const PreviewUpdateSmsBankingRequestActionEnum CANCEL =
      _$previewUpdateSmsBankingRequestActionEnum_CANCEL;

  /// Đăng ký mới hay hủy dịch vụ ?
  @BuiltValueEnumConst(wireName: r'REGISTER')
  static const PreviewUpdateSmsBankingRequestActionEnum REGISTER =
      _$previewUpdateSmsBankingRequestActionEnum_REGISTER;

  /// Đăng ký mới hay hủy dịch vụ ?
  @BuiltValueEnumConst(wireName: r'CHANGE_ACCOUNT')
  static const PreviewUpdateSmsBankingRequestActionEnum CHANGE_ACCOUNT =
      _$previewUpdateSmsBankingRequestActionEnum_CHANGE_ACCOUNT;

  static Serializer<PreviewUpdateSmsBankingRequestActionEnum> get serializer =>
      _$previewUpdateSmsBankingRequestActionEnumSerializer;

  const PreviewUpdateSmsBankingRequestActionEnum._(String name) : super(name);

  static BuiltSet<PreviewUpdateSmsBankingRequestActionEnum> get values =>
      _$previewUpdateSmsBankingRequestActionEnumValues;
  static PreviewUpdateSmsBankingRequestActionEnum valueOf(String name) =>
      _$previewUpdateSmsBankingRequestActionEnumValueOf(name);
}
