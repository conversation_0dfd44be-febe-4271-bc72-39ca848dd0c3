// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferRequest extends ReviewTransferRequest {
  @override
  final int? is247;
  @override
  final int? isNow;
  @override
  final String? channelCode;
  @override
  final String? transactionName;
  @override
  final int? transactionGroup;
  @override
  final String? sourceAccountNumber;
  @override
  final String? targetAccountNumber;
  @override
  final String? cifNumber;
  @override
  final int? targetAccountType;
  @override
  final String? targetBankCode;
  @override
  final num? amount;

  factory _$ReviewTransferRequest(
          [void Function(ReviewTransferRequestBuilder)? updates]) =>
      (ReviewTransferRequestBuilder()..update(updates))._build();

  _$ReviewTransferRequest._(
      {this.is247,
      this.isNow,
      this.channelCode,
      this.transactionName,
      this.transactionGroup,
      this.sourceAccountNumber,
      this.targetAccountNumber,
      this.cifNumber,
      this.targetAccountType,
      this.targetBankCode,
      this.amount})
      : super._();
  @override
  ReviewTransferRequest rebuild(
          void Function(ReviewTransferRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferRequestBuilder toBuilder() =>
      ReviewTransferRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferRequest &&
        is247 == other.is247 &&
        isNow == other.isNow &&
        channelCode == other.channelCode &&
        transactionName == other.transactionName &&
        transactionGroup == other.transactionGroup &&
        sourceAccountNumber == other.sourceAccountNumber &&
        targetAccountNumber == other.targetAccountNumber &&
        cifNumber == other.cifNumber &&
        targetAccountType == other.targetAccountType &&
        targetBankCode == other.targetBankCode &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, is247.hashCode);
    _$hash = $jc(_$hash, isNow.hashCode);
    _$hash = $jc(_$hash, channelCode.hashCode);
    _$hash = $jc(_$hash, transactionName.hashCode);
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, sourceAccountNumber.hashCode);
    _$hash = $jc(_$hash, targetAccountNumber.hashCode);
    _$hash = $jc(_$hash, cifNumber.hashCode);
    _$hash = $jc(_$hash, targetAccountType.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewTransferRequest')
          ..add('is247', is247)
          ..add('isNow', isNow)
          ..add('channelCode', channelCode)
          ..add('transactionName', transactionName)
          ..add('transactionGroup', transactionGroup)
          ..add('sourceAccountNumber', sourceAccountNumber)
          ..add('targetAccountNumber', targetAccountNumber)
          ..add('cifNumber', cifNumber)
          ..add('targetAccountType', targetAccountType)
          ..add('targetBankCode', targetBankCode)
          ..add('amount', amount))
        .toString();
  }
}

class ReviewTransferRequestBuilder
    implements Builder<ReviewTransferRequest, ReviewTransferRequestBuilder> {
  _$ReviewTransferRequest? _$v;

  int? _is247;
  int? get is247 => _$this._is247;
  set is247(int? is247) => _$this._is247 = is247;

  int? _isNow;
  int? get isNow => _$this._isNow;
  set isNow(int? isNow) => _$this._isNow = isNow;

  String? _channelCode;
  String? get channelCode => _$this._channelCode;
  set channelCode(String? channelCode) => _$this._channelCode = channelCode;

  String? _transactionName;
  String? get transactionName => _$this._transactionName;
  set transactionName(String? transactionName) =>
      _$this._transactionName = transactionName;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _sourceAccountNumber;
  String? get sourceAccountNumber => _$this._sourceAccountNumber;
  set sourceAccountNumber(String? sourceAccountNumber) =>
      _$this._sourceAccountNumber = sourceAccountNumber;

  String? _targetAccountNumber;
  String? get targetAccountNumber => _$this._targetAccountNumber;
  set targetAccountNumber(String? targetAccountNumber) =>
      _$this._targetAccountNumber = targetAccountNumber;

  String? _cifNumber;
  String? get cifNumber => _$this._cifNumber;
  set cifNumber(String? cifNumber) => _$this._cifNumber = cifNumber;

  int? _targetAccountType;
  int? get targetAccountType => _$this._targetAccountType;
  set targetAccountType(int? targetAccountType) =>
      _$this._targetAccountType = targetAccountType;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  ReviewTransferRequestBuilder() {
    ReviewTransferRequest._defaults(this);
  }

  ReviewTransferRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _is247 = $v.is247;
      _isNow = $v.isNow;
      _channelCode = $v.channelCode;
      _transactionName = $v.transactionName;
      _transactionGroup = $v.transactionGroup;
      _sourceAccountNumber = $v.sourceAccountNumber;
      _targetAccountNumber = $v.targetAccountNumber;
      _cifNumber = $v.cifNumber;
      _targetAccountType = $v.targetAccountType;
      _targetBankCode = $v.targetBankCode;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferRequest other) {
    _$v = other as _$ReviewTransferRequest;
  }

  @override
  void update(void Function(ReviewTransferRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferRequest build() => _build();

  _$ReviewTransferRequest _build() {
    final _$result = _$v ??
        _$ReviewTransferRequest._(
          is247: is247,
          isNow: isNow,
          channelCode: channelCode,
          transactionName: transactionName,
          transactionGroup: transactionGroup,
          sourceAccountNumber: sourceAccountNumber,
          targetAccountNumber: targetAccountNumber,
          cifNumber: cifNumber,
          targetAccountType: targetAccountType,
          targetBankCode: targetBankCode,
          amount: amount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
