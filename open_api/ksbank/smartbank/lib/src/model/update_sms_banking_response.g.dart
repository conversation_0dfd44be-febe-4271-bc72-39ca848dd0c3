// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_sms_banking_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const UpdateSmsBankingResponseActionEnum
    _$updateSmsBankingResponseActionEnum_CANCEL =
    const UpdateSmsBankingResponseActionEnum._('CANCEL');
const UpdateSmsBankingResponseActionEnum
    _$updateSmsBankingResponseActionEnum_REGISTER =
    const UpdateSmsBankingResponseActionEnum._('REGISTER');
const UpdateSmsBankingResponseActionEnum
    _$updateSmsBankingResponseActionEnum_CHANGE_ACCOUNT =
    const UpdateSmsBankingResponseActionEnum._('CHANGE_ACCOUNT');

UpdateSmsBankingResponseActionEnum _$updateSmsBankingResponseActionEnumValueOf(
    String name) {
  switch (name) {
    case 'CANCEL':
      return _$updateSmsBankingResponseActionEnum_CANCEL;
    case 'REGISTER':
      return _$updateSmsBankingResponseActionEnum_REGISTER;
    case 'CHANGE_ACCOUNT':
      return _$updateSmsBankingResponseActionEnum_CHANGE_ACCOUNT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<UpdateSmsBankingResponseActionEnum>
    _$updateSmsBankingResponseActionEnumValues = BuiltSet<
        UpdateSmsBankingResponseActionEnum>(const <UpdateSmsBankingResponseActionEnum>[
  _$updateSmsBankingResponseActionEnum_CANCEL,
  _$updateSmsBankingResponseActionEnum_REGISTER,
  _$updateSmsBankingResponseActionEnum_CHANGE_ACCOUNT,
]);

Serializer<UpdateSmsBankingResponseActionEnum>
    _$updateSmsBankingResponseActionEnumSerializer =
    _$UpdateSmsBankingResponseActionEnumSerializer();

class _$UpdateSmsBankingResponseActionEnumSerializer
    implements PrimitiveSerializer<UpdateSmsBankingResponseActionEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'CANCEL': 'CANCEL',
    'REGISTER': 'REGISTER',
    'CHANGE_ACCOUNT': 'CHANGE_ACCOUNT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'CANCEL': 'CANCEL',
    'REGISTER': 'REGISTER',
    'CHANGE_ACCOUNT': 'CHANGE_ACCOUNT',
  };

  @override
  final Iterable<Type> types = const <Type>[UpdateSmsBankingResponseActionEnum];
  @override
  final String wireName = 'UpdateSmsBankingResponseActionEnum';

  @override
  Object serialize(
          Serializers serializers, UpdateSmsBankingResponseActionEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  UpdateSmsBankingResponseActionEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      UpdateSmsBankingResponseActionEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$UpdateSmsBankingResponse extends UpdateSmsBankingResponse {
  @override
  final num? fee;
  @override
  final UpdateSmsBankingResponseActionEnum? action;
  @override
  final String? regPhoneNumbers;
  @override
  final String? regCasaAccounts;
  @override
  final int? regTdAccounts;
  @override
  final int? regLoanAccounts;

  factory _$UpdateSmsBankingResponse(
          [void Function(UpdateSmsBankingResponseBuilder)? updates]) =>
      (UpdateSmsBankingResponseBuilder()..update(updates))._build();

  _$UpdateSmsBankingResponse._(
      {this.fee,
      this.action,
      this.regPhoneNumbers,
      this.regCasaAccounts,
      this.regTdAccounts,
      this.regLoanAccounts})
      : super._();
  @override
  UpdateSmsBankingResponse rebuild(
          void Function(UpdateSmsBankingResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateSmsBankingResponseBuilder toBuilder() =>
      UpdateSmsBankingResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateSmsBankingResponse &&
        fee == other.fee &&
        action == other.action &&
        regPhoneNumbers == other.regPhoneNumbers &&
        regCasaAccounts == other.regCasaAccounts &&
        regTdAccounts == other.regTdAccounts &&
        regLoanAccounts == other.regLoanAccounts;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jc(_$hash, action.hashCode);
    _$hash = $jc(_$hash, regPhoneNumbers.hashCode);
    _$hash = $jc(_$hash, regCasaAccounts.hashCode);
    _$hash = $jc(_$hash, regTdAccounts.hashCode);
    _$hash = $jc(_$hash, regLoanAccounts.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateSmsBankingResponse')
          ..add('fee', fee)
          ..add('action', action)
          ..add('regPhoneNumbers', regPhoneNumbers)
          ..add('regCasaAccounts', regCasaAccounts)
          ..add('regTdAccounts', regTdAccounts)
          ..add('regLoanAccounts', regLoanAccounts))
        .toString();
  }
}

class UpdateSmsBankingResponseBuilder
    implements
        Builder<UpdateSmsBankingResponse, UpdateSmsBankingResponseBuilder> {
  _$UpdateSmsBankingResponse? _$v;

  num? _fee;
  num? get fee => _$this._fee;
  set fee(num? fee) => _$this._fee = fee;

  UpdateSmsBankingResponseActionEnum? _action;
  UpdateSmsBankingResponseActionEnum? get action => _$this._action;
  set action(UpdateSmsBankingResponseActionEnum? action) =>
      _$this._action = action;

  String? _regPhoneNumbers;
  String? get regPhoneNumbers => _$this._regPhoneNumbers;
  set regPhoneNumbers(String? regPhoneNumbers) =>
      _$this._regPhoneNumbers = regPhoneNumbers;

  String? _regCasaAccounts;
  String? get regCasaAccounts => _$this._regCasaAccounts;
  set regCasaAccounts(String? regCasaAccounts) =>
      _$this._regCasaAccounts = regCasaAccounts;

  int? _regTdAccounts;
  int? get regTdAccounts => _$this._regTdAccounts;
  set regTdAccounts(int? regTdAccounts) =>
      _$this._regTdAccounts = regTdAccounts;

  int? _regLoanAccounts;
  int? get regLoanAccounts => _$this._regLoanAccounts;
  set regLoanAccounts(int? regLoanAccounts) =>
      _$this._regLoanAccounts = regLoanAccounts;

  UpdateSmsBankingResponseBuilder() {
    UpdateSmsBankingResponse._defaults(this);
  }

  UpdateSmsBankingResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _fee = $v.fee;
      _action = $v.action;
      _regPhoneNumbers = $v.regPhoneNumbers;
      _regCasaAccounts = $v.regCasaAccounts;
      _regTdAccounts = $v.regTdAccounts;
      _regLoanAccounts = $v.regLoanAccounts;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateSmsBankingResponse other) {
    _$v = other as _$UpdateSmsBankingResponse;
  }

  @override
  void update(void Function(UpdateSmsBankingResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateSmsBankingResponse build() => _build();

  _$UpdateSmsBankingResponse _build() {
    final _$result = _$v ??
        _$UpdateSmsBankingResponse._(
          fee: fee,
          action: action,
          regPhoneNumbers: regPhoneNumbers,
          regCasaAccounts: regCasaAccounts,
          regTdAccounts: regTdAccounts,
          regLoanAccounts: regLoanAccounts,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
