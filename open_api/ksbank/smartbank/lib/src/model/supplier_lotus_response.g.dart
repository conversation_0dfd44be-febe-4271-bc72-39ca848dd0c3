// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supplier_lotus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SupplierLotusResponse extends SupplierLotusResponse {
  @override
  final String? supplierId;
  @override
  final String? supplierCode;
  @override
  final String? supplierName;

  factory _$SupplierLotusResponse(
          [void Function(SupplierLotusResponseBuilder)? updates]) =>
      (SupplierLotusResponseBuilder()..update(updates))._build();

  _$SupplierLotusResponse._(
      {this.supplierId, this.supplierCode, this.supplierName})
      : super._();
  @override
  SupplierLotusResponse rebuild(
          void Function(SupplierLotusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SupplierLotusResponseBuilder toBuilder() =>
      SupplierLotusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SupplierLotusResponse &&
        supplierId == other.supplierId &&
        supplierCode == other.supplierCode &&
        supplierName == other.supplierName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SupplierLotusResponse')
          ..add('supplierId', supplierId)
          ..add('supplierCode', supplierCode)
          ..add('supplierName', supplierName))
        .toString();
  }
}

class SupplierLotusResponseBuilder
    implements Builder<SupplierLotusResponse, SupplierLotusResponseBuilder> {
  _$SupplierLotusResponse? _$v;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  SupplierLotusResponseBuilder() {
    SupplierLotusResponse._defaults(this);
  }

  SupplierLotusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _supplierId = $v.supplierId;
      _supplierCode = $v.supplierCode;
      _supplierName = $v.supplierName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SupplierLotusResponse other) {
    _$v = other as _$SupplierLotusResponse;
  }

  @override
  void update(void Function(SupplierLotusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SupplierLotusResponse build() => _build();

  _$SupplierLotusResponse _build() {
    final _$result = _$v ??
        _$SupplierLotusResponse._(
          supplierId: supplierId,
          supplierCode: supplierCode,
          supplierName: supplierName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
