// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_contact_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$UpdateContactRequest extends UpdateContactRequest {
  @override
  final String? cifNo;
  @override
  final String? aliasName;
  @override
  final String? accountNo;
  @override
  final int? favorite;
  @override
  final DateTime? updateDt;

  factory _$UpdateContactRequest(
          [void Function(UpdateContactRequestBuilder)? updates]) =>
      (UpdateContactRequestBuilder()..update(updates))._build();

  _$UpdateContactRequest._(
      {this.cifNo,
      this.aliasName,
      this.accountNo,
      this.favorite,
      this.updateDt})
      : super._();
  @override
  UpdateContactRequest rebuild(
          void Function(UpdateContactRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateContactRequestBuilder toBuilder() =>
      UpdateContactRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateContactRequest &&
        cifNo == other.cifNo &&
        aliasName == other.aliasName &&
        accountNo == other.accountNo &&
        favorite == other.favorite &&
        updateDt == other.updateDt;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, favorite.hashCode);
    _$hash = $jc(_$hash, updateDt.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateContactRequest')
          ..add('cifNo', cifNo)
          ..add('aliasName', aliasName)
          ..add('accountNo', accountNo)
          ..add('favorite', favorite)
          ..add('updateDt', updateDt))
        .toString();
  }
}

class UpdateContactRequestBuilder
    implements Builder<UpdateContactRequest, UpdateContactRequestBuilder> {
  _$UpdateContactRequest? _$v;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  int? _favorite;
  int? get favorite => _$this._favorite;
  set favorite(int? favorite) => _$this._favorite = favorite;

  DateTime? _updateDt;
  DateTime? get updateDt => _$this._updateDt;
  set updateDt(DateTime? updateDt) => _$this._updateDt = updateDt;

  UpdateContactRequestBuilder() {
    UpdateContactRequest._defaults(this);
  }

  UpdateContactRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cifNo = $v.cifNo;
      _aliasName = $v.aliasName;
      _accountNo = $v.accountNo;
      _favorite = $v.favorite;
      _updateDt = $v.updateDt;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateContactRequest other) {
    _$v = other as _$UpdateContactRequest;
  }

  @override
  void update(void Function(UpdateContactRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateContactRequest build() => _build();

  _$UpdateContactRequest _build() {
    final _$result = _$v ??
        _$UpdateContactRequest._(
          cifNo: cifNo,
          aliasName: aliasName,
          accountNo: accountNo,
          favorite: favorite,
          updateDt: updateDt,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
