// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_template_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionTemplateDto extends TransactionTemplateDto {
  @override
  final String? id;
  @override
  final String? cifNo;
  @override
  final String? templatesName;
  @override
  final String? sourceAccountNo;
  @override
  final String? targetBankCodeId;
  @override
  final String? targetBankCode;
  @override
  final int? isAccount;
  @override
  final String? targetBankCodeCitAd;
  @override
  final String? targetBankName;
  @override
  final String? shortName;
  @override
  final String? targetBankCommonName;
  @override
  final String? targetBankAvatarUrl;
  @override
  final String? targetBankAvatarUrlType;
  @override
  final String? targetAccountName;
  @override
  final String? aliasName;
  @override
  final String? targetAccountNo;
  @override
  final int? targetAccountNoType;
  @override
  final num? amount;
  @override
  final String? description;
  @override
  final int? is247;
  @override
  final int? isNow;
  @override
  final int? whoCharge;
  @override
  final int? chargeAmount;
  @override
  final int? categoryId;
  @override
  final String? categoryName;
  @override
  final String? categoryUrl;
  @override
  final String? categoryUrlType;
  @override
  final int? regionId;
  @override
  final int? bankId;
  @override
  final String? branchId;
  @override
  final String? branchName;
  @override
  final String? idCard;
  @override
  final DateTime? issueDate;
  @override
  final String? placeBy;
  @override
  final DateTime? createDt;
  @override
  final DateTime? updateDt;

  factory _$TransactionTemplateDto(
          [void Function(TransactionTemplateDtoBuilder)? updates]) =>
      (TransactionTemplateDtoBuilder()..update(updates))._build();

  _$TransactionTemplateDto._(
      {this.id,
      this.cifNo,
      this.templatesName,
      this.sourceAccountNo,
      this.targetBankCodeId,
      this.targetBankCode,
      this.isAccount,
      this.targetBankCodeCitAd,
      this.targetBankName,
      this.shortName,
      this.targetBankCommonName,
      this.targetBankAvatarUrl,
      this.targetBankAvatarUrlType,
      this.targetAccountName,
      this.aliasName,
      this.targetAccountNo,
      this.targetAccountNoType,
      this.amount,
      this.description,
      this.is247,
      this.isNow,
      this.whoCharge,
      this.chargeAmount,
      this.categoryId,
      this.categoryName,
      this.categoryUrl,
      this.categoryUrlType,
      this.regionId,
      this.bankId,
      this.branchId,
      this.branchName,
      this.idCard,
      this.issueDate,
      this.placeBy,
      this.createDt,
      this.updateDt})
      : super._();
  @override
  TransactionTemplateDto rebuild(
          void Function(TransactionTemplateDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionTemplateDtoBuilder toBuilder() =>
      TransactionTemplateDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionTemplateDto &&
        id == other.id &&
        cifNo == other.cifNo &&
        templatesName == other.templatesName &&
        sourceAccountNo == other.sourceAccountNo &&
        targetBankCodeId == other.targetBankCodeId &&
        targetBankCode == other.targetBankCode &&
        isAccount == other.isAccount &&
        targetBankCodeCitAd == other.targetBankCodeCitAd &&
        targetBankName == other.targetBankName &&
        shortName == other.shortName &&
        targetBankCommonName == other.targetBankCommonName &&
        targetBankAvatarUrl == other.targetBankAvatarUrl &&
        targetBankAvatarUrlType == other.targetBankAvatarUrlType &&
        targetAccountName == other.targetAccountName &&
        aliasName == other.aliasName &&
        targetAccountNo == other.targetAccountNo &&
        targetAccountNoType == other.targetAccountNoType &&
        amount == other.amount &&
        description == other.description &&
        is247 == other.is247 &&
        isNow == other.isNow &&
        whoCharge == other.whoCharge &&
        chargeAmount == other.chargeAmount &&
        categoryId == other.categoryId &&
        categoryName == other.categoryName &&
        categoryUrl == other.categoryUrl &&
        categoryUrlType == other.categoryUrlType &&
        regionId == other.regionId &&
        bankId == other.bankId &&
        branchId == other.branchId &&
        branchName == other.branchName &&
        idCard == other.idCard &&
        issueDate == other.issueDate &&
        placeBy == other.placeBy &&
        createDt == other.createDt &&
        updateDt == other.updateDt;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, templatesName.hashCode);
    _$hash = $jc(_$hash, sourceAccountNo.hashCode);
    _$hash = $jc(_$hash, targetBankCodeId.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, targetBankCodeCitAd.hashCode);
    _$hash = $jc(_$hash, targetBankName.hashCode);
    _$hash = $jc(_$hash, shortName.hashCode);
    _$hash = $jc(_$hash, targetBankCommonName.hashCode);
    _$hash = $jc(_$hash, targetBankAvatarUrl.hashCode);
    _$hash = $jc(_$hash, targetBankAvatarUrlType.hashCode);
    _$hash = $jc(_$hash, targetAccountName.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, targetAccountNo.hashCode);
    _$hash = $jc(_$hash, targetAccountNoType.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, is247.hashCode);
    _$hash = $jc(_$hash, isNow.hashCode);
    _$hash = $jc(_$hash, whoCharge.hashCode);
    _$hash = $jc(_$hash, chargeAmount.hashCode);
    _$hash = $jc(_$hash, categoryId.hashCode);
    _$hash = $jc(_$hash, categoryName.hashCode);
    _$hash = $jc(_$hash, categoryUrl.hashCode);
    _$hash = $jc(_$hash, categoryUrlType.hashCode);
    _$hash = $jc(_$hash, regionId.hashCode);
    _$hash = $jc(_$hash, bankId.hashCode);
    _$hash = $jc(_$hash, branchId.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, idCard.hashCode);
    _$hash = $jc(_$hash, issueDate.hashCode);
    _$hash = $jc(_$hash, placeBy.hashCode);
    _$hash = $jc(_$hash, createDt.hashCode);
    _$hash = $jc(_$hash, updateDt.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionTemplateDto')
          ..add('id', id)
          ..add('cifNo', cifNo)
          ..add('templatesName', templatesName)
          ..add('sourceAccountNo', sourceAccountNo)
          ..add('targetBankCodeId', targetBankCodeId)
          ..add('targetBankCode', targetBankCode)
          ..add('isAccount', isAccount)
          ..add('targetBankCodeCitAd', targetBankCodeCitAd)
          ..add('targetBankName', targetBankName)
          ..add('shortName', shortName)
          ..add('targetBankCommonName', targetBankCommonName)
          ..add('targetBankAvatarUrl', targetBankAvatarUrl)
          ..add('targetBankAvatarUrlType', targetBankAvatarUrlType)
          ..add('targetAccountName', targetAccountName)
          ..add('aliasName', aliasName)
          ..add('targetAccountNo', targetAccountNo)
          ..add('targetAccountNoType', targetAccountNoType)
          ..add('amount', amount)
          ..add('description', description)
          ..add('is247', is247)
          ..add('isNow', isNow)
          ..add('whoCharge', whoCharge)
          ..add('chargeAmount', chargeAmount)
          ..add('categoryId', categoryId)
          ..add('categoryName', categoryName)
          ..add('categoryUrl', categoryUrl)
          ..add('categoryUrlType', categoryUrlType)
          ..add('regionId', regionId)
          ..add('bankId', bankId)
          ..add('branchId', branchId)
          ..add('branchName', branchName)
          ..add('idCard', idCard)
          ..add('issueDate', issueDate)
          ..add('placeBy', placeBy)
          ..add('createDt', createDt)
          ..add('updateDt', updateDt))
        .toString();
  }
}

class TransactionTemplateDtoBuilder
    implements Builder<TransactionTemplateDto, TransactionTemplateDtoBuilder> {
  _$TransactionTemplateDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _templatesName;
  String? get templatesName => _$this._templatesName;
  set templatesName(String? templatesName) =>
      _$this._templatesName = templatesName;

  String? _sourceAccountNo;
  String? get sourceAccountNo => _$this._sourceAccountNo;
  set sourceAccountNo(String? sourceAccountNo) =>
      _$this._sourceAccountNo = sourceAccountNo;

  String? _targetBankCodeId;
  String? get targetBankCodeId => _$this._targetBankCodeId;
  set targetBankCodeId(String? targetBankCodeId) =>
      _$this._targetBankCodeId = targetBankCodeId;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _targetBankCodeCitAd;
  String? get targetBankCodeCitAd => _$this._targetBankCodeCitAd;
  set targetBankCodeCitAd(String? targetBankCodeCitAd) =>
      _$this._targetBankCodeCitAd = targetBankCodeCitAd;

  String? _targetBankName;
  String? get targetBankName => _$this._targetBankName;
  set targetBankName(String? targetBankName) =>
      _$this._targetBankName = targetBankName;

  String? _shortName;
  String? get shortName => _$this._shortName;
  set shortName(String? shortName) => _$this._shortName = shortName;

  String? _targetBankCommonName;
  String? get targetBankCommonName => _$this._targetBankCommonName;
  set targetBankCommonName(String? targetBankCommonName) =>
      _$this._targetBankCommonName = targetBankCommonName;

  String? _targetBankAvatarUrl;
  String? get targetBankAvatarUrl => _$this._targetBankAvatarUrl;
  set targetBankAvatarUrl(String? targetBankAvatarUrl) =>
      _$this._targetBankAvatarUrl = targetBankAvatarUrl;

  String? _targetBankAvatarUrlType;
  String? get targetBankAvatarUrlType => _$this._targetBankAvatarUrlType;
  set targetBankAvatarUrlType(String? targetBankAvatarUrlType) =>
      _$this._targetBankAvatarUrlType = targetBankAvatarUrlType;

  String? _targetAccountName;
  String? get targetAccountName => _$this._targetAccountName;
  set targetAccountName(String? targetAccountName) =>
      _$this._targetAccountName = targetAccountName;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  String? _targetAccountNo;
  String? get targetAccountNo => _$this._targetAccountNo;
  set targetAccountNo(String? targetAccountNo) =>
      _$this._targetAccountNo = targetAccountNo;

  int? _targetAccountNoType;
  int? get targetAccountNoType => _$this._targetAccountNoType;
  set targetAccountNoType(int? targetAccountNoType) =>
      _$this._targetAccountNoType = targetAccountNoType;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _is247;
  int? get is247 => _$this._is247;
  set is247(int? is247) => _$this._is247 = is247;

  int? _isNow;
  int? get isNow => _$this._isNow;
  set isNow(int? isNow) => _$this._isNow = isNow;

  int? _whoCharge;
  int? get whoCharge => _$this._whoCharge;
  set whoCharge(int? whoCharge) => _$this._whoCharge = whoCharge;

  int? _chargeAmount;
  int? get chargeAmount => _$this._chargeAmount;
  set chargeAmount(int? chargeAmount) => _$this._chargeAmount = chargeAmount;

  int? _categoryId;
  int? get categoryId => _$this._categoryId;
  set categoryId(int? categoryId) => _$this._categoryId = categoryId;

  String? _categoryName;
  String? get categoryName => _$this._categoryName;
  set categoryName(String? categoryName) => _$this._categoryName = categoryName;

  String? _categoryUrl;
  String? get categoryUrl => _$this._categoryUrl;
  set categoryUrl(String? categoryUrl) => _$this._categoryUrl = categoryUrl;

  String? _categoryUrlType;
  String? get categoryUrlType => _$this._categoryUrlType;
  set categoryUrlType(String? categoryUrlType) =>
      _$this._categoryUrlType = categoryUrlType;

  int? _regionId;
  int? get regionId => _$this._regionId;
  set regionId(int? regionId) => _$this._regionId = regionId;

  int? _bankId;
  int? get bankId => _$this._bankId;
  set bankId(int? bankId) => _$this._bankId = bankId;

  String? _branchId;
  String? get branchId => _$this._branchId;
  set branchId(String? branchId) => _$this._branchId = branchId;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _idCard;
  String? get idCard => _$this._idCard;
  set idCard(String? idCard) => _$this._idCard = idCard;

  DateTime? _issueDate;
  DateTime? get issueDate => _$this._issueDate;
  set issueDate(DateTime? issueDate) => _$this._issueDate = issueDate;

  String? _placeBy;
  String? get placeBy => _$this._placeBy;
  set placeBy(String? placeBy) => _$this._placeBy = placeBy;

  DateTime? _createDt;
  DateTime? get createDt => _$this._createDt;
  set createDt(DateTime? createDt) => _$this._createDt = createDt;

  DateTime? _updateDt;
  DateTime? get updateDt => _$this._updateDt;
  set updateDt(DateTime? updateDt) => _$this._updateDt = updateDt;

  TransactionTemplateDtoBuilder() {
    TransactionTemplateDto._defaults(this);
  }

  TransactionTemplateDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _cifNo = $v.cifNo;
      _templatesName = $v.templatesName;
      _sourceAccountNo = $v.sourceAccountNo;
      _targetBankCodeId = $v.targetBankCodeId;
      _targetBankCode = $v.targetBankCode;
      _isAccount = $v.isAccount;
      _targetBankCodeCitAd = $v.targetBankCodeCitAd;
      _targetBankName = $v.targetBankName;
      _shortName = $v.shortName;
      _targetBankCommonName = $v.targetBankCommonName;
      _targetBankAvatarUrl = $v.targetBankAvatarUrl;
      _targetBankAvatarUrlType = $v.targetBankAvatarUrlType;
      _targetAccountName = $v.targetAccountName;
      _aliasName = $v.aliasName;
      _targetAccountNo = $v.targetAccountNo;
      _targetAccountNoType = $v.targetAccountNoType;
      _amount = $v.amount;
      _description = $v.description;
      _is247 = $v.is247;
      _isNow = $v.isNow;
      _whoCharge = $v.whoCharge;
      _chargeAmount = $v.chargeAmount;
      _categoryId = $v.categoryId;
      _categoryName = $v.categoryName;
      _categoryUrl = $v.categoryUrl;
      _categoryUrlType = $v.categoryUrlType;
      _regionId = $v.regionId;
      _bankId = $v.bankId;
      _branchId = $v.branchId;
      _branchName = $v.branchName;
      _idCard = $v.idCard;
      _issueDate = $v.issueDate;
      _placeBy = $v.placeBy;
      _createDt = $v.createDt;
      _updateDt = $v.updateDt;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionTemplateDto other) {
    _$v = other as _$TransactionTemplateDto;
  }

  @override
  void update(void Function(TransactionTemplateDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionTemplateDto build() => _build();

  _$TransactionTemplateDto _build() {
    final _$result = _$v ??
        _$TransactionTemplateDto._(
          id: id,
          cifNo: cifNo,
          templatesName: templatesName,
          sourceAccountNo: sourceAccountNo,
          targetBankCodeId: targetBankCodeId,
          targetBankCode: targetBankCode,
          isAccount: isAccount,
          targetBankCodeCitAd: targetBankCodeCitAd,
          targetBankName: targetBankName,
          shortName: shortName,
          targetBankCommonName: targetBankCommonName,
          targetBankAvatarUrl: targetBankAvatarUrl,
          targetBankAvatarUrlType: targetBankAvatarUrlType,
          targetAccountName: targetAccountName,
          aliasName: aliasName,
          targetAccountNo: targetAccountNo,
          targetAccountNoType: targetAccountNoType,
          amount: amount,
          description: description,
          is247: is247,
          isNow: isNow,
          whoCharge: whoCharge,
          chargeAmount: chargeAmount,
          categoryId: categoryId,
          categoryName: categoryName,
          categoryUrl: categoryUrl,
          categoryUrlType: categoryUrlType,
          regionId: regionId,
          bankId: bankId,
          branchId: branchId,
          branchName: branchName,
          idCard: idCard,
          issueDate: issueDate,
          placeBy: placeBy,
          createDt: createDt,
          updateDt: updateDt,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
