// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_vvip_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const ReviewVvipAccountResponseCheckCodeEnum
    _$reviewVvipAccountResponseCheckCodeEnum_KHONG_XAC_DINH =
    const ReviewVvipAccountResponseCheckCodeEnum._('KHONG_XAC_DINH');
const ReviewVvipAccountResponseCheckCodeEnum
    _$reviewVvipAccountResponseCheckCodeEnum_DA_SU_DUNG =
    const ReviewVvipAccountResponseCheckCodeEnum._('DA_SU_DUNG');
const ReviewVvipAccountResponseCheckCodeEnum
    _$reviewVvipAccountResponseCheckCodeEnum_DA_DANG_KI =
    const ReviewVvipAccountResponseCheckCodeEnum._('DA_DANG_KI');
const ReviewVvipAccountResponseCheckCodeEnum
    _$reviewVvipAccountResponseCheckCodeEnum_CHUA_SU_DUNG =
    const ReviewVvipAccountResponseCheckCodeEnum._('CHUA_SU_DUNG');
const ReviewVvipAccountResponseCheckCodeEnum
    _$reviewVvipAccountResponseCheckCodeEnum_CHON_CHUA_SU_DUNG =
    const ReviewVvipAccountResponseCheckCodeEnum._('CHON_CHUA_SU_DUNG');

ReviewVvipAccountResponseCheckCodeEnum
    _$reviewVvipAccountResponseCheckCodeEnumValueOf(String name) {
  switch (name) {
    case 'KHONG_XAC_DINH':
      return _$reviewVvipAccountResponseCheckCodeEnum_KHONG_XAC_DINH;
    case 'DA_SU_DUNG':
      return _$reviewVvipAccountResponseCheckCodeEnum_DA_SU_DUNG;
    case 'DA_DANG_KI':
      return _$reviewVvipAccountResponseCheckCodeEnum_DA_DANG_KI;
    case 'CHUA_SU_DUNG':
      return _$reviewVvipAccountResponseCheckCodeEnum_CHUA_SU_DUNG;
    case 'CHON_CHUA_SU_DUNG':
      return _$reviewVvipAccountResponseCheckCodeEnum_CHON_CHUA_SU_DUNG;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<ReviewVvipAccountResponseCheckCodeEnum>
    _$reviewVvipAccountResponseCheckCodeEnumValues = BuiltSet<
        ReviewVvipAccountResponseCheckCodeEnum>(const <ReviewVvipAccountResponseCheckCodeEnum>[
  _$reviewVvipAccountResponseCheckCodeEnum_KHONG_XAC_DINH,
  _$reviewVvipAccountResponseCheckCodeEnum_DA_SU_DUNG,
  _$reviewVvipAccountResponseCheckCodeEnum_DA_DANG_KI,
  _$reviewVvipAccountResponseCheckCodeEnum_CHUA_SU_DUNG,
  _$reviewVvipAccountResponseCheckCodeEnum_CHON_CHUA_SU_DUNG,
]);

Serializer<ReviewVvipAccountResponseCheckCodeEnum>
    _$reviewVvipAccountResponseCheckCodeEnumSerializer =
    _$ReviewVvipAccountResponseCheckCodeEnumSerializer();

class _$ReviewVvipAccountResponseCheckCodeEnumSerializer
    implements PrimitiveSerializer<ReviewVvipAccountResponseCheckCodeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'KHONG_XAC_DINH': 'KHONG_XAC_DINH',
    'DA_SU_DUNG': 'DA_SU_DUNG',
    'DA_DANG_KI': 'DA_DANG_KI',
    'CHUA_SU_DUNG': 'CHUA_SU_DUNG',
    'CHON_CHUA_SU_DUNG': 'CHON_CHUA_SU_DUNG',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'KHONG_XAC_DINH': 'KHONG_XAC_DINH',
    'DA_SU_DUNG': 'DA_SU_DUNG',
    'DA_DANG_KI': 'DA_DANG_KI',
    'CHUA_SU_DUNG': 'CHUA_SU_DUNG',
    'CHON_CHUA_SU_DUNG': 'CHON_CHUA_SU_DUNG',
  };

  @override
  final Iterable<Type> types = const <Type>[
    ReviewVvipAccountResponseCheckCodeEnum
  ];
  @override
  final String wireName = 'ReviewVvipAccountResponseCheckCodeEnum';

  @override
  Object serialize(Serializers serializers,
          ReviewVvipAccountResponseCheckCodeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  ReviewVvipAccountResponseCheckCodeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      ReviewVvipAccountResponseCheckCodeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$ReviewVvipAccountResponse extends ReviewVvipAccountResponse {
  @override
  final String? vipAccountNo;
  @override
  final String? vipGroup;
  @override
  final num? feeAmount;
  @override
  final num? feeVat;
  @override
  final num? feeAmountIncludeVat;
  @override
  final String? feeAmountStr;
  @override
  final String? feeAmountIncludeVatStr;
  @override
  final ReviewVvipAccountResponseCheckCodeEnum? checkCode;
  @override
  final String? checkName;
  @override
  final String? transactionNumber;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;

  factory _$ReviewVvipAccountResponse(
          [void Function(ReviewVvipAccountResponseBuilder)? updates]) =>
      (ReviewVvipAccountResponseBuilder()..update(updates))._build();

  _$ReviewVvipAccountResponse._(
      {this.vipAccountNo,
      this.vipGroup,
      this.feeAmount,
      this.feeVat,
      this.feeAmountIncludeVat,
      this.feeAmountStr,
      this.feeAmountIncludeVatStr,
      this.checkCode,
      this.checkName,
      this.transactionNumber,
      this.transNextStep,
      this.content})
      : super._();
  @override
  ReviewVvipAccountResponse rebuild(
          void Function(ReviewVvipAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewVvipAccountResponseBuilder toBuilder() =>
      ReviewVvipAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewVvipAccountResponse &&
        vipAccountNo == other.vipAccountNo &&
        vipGroup == other.vipGroup &&
        feeAmount == other.feeAmount &&
        feeVat == other.feeVat &&
        feeAmountIncludeVat == other.feeAmountIncludeVat &&
        feeAmountStr == other.feeAmountStr &&
        feeAmountIncludeVatStr == other.feeAmountIncludeVatStr &&
        checkCode == other.checkCode &&
        checkName == other.checkName &&
        transactionNumber == other.transactionNumber &&
        transNextStep == other.transNextStep &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, vipAccountNo.hashCode);
    _$hash = $jc(_$hash, vipGroup.hashCode);
    _$hash = $jc(_$hash, feeAmount.hashCode);
    _$hash = $jc(_$hash, feeVat.hashCode);
    _$hash = $jc(_$hash, feeAmountIncludeVat.hashCode);
    _$hash = $jc(_$hash, feeAmountStr.hashCode);
    _$hash = $jc(_$hash, feeAmountIncludeVatStr.hashCode);
    _$hash = $jc(_$hash, checkCode.hashCode);
    _$hash = $jc(_$hash, checkName.hashCode);
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewVvipAccountResponse')
          ..add('vipAccountNo', vipAccountNo)
          ..add('vipGroup', vipGroup)
          ..add('feeAmount', feeAmount)
          ..add('feeVat', feeVat)
          ..add('feeAmountIncludeVat', feeAmountIncludeVat)
          ..add('feeAmountStr', feeAmountStr)
          ..add('feeAmountIncludeVatStr', feeAmountIncludeVatStr)
          ..add('checkCode', checkCode)
          ..add('checkName', checkName)
          ..add('transactionNumber', transactionNumber)
          ..add('transNextStep', transNextStep)
          ..add('content', content))
        .toString();
  }
}

class ReviewVvipAccountResponseBuilder
    implements
        Builder<ReviewVvipAccountResponse, ReviewVvipAccountResponseBuilder> {
  _$ReviewVvipAccountResponse? _$v;

  String? _vipAccountNo;
  String? get vipAccountNo => _$this._vipAccountNo;
  set vipAccountNo(String? vipAccountNo) => _$this._vipAccountNo = vipAccountNo;

  String? _vipGroup;
  String? get vipGroup => _$this._vipGroup;
  set vipGroup(String? vipGroup) => _$this._vipGroup = vipGroup;

  num? _feeAmount;
  num? get feeAmount => _$this._feeAmount;
  set feeAmount(num? feeAmount) => _$this._feeAmount = feeAmount;

  num? _feeVat;
  num? get feeVat => _$this._feeVat;
  set feeVat(num? feeVat) => _$this._feeVat = feeVat;

  num? _feeAmountIncludeVat;
  num? get feeAmountIncludeVat => _$this._feeAmountIncludeVat;
  set feeAmountIncludeVat(num? feeAmountIncludeVat) =>
      _$this._feeAmountIncludeVat = feeAmountIncludeVat;

  String? _feeAmountStr;
  String? get feeAmountStr => _$this._feeAmountStr;
  set feeAmountStr(String? feeAmountStr) => _$this._feeAmountStr = feeAmountStr;

  String? _feeAmountIncludeVatStr;
  String? get feeAmountIncludeVatStr => _$this._feeAmountIncludeVatStr;
  set feeAmountIncludeVatStr(String? feeAmountIncludeVatStr) =>
      _$this._feeAmountIncludeVatStr = feeAmountIncludeVatStr;

  ReviewVvipAccountResponseCheckCodeEnum? _checkCode;
  ReviewVvipAccountResponseCheckCodeEnum? get checkCode => _$this._checkCode;
  set checkCode(ReviewVvipAccountResponseCheckCodeEnum? checkCode) =>
      _$this._checkCode = checkCode;

  String? _checkName;
  String? get checkName => _$this._checkName;
  set checkName(String? checkName) => _$this._checkName = checkName;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  ReviewVvipAccountResponseBuilder() {
    ReviewVvipAccountResponse._defaults(this);
  }

  ReviewVvipAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _vipAccountNo = $v.vipAccountNo;
      _vipGroup = $v.vipGroup;
      _feeAmount = $v.feeAmount;
      _feeVat = $v.feeVat;
      _feeAmountIncludeVat = $v.feeAmountIncludeVat;
      _feeAmountStr = $v.feeAmountStr;
      _feeAmountIncludeVatStr = $v.feeAmountIncludeVatStr;
      _checkCode = $v.checkCode;
      _checkName = $v.checkName;
      _transactionNumber = $v.transactionNumber;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewVvipAccountResponse other) {
    _$v = other as _$ReviewVvipAccountResponse;
  }

  @override
  void update(void Function(ReviewVvipAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewVvipAccountResponse build() => _build();

  _$ReviewVvipAccountResponse _build() {
    final _$result = _$v ??
        _$ReviewVvipAccountResponse._(
          vipAccountNo: vipAccountNo,
          vipGroup: vipGroup,
          feeAmount: feeAmount,
          feeVat: feeVat,
          feeAmountIncludeVat: feeAmountIncludeVat,
          feeAmountStr: feeAmountStr,
          feeAmountIncludeVatStr: feeAmountIncludeVatStr,
          checkCode: checkCode,
          checkName: checkName,
          transactionNumber: transactionNumber,
          transNextStep: transNextStep,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
