// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recent_transaction_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$RecentTransactionRequest extends RecentTransactionRequest {
  @override
  final String? bankCif;
  @override
  final String? accountNo;

  factory _$RecentTransactionRequest(
          [void Function(RecentTransactionRequestBuilder)? updates]) =>
      (RecentTransactionRequestBuilder()..update(updates))._build();

  _$RecentTransactionRequest._({this.bankCif, this.accountNo}) : super._();
  @override
  RecentTransactionRequest rebuild(
          void Function(RecentTransactionRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  RecentTransactionRequestBuilder toBuilder() =>
      RecentTransactionRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RecentTransactionRequest &&
        bankCif == other.bankCif &&
        accountNo == other.accountNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'RecentTransactionRequest')
          ..add('bankCif', bankCif)
          ..add('accountNo', accountNo))
        .toString();
  }
}

class RecentTransactionRequestBuilder
    implements
        Builder<RecentTransactionRequest, RecentTransactionRequestBuilder> {
  _$RecentTransactionRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  RecentTransactionRequestBuilder() {
    RecentTransactionRequest._defaults(this);
  }

  RecentTransactionRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _accountNo = $v.accountNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(RecentTransactionRequest other) {
    _$v = other as _$RecentTransactionRequest;
  }

  @override
  void update(void Function(RecentTransactionRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  RecentTransactionRequest build() => _build();

  _$RecentTransactionRequest _build() {
    final _$result = _$v ??
        _$RecentTransactionRequest._(
          bankCif: bankCif,
          accountNo: accountNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
