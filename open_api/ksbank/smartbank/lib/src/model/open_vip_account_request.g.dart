// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_vip_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OpenVipAccountRequest extends OpenVipAccountRequest {
  @override
  final String? transactionNo;
  @override
  final String? accountNo;
  @override
  final String? fromAccount;
  @override
  final String? aliasName;
  @override
  final VerifySoftOtpRequest? verifySoftOtp;
  @override
  final num? freeAmount;
  @override
  final String? referralCode;

  factory _$OpenVipAccountRequest(
          [void Function(OpenVipAccountRequestBuilder)? updates]) =>
      (OpenVipAccountRequestBuilder()..update(updates))._build();

  _$OpenVipAccountRequest._(
      {this.transactionNo,
      this.accountNo,
      this.fromAccount,
      this.aliasName,
      this.verifySoftOtp,
      this.freeAmount,
      this.referralCode})
      : super._();
  @override
  OpenVipAccountRequest rebuild(
          void Function(OpenVipAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OpenVipAccountRequestBuilder toBuilder() =>
      OpenVipAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OpenVipAccountRequest &&
        transactionNo == other.transactionNo &&
        accountNo == other.accountNo &&
        fromAccount == other.fromAccount &&
        aliasName == other.aliasName &&
        verifySoftOtp == other.verifySoftOtp &&
        freeAmount == other.freeAmount &&
        referralCode == other.referralCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, fromAccount.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, verifySoftOtp.hashCode);
    _$hash = $jc(_$hash, freeAmount.hashCode);
    _$hash = $jc(_$hash, referralCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OpenVipAccountRequest')
          ..add('transactionNo', transactionNo)
          ..add('accountNo', accountNo)
          ..add('fromAccount', fromAccount)
          ..add('aliasName', aliasName)
          ..add('verifySoftOtp', verifySoftOtp)
          ..add('freeAmount', freeAmount)
          ..add('referralCode', referralCode))
        .toString();
  }
}

class OpenVipAccountRequestBuilder
    implements Builder<OpenVipAccountRequest, OpenVipAccountRequestBuilder> {
  _$OpenVipAccountRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _fromAccount;
  String? get fromAccount => _$this._fromAccount;
  set fromAccount(String? fromAccount) => _$this._fromAccount = fromAccount;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  VerifySoftOtpRequestBuilder? _verifySoftOtp;
  VerifySoftOtpRequestBuilder get verifySoftOtp =>
      _$this._verifySoftOtp ??= VerifySoftOtpRequestBuilder();
  set verifySoftOtp(VerifySoftOtpRequestBuilder? verifySoftOtp) =>
      _$this._verifySoftOtp = verifySoftOtp;

  num? _freeAmount;
  num? get freeAmount => _$this._freeAmount;
  set freeAmount(num? freeAmount) => _$this._freeAmount = freeAmount;

  String? _referralCode;
  String? get referralCode => _$this._referralCode;
  set referralCode(String? referralCode) => _$this._referralCode = referralCode;

  OpenVipAccountRequestBuilder() {
    OpenVipAccountRequest._defaults(this);
  }

  OpenVipAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _accountNo = $v.accountNo;
      _fromAccount = $v.fromAccount;
      _aliasName = $v.aliasName;
      _verifySoftOtp = $v.verifySoftOtp?.toBuilder();
      _freeAmount = $v.freeAmount;
      _referralCode = $v.referralCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OpenVipAccountRequest other) {
    _$v = other as _$OpenVipAccountRequest;
  }

  @override
  void update(void Function(OpenVipAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OpenVipAccountRequest build() => _build();

  _$OpenVipAccountRequest _build() {
    _$OpenVipAccountRequest _$result;
    try {
      _$result = _$v ??
          _$OpenVipAccountRequest._(
            transactionNo: transactionNo,
            accountNo: accountNo,
            fromAccount: fromAccount,
            aliasName: aliasName,
            verifySoftOtp: _verifySoftOtp?.build(),
            freeAmount: freeAmount,
            referralCode: referralCode,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'verifySoftOtp';
        _verifySoftOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'OpenVipAccountRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
