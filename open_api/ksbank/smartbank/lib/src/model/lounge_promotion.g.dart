// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lounge_promotion.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoungePromotion extends LoungePromotion {
  @override
  final String? title;
  @override
  final BuiltList<String>? columns;
  @override
  final BuiltList<LoungeItems>? items;

  factory _$LoungePromotion([void Function(LoungePromotionBuilder)? updates]) =>
      (LoungePromotionBuilder()..update(updates))._build();

  _$LoungePromotion._({this.title, this.columns, this.items}) : super._();
  @override
  LoungePromotion rebuild(void Function(LoungePromotionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoungePromotionBuilder toBuilder() => LoungePromotionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoungePromotion &&
        title == other.title &&
        columns == other.columns &&
        items == other.items;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, columns.hashCode);
    _$hash = $jc(_$hash, items.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoungePromotion')
          ..add('title', title)
          ..add('columns', columns)
          ..add('items', items))
        .toString();
  }
}

class LoungePromotionBuilder
    implements Builder<LoungePromotion, LoungePromotionBuilder> {
  _$LoungePromotion? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  ListBuilder<String>? _columns;
  ListBuilder<String> get columns => _$this._columns ??= ListBuilder<String>();
  set columns(ListBuilder<String>? columns) => _$this._columns = columns;

  ListBuilder<LoungeItems>? _items;
  ListBuilder<LoungeItems> get items =>
      _$this._items ??= ListBuilder<LoungeItems>();
  set items(ListBuilder<LoungeItems>? items) => _$this._items = items;

  LoungePromotionBuilder() {
    LoungePromotion._defaults(this);
  }

  LoungePromotionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _columns = $v.columns?.toBuilder();
      _items = $v.items?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoungePromotion other) {
    _$v = other as _$LoungePromotion;
  }

  @override
  void update(void Function(LoungePromotionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoungePromotion build() => _build();

  _$LoungePromotion _build() {
    _$LoungePromotion _$result;
    try {
      _$result = _$v ??
          _$LoungePromotion._(
            title: title,
            columns: _columns?.build(),
            items: _items?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'columns';
        _columns?.build();
        _$failedField = 'items';
        _items?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'LoungePromotion', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
