// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'query_bill_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$QueryBillResponse extends QueryBillResponse {
  @override
  final String? serviceCode;
  @override
  final String? serviceName;
  @override
  final String? supplierCode;
  @override
  final String? supplierName;
  @override
  final String? serviceIcon;
  @override
  final String? productCode;
  @override
  final String? customerCode;
  @override
  final String? customerName;
  @override
  final String? customerAddress;
  @override
  final num? totalAmount;
  @override
  final num? totalFee;
  @override
  final String? periods;
  @override
  final BuiltList<BillInfoDto>? bills;
  @override
  final bool? allowManualAmount;

  factory _$QueryBillResponse(
          [void Function(QueryBillResponseBuilder)? updates]) =>
      (QueryBillResponseBuilder()..update(updates))._build();

  _$QueryBillResponse._(
      {this.serviceCode,
      this.serviceName,
      this.supplierCode,
      this.supplierName,
      this.serviceIcon,
      this.productCode,
      this.customerCode,
      this.customerName,
      this.customerAddress,
      this.totalAmount,
      this.totalFee,
      this.periods,
      this.bills,
      this.allowManualAmount})
      : super._();
  @override
  QueryBillResponse rebuild(void Function(QueryBillResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  QueryBillResponseBuilder toBuilder() =>
      QueryBillResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is QueryBillResponse &&
        serviceCode == other.serviceCode &&
        serviceName == other.serviceName &&
        supplierCode == other.supplierCode &&
        supplierName == other.supplierName &&
        serviceIcon == other.serviceIcon &&
        productCode == other.productCode &&
        customerCode == other.customerCode &&
        customerName == other.customerName &&
        customerAddress == other.customerAddress &&
        totalAmount == other.totalAmount &&
        totalFee == other.totalFee &&
        periods == other.periods &&
        bills == other.bills &&
        allowManualAmount == other.allowManualAmount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, serviceCode.hashCode);
    _$hash = $jc(_$hash, serviceName.hashCode);
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, serviceIcon.hashCode);
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, customerAddress.hashCode);
    _$hash = $jc(_$hash, totalAmount.hashCode);
    _$hash = $jc(_$hash, totalFee.hashCode);
    _$hash = $jc(_$hash, periods.hashCode);
    _$hash = $jc(_$hash, bills.hashCode);
    _$hash = $jc(_$hash, allowManualAmount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'QueryBillResponse')
          ..add('serviceCode', serviceCode)
          ..add('serviceName', serviceName)
          ..add('supplierCode', supplierCode)
          ..add('supplierName', supplierName)
          ..add('serviceIcon', serviceIcon)
          ..add('productCode', productCode)
          ..add('customerCode', customerCode)
          ..add('customerName', customerName)
          ..add('customerAddress', customerAddress)
          ..add('totalAmount', totalAmount)
          ..add('totalFee', totalFee)
          ..add('periods', periods)
          ..add('bills', bills)
          ..add('allowManualAmount', allowManualAmount))
        .toString();
  }
}

class QueryBillResponseBuilder
    implements Builder<QueryBillResponse, QueryBillResponseBuilder> {
  _$QueryBillResponse? _$v;

  String? _serviceCode;
  String? get serviceCode => _$this._serviceCode;
  set serviceCode(String? serviceCode) => _$this._serviceCode = serviceCode;

  String? _serviceName;
  String? get serviceName => _$this._serviceName;
  set serviceName(String? serviceName) => _$this._serviceName = serviceName;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _serviceIcon;
  String? get serviceIcon => _$this._serviceIcon;
  set serviceIcon(String? serviceIcon) => _$this._serviceIcon = serviceIcon;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _customerAddress;
  String? get customerAddress => _$this._customerAddress;
  set customerAddress(String? customerAddress) =>
      _$this._customerAddress = customerAddress;

  num? _totalAmount;
  num? get totalAmount => _$this._totalAmount;
  set totalAmount(num? totalAmount) => _$this._totalAmount = totalAmount;

  num? _totalFee;
  num? get totalFee => _$this._totalFee;
  set totalFee(num? totalFee) => _$this._totalFee = totalFee;

  String? _periods;
  String? get periods => _$this._periods;
  set periods(String? periods) => _$this._periods = periods;

  ListBuilder<BillInfoDto>? _bills;
  ListBuilder<BillInfoDto> get bills =>
      _$this._bills ??= ListBuilder<BillInfoDto>();
  set bills(ListBuilder<BillInfoDto>? bills) => _$this._bills = bills;

  bool? _allowManualAmount;
  bool? get allowManualAmount => _$this._allowManualAmount;
  set allowManualAmount(bool? allowManualAmount) =>
      _$this._allowManualAmount = allowManualAmount;

  QueryBillResponseBuilder() {
    QueryBillResponse._defaults(this);
  }

  QueryBillResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _serviceCode = $v.serviceCode;
      _serviceName = $v.serviceName;
      _supplierCode = $v.supplierCode;
      _supplierName = $v.supplierName;
      _serviceIcon = $v.serviceIcon;
      _productCode = $v.productCode;
      _customerCode = $v.customerCode;
      _customerName = $v.customerName;
      _customerAddress = $v.customerAddress;
      _totalAmount = $v.totalAmount;
      _totalFee = $v.totalFee;
      _periods = $v.periods;
      _bills = $v.bills?.toBuilder();
      _allowManualAmount = $v.allowManualAmount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(QueryBillResponse other) {
    _$v = other as _$QueryBillResponse;
  }

  @override
  void update(void Function(QueryBillResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  QueryBillResponse build() => _build();

  _$QueryBillResponse _build() {
    _$QueryBillResponse _$result;
    try {
      _$result = _$v ??
          _$QueryBillResponse._(
            serviceCode: serviceCode,
            serviceName: serviceName,
            supplierCode: supplierCode,
            supplierName: supplierName,
            serviceIcon: serviceIcon,
            productCode: productCode,
            customerCode: customerCode,
            customerName: customerName,
            customerAddress: customerAddress,
            totalAmount: totalAmount,
            totalFee: totalFee,
            periods: periods,
            bills: _bills?.build(),
            allowManualAmount: allowManualAmount,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'bills';
        _bills?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'QueryBillResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
