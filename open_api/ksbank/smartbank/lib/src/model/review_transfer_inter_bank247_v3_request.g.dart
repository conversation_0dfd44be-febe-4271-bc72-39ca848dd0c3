// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_transfer_inter_bank247_v3_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewTransferInterBank247V3Request
    extends ReviewTransferInterBank247V3Request {
  @override
  final String? description;
  @override
  final int? transactionGroup;
  @override
  final String? accountNoFrom;
  @override
  final String? accountNoTo;
  @override
  final int? isAccount;
  @override
  final String? targetBankCode;
  @override
  final num? amount;
  @override
  final String? channelCode;

  factory _$ReviewTransferInterBank247V3Request(
          [void Function(ReviewTransferInterBank247V3RequestBuilder)?
              updates]) =>
      (ReviewTransferInterBank247V3RequestBuilder()..update(updates))._build();

  _$ReviewTransferInterBank247V3Request._(
      {this.description,
      this.transactionGroup,
      this.accountNoFrom,
      this.accountNoTo,
      this.isAccount,
      this.targetBankCode,
      this.amount,
      this.channelCode})
      : super._();
  @override
  ReviewTransferInterBank247V3Request rebuild(
          void Function(ReviewTransferInterBank247V3RequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewTransferInterBank247V3RequestBuilder toBuilder() =>
      ReviewTransferInterBank247V3RequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewTransferInterBank247V3Request &&
        description == other.description &&
        transactionGroup == other.transactionGroup &&
        accountNoFrom == other.accountNoFrom &&
        accountNoTo == other.accountNoTo &&
        isAccount == other.isAccount &&
        targetBankCode == other.targetBankCode &&
        amount == other.amount &&
        channelCode == other.channelCode;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, transactionGroup.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jc(_$hash, targetBankCode.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, channelCode.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewTransferInterBank247V3Request')
          ..add('description', description)
          ..add('transactionGroup', transactionGroup)
          ..add('accountNoFrom', accountNoFrom)
          ..add('accountNoTo', accountNoTo)
          ..add('isAccount', isAccount)
          ..add('targetBankCode', targetBankCode)
          ..add('amount', amount)
          ..add('channelCode', channelCode))
        .toString();
  }
}

class ReviewTransferInterBank247V3RequestBuilder
    implements
        Builder<ReviewTransferInterBank247V3Request,
            ReviewTransferInterBank247V3RequestBuilder> {
  _$ReviewTransferInterBank247V3Request? _$v;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  int? _transactionGroup;
  int? get transactionGroup => _$this._transactionGroup;
  set transactionGroup(int? transactionGroup) =>
      _$this._transactionGroup = transactionGroup;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  String? _targetBankCode;
  String? get targetBankCode => _$this._targetBankCode;
  set targetBankCode(String? targetBankCode) =>
      _$this._targetBankCode = targetBankCode;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _channelCode;
  String? get channelCode => _$this._channelCode;
  set channelCode(String? channelCode) => _$this._channelCode = channelCode;

  ReviewTransferInterBank247V3RequestBuilder() {
    ReviewTransferInterBank247V3Request._defaults(this);
  }

  ReviewTransferInterBank247V3RequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _description = $v.description;
      _transactionGroup = $v.transactionGroup;
      _accountNoFrom = $v.accountNoFrom;
      _accountNoTo = $v.accountNoTo;
      _isAccount = $v.isAccount;
      _targetBankCode = $v.targetBankCode;
      _amount = $v.amount;
      _channelCode = $v.channelCode;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewTransferInterBank247V3Request other) {
    _$v = other as _$ReviewTransferInterBank247V3Request;
  }

  @override
  void update(
      void Function(ReviewTransferInterBank247V3RequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewTransferInterBank247V3Request build() => _build();

  _$ReviewTransferInterBank247V3Request _build() {
    final _$result = _$v ??
        _$ReviewTransferInterBank247V3Request._(
          description: description,
          transactionGroup: transactionGroup,
          accountNoFrom: accountNoFrom,
          accountNoTo: accountNoTo,
          isAccount: isAccount,
          targetBankCode: targetBankCode,
          amount: amount,
          channelCode: channelCode,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
