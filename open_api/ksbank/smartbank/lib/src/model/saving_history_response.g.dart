// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saving_history_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const SavingHistoryResponseSavingTypeEnum
    _$savingHistoryResponseSavingTypeEnum_ONLINE_SAVING =
    const SavingHistoryResponseSavingTypeEnum._('ONLINE_SAVING');
const SavingHistoryResponseSavingTypeEnum
    _$savingHistoryResponseSavingTypeEnum_TARGET_SAVING =
    const SavingHistoryResponseSavingTypeEnum._('TARGET_SAVING');
const SavingHistoryResponseSavingTypeEnum
    _$savingHistoryResponseSavingTypeEnum_OFFLINE_SAVING =
    const SavingHistoryResponseSavingTypeEnum._('OFFLINE_SAVING');

SavingHistoryResponseSavingTypeEnum
    _$savingHistoryResponseSavingTypeEnumValueOf(String name) {
  switch (name) {
    case 'ONLINE_SAVING':
      return _$savingHistoryResponseSavingTypeEnum_ONLINE_SAVING;
    case 'TARGET_SAVING':
      return _$savingHistoryResponseSavingTypeEnum_TARGET_SAVING;
    case 'OFFLINE_SAVING':
      return _$savingHistoryResponseSavingTypeEnum_OFFLINE_SAVING;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<SavingHistoryResponseSavingTypeEnum>
    _$savingHistoryResponseSavingTypeEnumValues = BuiltSet<
        SavingHistoryResponseSavingTypeEnum>(const <SavingHistoryResponseSavingTypeEnum>[
  _$savingHistoryResponseSavingTypeEnum_ONLINE_SAVING,
  _$savingHistoryResponseSavingTypeEnum_TARGET_SAVING,
  _$savingHistoryResponseSavingTypeEnum_OFFLINE_SAVING,
]);

const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_VND =
    const SavingHistoryResponseCurrencyEnum._('VND');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_USD =
    const SavingHistoryResponseCurrencyEnum._('USD');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_ACB =
    const SavingHistoryResponseCurrencyEnum._('ACB');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_JPY =
    const SavingHistoryResponseCurrencyEnum._('JPY');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_GOLD =
    const SavingHistoryResponseCurrencyEnum._('GOLD');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_EUR =
    const SavingHistoryResponseCurrencyEnum._('EUR');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_GBP =
    const SavingHistoryResponseCurrencyEnum._('GBP');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_CHF =
    const SavingHistoryResponseCurrencyEnum._('CHF');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_AUD =
    const SavingHistoryResponseCurrencyEnum._('AUD');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_CAD =
    const SavingHistoryResponseCurrencyEnum._('CAD');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_SGD =
    const SavingHistoryResponseCurrencyEnum._('SGD');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_THB =
    const SavingHistoryResponseCurrencyEnum._('THB');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_NOK =
    const SavingHistoryResponseCurrencyEnum._('NOK');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_NZD =
    const SavingHistoryResponseCurrencyEnum._('NZD');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_DKK =
    const SavingHistoryResponseCurrencyEnum._('DKK');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_HKD =
    const SavingHistoryResponseCurrencyEnum._('HKD');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_SEK =
    const SavingHistoryResponseCurrencyEnum._('SEK');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_MYR =
    const SavingHistoryResponseCurrencyEnum._('MYR');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_XAU =
    const SavingHistoryResponseCurrencyEnum._('XAU');
const SavingHistoryResponseCurrencyEnum
    _$savingHistoryResponseCurrencyEnum_MMK =
    const SavingHistoryResponseCurrencyEnum._('MMK');

SavingHistoryResponseCurrencyEnum _$savingHistoryResponseCurrencyEnumValueOf(
    String name) {
  switch (name) {
    case 'VND':
      return _$savingHistoryResponseCurrencyEnum_VND;
    case 'USD':
      return _$savingHistoryResponseCurrencyEnum_USD;
    case 'ACB':
      return _$savingHistoryResponseCurrencyEnum_ACB;
    case 'JPY':
      return _$savingHistoryResponseCurrencyEnum_JPY;
    case 'GOLD':
      return _$savingHistoryResponseCurrencyEnum_GOLD;
    case 'EUR':
      return _$savingHistoryResponseCurrencyEnum_EUR;
    case 'GBP':
      return _$savingHistoryResponseCurrencyEnum_GBP;
    case 'CHF':
      return _$savingHistoryResponseCurrencyEnum_CHF;
    case 'AUD':
      return _$savingHistoryResponseCurrencyEnum_AUD;
    case 'CAD':
      return _$savingHistoryResponseCurrencyEnum_CAD;
    case 'SGD':
      return _$savingHistoryResponseCurrencyEnum_SGD;
    case 'THB':
      return _$savingHistoryResponseCurrencyEnum_THB;
    case 'NOK':
      return _$savingHistoryResponseCurrencyEnum_NOK;
    case 'NZD':
      return _$savingHistoryResponseCurrencyEnum_NZD;
    case 'DKK':
      return _$savingHistoryResponseCurrencyEnum_DKK;
    case 'HKD':
      return _$savingHistoryResponseCurrencyEnum_HKD;
    case 'SEK':
      return _$savingHistoryResponseCurrencyEnum_SEK;
    case 'MYR':
      return _$savingHistoryResponseCurrencyEnum_MYR;
    case 'XAU':
      return _$savingHistoryResponseCurrencyEnum_XAU;
    case 'MMK':
      return _$savingHistoryResponseCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<SavingHistoryResponseCurrencyEnum>
    _$savingHistoryResponseCurrencyEnumValues = BuiltSet<
        SavingHistoryResponseCurrencyEnum>(const <SavingHistoryResponseCurrencyEnum>[
  _$savingHistoryResponseCurrencyEnum_VND,
  _$savingHistoryResponseCurrencyEnum_USD,
  _$savingHistoryResponseCurrencyEnum_ACB,
  _$savingHistoryResponseCurrencyEnum_JPY,
  _$savingHistoryResponseCurrencyEnum_GOLD,
  _$savingHistoryResponseCurrencyEnum_EUR,
  _$savingHistoryResponseCurrencyEnum_GBP,
  _$savingHistoryResponseCurrencyEnum_CHF,
  _$savingHistoryResponseCurrencyEnum_AUD,
  _$savingHistoryResponseCurrencyEnum_CAD,
  _$savingHistoryResponseCurrencyEnum_SGD,
  _$savingHistoryResponseCurrencyEnum_THB,
  _$savingHistoryResponseCurrencyEnum_NOK,
  _$savingHistoryResponseCurrencyEnum_NZD,
  _$savingHistoryResponseCurrencyEnum_DKK,
  _$savingHistoryResponseCurrencyEnum_HKD,
  _$savingHistoryResponseCurrencyEnum_SEK,
  _$savingHistoryResponseCurrencyEnum_MYR,
  _$savingHistoryResponseCurrencyEnum_XAU,
  _$savingHistoryResponseCurrencyEnum_MMK,
]);

Serializer<SavingHistoryResponseSavingTypeEnum>
    _$savingHistoryResponseSavingTypeEnumSerializer =
    _$SavingHistoryResponseSavingTypeEnumSerializer();
Serializer<SavingHistoryResponseCurrencyEnum>
    _$savingHistoryResponseCurrencyEnumSerializer =
    _$SavingHistoryResponseCurrencyEnumSerializer();

class _$SavingHistoryResponseSavingTypeEnumSerializer
    implements PrimitiveSerializer<SavingHistoryResponseSavingTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'ONLINE_SAVING': 'ONLINE_SAVING',
    'TARGET_SAVING': 'TARGET_SAVING',
    'OFFLINE_SAVING': 'OFFLINE_SAVING',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'ONLINE_SAVING': 'ONLINE_SAVING',
    'TARGET_SAVING': 'TARGET_SAVING',
    'OFFLINE_SAVING': 'OFFLINE_SAVING',
  };

  @override
  final Iterable<Type> types = const <Type>[
    SavingHistoryResponseSavingTypeEnum
  ];
  @override
  final String wireName = 'SavingHistoryResponseSavingTypeEnum';

  @override
  Object serialize(
          Serializers serializers, SavingHistoryResponseSavingTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  SavingHistoryResponseSavingTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      SavingHistoryResponseSavingTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$SavingHistoryResponseCurrencyEnumSerializer
    implements PrimitiveSerializer<SavingHistoryResponseCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[SavingHistoryResponseCurrencyEnum];
  @override
  final String wireName = 'SavingHistoryResponseCurrencyEnum';

  @override
  Object serialize(
          Serializers serializers, SavingHistoryResponseCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  SavingHistoryResponseCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      SavingHistoryResponseCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$SavingHistoryResponse extends SavingHistoryResponse {
  @override
  final String? accountNo;
  @override
  final SavingHistoryResponseSavingTypeEnum? savingType;
  @override
  final String? accountName;
  @override
  final double? amount;
  @override
  final SavingHistoryResponseCurrencyEnum? currency;
  @override
  final int? accountStatus;
  @override
  final DateTime? startDate;
  @override
  final DateTime? dueDate;

  factory _$SavingHistoryResponse(
          [void Function(SavingHistoryResponseBuilder)? updates]) =>
      (SavingHistoryResponseBuilder()..update(updates))._build();

  _$SavingHistoryResponse._(
      {this.accountNo,
      this.savingType,
      this.accountName,
      this.amount,
      this.currency,
      this.accountStatus,
      this.startDate,
      this.dueDate})
      : super._();
  @override
  SavingHistoryResponse rebuild(
          void Function(SavingHistoryResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SavingHistoryResponseBuilder toBuilder() =>
      SavingHistoryResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SavingHistoryResponse &&
        accountNo == other.accountNo &&
        savingType == other.savingType &&
        accountName == other.accountName &&
        amount == other.amount &&
        currency == other.currency &&
        accountStatus == other.accountStatus &&
        startDate == other.startDate &&
        dueDate == other.dueDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, savingType.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, accountStatus.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SavingHistoryResponse')
          ..add('accountNo', accountNo)
          ..add('savingType', savingType)
          ..add('accountName', accountName)
          ..add('amount', amount)
          ..add('currency', currency)
          ..add('accountStatus', accountStatus)
          ..add('startDate', startDate)
          ..add('dueDate', dueDate))
        .toString();
  }
}

class SavingHistoryResponseBuilder
    implements Builder<SavingHistoryResponse, SavingHistoryResponseBuilder> {
  _$SavingHistoryResponse? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  SavingHistoryResponseSavingTypeEnum? _savingType;
  SavingHistoryResponseSavingTypeEnum? get savingType => _$this._savingType;
  set savingType(SavingHistoryResponseSavingTypeEnum? savingType) =>
      _$this._savingType = savingType;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  SavingHistoryResponseCurrencyEnum? _currency;
  SavingHistoryResponseCurrencyEnum? get currency => _$this._currency;
  set currency(SavingHistoryResponseCurrencyEnum? currency) =>
      _$this._currency = currency;

  int? _accountStatus;
  int? get accountStatus => _$this._accountStatus;
  set accountStatus(int? accountStatus) =>
      _$this._accountStatus = accountStatus;

  DateTime? _startDate;
  DateTime? get startDate => _$this._startDate;
  set startDate(DateTime? startDate) => _$this._startDate = startDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  SavingHistoryResponseBuilder() {
    SavingHistoryResponse._defaults(this);
  }

  SavingHistoryResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _savingType = $v.savingType;
      _accountName = $v.accountName;
      _amount = $v.amount;
      _currency = $v.currency;
      _accountStatus = $v.accountStatus;
      _startDate = $v.startDate;
      _dueDate = $v.dueDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SavingHistoryResponse other) {
    _$v = other as _$SavingHistoryResponse;
  }

  @override
  void update(void Function(SavingHistoryResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SavingHistoryResponse build() => _build();

  _$SavingHistoryResponse _build() {
    final _$result = _$v ??
        _$SavingHistoryResponse._(
          accountNo: accountNo,
          savingType: savingType,
          accountName: accountName,
          amount: amount,
          currency: currency,
          accountStatus: accountStatus,
          startDate: startDate,
          dueDate: dueDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
