// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_create_schedule_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewCreateScheduleResponse extends ReviewCreateScheduleResponse {
  @override
  final String? transactionNumber;
  @override
  final TransNextStep? transNextStep;
  @override
  final String? content;
  @override
  final String? customerCode;
  @override
  final String? customerName;
  @override
  final String? customerAddress;

  factory _$ReviewCreateScheduleResponse(
          [void Function(ReviewCreateScheduleResponseBuilder)? updates]) =>
      (ReviewCreateScheduleResponseBuilder()..update(updates))._build();

  _$ReviewCreateScheduleResponse._(
      {this.transactionNumber,
      this.transNextStep,
      this.content,
      this.customerCode,
      this.customerName,
      this.customerAddress})
      : super._();
  @override
  ReviewCreateScheduleResponse rebuild(
          void Function(ReviewCreateScheduleResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCreateScheduleResponseBuilder toBuilder() =>
      ReviewCreateScheduleResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCreateScheduleResponse &&
        transactionNumber == other.transactionNumber &&
        transNextStep == other.transNextStep &&
        content == other.content &&
        customerCode == other.customerCode &&
        customerName == other.customerName &&
        customerAddress == other.customerAddress;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, transNextStep.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, customerAddress.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewCreateScheduleResponse')
          ..add('transactionNumber', transactionNumber)
          ..add('transNextStep', transNextStep)
          ..add('content', content)
          ..add('customerCode', customerCode)
          ..add('customerName', customerName)
          ..add('customerAddress', customerAddress))
        .toString();
  }
}

class ReviewCreateScheduleResponseBuilder
    implements
        Builder<ReviewCreateScheduleResponse,
            ReviewCreateScheduleResponseBuilder> {
  _$ReviewCreateScheduleResponse? _$v;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  TransNextStep? _transNextStep;
  TransNextStep? get transNextStep => _$this._transNextStep;
  set transNextStep(TransNextStep? transNextStep) =>
      _$this._transNextStep = transNextStep;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _customerAddress;
  String? get customerAddress => _$this._customerAddress;
  set customerAddress(String? customerAddress) =>
      _$this._customerAddress = customerAddress;

  ReviewCreateScheduleResponseBuilder() {
    ReviewCreateScheduleResponse._defaults(this);
  }

  ReviewCreateScheduleResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNumber = $v.transactionNumber;
      _transNextStep = $v.transNextStep;
      _content = $v.content;
      _customerCode = $v.customerCode;
      _customerName = $v.customerName;
      _customerAddress = $v.customerAddress;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCreateScheduleResponse other) {
    _$v = other as _$ReviewCreateScheduleResponse;
  }

  @override
  void update(void Function(ReviewCreateScheduleResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCreateScheduleResponse build() => _build();

  _$ReviewCreateScheduleResponse _build() {
    final _$result = _$v ??
        _$ReviewCreateScheduleResponse._(
          transactionNumber: transactionNumber,
          transNextStep: transNextStep,
          content: content,
          customerCode: customerCode,
          customerName: customerName,
          customerAddress: customerAddress,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
