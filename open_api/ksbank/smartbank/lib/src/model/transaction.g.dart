// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Transaction extends Transaction {
  @override
  final String? transactionNumber;
  @override
  final String? rtxnNo;
  @override
  final String? transactionTime;
  @override
  final String? description;
  @override
  final String? accountNoFrom;
  @override
  final String? accountNoTo;
  @override
  final num? amount;
  @override
  final int? fee;
  @override
  final int? tax;

  factory _$Transaction([void Function(TransactionBuilder)? updates]) =>
      (TransactionBuilder()..update(updates))._build();

  _$Transaction._(
      {this.transactionNumber,
      this.rtxnNo,
      this.transactionTime,
      this.description,
      this.accountNoFrom,
      this.accountNoTo,
      this.amount,
      this.fee,
      this.tax})
      : super._();
  @override
  Transaction rebuild(void Function(TransactionBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionBuilder toBuilder() => TransactionBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Transaction &&
        transactionNumber == other.transactionNumber &&
        rtxnNo == other.rtxnNo &&
        transactionTime == other.transactionTime &&
        description == other.description &&
        accountNoFrom == other.accountNoFrom &&
        accountNoTo == other.accountNoTo &&
        amount == other.amount &&
        fee == other.fee &&
        tax == other.tax;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, rtxnNo.hashCode);
    _$hash = $jc(_$hash, transactionTime.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, accountNoFrom.hashCode);
    _$hash = $jc(_$hash, accountNoTo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jc(_$hash, tax.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Transaction')
          ..add('transactionNumber', transactionNumber)
          ..add('rtxnNo', rtxnNo)
          ..add('transactionTime', transactionTime)
          ..add('description', description)
          ..add('accountNoFrom', accountNoFrom)
          ..add('accountNoTo', accountNoTo)
          ..add('amount', amount)
          ..add('fee', fee)
          ..add('tax', tax))
        .toString();
  }
}

class TransactionBuilder implements Builder<Transaction, TransactionBuilder> {
  _$Transaction? _$v;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  String? _rtxnNo;
  String? get rtxnNo => _$this._rtxnNo;
  set rtxnNo(String? rtxnNo) => _$this._rtxnNo = rtxnNo;

  String? _transactionTime;
  String? get transactionTime => _$this._transactionTime;
  set transactionTime(String? transactionTime) =>
      _$this._transactionTime = transactionTime;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _accountNoFrom;
  String? get accountNoFrom => _$this._accountNoFrom;
  set accountNoFrom(String? accountNoFrom) =>
      _$this._accountNoFrom = accountNoFrom;

  String? _accountNoTo;
  String? get accountNoTo => _$this._accountNoTo;
  set accountNoTo(String? accountNoTo) => _$this._accountNoTo = accountNoTo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  int? _fee;
  int? get fee => _$this._fee;
  set fee(int? fee) => _$this._fee = fee;

  int? _tax;
  int? get tax => _$this._tax;
  set tax(int? tax) => _$this._tax = tax;

  TransactionBuilder() {
    Transaction._defaults(this);
  }

  TransactionBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNumber = $v.transactionNumber;
      _rtxnNo = $v.rtxnNo;
      _transactionTime = $v.transactionTime;
      _description = $v.description;
      _accountNoFrom = $v.accountNoFrom;
      _accountNoTo = $v.accountNoTo;
      _amount = $v.amount;
      _fee = $v.fee;
      _tax = $v.tax;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Transaction other) {
    _$v = other as _$Transaction;
  }

  @override
  void update(void Function(TransactionBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Transaction build() => _build();

  _$Transaction _build() {
    final _$result = _$v ??
        _$Transaction._(
          transactionNumber: transactionNumber,
          rtxnNo: rtxnNo,
          transactionTime: transactionTime,
          description: description,
          accountNoFrom: accountNoFrom,
          accountNoTo: accountNoTo,
          amount: amount,
          fee: fee,
          tax: tax,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
