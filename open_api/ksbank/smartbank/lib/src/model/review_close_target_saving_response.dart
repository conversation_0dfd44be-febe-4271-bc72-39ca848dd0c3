//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_smartbank/src/model/trans_next_step.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'review_close_target_saving_response.g.dart';

/// ReviewCloseTargetSavingResponse
///
/// Properties:
/// * [transactionNo] - Số giao dịch
/// * [transactionDate] - <PERSON><PERSON><PERSON> thực hiện giao dịch. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [targetAccountNo] - Số tài khoản tiết kiệm mục tiêu
/// * [accountName] - Tên tài khoản
/// * [balance] - <PERSON><PERSON> tiền tiết kiệm
/// * [currency] - <PERSON><PERSON><PERSON> tiền
/// * [rate] - <PERSON><PERSON><PERSON> su<PERSON>
/// * [finalAmount] - <PERSON><PERSON> tiền nhận đ<PERSON> (sau khi tính lãi)
/// * [finalAccountNo] - Tài khoản thanh toán nhân được tiền tất toán
/// * [contractDate] - Ngày thực hiện giao dịch. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [transNextStep]
/// * [content] - Nội dung content app show nếu có
@BuiltValue()
abstract class ReviewCloseTargetSavingResponse
    implements
        Built<ReviewCloseTargetSavingResponse,
            ReviewCloseTargetSavingResponseBuilder> {
  /// Số giao dịch
  @BuiltValueField(wireName: r'transactionNo')
  String? get transactionNo;

  /// Ngày thực hiện giao dịch. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'transactionDate')
  DateTime? get transactionDate;

  /// Số tài khoản tiết kiệm mục tiêu
  @BuiltValueField(wireName: r'targetAccountNo')
  String? get targetAccountNo;

  /// Tên tài khoản
  @BuiltValueField(wireName: r'accountName')
  String? get accountName;

  /// Số tiền tiết kiệm
  @BuiltValueField(wireName: r'balance')
  num? get balance;

  /// Loại tiền
  @BuiltValueField(wireName: r'currency')
  ReviewCloseTargetSavingResponseCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  /// Lãi suất
  @BuiltValueField(wireName: r'rate')
  double? get rate;

  /// Sô tiền nhận được (sau khi tính lãi)
  @BuiltValueField(wireName: r'finalAmount')
  num? get finalAmount;

  /// Tài khoản thanh toán nhân được tiền tất toán
  @BuiltValueField(wireName: r'finalAccountNo')
  String? get finalAccountNo;

  /// Ngày thực hiện giao dịch. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'contractDate')
  DateTime? get contractDate;

  @BuiltValueField(wireName: r'transNextStep')
  TransNextStep? get transNextStep;
  // enum transNextStepEnum {  NONE,  ENABLE_FACE_ID,  SHOW_GUIDE,  SHOW_GUIDE_GTTT_EXPIRED,  };

  /// Nội dung content app show nếu có
  @BuiltValueField(wireName: r'content')
  String? get content;

  ReviewCloseTargetSavingResponse._();

  factory ReviewCloseTargetSavingResponse(
          [void updates(ReviewCloseTargetSavingResponseBuilder b)]) =
      _$ReviewCloseTargetSavingResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ReviewCloseTargetSavingResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ReviewCloseTargetSavingResponse> get serializer =>
      _$ReviewCloseTargetSavingResponseSerializer();
}

class _$ReviewCloseTargetSavingResponseSerializer
    implements PrimitiveSerializer<ReviewCloseTargetSavingResponse> {
  @override
  final Iterable<Type> types = const [
    ReviewCloseTargetSavingResponse,
    _$ReviewCloseTargetSavingResponse
  ];

  @override
  final String wireName = r'ReviewCloseTargetSavingResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ReviewCloseTargetSavingResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.transactionNo != null) {
      yield r'transactionNo';
      yield serializers.serialize(
        object.transactionNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.transactionDate != null) {
      yield r'transactionDate';
      yield serializers.serialize(
        object.transactionDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.targetAccountNo != null) {
      yield r'targetAccountNo';
      yield serializers.serialize(
        object.targetAccountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountName != null) {
      yield r'accountName';
      yield serializers.serialize(
        object.accountName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.balance != null) {
      yield r'balance';
      yield serializers.serialize(
        object.balance,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType: const FullType.nullable(
            ReviewCloseTargetSavingResponseCurrencyEnum),
      );
    }
    if (object.rate != null) {
      yield r'rate';
      yield serializers.serialize(
        object.rate,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.finalAmount != null) {
      yield r'finalAmount';
      yield serializers.serialize(
        object.finalAmount,
        specifiedType: const FullType.nullable(num),
      );
    }
    if (object.finalAccountNo != null) {
      yield r'finalAccountNo';
      yield serializers.serialize(
        object.finalAccountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.contractDate != null) {
      yield r'contractDate';
      yield serializers.serialize(
        object.contractDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.transNextStep != null) {
      yield r'transNextStep';
      yield serializers.serialize(
        object.transNextStep,
        specifiedType: const FullType.nullable(TransNextStep),
      );
    }
    if (object.content != null) {
      yield r'content';
      yield serializers.serialize(
        object.content,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ReviewCloseTargetSavingResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ReviewCloseTargetSavingResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'transactionNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.transactionNo = valueDes;
          break;
        case r'transactionDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.transactionDate = valueDes;
          break;
        case r'targetAccountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.targetAccountNo = valueDes;
          break;
        case r'accountName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountName = valueDes;
          break;
        case r'balance':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.balance = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                ReviewCloseTargetSavingResponseCurrencyEnum),
          ) as ReviewCloseTargetSavingResponseCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        case r'rate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.rate = valueDes;
          break;
        case r'finalAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(num),
          ) as num?;
          if (valueDes == null) continue;
          result.finalAmount = valueDes;
          break;
        case r'finalAccountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.finalAccountNo = valueDes;
          break;
        case r'contractDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.contractDate = valueDes;
          break;
        case r'transNextStep':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(TransNextStep),
          ) as TransNextStep?;
          if (valueDes == null) continue;
          result.transNextStep = valueDes;
          break;
        case r'content':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.content = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ReviewCloseTargetSavingResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ReviewCloseTargetSavingResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class ReviewCloseTargetSavingResponseCurrencyEnum extends EnumClass {
  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'VND')
  static const ReviewCloseTargetSavingResponseCurrencyEnum VND =
      _$reviewCloseTargetSavingResponseCurrencyEnum_VND;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'USD')
  static const ReviewCloseTargetSavingResponseCurrencyEnum USD =
      _$reviewCloseTargetSavingResponseCurrencyEnum_USD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'ACB')
  static const ReviewCloseTargetSavingResponseCurrencyEnum ACB =
      _$reviewCloseTargetSavingResponseCurrencyEnum_ACB;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'JPY')
  static const ReviewCloseTargetSavingResponseCurrencyEnum JPY =
      _$reviewCloseTargetSavingResponseCurrencyEnum_JPY;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const ReviewCloseTargetSavingResponseCurrencyEnum GOLD =
      _$reviewCloseTargetSavingResponseCurrencyEnum_GOLD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'EUR')
  static const ReviewCloseTargetSavingResponseCurrencyEnum EUR =
      _$reviewCloseTargetSavingResponseCurrencyEnum_EUR;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'GBP')
  static const ReviewCloseTargetSavingResponseCurrencyEnum GBP =
      _$reviewCloseTargetSavingResponseCurrencyEnum_GBP;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'CHF')
  static const ReviewCloseTargetSavingResponseCurrencyEnum CHF =
      _$reviewCloseTargetSavingResponseCurrencyEnum_CHF;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'AUD')
  static const ReviewCloseTargetSavingResponseCurrencyEnum AUD =
      _$reviewCloseTargetSavingResponseCurrencyEnum_AUD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'CAD')
  static const ReviewCloseTargetSavingResponseCurrencyEnum CAD =
      _$reviewCloseTargetSavingResponseCurrencyEnum_CAD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'SGD')
  static const ReviewCloseTargetSavingResponseCurrencyEnum SGD =
      _$reviewCloseTargetSavingResponseCurrencyEnum_SGD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'THB')
  static const ReviewCloseTargetSavingResponseCurrencyEnum THB =
      _$reviewCloseTargetSavingResponseCurrencyEnum_THB;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'NOK')
  static const ReviewCloseTargetSavingResponseCurrencyEnum NOK =
      _$reviewCloseTargetSavingResponseCurrencyEnum_NOK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'NZD')
  static const ReviewCloseTargetSavingResponseCurrencyEnum NZD =
      _$reviewCloseTargetSavingResponseCurrencyEnum_NZD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'DKK')
  static const ReviewCloseTargetSavingResponseCurrencyEnum DKK =
      _$reviewCloseTargetSavingResponseCurrencyEnum_DKK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'HKD')
  static const ReviewCloseTargetSavingResponseCurrencyEnum HKD =
      _$reviewCloseTargetSavingResponseCurrencyEnum_HKD;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'SEK')
  static const ReviewCloseTargetSavingResponseCurrencyEnum SEK =
      _$reviewCloseTargetSavingResponseCurrencyEnum_SEK;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'MYR')
  static const ReviewCloseTargetSavingResponseCurrencyEnum MYR =
      _$reviewCloseTargetSavingResponseCurrencyEnum_MYR;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'XAU')
  static const ReviewCloseTargetSavingResponseCurrencyEnum XAU =
      _$reviewCloseTargetSavingResponseCurrencyEnum_XAU;

  /// Loại tiền
  @BuiltValueEnumConst(wireName: r'MMK')
  static const ReviewCloseTargetSavingResponseCurrencyEnum MMK =
      _$reviewCloseTargetSavingResponseCurrencyEnum_MMK;

  static Serializer<ReviewCloseTargetSavingResponseCurrencyEnum>
      get serializer => _$reviewCloseTargetSavingResponseCurrencyEnumSerializer;

  const ReviewCloseTargetSavingResponseCurrencyEnum._(String name)
      : super(name);

  static BuiltSet<ReviewCloseTargetSavingResponseCurrencyEnum> get values =>
      _$reviewCloseTargetSavingResponseCurrencyEnumValues;
  static ReviewCloseTargetSavingResponseCurrencyEnum valueOf(String name) =>
      _$reviewCloseTargetSavingResponseCurrencyEnumValueOf(name);
}
