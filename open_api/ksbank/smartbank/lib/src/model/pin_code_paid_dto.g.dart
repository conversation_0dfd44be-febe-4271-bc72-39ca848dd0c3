// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pin_code_paid_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PinCodePaidDto extends PinCodePaidDto {
  @override
  final String? name;
  @override
  final String? cardCode;
  @override
  final String? cardSerial;
  @override
  final String? expireDate;
  @override
  final String? syntaxRechargePrepaid;
  @override
  final String? syntaxRechargePostpaid;
  @override
  final String? pinCodeType;
  @override
  final String? pinCodeName;
  @override
  final String? pinCodeDesc;
  @override
  final String? supplierName;

  factory _$PinCodePaidDto([void Function(PinCodePaidDtoBuilder)? updates]) =>
      (PinCodePaidDtoBuilder()..update(updates))._build();

  _$PinCodePaidDto._(
      {this.name,
      this.cardCode,
      this.cardSerial,
      this.expireDate,
      this.syntaxRechargePrepaid,
      this.syntaxRechargePostpaid,
      this.pinCodeType,
      this.pinCodeName,
      this.pinCodeDesc,
      this.supplierName})
      : super._();
  @override
  PinCodePaidDto rebuild(void Function(PinCodePaidDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PinCodePaidDtoBuilder toBuilder() => PinCodePaidDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PinCodePaidDto &&
        name == other.name &&
        cardCode == other.cardCode &&
        cardSerial == other.cardSerial &&
        expireDate == other.expireDate &&
        syntaxRechargePrepaid == other.syntaxRechargePrepaid &&
        syntaxRechargePostpaid == other.syntaxRechargePostpaid &&
        pinCodeType == other.pinCodeType &&
        pinCodeName == other.pinCodeName &&
        pinCodeDesc == other.pinCodeDesc &&
        supplierName == other.supplierName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, cardCode.hashCode);
    _$hash = $jc(_$hash, cardSerial.hashCode);
    _$hash = $jc(_$hash, expireDate.hashCode);
    _$hash = $jc(_$hash, syntaxRechargePrepaid.hashCode);
    _$hash = $jc(_$hash, syntaxRechargePostpaid.hashCode);
    _$hash = $jc(_$hash, pinCodeType.hashCode);
    _$hash = $jc(_$hash, pinCodeName.hashCode);
    _$hash = $jc(_$hash, pinCodeDesc.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PinCodePaidDto')
          ..add('name', name)
          ..add('cardCode', cardCode)
          ..add('cardSerial', cardSerial)
          ..add('expireDate', expireDate)
          ..add('syntaxRechargePrepaid', syntaxRechargePrepaid)
          ..add('syntaxRechargePostpaid', syntaxRechargePostpaid)
          ..add('pinCodeType', pinCodeType)
          ..add('pinCodeName', pinCodeName)
          ..add('pinCodeDesc', pinCodeDesc)
          ..add('supplierName', supplierName))
        .toString();
  }
}

class PinCodePaidDtoBuilder
    implements Builder<PinCodePaidDto, PinCodePaidDtoBuilder> {
  _$PinCodePaidDto? _$v;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _cardCode;
  String? get cardCode => _$this._cardCode;
  set cardCode(String? cardCode) => _$this._cardCode = cardCode;

  String? _cardSerial;
  String? get cardSerial => _$this._cardSerial;
  set cardSerial(String? cardSerial) => _$this._cardSerial = cardSerial;

  String? _expireDate;
  String? get expireDate => _$this._expireDate;
  set expireDate(String? expireDate) => _$this._expireDate = expireDate;

  String? _syntaxRechargePrepaid;
  String? get syntaxRechargePrepaid => _$this._syntaxRechargePrepaid;
  set syntaxRechargePrepaid(String? syntaxRechargePrepaid) =>
      _$this._syntaxRechargePrepaid = syntaxRechargePrepaid;

  String? _syntaxRechargePostpaid;
  String? get syntaxRechargePostpaid => _$this._syntaxRechargePostpaid;
  set syntaxRechargePostpaid(String? syntaxRechargePostpaid) =>
      _$this._syntaxRechargePostpaid = syntaxRechargePostpaid;

  String? _pinCodeType;
  String? get pinCodeType => _$this._pinCodeType;
  set pinCodeType(String? pinCodeType) => _$this._pinCodeType = pinCodeType;

  String? _pinCodeName;
  String? get pinCodeName => _$this._pinCodeName;
  set pinCodeName(String? pinCodeName) => _$this._pinCodeName = pinCodeName;

  String? _pinCodeDesc;
  String? get pinCodeDesc => _$this._pinCodeDesc;
  set pinCodeDesc(String? pinCodeDesc) => _$this._pinCodeDesc = pinCodeDesc;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  PinCodePaidDtoBuilder() {
    PinCodePaidDto._defaults(this);
  }

  PinCodePaidDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _name = $v.name;
      _cardCode = $v.cardCode;
      _cardSerial = $v.cardSerial;
      _expireDate = $v.expireDate;
      _syntaxRechargePrepaid = $v.syntaxRechargePrepaid;
      _syntaxRechargePostpaid = $v.syntaxRechargePostpaid;
      _pinCodeType = $v.pinCodeType;
      _pinCodeName = $v.pinCodeName;
      _pinCodeDesc = $v.pinCodeDesc;
      _supplierName = $v.supplierName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PinCodePaidDto other) {
    _$v = other as _$PinCodePaidDto;
  }

  @override
  void update(void Function(PinCodePaidDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PinCodePaidDto build() => _build();

  _$PinCodePaidDto _build() {
    final _$result = _$v ??
        _$PinCodePaidDto._(
          name: name,
          cardCode: cardCode,
          cardSerial: cardSerial,
          expireDate: expireDate,
          syntaxRechargePrepaid: syntaxRechargePrepaid,
          syntaxRechargePostpaid: syntaxRechargePostpaid,
          pinCodeType: pinCodeType,
          pinCodeName: pinCodeName,
          pinCodeDesc: pinCodeDesc,
          supplierName: supplierName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
