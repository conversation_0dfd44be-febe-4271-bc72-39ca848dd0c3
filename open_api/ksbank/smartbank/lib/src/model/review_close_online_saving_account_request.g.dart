// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_close_online_saving_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_VND =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('VND');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_USD =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('USD');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_ACB =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('ACB');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_JPY =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('JPY');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GOLD =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('GOLD');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_EUR =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('EUR');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GBP =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('GBP');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CHF =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('CHF');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_AUD =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('AUD');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CAD =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('CAD');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SGD =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('SGD');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_THB =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('THB');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NOK =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('NOK');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NZD =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('NZD');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_DKK =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('DKK');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_HKD =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('HKD');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SEK =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('SEK');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MYR =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('MYR');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_XAU =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('XAU');
const ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MMK =
    const ReviewCloseOnlineSavingAccountRequestCurrencyEnum._('MMK');

ReviewCloseOnlineSavingAccountRequestCurrencyEnum
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnumValueOf(String name) {
  switch (name) {
    case 'VND':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_VND;
    case 'USD':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_USD;
    case 'ACB':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_ACB;
    case 'JPY':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_JPY;
    case 'GOLD':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GOLD;
    case 'EUR':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_EUR;
    case 'GBP':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GBP;
    case 'CHF':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CHF;
    case 'AUD':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_AUD;
    case 'CAD':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CAD;
    case 'SGD':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SGD;
    case 'THB':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_THB;
    case 'NOK':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NOK;
    case 'NZD':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NZD;
    case 'DKK':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_DKK;
    case 'HKD':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_HKD;
    case 'SEK':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SEK;
    case 'MYR':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MYR;
    case 'XAU':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_XAU;
    case 'MMK':
      return _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MMK;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<ReviewCloseOnlineSavingAccountRequestCurrencyEnum>
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnumValues = BuiltSet<
        ReviewCloseOnlineSavingAccountRequestCurrencyEnum>(const <ReviewCloseOnlineSavingAccountRequestCurrencyEnum>[
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_VND,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_USD,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_ACB,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_JPY,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GOLD,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_EUR,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_GBP,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CHF,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_AUD,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_CAD,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SGD,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_THB,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NOK,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_NZD,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_DKK,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_HKD,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_SEK,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MYR,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_XAU,
  _$reviewCloseOnlineSavingAccountRequestCurrencyEnum_MMK,
]);

Serializer<ReviewCloseOnlineSavingAccountRequestCurrencyEnum>
    _$reviewCloseOnlineSavingAccountRequestCurrencyEnumSerializer =
    _$ReviewCloseOnlineSavingAccountRequestCurrencyEnumSerializer();

class _$ReviewCloseOnlineSavingAccountRequestCurrencyEnumSerializer
    implements
        PrimitiveSerializer<ReviewCloseOnlineSavingAccountRequestCurrencyEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VND': 'VND',
    'USD': 'USD',
    'ACB': 'ACB',
    'JPY': 'JPY',
    'GOLD': 'GOLD',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'CAD': 'CAD',
    'SGD': 'SGD',
    'THB': 'THB',
    'NOK': 'NOK',
    'NZD': 'NZD',
    'DKK': 'DKK',
    'HKD': 'HKD',
    'SEK': 'SEK',
    'MYR': 'MYR',
    'XAU': 'XAU',
    'MMK': 'MMK',
  };

  @override
  final Iterable<Type> types = const <Type>[
    ReviewCloseOnlineSavingAccountRequestCurrencyEnum
  ];
  @override
  final String wireName = 'ReviewCloseOnlineSavingAccountRequestCurrencyEnum';

  @override
  Object serialize(Serializers serializers,
          ReviewCloseOnlineSavingAccountRequestCurrencyEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  ReviewCloseOnlineSavingAccountRequestCurrencyEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      ReviewCloseOnlineSavingAccountRequestCurrencyEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$ReviewCloseOnlineSavingAccountRequest
    extends ReviewCloseOnlineSavingAccountRequest {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? accountNumber;
  @override
  final String? accountName;
  @override
  final double? balance;
  @override
  final ReviewCloseOnlineSavingAccountRequestCurrencyEnum? currency;
  @override
  final double? rate;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final double? finalAmount;
  @override
  final String? destinationAccount;

  factory _$ReviewCloseOnlineSavingAccountRequest(
          [void Function(ReviewCloseOnlineSavingAccountRequestBuilder)?
              updates]) =>
      (ReviewCloseOnlineSavingAccountRequestBuilder()..update(updates))
          ._build();

  _$ReviewCloseOnlineSavingAccountRequest._(
      {this.bankCif,
      this.transactionNo,
      this.accountNumber,
      this.accountName,
      this.balance,
      this.currency,
      this.rate,
      this.contractDate,
      this.dueDate,
      this.finalAmount,
      this.destinationAccount})
      : super._();
  @override
  ReviewCloseOnlineSavingAccountRequest rebuild(
          void Function(ReviewCloseOnlineSavingAccountRequestBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCloseOnlineSavingAccountRequestBuilder toBuilder() =>
      ReviewCloseOnlineSavingAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCloseOnlineSavingAccountRequest &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        accountNumber == other.accountNumber &&
        accountName == other.accountName &&
        balance == other.balance &&
        currency == other.currency &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        finalAmount == other.finalAmount &&
        destinationAccount == other.destinationAccount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, destinationAccount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewCloseOnlineSavingAccountRequest')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('accountNumber', accountNumber)
          ..add('accountName', accountName)
          ..add('balance', balance)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('finalAmount', finalAmount)
          ..add('destinationAccount', destinationAccount))
        .toString();
  }
}

class ReviewCloseOnlineSavingAccountRequestBuilder
    implements
        Builder<ReviewCloseOnlineSavingAccountRequest,
            ReviewCloseOnlineSavingAccountRequestBuilder> {
  _$ReviewCloseOnlineSavingAccountRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  double? _balance;
  double? get balance => _$this._balance;
  set balance(double? balance) => _$this._balance = balance;

  ReviewCloseOnlineSavingAccountRequestCurrencyEnum? _currency;
  ReviewCloseOnlineSavingAccountRequestCurrencyEnum? get currency =>
      _$this._currency;
  set currency(ReviewCloseOnlineSavingAccountRequestCurrencyEnum? currency) =>
      _$this._currency = currency;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  double? _finalAmount;
  double? get finalAmount => _$this._finalAmount;
  set finalAmount(double? finalAmount) => _$this._finalAmount = finalAmount;

  String? _destinationAccount;
  String? get destinationAccount => _$this._destinationAccount;
  set destinationAccount(String? destinationAccount) =>
      _$this._destinationAccount = destinationAccount;

  ReviewCloseOnlineSavingAccountRequestBuilder() {
    ReviewCloseOnlineSavingAccountRequest._defaults(this);
  }

  ReviewCloseOnlineSavingAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _accountNumber = $v.accountNumber;
      _accountName = $v.accountName;
      _balance = $v.balance;
      _currency = $v.currency;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _finalAmount = $v.finalAmount;
      _destinationAccount = $v.destinationAccount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCloseOnlineSavingAccountRequest other) {
    _$v = other as _$ReviewCloseOnlineSavingAccountRequest;
  }

  @override
  void update(
      void Function(ReviewCloseOnlineSavingAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCloseOnlineSavingAccountRequest build() => _build();

  _$ReviewCloseOnlineSavingAccountRequest _build() {
    final _$result = _$v ??
        _$ReviewCloseOnlineSavingAccountRequest._(
          bankCif: bankCif,
          transactionNo: transactionNo,
          accountNumber: accountNumber,
          accountName: accountName,
          balance: balance,
          currency: currency,
          rate: rate,
          contractDate: contractDate,
          dueDate: dueDate,
          finalAmount: finalAmount,
          destinationAccount: destinationAccount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
