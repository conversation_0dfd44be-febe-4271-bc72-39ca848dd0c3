//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'loan_detail_response.g.dart';

/// LoanDetailResponse
///
/// Properties:
/// * [accountNo] - Số tài khoản
/// * [accountName] - Tên tài khoản
/// * [currency] - Loại tiền : VND, USD
/// * [loanAmount] - Số tiền vay
/// * [paidAmount] - Số tiền khách hàng đã trả
/// * [remainAmount] - Số tiền còn lại khách hàng phải trả
/// * [nextRefundDate] - Ngày tiếp theo khách hàng phải trả tiền. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_<PERSON>
/// * [payAmount] - <PERSON><PERSON> tiền khách hàng phải trả lần tiếp theo
/// * [contractDate] - Ngày mở tài khoản. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [dueDate] - Ngày tất toán tài khoản. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
/// * [purposeTypeName] - Tên của loại mục đích vay
/// * [rate] - Lãi suất
/// * [imageUrl] - Đường dẫn ảnh mô tả cho khoản vay
@BuiltValue()
abstract class LoanDetailResponse
    implements Built<LoanDetailResponse, LoanDetailResponseBuilder> {
  /// Số tài khoản
  @BuiltValueField(wireName: r'accountNo')
  String? get accountNo;

  /// Tên tài khoản
  @BuiltValueField(wireName: r'accountName')
  String? get accountName;

  /// Loại tiền : VND, USD
  @BuiltValueField(wireName: r'currency')
  LoanDetailResponseCurrencyEnum? get currency;
  // enum currencyEnum {  VND,  USD,  ACB,  JPY,  GOLD,  EUR,  GBP,  CHF,  AUD,  CAD,  SGD,  THB,  NOK,  NZD,  DKK,  HKD,  SEK,  MYR,  XAU,  MMK,  };

  /// Số tiền vay
  @BuiltValueField(wireName: r'loanAmount')
  double? get loanAmount;

  /// Số tiền khách hàng đã trả
  @BuiltValueField(wireName: r'paidAmount')
  double? get paidAmount;

  /// Số tiền còn lại khách hàng phải trả
  @BuiltValueField(wireName: r'remainAmount')
  double? get remainAmount;

  /// Ngày tiếp theo khách hàng phải trả tiền. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'nextRefundDate')
  DateTime? get nextRefundDate;

  /// Số tiền khách hàng phải trả lần tiếp theo
  @BuiltValueField(wireName: r'payAmount')
  double? get payAmount;

  /// Ngày mở tài khoản. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'contractDate')
  DateTime? get contractDate;

  /// Ngày tất toán tài khoản. Định dạng yyyy-MM-dd'T'HH:mm:ss.SSS'Z' , timezone = Asia/Ho_Chi_Minh
  @BuiltValueField(wireName: r'dueDate')
  DateTime? get dueDate;

  /// Tên của loại mục đích vay
  @BuiltValueField(wireName: r'purposeTypeName')
  String? get purposeTypeName;

  /// Lãi suất
  @BuiltValueField(wireName: r'rate')
  double? get rate;

  /// Đường dẫn ảnh mô tả cho khoản vay
  @BuiltValueField(wireName: r'imageUrl')
  String? get imageUrl;

  LoanDetailResponse._();

  factory LoanDetailResponse([void updates(LoanDetailResponseBuilder b)]) =
      _$LoanDetailResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(LoanDetailResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<LoanDetailResponse> get serializer =>
      _$LoanDetailResponseSerializer();
}

class _$LoanDetailResponseSerializer
    implements PrimitiveSerializer<LoanDetailResponse> {
  @override
  final Iterable<Type> types = const [LoanDetailResponse, _$LoanDetailResponse];

  @override
  final String wireName = r'LoanDetailResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    LoanDetailResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.accountNo != null) {
      yield r'accountNo';
      yield serializers.serialize(
        object.accountNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.accountName != null) {
      yield r'accountName';
      yield serializers.serialize(
        object.accountName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.currency != null) {
      yield r'currency';
      yield serializers.serialize(
        object.currency,
        specifiedType: const FullType.nullable(LoanDetailResponseCurrencyEnum),
      );
    }
    if (object.loanAmount != null) {
      yield r'loanAmount';
      yield serializers.serialize(
        object.loanAmount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.paidAmount != null) {
      yield r'paidAmount';
      yield serializers.serialize(
        object.paidAmount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.remainAmount != null) {
      yield r'remainAmount';
      yield serializers.serialize(
        object.remainAmount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.nextRefundDate != null) {
      yield r'nextRefundDate';
      yield serializers.serialize(
        object.nextRefundDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.payAmount != null) {
      yield r'payAmount';
      yield serializers.serialize(
        object.payAmount,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.contractDate != null) {
      yield r'contractDate';
      yield serializers.serialize(
        object.contractDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.dueDate != null) {
      yield r'dueDate';
      yield serializers.serialize(
        object.dueDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.purposeTypeName != null) {
      yield r'purposeTypeName';
      yield serializers.serialize(
        object.purposeTypeName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.rate != null) {
      yield r'rate';
      yield serializers.serialize(
        object.rate,
        specifiedType: const FullType.nullable(double),
      );
    }
    if (object.imageUrl != null) {
      yield r'imageUrl';
      yield serializers.serialize(
        object.imageUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    LoanDetailResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required LoanDetailResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'accountNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountNo = valueDes;
          break;
        case r'accountName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.accountName = valueDes;
          break;
        case r'currency':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(LoanDetailResponseCurrencyEnum),
          ) as LoanDetailResponseCurrencyEnum?;
          if (valueDes == null) continue;
          result.currency = valueDes;
          break;
        case r'loanAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.loanAmount = valueDes;
          break;
        case r'paidAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.paidAmount = valueDes;
          break;
        case r'remainAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.remainAmount = valueDes;
          break;
        case r'nextRefundDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.nextRefundDate = valueDes;
          break;
        case r'payAmount':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.payAmount = valueDes;
          break;
        case r'contractDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.contractDate = valueDes;
          break;
        case r'dueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.dueDate = valueDes;
          break;
        case r'purposeTypeName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.purposeTypeName = valueDes;
          break;
        case r'rate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(double),
          ) as double?;
          if (valueDes == null) continue;
          result.rate = valueDes;
          break;
        case r'imageUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.imageUrl = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  LoanDetailResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = LoanDetailResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class LoanDetailResponseCurrencyEnum extends EnumClass {
  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'VND')
  static const LoanDetailResponseCurrencyEnum VND =
      _$loanDetailResponseCurrencyEnum_VND;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'USD')
  static const LoanDetailResponseCurrencyEnum USD =
      _$loanDetailResponseCurrencyEnum_USD;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'ACB')
  static const LoanDetailResponseCurrencyEnum ACB =
      _$loanDetailResponseCurrencyEnum_ACB;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'JPY')
  static const LoanDetailResponseCurrencyEnum JPY =
      _$loanDetailResponseCurrencyEnum_JPY;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'GOLD')
  static const LoanDetailResponseCurrencyEnum GOLD =
      _$loanDetailResponseCurrencyEnum_GOLD;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'EUR')
  static const LoanDetailResponseCurrencyEnum EUR =
      _$loanDetailResponseCurrencyEnum_EUR;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'GBP')
  static const LoanDetailResponseCurrencyEnum GBP =
      _$loanDetailResponseCurrencyEnum_GBP;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'CHF')
  static const LoanDetailResponseCurrencyEnum CHF =
      _$loanDetailResponseCurrencyEnum_CHF;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'AUD')
  static const LoanDetailResponseCurrencyEnum AUD =
      _$loanDetailResponseCurrencyEnum_AUD;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'CAD')
  static const LoanDetailResponseCurrencyEnum CAD =
      _$loanDetailResponseCurrencyEnum_CAD;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'SGD')
  static const LoanDetailResponseCurrencyEnum SGD =
      _$loanDetailResponseCurrencyEnum_SGD;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'THB')
  static const LoanDetailResponseCurrencyEnum THB =
      _$loanDetailResponseCurrencyEnum_THB;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'NOK')
  static const LoanDetailResponseCurrencyEnum NOK =
      _$loanDetailResponseCurrencyEnum_NOK;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'NZD')
  static const LoanDetailResponseCurrencyEnum NZD =
      _$loanDetailResponseCurrencyEnum_NZD;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'DKK')
  static const LoanDetailResponseCurrencyEnum DKK =
      _$loanDetailResponseCurrencyEnum_DKK;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'HKD')
  static const LoanDetailResponseCurrencyEnum HKD =
      _$loanDetailResponseCurrencyEnum_HKD;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'SEK')
  static const LoanDetailResponseCurrencyEnum SEK =
      _$loanDetailResponseCurrencyEnum_SEK;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'MYR')
  static const LoanDetailResponseCurrencyEnum MYR =
      _$loanDetailResponseCurrencyEnum_MYR;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'XAU')
  static const LoanDetailResponseCurrencyEnum XAU =
      _$loanDetailResponseCurrencyEnum_XAU;

  /// Loại tiền : VND, USD
  @BuiltValueEnumConst(wireName: r'MMK')
  static const LoanDetailResponseCurrencyEnum MMK =
      _$loanDetailResponseCurrencyEnum_MMK;

  static Serializer<LoanDetailResponseCurrencyEnum> get serializer =>
      _$loanDetailResponseCurrencyEnumSerializer;

  const LoanDetailResponseCurrencyEnum._(String name) : super(name);

  static BuiltSet<LoanDetailResponseCurrencyEnum> get values =>
      _$loanDetailResponseCurrencyEnumValues;
  static LoanDetailResponseCurrencyEnum valueOf(String name) =>
      _$loanDetailResponseCurrencyEnumValueOf(name);
}
