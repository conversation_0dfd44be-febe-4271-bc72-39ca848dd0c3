//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'trans_next_step.g.dart';

class TransNextStep extends EnumClass {
  /// B<PERSON>ớc tiếp theo của luồng giao dich
  @BuiltValueEnumConst(wireName: r'NONE')
  static const TransNextStep NONE = _$NONE;

  /// B<PERSON>ớc tiếp theo của luồng giao dich
  @BuiltValueEnumConst(wireName: r'ENABLE_FACE_ID')
  static const TransNextStep ENABLE_FACE_ID = _$ENABLE_FACE_ID;

  /// Bước tiếp theo của luồng giao dich
  @BuiltValueEnumConst(wireName: r'SHOW_GUIDE')
  static const TransNextStep SHOW_GUIDE = _$SHOW_GUIDE;

  /// <PERSON><PERSON><PERSON><PERSON> tiếp theo của luồng giao dich
  @BuiltValueEnumConst(wireName: r'SHOW_GUIDE_GTTT_EXPIRED')
  static const TransNextStep SHOW_GUIDE_GTTT_EXPIRED =
      _$SHOW_GUIDE_GTTT_EXPIRED;

  static Serializer<TransNextStep> get serializer => _$transNextStepSerializer;

  const TransNextStep._(String name) : super(name);

  static BuiltSet<TransNextStep> get values => _$values;
  static TransNextStep valueOf(String name) => _$valueOf(name);
}

/// Optionally, enum_class can generate a mixin to go with your enum for use
/// with Angular. It exposes your enum constants as getters. So, if you mix it
/// in to your Dart component class, the values become available to the
/// corresponding Angular template.
///
/// Trigger mixin generation by writing a line like this one next to your enum.
abstract class TransNextStepMixin = Object with _$TransNextStepMixin;
