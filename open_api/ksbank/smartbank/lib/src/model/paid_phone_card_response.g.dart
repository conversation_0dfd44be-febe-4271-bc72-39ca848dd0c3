// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paid_phone_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaidPhoneCardResponse extends PaidPhoneCardResponse {
  @override
  final String? cardId;
  @override
  final String? orderNo;
  @override
  final String? seriNumber;
  @override
  final String? expired;
  @override
  final num? cardValue;

  factory _$PaidPhoneCardResponse(
          [void Function(PaidPhoneCardResponseBuilder)? updates]) =>
      (PaidPhoneCardResponseBuilder()..update(updates))._build();

  _$PaidPhoneCardResponse._(
      {this.cardId,
      this.orderNo,
      this.seriNumber,
      this.expired,
      this.cardValue})
      : super._();
  @override
  PaidPhoneCardResponse rebuild(
          void Function(PaidPhoneCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaidPhoneCardResponseBuilder toBuilder() =>
      PaidPhoneCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaidPhoneCardResponse &&
        cardId == other.cardId &&
        orderNo == other.orderNo &&
        seriNumber == other.seriNumber &&
        expired == other.expired &&
        cardValue == other.cardValue;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jc(_$hash, orderNo.hashCode);
    _$hash = $jc(_$hash, seriNumber.hashCode);
    _$hash = $jc(_$hash, expired.hashCode);
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaidPhoneCardResponse')
          ..add('cardId', cardId)
          ..add('orderNo', orderNo)
          ..add('seriNumber', seriNumber)
          ..add('expired', expired)
          ..add('cardValue', cardValue))
        .toString();
  }
}

class PaidPhoneCardResponseBuilder
    implements Builder<PaidPhoneCardResponse, PaidPhoneCardResponseBuilder> {
  _$PaidPhoneCardResponse? _$v;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  String? _orderNo;
  String? get orderNo => _$this._orderNo;
  set orderNo(String? orderNo) => _$this._orderNo = orderNo;

  String? _seriNumber;
  String? get seriNumber => _$this._seriNumber;
  set seriNumber(String? seriNumber) => _$this._seriNumber = seriNumber;

  String? _expired;
  String? get expired => _$this._expired;
  set expired(String? expired) => _$this._expired = expired;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  PaidPhoneCardResponseBuilder() {
    PaidPhoneCardResponse._defaults(this);
  }

  PaidPhoneCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardId = $v.cardId;
      _orderNo = $v.orderNo;
      _seriNumber = $v.seriNumber;
      _expired = $v.expired;
      _cardValue = $v.cardValue;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaidPhoneCardResponse other) {
    _$v = other as _$PaidPhoneCardResponse;
  }

  @override
  void update(void Function(PaidPhoneCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaidPhoneCardResponse build() => _build();

  _$PaidPhoneCardResponse _build() {
    final _$result = _$v ??
        _$PaidPhoneCardResponse._(
          cardId: cardId,
          orderNo: orderNo,
          seriNumber: seriNumber,
          expired: expired,
          cardValue: cardValue,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
