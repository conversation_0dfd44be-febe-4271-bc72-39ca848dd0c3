// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pre_check_issue_virtual_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PreCheckIssueVirtualCardResponse
    extends PreCheckIssueVirtualCardResponse {
  @override
  final bool? valid;

  factory _$PreCheckIssueVirtualCardResponse(
          [void Function(PreCheckIssueVirtualCardResponseBuilder)? updates]) =>
      (PreCheckIssueVirtualCardResponseBuilder()..update(updates))._build();

  _$PreCheckIssueVirtualCardResponse._({this.valid}) : super._();
  @override
  PreCheckIssueVirtualCardResponse rebuild(
          void Function(PreCheckIssueVirtualCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PreCheckIssueVirtualCardResponseBuilder toBuilder() =>
      PreCheckIssueVirtualCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PreCheckIssueVirtualCardResponse && valid == other.valid;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, valid.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PreCheckIssueVirtualCardResponse')
          ..add('valid', valid))
        .toString();
  }
}

class PreCheckIssueVirtualCardResponseBuilder
    implements
        Builder<PreCheckIssueVirtualCardResponse,
            PreCheckIssueVirtualCardResponseBuilder> {
  _$PreCheckIssueVirtualCardResponse? _$v;

  bool? _valid;
  bool? get valid => _$this._valid;
  set valid(bool? valid) => _$this._valid = valid;

  PreCheckIssueVirtualCardResponseBuilder() {
    PreCheckIssueVirtualCardResponse._defaults(this);
  }

  PreCheckIssueVirtualCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _valid = $v.valid;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PreCheckIssueVirtualCardResponse other) {
    _$v = other as _$PreCheckIssueVirtualCardResponse;
  }

  @override
  void update(void Function(PreCheckIssueVirtualCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PreCheckIssueVirtualCardResponse build() => _build();

  _$PreCheckIssueVirtualCardResponse _build() {
    final _$result = _$v ??
        _$PreCheckIssueVirtualCardResponse._(
          valid: valid,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
