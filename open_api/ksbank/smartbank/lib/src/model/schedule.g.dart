// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Schedule extends Schedule {
  @override
  final String? id;
  @override
  final String? remoteScheduleId;
  @override
  final String? remoteScheduleHistoryId;
  @override
  final String? name;
  @override
  final String? target;
  @override
  final String? accessKey;

  factory _$Schedule([void Function(ScheduleBuilder)? updates]) =>
      (ScheduleBuilder()..update(updates))._build();

  _$Schedule._(
      {this.id,
      this.remoteScheduleId,
      this.remoteScheduleHistoryId,
      this.name,
      this.target,
      this.accessKey})
      : super._();
  @override
  Schedule rebuild(void Function(ScheduleBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ScheduleBuilder toBuilder() => ScheduleBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Schedule &&
        id == other.id &&
        remoteScheduleId == other.remoteScheduleId &&
        remoteScheduleHistoryId == other.remoteScheduleHistoryId &&
        name == other.name &&
        target == other.target &&
        accessKey == other.accessKey;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, remoteScheduleId.hashCode);
    _$hash = $jc(_$hash, remoteScheduleHistoryId.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, target.hashCode);
    _$hash = $jc(_$hash, accessKey.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Schedule')
          ..add('id', id)
          ..add('remoteScheduleId', remoteScheduleId)
          ..add('remoteScheduleHistoryId', remoteScheduleHistoryId)
          ..add('name', name)
          ..add('target', target)
          ..add('accessKey', accessKey))
        .toString();
  }
}

class ScheduleBuilder implements Builder<Schedule, ScheduleBuilder> {
  _$Schedule? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _remoteScheduleId;
  String? get remoteScheduleId => _$this._remoteScheduleId;
  set remoteScheduleId(String? remoteScheduleId) =>
      _$this._remoteScheduleId = remoteScheduleId;

  String? _remoteScheduleHistoryId;
  String? get remoteScheduleHistoryId => _$this._remoteScheduleHistoryId;
  set remoteScheduleHistoryId(String? remoteScheduleHistoryId) =>
      _$this._remoteScheduleHistoryId = remoteScheduleHistoryId;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _target;
  String? get target => _$this._target;
  set target(String? target) => _$this._target = target;

  String? _accessKey;
  String? get accessKey => _$this._accessKey;
  set accessKey(String? accessKey) => _$this._accessKey = accessKey;

  ScheduleBuilder() {
    Schedule._defaults(this);
  }

  ScheduleBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _remoteScheduleId = $v.remoteScheduleId;
      _remoteScheduleHistoryId = $v.remoteScheduleHistoryId;
      _name = $v.name;
      _target = $v.target;
      _accessKey = $v.accessKey;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Schedule other) {
    _$v = other as _$Schedule;
  }

  @override
  void update(void Function(ScheduleBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Schedule build() => _build();

  _$Schedule _build() {
    final _$result = _$v ??
        _$Schedule._(
          id: id,
          remoteScheduleId: remoteScheduleId,
          remoteScheduleHistoryId: remoteScheduleHistoryId,
          name: name,
          target: target,
          accessKey: accessKey,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
