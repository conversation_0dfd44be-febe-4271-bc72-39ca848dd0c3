// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdrawal_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$WithdrawalCodeResponse extends WithdrawalCodeResponse {
  @override
  final String? withdrawalCode;
  @override
  final String? providerCode;
  @override
  final String? withdrawAmount;
  @override
  final String? fees;
  @override
  final String? status;
  @override
  final DateTime? expiredAt;

  factory _$WithdrawalCodeResponse(
          [void Function(WithdrawalCodeResponseBuilder)? updates]) =>
      (WithdrawalCodeResponseBuilder()..update(updates))._build();

  _$WithdrawalCodeResponse._(
      {this.withdrawalCode,
      this.providerCode,
      this.withdrawAmount,
      this.fees,
      this.status,
      this.expiredAt})
      : super._();
  @override
  WithdrawalCodeResponse rebuild(
          void Function(WithdrawalCodeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  WithdrawalCodeResponseBuilder toBuilder() =>
      WithdrawalCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is WithdrawalCodeResponse &&
        withdrawalCode == other.withdrawalCode &&
        providerCode == other.providerCode &&
        withdrawAmount == other.withdrawAmount &&
        fees == other.fees &&
        status == other.status &&
        expiredAt == other.expiredAt;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, withdrawalCode.hashCode);
    _$hash = $jc(_$hash, providerCode.hashCode);
    _$hash = $jc(_$hash, withdrawAmount.hashCode);
    _$hash = $jc(_$hash, fees.hashCode);
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jc(_$hash, expiredAt.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'WithdrawalCodeResponse')
          ..add('withdrawalCode', withdrawalCode)
          ..add('providerCode', providerCode)
          ..add('withdrawAmount', withdrawAmount)
          ..add('fees', fees)
          ..add('status', status)
          ..add('expiredAt', expiredAt))
        .toString();
  }
}

class WithdrawalCodeResponseBuilder
    implements Builder<WithdrawalCodeResponse, WithdrawalCodeResponseBuilder> {
  _$WithdrawalCodeResponse? _$v;

  String? _withdrawalCode;
  String? get withdrawalCode => _$this._withdrawalCode;
  set withdrawalCode(String? withdrawalCode) =>
      _$this._withdrawalCode = withdrawalCode;

  String? _providerCode;
  String? get providerCode => _$this._providerCode;
  set providerCode(String? providerCode) => _$this._providerCode = providerCode;

  String? _withdrawAmount;
  String? get withdrawAmount => _$this._withdrawAmount;
  set withdrawAmount(String? withdrawAmount) =>
      _$this._withdrawAmount = withdrawAmount;

  String? _fees;
  String? get fees => _$this._fees;
  set fees(String? fees) => _$this._fees = fees;

  String? _status;
  String? get status => _$this._status;
  set status(String? status) => _$this._status = status;

  DateTime? _expiredAt;
  DateTime? get expiredAt => _$this._expiredAt;
  set expiredAt(DateTime? expiredAt) => _$this._expiredAt = expiredAt;

  WithdrawalCodeResponseBuilder() {
    WithdrawalCodeResponse._defaults(this);
  }

  WithdrawalCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _withdrawalCode = $v.withdrawalCode;
      _providerCode = $v.providerCode;
      _withdrawAmount = $v.withdrawAmount;
      _fees = $v.fees;
      _status = $v.status;
      _expiredAt = $v.expiredAt;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(WithdrawalCodeResponse other) {
    _$v = other as _$WithdrawalCodeResponse;
  }

  @override
  void update(void Function(WithdrawalCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  WithdrawalCodeResponse build() => _build();

  _$WithdrawalCodeResponse _build() {
    final _$result = _$v ??
        _$WithdrawalCodeResponse._(
          withdrawalCode: withdrawalCode,
          providerCode: providerCode,
          withdrawAmount: withdrawAmount,
          fees: fees,
          status: status,
          expiredAt: expiredAt,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
