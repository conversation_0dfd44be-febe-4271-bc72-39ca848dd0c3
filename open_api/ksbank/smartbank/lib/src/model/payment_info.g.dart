// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentInfo extends PaymentInfo {
  @override
  final String? refTransactionNo;

  factory _$PaymentInfo([void Function(PaymentInfoBuilder)? updates]) =>
      (PaymentInfoBuilder()..update(updates))._build();

  _$PaymentInfo._({this.refTransactionNo}) : super._();
  @override
  PaymentInfo rebuild(void Function(PaymentInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentInfoBuilder toBuilder() => PaymentInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentInfo && refTransactionNo == other.refTransactionNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, refTransactionNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentInfo')
          ..add('refTransactionNo', refTransactionNo))
        .toString();
  }
}

class PaymentInfoBuilder implements Builder<PaymentInfo, PaymentInfoBuilder> {
  _$PaymentInfo? _$v;

  String? _refTransactionNo;
  String? get refTransactionNo => _$this._refTransactionNo;
  set refTransactionNo(String? refTransactionNo) =>
      _$this._refTransactionNo = refTransactionNo;

  PaymentInfoBuilder() {
    PaymentInfo._defaults(this);
  }

  PaymentInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _refTransactionNo = $v.refTransactionNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentInfo other) {
    _$v = other as _$PaymentInfo;
  }

  @override
  void update(void Function(PaymentInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentInfo build() => _build();

  _$PaymentInfo _build() {
    final _$result = _$v ??
        _$PaymentInfo._(
          refTransactionNo: refTransactionNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
