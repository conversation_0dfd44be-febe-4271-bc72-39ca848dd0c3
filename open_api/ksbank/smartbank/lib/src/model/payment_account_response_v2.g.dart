// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_account_response_v2.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentAccountResponseV2 extends PaymentAccountResponseV2 {
  @override
  final String? accountNo;
  @override
  final int? accountType;
  @override
  final int? accountStatus;
  @override
  final String? accountName;
  @override
  final String? alias;
  @override
  final int? currency;
  @override
  final String? miAcctTypCd;
  @override
  final String? nickName;
  @override
  final String? postChecks;
  @override
  final String? statusNickName;
  @override
  final String? vietQrCode;
  @override
  final num? availableBalance;
  @override
  final num? accountBalance;
  @override
  final num? overdraftAmount;
  @override
  final num? remainOverdraftAmount;

  factory _$PaymentAccountResponseV2(
          [void Function(PaymentAccountResponseV2Builder)? updates]) =>
      (PaymentAccountResponseV2Builder()..update(updates))._build();

  _$PaymentAccountResponseV2._(
      {this.accountNo,
      this.accountType,
      this.accountStatus,
      this.accountName,
      this.alias,
      this.currency,
      this.miAcctTypCd,
      this.nickName,
      this.postChecks,
      this.statusNickName,
      this.vietQrCode,
      this.availableBalance,
      this.accountBalance,
      this.overdraftAmount,
      this.remainOverdraftAmount})
      : super._();
  @override
  PaymentAccountResponseV2 rebuild(
          void Function(PaymentAccountResponseV2Builder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentAccountResponseV2Builder toBuilder() =>
      PaymentAccountResponseV2Builder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentAccountResponseV2 &&
        accountNo == other.accountNo &&
        accountType == other.accountType &&
        accountStatus == other.accountStatus &&
        accountName == other.accountName &&
        alias == other.alias &&
        currency == other.currency &&
        miAcctTypCd == other.miAcctTypCd &&
        nickName == other.nickName &&
        postChecks == other.postChecks &&
        statusNickName == other.statusNickName &&
        vietQrCode == other.vietQrCode &&
        availableBalance == other.availableBalance &&
        accountBalance == other.accountBalance &&
        overdraftAmount == other.overdraftAmount &&
        remainOverdraftAmount == other.remainOverdraftAmount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountType.hashCode);
    _$hash = $jc(_$hash, accountStatus.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, alias.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, miAcctTypCd.hashCode);
    _$hash = $jc(_$hash, nickName.hashCode);
    _$hash = $jc(_$hash, postChecks.hashCode);
    _$hash = $jc(_$hash, statusNickName.hashCode);
    _$hash = $jc(_$hash, vietQrCode.hashCode);
    _$hash = $jc(_$hash, availableBalance.hashCode);
    _$hash = $jc(_$hash, accountBalance.hashCode);
    _$hash = $jc(_$hash, overdraftAmount.hashCode);
    _$hash = $jc(_$hash, remainOverdraftAmount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentAccountResponseV2')
          ..add('accountNo', accountNo)
          ..add('accountType', accountType)
          ..add('accountStatus', accountStatus)
          ..add('accountName', accountName)
          ..add('alias', alias)
          ..add('currency', currency)
          ..add('miAcctTypCd', miAcctTypCd)
          ..add('nickName', nickName)
          ..add('postChecks', postChecks)
          ..add('statusNickName', statusNickName)
          ..add('vietQrCode', vietQrCode)
          ..add('availableBalance', availableBalance)
          ..add('accountBalance', accountBalance)
          ..add('overdraftAmount', overdraftAmount)
          ..add('remainOverdraftAmount', remainOverdraftAmount))
        .toString();
  }
}

class PaymentAccountResponseV2Builder
    implements
        Builder<PaymentAccountResponseV2, PaymentAccountResponseV2Builder> {
  _$PaymentAccountResponseV2? _$v;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  int? _accountType;
  int? get accountType => _$this._accountType;
  set accountType(int? accountType) => _$this._accountType = accountType;

  int? _accountStatus;
  int? get accountStatus => _$this._accountStatus;
  set accountStatus(int? accountStatus) =>
      _$this._accountStatus = accountStatus;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _alias;
  String? get alias => _$this._alias;
  set alias(String? alias) => _$this._alias = alias;

  int? _currency;
  int? get currency => _$this._currency;
  set currency(int? currency) => _$this._currency = currency;

  String? _miAcctTypCd;
  String? get miAcctTypCd => _$this._miAcctTypCd;
  set miAcctTypCd(String? miAcctTypCd) => _$this._miAcctTypCd = miAcctTypCd;

  String? _nickName;
  String? get nickName => _$this._nickName;
  set nickName(String? nickName) => _$this._nickName = nickName;

  String? _postChecks;
  String? get postChecks => _$this._postChecks;
  set postChecks(String? postChecks) => _$this._postChecks = postChecks;

  String? _statusNickName;
  String? get statusNickName => _$this._statusNickName;
  set statusNickName(String? statusNickName) =>
      _$this._statusNickName = statusNickName;

  String? _vietQrCode;
  String? get vietQrCode => _$this._vietQrCode;
  set vietQrCode(String? vietQrCode) => _$this._vietQrCode = vietQrCode;

  num? _availableBalance;
  num? get availableBalance => _$this._availableBalance;
  set availableBalance(num? availableBalance) =>
      _$this._availableBalance = availableBalance;

  num? _accountBalance;
  num? get accountBalance => _$this._accountBalance;
  set accountBalance(num? accountBalance) =>
      _$this._accountBalance = accountBalance;

  num? _overdraftAmount;
  num? get overdraftAmount => _$this._overdraftAmount;
  set overdraftAmount(num? overdraftAmount) =>
      _$this._overdraftAmount = overdraftAmount;

  num? _remainOverdraftAmount;
  num? get remainOverdraftAmount => _$this._remainOverdraftAmount;
  set remainOverdraftAmount(num? remainOverdraftAmount) =>
      _$this._remainOverdraftAmount = remainOverdraftAmount;

  PaymentAccountResponseV2Builder() {
    PaymentAccountResponseV2._defaults(this);
  }

  PaymentAccountResponseV2Builder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accountNo = $v.accountNo;
      _accountType = $v.accountType;
      _accountStatus = $v.accountStatus;
      _accountName = $v.accountName;
      _alias = $v.alias;
      _currency = $v.currency;
      _miAcctTypCd = $v.miAcctTypCd;
      _nickName = $v.nickName;
      _postChecks = $v.postChecks;
      _statusNickName = $v.statusNickName;
      _vietQrCode = $v.vietQrCode;
      _availableBalance = $v.availableBalance;
      _accountBalance = $v.accountBalance;
      _overdraftAmount = $v.overdraftAmount;
      _remainOverdraftAmount = $v.remainOverdraftAmount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentAccountResponseV2 other) {
    _$v = other as _$PaymentAccountResponseV2;
  }

  @override
  void update(void Function(PaymentAccountResponseV2Builder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentAccountResponseV2 build() => _build();

  _$PaymentAccountResponseV2 _build() {
    final _$result = _$v ??
        _$PaymentAccountResponseV2._(
          accountNo: accountNo,
          accountType: accountType,
          accountStatus: accountStatus,
          accountName: accountName,
          alias: alias,
          currency: currency,
          miAcctTypCd: miAcctTypCd,
          nickName: nickName,
          postChecks: postChecks,
          statusNickName: statusNickName,
          vietQrCode: vietQrCode,
          availableBalance: availableBalance,
          accountBalance: accountBalance,
          overdraftAmount: overdraftAmount,
          remainOverdraftAmount: remainOverdraftAmount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
