// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statement_item.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$StatementItem extends StatementItem {
  @override
  final String? title;
  @override
  final String? description;
  @override
  final String? fromDate;
  @override
  final String? toDate;

  factory _$StatementItem([void Function(StatementItemBuilder)? updates]) =>
      (StatementItemBuilder()..update(updates))._build();

  _$StatementItem._({this.title, this.description, this.fromDate, this.toDate})
      : super._();
  @override
  StatementItem rebuild(void Function(StatementItemBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  StatementItemBuilder toBuilder() => StatementItemBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is StatementItem &&
        title == other.title &&
        description == other.description &&
        fromDate == other.fromDate &&
        toDate == other.toDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, fromDate.hashCode);
    _$hash = $jc(_$hash, toDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'StatementItem')
          ..add('title', title)
          ..add('description', description)
          ..add('fromDate', fromDate)
          ..add('toDate', toDate))
        .toString();
  }
}

class StatementItemBuilder
    implements Builder<StatementItem, StatementItemBuilder> {
  _$StatementItem? _$v;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _fromDate;
  String? get fromDate => _$this._fromDate;
  set fromDate(String? fromDate) => _$this._fromDate = fromDate;

  String? _toDate;
  String? get toDate => _$this._toDate;
  set toDate(String? toDate) => _$this._toDate = toDate;

  StatementItemBuilder() {
    StatementItem._defaults(this);
  }

  StatementItemBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _title = $v.title;
      _description = $v.description;
      _fromDate = $v.fromDate;
      _toDate = $v.toDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(StatementItem other) {
    _$v = other as _$StatementItem;
  }

  @override
  void update(void Function(StatementItemBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  StatementItem build() => _build();

  _$StatementItem _build() {
    final _$result = _$v ??
        _$StatementItem._(
          title: title,
          description: description,
          fromDate: fromDate,
          toDate: toDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
