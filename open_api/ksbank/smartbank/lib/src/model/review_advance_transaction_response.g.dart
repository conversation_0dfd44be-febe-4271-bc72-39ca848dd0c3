// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_advance_transaction_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewAdvanceTransactionResponse
    extends ReviewAdvanceTransactionResponse {
  @override
  final BuiltList<ReviewTransactionCommandResponse>? transactionCommands;
  @override
  final num? totalFee;

  factory _$ReviewAdvanceTransactionResponse(
          [void Function(ReviewAdvanceTransactionResponseBuilder)? updates]) =>
      (ReviewAdvanceTransactionResponseBuilder()..update(updates))._build();

  _$ReviewAdvanceTransactionResponse._(
      {this.transactionCommands, this.totalFee})
      : super._();
  @override
  ReviewAdvanceTransactionResponse rebuild(
          void Function(ReviewAdvanceTransactionResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewAdvanceTransactionResponseBuilder toBuilder() =>
      ReviewAdvanceTransactionResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewAdvanceTransactionResponse &&
        transactionCommands == other.transactionCommands &&
        totalFee == other.totalFee;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionCommands.hashCode);
    _$hash = $jc(_$hash, totalFee.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewAdvanceTransactionResponse')
          ..add('transactionCommands', transactionCommands)
          ..add('totalFee', totalFee))
        .toString();
  }
}

class ReviewAdvanceTransactionResponseBuilder
    implements
        Builder<ReviewAdvanceTransactionResponse,
            ReviewAdvanceTransactionResponseBuilder> {
  _$ReviewAdvanceTransactionResponse? _$v;

  ListBuilder<ReviewTransactionCommandResponse>? _transactionCommands;
  ListBuilder<ReviewTransactionCommandResponse> get transactionCommands =>
      _$this._transactionCommands ??=
          ListBuilder<ReviewTransactionCommandResponse>();
  set transactionCommands(
          ListBuilder<ReviewTransactionCommandResponse>? transactionCommands) =>
      _$this._transactionCommands = transactionCommands;

  num? _totalFee;
  num? get totalFee => _$this._totalFee;
  set totalFee(num? totalFee) => _$this._totalFee = totalFee;

  ReviewAdvanceTransactionResponseBuilder() {
    ReviewAdvanceTransactionResponse._defaults(this);
  }

  ReviewAdvanceTransactionResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionCommands = $v.transactionCommands?.toBuilder();
      _totalFee = $v.totalFee;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewAdvanceTransactionResponse other) {
    _$v = other as _$ReviewAdvanceTransactionResponse;
  }

  @override
  void update(void Function(ReviewAdvanceTransactionResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewAdvanceTransactionResponse build() => _build();

  _$ReviewAdvanceTransactionResponse _build() {
    _$ReviewAdvanceTransactionResponse _$result;
    try {
      _$result = _$v ??
          _$ReviewAdvanceTransactionResponse._(
            transactionCommands: _transactionCommands?.build(),
            totalFee: totalFee,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'transactionCommands';
        _transactionCommands?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'ReviewAdvanceTransactionResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
