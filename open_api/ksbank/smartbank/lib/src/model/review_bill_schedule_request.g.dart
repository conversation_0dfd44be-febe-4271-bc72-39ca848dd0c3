// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_bill_schedule_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewBillScheduleRequest extends ReviewBillScheduleRequest {
  @override
  final String? productCode;
  @override
  final String? customerCode;
  @override
  final String? accountNo;

  factory _$ReviewBillScheduleRequest(
          [void Function(ReviewBillScheduleRequestBuilder)? updates]) =>
      (ReviewBillScheduleRequestBuilder()..update(updates))._build();

  _$ReviewBillScheduleRequest._(
      {this.productCode, this.customerCode, this.accountNo})
      : super._();
  @override
  ReviewBillScheduleRequest rebuild(
          void Function(ReviewBillScheduleRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewBillScheduleRequestBuilder toBuilder() =>
      ReviewBillScheduleRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewBillScheduleRequest &&
        productCode == other.productCode &&
        customerCode == other.customerCode &&
        accountNo == other.accountNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, productCode.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewBillScheduleRequest')
          ..add('productCode', productCode)
          ..add('customerCode', customerCode)
          ..add('accountNo', accountNo))
        .toString();
  }
}

class ReviewBillScheduleRequestBuilder
    implements
        Builder<ReviewBillScheduleRequest, ReviewBillScheduleRequestBuilder> {
  _$ReviewBillScheduleRequest? _$v;

  String? _productCode;
  String? get productCode => _$this._productCode;
  set productCode(String? productCode) => _$this._productCode = productCode;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  ReviewBillScheduleRequestBuilder() {
    ReviewBillScheduleRequest._defaults(this);
  }

  ReviewBillScheduleRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _productCode = $v.productCode;
      _customerCode = $v.customerCode;
      _accountNo = $v.accountNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewBillScheduleRequest other) {
    _$v = other as _$ReviewBillScheduleRequest;
  }

  @override
  void update(void Function(ReviewBillScheduleRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewBillScheduleRequest build() => _build();

  _$ReviewBillScheduleRequest _build() {
    final _$result = _$v ??
        _$ReviewBillScheduleRequest._(
          productCode: productCode,
          customerCode: customerCode,
          accountNo: accountNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
