// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pre_check_reset_pin_card_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PreCheckResetPINCardRequest extends PreCheckResetPINCardRequest {
  @override
  final String? idCardNo;
  @override
  final String? cardId;

  factory _$PreCheckResetPINCardRequest(
          [void Function(PreCheckResetPINCardRequestBuilder)? updates]) =>
      (PreCheckResetPINCardRequestBuilder()..update(updates))._build();

  _$PreCheckResetPINCardRequest._({this.idCardNo, this.cardId}) : super._();
  @override
  PreCheckResetPINCardRequest rebuild(
          void Function(PreCheckResetPINCardRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PreCheckResetPINCardRequestBuilder toBuilder() =>
      PreCheckResetPINCardRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PreCheckResetPINCardRequest &&
        idCardNo == other.idCardNo &&
        cardId == other.cardId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, idCardNo.hashCode);
    _$hash = $jc(_$hash, cardId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PreCheckResetPINCardRequest')
          ..add('idCardNo', idCardNo)
          ..add('cardId', cardId))
        .toString();
  }
}

class PreCheckResetPINCardRequestBuilder
    implements
        Builder<PreCheckResetPINCardRequest,
            PreCheckResetPINCardRequestBuilder> {
  _$PreCheckResetPINCardRequest? _$v;

  String? _idCardNo;
  String? get idCardNo => _$this._idCardNo;
  set idCardNo(String? idCardNo) => _$this._idCardNo = idCardNo;

  String? _cardId;
  String? get cardId => _$this._cardId;
  set cardId(String? cardId) => _$this._cardId = cardId;

  PreCheckResetPINCardRequestBuilder() {
    PreCheckResetPINCardRequest._defaults(this);
  }

  PreCheckResetPINCardRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _idCardNo = $v.idCardNo;
      _cardId = $v.cardId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PreCheckResetPINCardRequest other) {
    _$v = other as _$PreCheckResetPINCardRequest;
  }

  @override
  void update(void Function(PreCheckResetPINCardRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PreCheckResetPINCardRequest build() => _build();

  _$PreCheckResetPINCardRequest _build() {
    final _$result = _$v ??
        _$PreCheckResetPINCardRequest._(
          idCardNo: idCardNo,
          cardId: cardId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
