// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'online_saving_certificate_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$OnlineSavingCertificateResponse
    extends OnlineSavingCertificateResponse {
  @override
  final String? bankCif;
  @override
  final String? customerName;
  @override
  final String? idCard;
  @override
  final DateTime? transactionDate;
  @override
  final String? accountNumber;
  @override
  final String? accountName;
  @override
  final String? termId;
  @override
  final String? termName;
  @override
  final String? currency;
  @override
  final double? rate;
  @override
  final double? balance;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? dueDate;
  @override
  final String? finalTypeName;
  @override
  final double? finalAmount;
  @override
  final String? finalAccountNumber;
  @override
  final String? qrCodeImage;

  factory _$OnlineSavingCertificateResponse(
          [void Function(OnlineSavingCertificateResponseBuilder)? updates]) =>
      (OnlineSavingCertificateResponseBuilder()..update(updates))._build();

  _$OnlineSavingCertificateResponse._(
      {this.bankCif,
      this.customerName,
      this.idCard,
      this.transactionDate,
      this.accountNumber,
      this.accountName,
      this.termId,
      this.termName,
      this.currency,
      this.rate,
      this.balance,
      this.contractDate,
      this.dueDate,
      this.finalTypeName,
      this.finalAmount,
      this.finalAccountNumber,
      this.qrCodeImage})
      : super._();
  @override
  OnlineSavingCertificateResponse rebuild(
          void Function(OnlineSavingCertificateResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OnlineSavingCertificateResponseBuilder toBuilder() =>
      OnlineSavingCertificateResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OnlineSavingCertificateResponse &&
        bankCif == other.bankCif &&
        customerName == other.customerName &&
        idCard == other.idCard &&
        transactionDate == other.transactionDate &&
        accountNumber == other.accountNumber &&
        accountName == other.accountName &&
        termId == other.termId &&
        termName == other.termName &&
        currency == other.currency &&
        rate == other.rate &&
        balance == other.balance &&
        contractDate == other.contractDate &&
        dueDate == other.dueDate &&
        finalTypeName == other.finalTypeName &&
        finalAmount == other.finalAmount &&
        finalAccountNumber == other.finalAccountNumber &&
        qrCodeImage == other.qrCodeImage;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, idCard.hashCode);
    _$hash = $jc(_$hash, transactionDate.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, termId.hashCode);
    _$hash = $jc(_$hash, termName.hashCode);
    _$hash = $jc(_$hash, currency.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, dueDate.hashCode);
    _$hash = $jc(_$hash, finalTypeName.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, finalAccountNumber.hashCode);
    _$hash = $jc(_$hash, qrCodeImage.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OnlineSavingCertificateResponse')
          ..add('bankCif', bankCif)
          ..add('customerName', customerName)
          ..add('idCard', idCard)
          ..add('transactionDate', transactionDate)
          ..add('accountNumber', accountNumber)
          ..add('accountName', accountName)
          ..add('termId', termId)
          ..add('termName', termName)
          ..add('currency', currency)
          ..add('rate', rate)
          ..add('balance', balance)
          ..add('contractDate', contractDate)
          ..add('dueDate', dueDate)
          ..add('finalTypeName', finalTypeName)
          ..add('finalAmount', finalAmount)
          ..add('finalAccountNumber', finalAccountNumber)
          ..add('qrCodeImage', qrCodeImage))
        .toString();
  }
}

class OnlineSavingCertificateResponseBuilder
    implements
        Builder<OnlineSavingCertificateResponse,
            OnlineSavingCertificateResponseBuilder> {
  _$OnlineSavingCertificateResponse? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _idCard;
  String? get idCard => _$this._idCard;
  set idCard(String? idCard) => _$this._idCard = idCard;

  DateTime? _transactionDate;
  DateTime? get transactionDate => _$this._transactionDate;
  set transactionDate(DateTime? transactionDate) =>
      _$this._transactionDate = transactionDate;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  String? _termId;
  String? get termId => _$this._termId;
  set termId(String? termId) => _$this._termId = termId;

  String? _termName;
  String? get termName => _$this._termName;
  set termName(String? termName) => _$this._termName = termName;

  String? _currency;
  String? get currency => _$this._currency;
  set currency(String? currency) => _$this._currency = currency;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  double? _balance;
  double? get balance => _$this._balance;
  set balance(double? balance) => _$this._balance = balance;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  DateTime? _dueDate;
  DateTime? get dueDate => _$this._dueDate;
  set dueDate(DateTime? dueDate) => _$this._dueDate = dueDate;

  String? _finalTypeName;
  String? get finalTypeName => _$this._finalTypeName;
  set finalTypeName(String? finalTypeName) =>
      _$this._finalTypeName = finalTypeName;

  double? _finalAmount;
  double? get finalAmount => _$this._finalAmount;
  set finalAmount(double? finalAmount) => _$this._finalAmount = finalAmount;

  String? _finalAccountNumber;
  String? get finalAccountNumber => _$this._finalAccountNumber;
  set finalAccountNumber(String? finalAccountNumber) =>
      _$this._finalAccountNumber = finalAccountNumber;

  String? _qrCodeImage;
  String? get qrCodeImage => _$this._qrCodeImage;
  set qrCodeImage(String? qrCodeImage) => _$this._qrCodeImage = qrCodeImage;

  OnlineSavingCertificateResponseBuilder() {
    OnlineSavingCertificateResponse._defaults(this);
  }

  OnlineSavingCertificateResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _customerName = $v.customerName;
      _idCard = $v.idCard;
      _transactionDate = $v.transactionDate;
      _accountNumber = $v.accountNumber;
      _accountName = $v.accountName;
      _termId = $v.termId;
      _termName = $v.termName;
      _currency = $v.currency;
      _rate = $v.rate;
      _balance = $v.balance;
      _contractDate = $v.contractDate;
      _dueDate = $v.dueDate;
      _finalTypeName = $v.finalTypeName;
      _finalAmount = $v.finalAmount;
      _finalAccountNumber = $v.finalAccountNumber;
      _qrCodeImage = $v.qrCodeImage;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OnlineSavingCertificateResponse other) {
    _$v = other as _$OnlineSavingCertificateResponse;
  }

  @override
  void update(void Function(OnlineSavingCertificateResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OnlineSavingCertificateResponse build() => _build();

  _$OnlineSavingCertificateResponse _build() {
    final _$result = _$v ??
        _$OnlineSavingCertificateResponse._(
          bankCif: bankCif,
          customerName: customerName,
          idCard: idCard,
          transactionDate: transactionDate,
          accountNumber: accountNumber,
          accountName: accountName,
          termId: termId,
          termName: termName,
          currency: currency,
          rate: rate,
          balance: balance,
          contractDate: contractDate,
          dueDate: dueDate,
          finalTypeName: finalTypeName,
          finalAmount: finalAmount,
          finalAccountNumber: finalAccountNumber,
          qrCodeImage: qrCodeImage,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
