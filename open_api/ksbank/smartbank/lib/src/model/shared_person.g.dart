// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shared_person.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SharedPerson extends Shared<PERSON>erson {
  @override
  final String? phoneNumber;
  @override
  final num? amount;

  factory _$SharedPerson([void Function(SharedPersonBuilder)? updates]) =>
      (SharedPersonBuilder()..update(updates))._build();

  _$SharedPerson._({this.phoneNumber, this.amount}) : super._();
  @override
  SharedPerson rebuild(void Function(SharedPersonBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SharedPersonBuilder toBuilder() => SharedPersonBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SharedPerson &&
        phoneNumber == other.phoneNumber &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SharedPerson')
          ..add('phoneNumber', phoneNumber)
          ..add('amount', amount))
        .toString();
  }
}

class SharedPersonBuilder
    implements Builder<SharedPerson, SharedPersonBuilder> {
  _$SharedPerson? _$v;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  SharedPersonBuilder() {
    SharedPerson._defaults(this);
  }

  SharedPersonBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _phoneNumber = $v.phoneNumber;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SharedPerson other) {
    _$v = other as _$SharedPerson;
  }

  @override
  void update(void Function(SharedPersonBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SharedPerson build() => _build();

  _$SharedPerson _build() {
    final _$result = _$v ??
        _$SharedPerson._(
          phoneNumber: phoneNumber,
          amount: amount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
