// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_topup_lotus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentTopupLotusResponse extends PaymentTopupLotusResponse {
  @override
  final String? orderNo;
  @override
  final String? systemTrace;
  @override
  final String? transactionNo;
  @override
  final DateTime? transactionTime;
  @override
  final num? amount;
  @override
  final num? value;

  factory _$PaymentTopupLotusResponse(
          [void Function(PaymentTopupLotusResponseBuilder)? updates]) =>
      (PaymentTopupLotusResponseBuilder()..update(updates))._build();

  _$PaymentTopupLotusResponse._(
      {this.orderNo,
      this.systemTrace,
      this.transactionNo,
      this.transactionTime,
      this.amount,
      this.value})
      : super._();
  @override
  PaymentTopupLotusResponse rebuild(
          void Function(PaymentTopupLotusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentTopupLotusResponseBuilder toBuilder() =>
      PaymentTopupLotusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentTopupLotusResponse &&
        orderNo == other.orderNo &&
        systemTrace == other.systemTrace &&
        transactionNo == other.transactionNo &&
        transactionTime == other.transactionTime &&
        amount == other.amount &&
        value == other.value;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, orderNo.hashCode);
    _$hash = $jc(_$hash, systemTrace.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, transactionTime.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, value.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentTopupLotusResponse')
          ..add('orderNo', orderNo)
          ..add('systemTrace', systemTrace)
          ..add('transactionNo', transactionNo)
          ..add('transactionTime', transactionTime)
          ..add('amount', amount)
          ..add('value', value))
        .toString();
  }
}

class PaymentTopupLotusResponseBuilder
    implements
        Builder<PaymentTopupLotusResponse, PaymentTopupLotusResponseBuilder> {
  _$PaymentTopupLotusResponse? _$v;

  String? _orderNo;
  String? get orderNo => _$this._orderNo;
  set orderNo(String? orderNo) => _$this._orderNo = orderNo;

  String? _systemTrace;
  String? get systemTrace => _$this._systemTrace;
  set systemTrace(String? systemTrace) => _$this._systemTrace = systemTrace;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _transactionTime;
  DateTime? get transactionTime => _$this._transactionTime;
  set transactionTime(DateTime? transactionTime) =>
      _$this._transactionTime = transactionTime;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  num? _value;
  num? get value => _$this._value;
  set value(num? value) => _$this._value = value;

  PaymentTopupLotusResponseBuilder() {
    PaymentTopupLotusResponse._defaults(this);
  }

  PaymentTopupLotusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _orderNo = $v.orderNo;
      _systemTrace = $v.systemTrace;
      _transactionNo = $v.transactionNo;
      _transactionTime = $v.transactionTime;
      _amount = $v.amount;
      _value = $v.value;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentTopupLotusResponse other) {
    _$v = other as _$PaymentTopupLotusResponse;
  }

  @override
  void update(void Function(PaymentTopupLotusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentTopupLotusResponse build() => _build();

  _$PaymentTopupLotusResponse _build() {
    final _$result = _$v ??
        _$PaymentTopupLotusResponse._(
          orderNo: orderNo,
          systemTrace: systemTrace,
          transactionNo: transactionNo,
          transactionTime: transactionTime,
          amount: amount,
          value: value,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
