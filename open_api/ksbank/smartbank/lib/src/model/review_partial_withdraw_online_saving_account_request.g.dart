// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_partial_withdraw_online_saving_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewPartialWithdrawOnlineSavingAccountRequest
    extends ReviewPartialWithdrawOnlineSavingAccountRequest {
  @override
  final String? bankCif;
  @override
  final String? accountNumber;
  @override
  final String? crAccountNo;
  @override
  final num? balance;
  @override
  final num? amount;

  factory _$ReviewPartialWithdrawOnlineSavingAccountRequest(
          [void Function(
                  ReviewPartialWithdrawOnlineSavingAccountRequestBuilder)?
              updates]) =>
      (ReviewPartialWithdrawOnlineSavingAccountRequestBuilder()
            ..update(updates))
          ._build();

  _$ReviewPartialWithdrawOnlineSavingAccountRequest._(
      {this.bankCif,
      this.accountNumber,
      this.crAccountNo,
      this.balance,
      this.amount})
      : super._();
  @override
  ReviewPartialWithdrawOnlineSavingAccountRequest rebuild(
          void Function(ReviewPartialWithdrawOnlineSavingAccountRequestBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewPartialWithdrawOnlineSavingAccountRequestBuilder toBuilder() =>
      ReviewPartialWithdrawOnlineSavingAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewPartialWithdrawOnlineSavingAccountRequest &&
        bankCif == other.bankCif &&
        accountNumber == other.accountNumber &&
        crAccountNo == other.crAccountNo &&
        balance == other.balance &&
        amount == other.amount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNumber.hashCode);
    _$hash = $jc(_$hash, crAccountNo.hashCode);
    _$hash = $jc(_$hash, balance.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'ReviewPartialWithdrawOnlineSavingAccountRequest')
          ..add('bankCif', bankCif)
          ..add('accountNumber', accountNumber)
          ..add('crAccountNo', crAccountNo)
          ..add('balance', balance)
          ..add('amount', amount))
        .toString();
  }
}

class ReviewPartialWithdrawOnlineSavingAccountRequestBuilder
    implements
        Builder<ReviewPartialWithdrawOnlineSavingAccountRequest,
            ReviewPartialWithdrawOnlineSavingAccountRequestBuilder> {
  _$ReviewPartialWithdrawOnlineSavingAccountRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNumber;
  String? get accountNumber => _$this._accountNumber;
  set accountNumber(String? accountNumber) =>
      _$this._accountNumber = accountNumber;

  String? _crAccountNo;
  String? get crAccountNo => _$this._crAccountNo;
  set crAccountNo(String? crAccountNo) => _$this._crAccountNo = crAccountNo;

  num? _balance;
  num? get balance => _$this._balance;
  set balance(num? balance) => _$this._balance = balance;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  ReviewPartialWithdrawOnlineSavingAccountRequestBuilder() {
    ReviewPartialWithdrawOnlineSavingAccountRequest._defaults(this);
  }

  ReviewPartialWithdrawOnlineSavingAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _accountNumber = $v.accountNumber;
      _crAccountNo = $v.crAccountNo;
      _balance = $v.balance;
      _amount = $v.amount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewPartialWithdrawOnlineSavingAccountRequest other) {
    _$v = other as _$ReviewPartialWithdrawOnlineSavingAccountRequest;
  }

  @override
  void update(
      void Function(ReviewPartialWithdrawOnlineSavingAccountRequestBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewPartialWithdrawOnlineSavingAccountRequest build() => _build();

  _$ReviewPartialWithdrawOnlineSavingAccountRequest _build() {
    final _$result = _$v ??
        _$ReviewPartialWithdrawOnlineSavingAccountRequest._(
          bankCif: bankCif,
          accountNumber: accountNumber,
          crAccountNo: crAccountNo,
          balance: balance,
          amount: amount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
