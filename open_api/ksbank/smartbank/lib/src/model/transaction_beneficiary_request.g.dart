// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_beneficiary_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionBeneficiaryRequest extends TransactionBeneficiaryRequest {
  @override
  final String? bankCif;
  @override
  final String? bankCode;
  @override
  final String? accountNo;
  @override
  final int? isAccount;

  factory _$TransactionBeneficiaryRequest(
          [void Function(TransactionBeneficiaryRequestBuilder)? updates]) =>
      (TransactionBeneficiaryRequestBuilder()..update(updates))._build();

  _$TransactionBeneficiaryRequest._(
      {this.bankCif, this.bankCode, this.accountNo, this.isAccount})
      : super._();
  @override
  TransactionBeneficiaryRequest rebuild(
          void Function(TransactionBeneficiaryRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionBeneficiaryRequestBuilder toBuilder() =>
      TransactionBeneficiaryRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionBeneficiaryRequest &&
        bankCif == other.bankCif &&
        bankCode == other.bankCode &&
        accountNo == other.accountNo &&
        isAccount == other.isAccount;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, bankCode.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, isAccount.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionBeneficiaryRequest')
          ..add('bankCif', bankCif)
          ..add('bankCode', bankCode)
          ..add('accountNo', accountNo)
          ..add('isAccount', isAccount))
        .toString();
  }
}

class TransactionBeneficiaryRequestBuilder
    implements
        Builder<TransactionBeneficiaryRequest,
            TransactionBeneficiaryRequestBuilder> {
  _$TransactionBeneficiaryRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _bankCode;
  String? get bankCode => _$this._bankCode;
  set bankCode(String? bankCode) => _$this._bankCode = bankCode;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  int? _isAccount;
  int? get isAccount => _$this._isAccount;
  set isAccount(int? isAccount) => _$this._isAccount = isAccount;

  TransactionBeneficiaryRequestBuilder() {
    TransactionBeneficiaryRequest._defaults(this);
  }

  TransactionBeneficiaryRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _bankCode = $v.bankCode;
      _accountNo = $v.accountNo;
      _isAccount = $v.isAccount;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionBeneficiaryRequest other) {
    _$v = other as _$TransactionBeneficiaryRequest;
  }

  @override
  void update(void Function(TransactionBeneficiaryRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionBeneficiaryRequest build() => _build();

  _$TransactionBeneficiaryRequest _build() {
    final _$result = _$v ??
        _$TransactionBeneficiaryRequest._(
          bankCif: bankCif,
          bankCode: bankCode,
          accountNo: accountNo,
          isAccount: isAccount,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
