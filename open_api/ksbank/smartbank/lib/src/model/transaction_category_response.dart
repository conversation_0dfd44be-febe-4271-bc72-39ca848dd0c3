//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'transaction_category_response.g.dart';

/// TransactionCategoryResponse
///
/// Properties:
/// * [id] - Mã phân loại giao dịch
/// * [name] - Tên phân loại giao dịch
/// * [cashFlow] - Luồng tiền đi hay đến
/// * [iconUrl]
@BuiltValue()
abstract class TransactionCategoryResponse
    implements
        Built<TransactionCategoryResponse, TransactionCategoryResponseBuilder> {
  /// Mã phân loại giao dịch
  @BuiltValueField(wireName: r'id')
  int? get id;

  /// Tên phân loại giao dịch
  @BuiltValueField(wireName: r'name')
  String? get name;

  /// Luồng tiền đi hay đến
  @BuiltValueField(wireName: r'cashFlow')
  TransactionCategoryResponseCashFlowEnum? get cashFlow;
  // enum cashFlowEnum {  RECEIVE,  SEND,  };

  @BuiltValueField(wireName: r'iconUrl')
  String? get iconUrl;

  TransactionCategoryResponse._();

  factory TransactionCategoryResponse(
          [void updates(TransactionCategoryResponseBuilder b)]) =
      _$TransactionCategoryResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TransactionCategoryResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TransactionCategoryResponse> get serializer =>
      _$TransactionCategoryResponseSerializer();
}

class _$TransactionCategoryResponseSerializer
    implements PrimitiveSerializer<TransactionCategoryResponse> {
  @override
  final Iterable<Type> types = const [
    TransactionCategoryResponse,
    _$TransactionCategoryResponse
  ];

  @override
  final String wireName = r'TransactionCategoryResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TransactionCategoryResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.cashFlow != null) {
      yield r'cashFlow';
      yield serializers.serialize(
        object.cashFlow,
        specifiedType:
            const FullType.nullable(TransactionCategoryResponseCashFlowEnum),
      );
    }
    if (object.iconUrl != null) {
      yield r'iconUrl';
      yield serializers.serialize(
        object.iconUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TransactionCategoryResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TransactionCategoryResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'cashFlow':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                TransactionCategoryResponseCashFlowEnum),
          ) as TransactionCategoryResponseCashFlowEnum?;
          if (valueDes == null) continue;
          result.cashFlow = valueDes;
          break;
        case r'iconUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.iconUrl = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TransactionCategoryResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TransactionCategoryResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class TransactionCategoryResponseCashFlowEnum extends EnumClass {
  /// Luồng tiền đi hay đến
  @BuiltValueEnumConst(wireName: r'RECEIVE')
  static const TransactionCategoryResponseCashFlowEnum RECEIVE =
      _$transactionCategoryResponseCashFlowEnum_RECEIVE;

  /// Luồng tiền đi hay đến
  @BuiltValueEnumConst(wireName: r'SEND')
  static const TransactionCategoryResponseCashFlowEnum SEND =
      _$transactionCategoryResponseCashFlowEnum_SEND;

  static Serializer<TransactionCategoryResponseCashFlowEnum> get serializer =>
      _$transactionCategoryResponseCashFlowEnumSerializer;

  const TransactionCategoryResponseCashFlowEnum._(String name) : super(name);

  static BuiltSet<TransactionCategoryResponseCashFlowEnum> get values =>
      _$transactionCategoryResponseCashFlowEnumValues;
  static TransactionCategoryResponseCashFlowEnum valueOf(String name) =>
      _$transactionCategoryResponseCashFlowEnumValueOf(name);
}
