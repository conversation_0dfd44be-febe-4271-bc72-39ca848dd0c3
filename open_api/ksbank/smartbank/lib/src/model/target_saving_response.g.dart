// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'target_saving_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TargetSavingResponse extends TargetSavingResponse {
  @override
  final num? targetAmount;
  @override
  final bool? isAddFromTransaction;
  @override
  final num? coefficient;
  @override
  final String? accountNo;
  @override
  final String? accountName;
  @override
  final num? availableBalance;
  @override
  final String? imageUrl;
  @override
  final double? rate;
  @override
  final DateTime? contractDate;
  @override
  final String? finalAccountNo;
  @override
  final num? finalAmount;
  @override
  final num? currentAmount;
  @override
  final num? amountRateCompletion;

  factory _$TargetSavingResponse(
          [void Function(TargetSavingResponseBuilder)? updates]) =>
      (TargetSavingResponseBuilder()..update(updates))._build();

  _$TargetSavingResponse._(
      {this.targetAmount,
      this.isAddFromTransaction,
      this.coefficient,
      this.accountNo,
      this.accountName,
      this.availableBalance,
      this.imageUrl,
      this.rate,
      this.contractDate,
      this.finalAccountNo,
      this.finalAmount,
      this.currentAmount,
      this.amountRateCompletion})
      : super._();
  @override
  TargetSavingResponse rebuild(
          void Function(TargetSavingResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TargetSavingResponseBuilder toBuilder() =>
      TargetSavingResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TargetSavingResponse &&
        targetAmount == other.targetAmount &&
        isAddFromTransaction == other.isAddFromTransaction &&
        coefficient == other.coefficient &&
        accountNo == other.accountNo &&
        accountName == other.accountName &&
        availableBalance == other.availableBalance &&
        imageUrl == other.imageUrl &&
        rate == other.rate &&
        contractDate == other.contractDate &&
        finalAccountNo == other.finalAccountNo &&
        finalAmount == other.finalAmount &&
        currentAmount == other.currentAmount &&
        amountRateCompletion == other.amountRateCompletion;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, targetAmount.hashCode);
    _$hash = $jc(_$hash, isAddFromTransaction.hashCode);
    _$hash = $jc(_$hash, coefficient.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, accountName.hashCode);
    _$hash = $jc(_$hash, availableBalance.hashCode);
    _$hash = $jc(_$hash, imageUrl.hashCode);
    _$hash = $jc(_$hash, rate.hashCode);
    _$hash = $jc(_$hash, contractDate.hashCode);
    _$hash = $jc(_$hash, finalAccountNo.hashCode);
    _$hash = $jc(_$hash, finalAmount.hashCode);
    _$hash = $jc(_$hash, currentAmount.hashCode);
    _$hash = $jc(_$hash, amountRateCompletion.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TargetSavingResponse')
          ..add('targetAmount', targetAmount)
          ..add('isAddFromTransaction', isAddFromTransaction)
          ..add('coefficient', coefficient)
          ..add('accountNo', accountNo)
          ..add('accountName', accountName)
          ..add('availableBalance', availableBalance)
          ..add('imageUrl', imageUrl)
          ..add('rate', rate)
          ..add('contractDate', contractDate)
          ..add('finalAccountNo', finalAccountNo)
          ..add('finalAmount', finalAmount)
          ..add('currentAmount', currentAmount)
          ..add('amountRateCompletion', amountRateCompletion))
        .toString();
  }
}

class TargetSavingResponseBuilder
    implements Builder<TargetSavingResponse, TargetSavingResponseBuilder> {
  _$TargetSavingResponse? _$v;

  num? _targetAmount;
  num? get targetAmount => _$this._targetAmount;
  set targetAmount(num? targetAmount) => _$this._targetAmount = targetAmount;

  bool? _isAddFromTransaction;
  bool? get isAddFromTransaction => _$this._isAddFromTransaction;
  set isAddFromTransaction(bool? isAddFromTransaction) =>
      _$this._isAddFromTransaction = isAddFromTransaction;

  num? _coefficient;
  num? get coefficient => _$this._coefficient;
  set coefficient(num? coefficient) => _$this._coefficient = coefficient;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  String? _accountName;
  String? get accountName => _$this._accountName;
  set accountName(String? accountName) => _$this._accountName = accountName;

  num? _availableBalance;
  num? get availableBalance => _$this._availableBalance;
  set availableBalance(num? availableBalance) =>
      _$this._availableBalance = availableBalance;

  String? _imageUrl;
  String? get imageUrl => _$this._imageUrl;
  set imageUrl(String? imageUrl) => _$this._imageUrl = imageUrl;

  double? _rate;
  double? get rate => _$this._rate;
  set rate(double? rate) => _$this._rate = rate;

  DateTime? _contractDate;
  DateTime? get contractDate => _$this._contractDate;
  set contractDate(DateTime? contractDate) =>
      _$this._contractDate = contractDate;

  String? _finalAccountNo;
  String? get finalAccountNo => _$this._finalAccountNo;
  set finalAccountNo(String? finalAccountNo) =>
      _$this._finalAccountNo = finalAccountNo;

  num? _finalAmount;
  num? get finalAmount => _$this._finalAmount;
  set finalAmount(num? finalAmount) => _$this._finalAmount = finalAmount;

  num? _currentAmount;
  num? get currentAmount => _$this._currentAmount;
  set currentAmount(num? currentAmount) =>
      _$this._currentAmount = currentAmount;

  num? _amountRateCompletion;
  num? get amountRateCompletion => _$this._amountRateCompletion;
  set amountRateCompletion(num? amountRateCompletion) =>
      _$this._amountRateCompletion = amountRateCompletion;

  TargetSavingResponseBuilder() {
    TargetSavingResponse._defaults(this);
  }

  TargetSavingResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _targetAmount = $v.targetAmount;
      _isAddFromTransaction = $v.isAddFromTransaction;
      _coefficient = $v.coefficient;
      _accountNo = $v.accountNo;
      _accountName = $v.accountName;
      _availableBalance = $v.availableBalance;
      _imageUrl = $v.imageUrl;
      _rate = $v.rate;
      _contractDate = $v.contractDate;
      _finalAccountNo = $v.finalAccountNo;
      _finalAmount = $v.finalAmount;
      _currentAmount = $v.currentAmount;
      _amountRateCompletion = $v.amountRateCompletion;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TargetSavingResponse other) {
    _$v = other as _$TargetSavingResponse;
  }

  @override
  void update(void Function(TargetSavingResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TargetSavingResponse build() => _build();

  _$TargetSavingResponse _build() {
    final _$result = _$v ??
        _$TargetSavingResponse._(
          targetAmount: targetAmount,
          isAddFromTransaction: isAddFromTransaction,
          coefficient: coefficient,
          accountNo: accountNo,
          accountName: accountName,
          availableBalance: availableBalance,
          imageUrl: imageUrl,
          rate: rate,
          contractDate: contractDate,
          finalAccountNo: finalAccountNo,
          finalAmount: finalAmount,
          currentAmount: currentAmount,
          amountRateCompletion: amountRateCompletion,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
