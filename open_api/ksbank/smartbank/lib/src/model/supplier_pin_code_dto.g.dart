// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supplier_pin_code_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SupplierPinCodeDto extends SupplierPinCodeDto {
  @override
  final String? supplierCode;
  @override
  final String? supplierName;
  @override
  final String? iconUrl;
  @override
  final BuiltList<PinCodeValueDto>? values;

  factory _$SupplierPinCodeDto(
          [void Function(SupplierPinCodeDtoBuilder)? updates]) =>
      (SupplierPinCodeDtoBuilder()..update(updates))._build();

  _$SupplierPinCodeDto._(
      {this.supplierCode, this.supplierName, this.iconUrl, this.values})
      : super._();
  @override
  SupplierPinCodeDto rebuild(
          void Function(SupplierPinCodeDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SupplierPinCodeDtoBuilder toBuilder() =>
      SupplierPinCodeDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SupplierPinCodeDto &&
        supplierCode == other.supplierCode &&
        supplierName == other.supplierName &&
        iconUrl == other.iconUrl &&
        values == other.values;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, supplierCode.hashCode);
    _$hash = $jc(_$hash, supplierName.hashCode);
    _$hash = $jc(_$hash, iconUrl.hashCode);
    _$hash = $jc(_$hash, values.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SupplierPinCodeDto')
          ..add('supplierCode', supplierCode)
          ..add('supplierName', supplierName)
          ..add('iconUrl', iconUrl)
          ..add('values', values))
        .toString();
  }
}

class SupplierPinCodeDtoBuilder
    implements Builder<SupplierPinCodeDto, SupplierPinCodeDtoBuilder> {
  _$SupplierPinCodeDto? _$v;

  String? _supplierCode;
  String? get supplierCode => _$this._supplierCode;
  set supplierCode(String? supplierCode) => _$this._supplierCode = supplierCode;

  String? _supplierName;
  String? get supplierName => _$this._supplierName;
  set supplierName(String? supplierName) => _$this._supplierName = supplierName;

  String? _iconUrl;
  String? get iconUrl => _$this._iconUrl;
  set iconUrl(String? iconUrl) => _$this._iconUrl = iconUrl;

  ListBuilder<PinCodeValueDto>? _values;
  ListBuilder<PinCodeValueDto> get values =>
      _$this._values ??= ListBuilder<PinCodeValueDto>();
  set values(ListBuilder<PinCodeValueDto>? values) => _$this._values = values;

  SupplierPinCodeDtoBuilder() {
    SupplierPinCodeDto._defaults(this);
  }

  SupplierPinCodeDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _supplierCode = $v.supplierCode;
      _supplierName = $v.supplierName;
      _iconUrl = $v.iconUrl;
      _values = $v.values?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SupplierPinCodeDto other) {
    _$v = other as _$SupplierPinCodeDto;
  }

  @override
  void update(void Function(SupplierPinCodeDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SupplierPinCodeDto build() => _build();

  _$SupplierPinCodeDto _build() {
    _$SupplierPinCodeDto _$result;
    try {
      _$result = _$v ??
          _$SupplierPinCodeDto._(
            supplierCode: supplierCode,
            supplierName: supplierName,
            iconUrl: iconUrl,
            values: _values?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'values';
        _values?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'SupplierPinCodeDto', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
