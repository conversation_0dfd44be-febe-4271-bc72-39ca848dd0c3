// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_app_module_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoginAppModuleRequest extends LoginAppModuleRequest {
  @override
  final String? appId;
  @override
  final String? redirectUrl;

  factory _$LoginAppModuleRequest(
          [void Function(LoginAppModuleRequestBuilder)? updates]) =>
      (LoginAppModuleRequestBuilder()..update(updates))._build();

  _$LoginAppModuleRequest._({this.appId, this.redirectUrl}) : super._();
  @override
  LoginAppModuleRequest rebuild(
          void Function(LoginAppModuleRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoginAppModuleRequestBuilder toBuilder() =>
      LoginAppModuleRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoginAppModuleRequest &&
        appId == other.appId &&
        redirectUrl == other.redirectUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, appId.hashCode);
    _$hash = $jc(_$hash, redirectUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoginAppModuleRequest')
          ..add('appId', appId)
          ..add('redirectUrl', redirectUrl))
        .toString();
  }
}

class LoginAppModuleRequestBuilder
    implements Builder<LoginAppModuleRequest, LoginAppModuleRequestBuilder> {
  _$LoginAppModuleRequest? _$v;

  String? _appId;
  String? get appId => _$this._appId;
  set appId(String? appId) => _$this._appId = appId;

  String? _redirectUrl;
  String? get redirectUrl => _$this._redirectUrl;
  set redirectUrl(String? redirectUrl) => _$this._redirectUrl = redirectUrl;

  LoginAppModuleRequestBuilder() {
    LoginAppModuleRequest._defaults(this);
  }

  LoginAppModuleRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _appId = $v.appId;
      _redirectUrl = $v.redirectUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoginAppModuleRequest other) {
    _$v = other as _$LoginAppModuleRequest;
  }

  @override
  void update(void Function(LoginAppModuleRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoginAppModuleRequest build() => _build();

  _$LoginAppModuleRequest _build() {
    final _$result = _$v ??
        _$LoginAppModuleRequest._(
          appId: appId,
          redirectUrl: redirectUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
