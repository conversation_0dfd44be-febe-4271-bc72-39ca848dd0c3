// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_schedule_update_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransferScheduleUpdateResponse extends TransferScheduleUpdateResponse {
  @override
  final TransferScheduleResponse? scheduleResponse;

  factory _$TransferScheduleUpdateResponse(
          [void Function(TransferScheduleUpdateResponseBuilder)? updates]) =>
      (TransferScheduleUpdateResponseBuilder()..update(updates))._build();

  _$TransferScheduleUpdateResponse._({this.scheduleResponse}) : super._();
  @override
  TransferScheduleUpdateResponse rebuild(
          void Function(TransferScheduleUpdateResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransferScheduleUpdateResponseBuilder toBuilder() =>
      TransferScheduleUpdateResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransferScheduleUpdateResponse &&
        scheduleResponse == other.scheduleResponse;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, scheduleResponse.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransferScheduleUpdateResponse')
          ..add('scheduleResponse', scheduleResponse))
        .toString();
  }
}

class TransferScheduleUpdateResponseBuilder
    implements
        Builder<TransferScheduleUpdateResponse,
            TransferScheduleUpdateResponseBuilder> {
  _$TransferScheduleUpdateResponse? _$v;

  TransferScheduleResponseBuilder? _scheduleResponse;
  TransferScheduleResponseBuilder get scheduleResponse =>
      _$this._scheduleResponse ??= TransferScheduleResponseBuilder();
  set scheduleResponse(TransferScheduleResponseBuilder? scheduleResponse) =>
      _$this._scheduleResponse = scheduleResponse;

  TransferScheduleUpdateResponseBuilder() {
    TransferScheduleUpdateResponse._defaults(this);
  }

  TransferScheduleUpdateResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _scheduleResponse = $v.scheduleResponse?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransferScheduleUpdateResponse other) {
    _$v = other as _$TransferScheduleUpdateResponse;
  }

  @override
  void update(void Function(TransferScheduleUpdateResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransferScheduleUpdateResponse build() => _build();

  _$TransferScheduleUpdateResponse _build() {
    _$TransferScheduleUpdateResponse _$result;
    try {
      _$result = _$v ??
          _$TransferScheduleUpdateResponse._(
            scheduleResponse: _scheduleResponse?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'scheduleResponse';
        _scheduleResponse?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransferScheduleUpdateResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
