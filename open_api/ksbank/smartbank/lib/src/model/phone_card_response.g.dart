// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'phone_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PhoneCardResponse extends PhoneCardResponse {
  @override
  final num? cardValue;

  factory _$PhoneCardResponse(
          [void Function(PhoneCardResponseBuilder)? updates]) =>
      (PhoneCardResponseBuilder()..update(updates))._build();

  _$PhoneCardResponse._({this.cardValue}) : super._();
  @override
  PhoneCardResponse rebuild(void Function(PhoneCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PhoneCardResponseBuilder toBuilder() =>
      PhoneCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PhoneCardResponse && cardValue == other.cardValue;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, cardValue.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PhoneCardResponse')
          ..add('cardValue', cardValue))
        .toString();
  }
}

class PhoneCardResponseBuilder
    implements Builder<PhoneCardResponse, PhoneCardResponseBuilder> {
  _$PhoneCardResponse? _$v;

  num? _cardValue;
  num? get cardValue => _$this._cardValue;
  set cardValue(num? cardValue) => _$this._cardValue = cardValue;

  PhoneCardResponseBuilder() {
    PhoneCardResponse._defaults(this);
  }

  PhoneCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _cardValue = $v.cardValue;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PhoneCardResponse other) {
    _$v = other as _$PhoneCardResponse;
  }

  @override
  void update(void Function(PhoneCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PhoneCardResponse build() => _build();

  _$PhoneCardResponse _build() {
    final _$result = _$v ??
        _$PhoneCardResponse._(
          cardValue: cardValue,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
