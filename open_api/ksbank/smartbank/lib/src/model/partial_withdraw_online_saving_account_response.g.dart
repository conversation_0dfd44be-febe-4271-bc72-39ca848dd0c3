// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'partial_withdraw_online_saving_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PartialWithdrawOnlineSavingAccountResponse
    extends PartialWithdrawOnlineSavingAccountResponse {
  @override
  final String? transactionNumber;
  @override
  final String? depNo;
  @override
  final DateTime? withdrawDate;

  factory _$PartialWithdrawOnlineSavingAccountResponse(
          [void Function(PartialWithdrawOnlineSavingAccountResponseBuilder)?
              updates]) =>
      (PartialWithdrawOnlineSavingAccountResponseBuilder()..update(updates))
          ._build();

  _$PartialWithdrawOnlineSavingAccountResponse._(
      {this.transactionNumber, this.depNo, this.withdrawDate})
      : super._();
  @override
  PartialWithdrawOnlineSavingAccountResponse rebuild(
          void Function(PartialWithdrawOnlineSavingAccountResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PartialWithdrawOnlineSavingAccountResponseBuilder toBuilder() =>
      PartialWithdrawOnlineSavingAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PartialWithdrawOnlineSavingAccountResponse &&
        transactionNumber == other.transactionNumber &&
        depNo == other.depNo &&
        withdrawDate == other.withdrawDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNumber.hashCode);
    _$hash = $jc(_$hash, depNo.hashCode);
    _$hash = $jc(_$hash, withdrawDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'PartialWithdrawOnlineSavingAccountResponse')
          ..add('transactionNumber', transactionNumber)
          ..add('depNo', depNo)
          ..add('withdrawDate', withdrawDate))
        .toString();
  }
}

class PartialWithdrawOnlineSavingAccountResponseBuilder
    implements
        Builder<PartialWithdrawOnlineSavingAccountResponse,
            PartialWithdrawOnlineSavingAccountResponseBuilder> {
  _$PartialWithdrawOnlineSavingAccountResponse? _$v;

  String? _transactionNumber;
  String? get transactionNumber => _$this._transactionNumber;
  set transactionNumber(String? transactionNumber) =>
      _$this._transactionNumber = transactionNumber;

  String? _depNo;
  String? get depNo => _$this._depNo;
  set depNo(String? depNo) => _$this._depNo = depNo;

  DateTime? _withdrawDate;
  DateTime? get withdrawDate => _$this._withdrawDate;
  set withdrawDate(DateTime? withdrawDate) =>
      _$this._withdrawDate = withdrawDate;

  PartialWithdrawOnlineSavingAccountResponseBuilder() {
    PartialWithdrawOnlineSavingAccountResponse._defaults(this);
  }

  PartialWithdrawOnlineSavingAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNumber = $v.transactionNumber;
      _depNo = $v.depNo;
      _withdrawDate = $v.withdrawDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PartialWithdrawOnlineSavingAccountResponse other) {
    _$v = other as _$PartialWithdrawOnlineSavingAccountResponse;
  }

  @override
  void update(
      void Function(PartialWithdrawOnlineSavingAccountResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  PartialWithdrawOnlineSavingAccountResponse build() => _build();

  _$PartialWithdrawOnlineSavingAccountResponse _build() {
    final _$result = _$v ??
        _$PartialWithdrawOnlineSavingAccountResponse._(
          transactionNumber: transactionNumber,
          depNo: depNo,
          withdrawDate: withdrawDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
