// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_close_target_saving_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ReviewCloseTargetSavingRequest extends ReviewCloseTargetSavingRequest {
  @override
  final String? bankCif;
  @override
  final String? transactionNo;
  @override
  final String? targetAccountNo;
  @override
  final String? finalAccountNo;

  factory _$ReviewCloseTargetSavingRequest(
          [void Function(ReviewCloseTargetSavingRequestBuilder)? updates]) =>
      (ReviewCloseTargetSavingRequestBuilder()..update(updates))._build();

  _$ReviewCloseTargetSavingRequest._(
      {this.bankCif,
      this.transactionNo,
      this.targetAccountNo,
      this.finalAccountNo})
      : super._();
  @override
  ReviewCloseTargetSavingRequest rebuild(
          void Function(ReviewCloseTargetSavingRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ReviewCloseTargetSavingRequestBuilder toBuilder() =>
      ReviewCloseTargetSavingRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ReviewCloseTargetSavingRequest &&
        bankCif == other.bankCif &&
        transactionNo == other.transactionNo &&
        targetAccountNo == other.targetAccountNo &&
        finalAccountNo == other.finalAccountNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, targetAccountNo.hashCode);
    _$hash = $jc(_$hash, finalAccountNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ReviewCloseTargetSavingRequest')
          ..add('bankCif', bankCif)
          ..add('transactionNo', transactionNo)
          ..add('targetAccountNo', targetAccountNo)
          ..add('finalAccountNo', finalAccountNo))
        .toString();
  }
}

class ReviewCloseTargetSavingRequestBuilder
    implements
        Builder<ReviewCloseTargetSavingRequest,
            ReviewCloseTargetSavingRequestBuilder> {
  _$ReviewCloseTargetSavingRequest? _$v;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _targetAccountNo;
  String? get targetAccountNo => _$this._targetAccountNo;
  set targetAccountNo(String? targetAccountNo) =>
      _$this._targetAccountNo = targetAccountNo;

  String? _finalAccountNo;
  String? get finalAccountNo => _$this._finalAccountNo;
  set finalAccountNo(String? finalAccountNo) =>
      _$this._finalAccountNo = finalAccountNo;

  ReviewCloseTargetSavingRequestBuilder() {
    ReviewCloseTargetSavingRequest._defaults(this);
  }

  ReviewCloseTargetSavingRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _bankCif = $v.bankCif;
      _transactionNo = $v.transactionNo;
      _targetAccountNo = $v.targetAccountNo;
      _finalAccountNo = $v.finalAccountNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ReviewCloseTargetSavingRequest other) {
    _$v = other as _$ReviewCloseTargetSavingRequest;
  }

  @override
  void update(void Function(ReviewCloseTargetSavingRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ReviewCloseTargetSavingRequest build() => _build();

  _$ReviewCloseTargetSavingRequest _build() {
    final _$result = _$v ??
        _$ReviewCloseTargetSavingRequest._(
          bankCif: bankCif,
          transactionNo: transactionNo,
          targetAccountNo: targetAccountNo,
          finalAccountNo: finalAccountNo,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
