// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pay_invoice_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PayInvoiceRequest extends PayInvoiceRequest {
  @override
  final String? transactionNo;
  @override
  final String? supplierId;
  @override
  final String? customerCode;
  @override
  final String? customerName;
  @override
  final String? customerAddress;
  @override
  final bool? isSavedNextTime;
  @override
  final String? bankCif;
  @override
  final String? accountNo;
  @override
  final VerifySoftOtpRequest? softOtp;
  @override
  final num? amount;
  @override
  final String? billIds;
  @override
  final num? fee;

  factory _$PayInvoiceRequest(
          [void Function(PayInvoiceRequestBuilder)? updates]) =>
      (PayInvoiceRequestBuilder()..update(updates))._build();

  _$PayInvoiceRequest._(
      {this.transactionNo,
      this.supplierId,
      this.customerCode,
      this.customerName,
      this.customerAddress,
      this.isSavedNextTime,
      this.bankCif,
      this.accountNo,
      this.softOtp,
      this.amount,
      this.billIds,
      this.fee})
      : super._();
  @override
  PayInvoiceRequest rebuild(void Function(PayInvoiceRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PayInvoiceRequestBuilder toBuilder() =>
      PayInvoiceRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PayInvoiceRequest &&
        transactionNo == other.transactionNo &&
        supplierId == other.supplierId &&
        customerCode == other.customerCode &&
        customerName == other.customerName &&
        customerAddress == other.customerAddress &&
        isSavedNextTime == other.isSavedNextTime &&
        bankCif == other.bankCif &&
        accountNo == other.accountNo &&
        softOtp == other.softOtp &&
        amount == other.amount &&
        billIds == other.billIds &&
        fee == other.fee;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, customerAddress.hashCode);
    _$hash = $jc(_$hash, isSavedNextTime.hashCode);
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, softOtp.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, billIds.hashCode);
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PayInvoiceRequest')
          ..add('transactionNo', transactionNo)
          ..add('supplierId', supplierId)
          ..add('customerCode', customerCode)
          ..add('customerName', customerName)
          ..add('customerAddress', customerAddress)
          ..add('isSavedNextTime', isSavedNextTime)
          ..add('bankCif', bankCif)
          ..add('accountNo', accountNo)
          ..add('softOtp', softOtp)
          ..add('amount', amount)
          ..add('billIds', billIds)
          ..add('fee', fee))
        .toString();
  }
}

class PayInvoiceRequestBuilder
    implements Builder<PayInvoiceRequest, PayInvoiceRequestBuilder> {
  _$PayInvoiceRequest? _$v;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _customerAddress;
  String? get customerAddress => _$this._customerAddress;
  set customerAddress(String? customerAddress) =>
      _$this._customerAddress = customerAddress;

  bool? _isSavedNextTime;
  bool? get isSavedNextTime => _$this._isSavedNextTime;
  set isSavedNextTime(bool? isSavedNextTime) =>
      _$this._isSavedNextTime = isSavedNextTime;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  VerifySoftOtpRequestBuilder? _softOtp;
  VerifySoftOtpRequestBuilder get softOtp =>
      _$this._softOtp ??= VerifySoftOtpRequestBuilder();
  set softOtp(VerifySoftOtpRequestBuilder? softOtp) =>
      _$this._softOtp = softOtp;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _billIds;
  String? get billIds => _$this._billIds;
  set billIds(String? billIds) => _$this._billIds = billIds;

  num? _fee;
  num? get fee => _$this._fee;
  set fee(num? fee) => _$this._fee = fee;

  PayInvoiceRequestBuilder() {
    PayInvoiceRequest._defaults(this);
  }

  PayInvoiceRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _transactionNo = $v.transactionNo;
      _supplierId = $v.supplierId;
      _customerCode = $v.customerCode;
      _customerName = $v.customerName;
      _customerAddress = $v.customerAddress;
      _isSavedNextTime = $v.isSavedNextTime;
      _bankCif = $v.bankCif;
      _accountNo = $v.accountNo;
      _softOtp = $v.softOtp?.toBuilder();
      _amount = $v.amount;
      _billIds = $v.billIds;
      _fee = $v.fee;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PayInvoiceRequest other) {
    _$v = other as _$PayInvoiceRequest;
  }

  @override
  void update(void Function(PayInvoiceRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PayInvoiceRequest build() => _build();

  _$PayInvoiceRequest _build() {
    _$PayInvoiceRequest _$result;
    try {
      _$result = _$v ??
          _$PayInvoiceRequest._(
            transactionNo: transactionNo,
            supplierId: supplierId,
            customerCode: customerCode,
            customerName: customerName,
            customerAddress: customerAddress,
            isSavedNextTime: isSavedNextTime,
            bankCif: bankCif,
            accountNo: accountNo,
            softOtp: _softOtp?.build(),
            amount: amount,
            billIds: billIds,
            fee: fee,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'softOtp';
        _softOtp?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PayInvoiceRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
