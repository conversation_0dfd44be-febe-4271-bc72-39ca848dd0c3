// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'param.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$Param extends Param {
  @override
  final String? value;
  @override
  final String? color;

  factory _$Param([void Function(ParamBuilder)? updates]) =>
      (ParamBuilder()..update(updates))._build();

  _$Param._({this.value, this.color}) : super._();
  @override
  Param rebuild(void Function(ParamBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ParamBuilder toBuilder() => ParamBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Param && value == other.value && color == other.color;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, value.hashCode);
    _$hash = $jc(_$hash, color.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'Param')
          ..add('value', value)
          ..add('color', color))
        .toString();
  }
}

class ParamBuilder implements Builder<Param, ParamBuilder> {
  _$Param? _$v;

  String? _value;
  String? get value => _$this._value;
  set value(String? value) => _$this._value = value;

  String? _color;
  String? get color => _$this._color;
  set color(String? color) => _$this._color = color;

  ParamBuilder() {
    Param._defaults(this);
  }

  ParamBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _value = $v.value;
      _color = $v.color;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(Param other) {
    _$v = other as _$Param;
  }

  @override
  void update(void Function(ParamBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  Param build() => _build();

  _$Param _build() {
    final _$result = _$v ??
        _$Param._(
          value: value,
          color: color,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
