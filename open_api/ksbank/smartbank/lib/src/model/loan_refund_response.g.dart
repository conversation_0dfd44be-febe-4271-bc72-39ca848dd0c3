// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_refund_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$LoanRefundResponse extends LoanRefundResponse {
  @override
  final double? amount;
  @override
  final String? transactionNo;
  @override
  final DateTime? refundedDate;

  factory _$LoanRefundResponse(
          [void Function(LoanRefundResponseBuilder)? updates]) =>
      (LoanRefundResponseBuilder()..update(updates))._build();

  _$LoanRefundResponse._({this.amount, this.transactionNo, this.refundedDate})
      : super._();
  @override
  LoanRefundResponse rebuild(
          void Function(LoanRefundResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanRefundResponseBuilder toBuilder() =>
      LoanRefundResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanRefundResponse &&
        amount == other.amount &&
        transactionNo == other.transactionNo &&
        refundedDate == other.refundedDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, transactionNo.hashCode);
    _$hash = $jc(_$hash, refundedDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanRefundResponse')
          ..add('amount', amount)
          ..add('transactionNo', transactionNo)
          ..add('refundedDate', refundedDate))
        .toString();
  }
}

class LoanRefundResponseBuilder
    implements Builder<LoanRefundResponse, LoanRefundResponseBuilder> {
  _$LoanRefundResponse? _$v;

  double? _amount;
  double? get amount => _$this._amount;
  set amount(double? amount) => _$this._amount = amount;

  String? _transactionNo;
  String? get transactionNo => _$this._transactionNo;
  set transactionNo(String? transactionNo) =>
      _$this._transactionNo = transactionNo;

  DateTime? _refundedDate;
  DateTime? get refundedDate => _$this._refundedDate;
  set refundedDate(DateTime? refundedDate) =>
      _$this._refundedDate = refundedDate;

  LoanRefundResponseBuilder() {
    LoanRefundResponse._defaults(this);
  }

  LoanRefundResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _amount = $v.amount;
      _transactionNo = $v.transactionNo;
      _refundedDate = $v.refundedDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanRefundResponse other) {
    _$v = other as _$LoanRefundResponse;
  }

  @override
  void update(void Function(LoanRefundResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanRefundResponse build() => _build();

  _$LoanRefundResponse _build() {
    final _$result = _$v ??
        _$LoanRefundResponse._(
          amount: amount,
          transactionNo: transactionNo,
          refundedDate: refundedDate,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
