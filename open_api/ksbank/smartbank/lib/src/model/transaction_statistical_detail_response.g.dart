// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_statistical_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$TransactionStatisticalDetailResponse
    extends TransactionStatisticalDetailResponse {
  @override
  final String? totalAmount;
  @override
  final BuiltList<TransactionStatisticalItemDto>? statisticalItemDtoList;

  factory _$TransactionStatisticalDetailResponse(
          [void Function(TransactionStatisticalDetailResponseBuilder)?
              updates]) =>
      (TransactionStatisticalDetailResponseBuilder()..update(updates))._build();

  _$TransactionStatisticalDetailResponse._(
      {this.totalAmount, this.statisticalItemDtoList})
      : super._();
  @override
  TransactionStatisticalDetailResponse rebuild(
          void Function(TransactionStatisticalDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  TransactionStatisticalDetailResponseBuilder toBuilder() =>
      TransactionStatisticalDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is TransactionStatisticalDetailResponse &&
        totalAmount == other.totalAmount &&
        statisticalItemDtoList == other.statisticalItemDtoList;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, totalAmount.hashCode);
    _$hash = $jc(_$hash, statisticalItemDtoList.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'TransactionStatisticalDetailResponse')
          ..add('totalAmount', totalAmount)
          ..add('statisticalItemDtoList', statisticalItemDtoList))
        .toString();
  }
}

class TransactionStatisticalDetailResponseBuilder
    implements
        Builder<TransactionStatisticalDetailResponse,
            TransactionStatisticalDetailResponseBuilder> {
  _$TransactionStatisticalDetailResponse? _$v;

  String? _totalAmount;
  String? get totalAmount => _$this._totalAmount;
  set totalAmount(String? totalAmount) => _$this._totalAmount = totalAmount;

  ListBuilder<TransactionStatisticalItemDto>? _statisticalItemDtoList;
  ListBuilder<TransactionStatisticalItemDto> get statisticalItemDtoList =>
      _$this._statisticalItemDtoList ??=
          ListBuilder<TransactionStatisticalItemDto>();
  set statisticalItemDtoList(
          ListBuilder<TransactionStatisticalItemDto>? statisticalItemDtoList) =>
      _$this._statisticalItemDtoList = statisticalItemDtoList;

  TransactionStatisticalDetailResponseBuilder() {
    TransactionStatisticalDetailResponse._defaults(this);
  }

  TransactionStatisticalDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _totalAmount = $v.totalAmount;
      _statisticalItemDtoList = $v.statisticalItemDtoList?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(TransactionStatisticalDetailResponse other) {
    _$v = other as _$TransactionStatisticalDetailResponse;
  }

  @override
  void update(
      void Function(TransactionStatisticalDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  TransactionStatisticalDetailResponse build() => _build();

  _$TransactionStatisticalDetailResponse _build() {
    _$TransactionStatisticalDetailResponse _$result;
    try {
      _$result = _$v ??
          _$TransactionStatisticalDetailResponse._(
            totalAmount: totalAmount,
            statisticalItemDtoList: _statisticalItemDtoList?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'statisticalItemDtoList';
        _statisticalItemDtoList?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'TransactionStatisticalDetailResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
