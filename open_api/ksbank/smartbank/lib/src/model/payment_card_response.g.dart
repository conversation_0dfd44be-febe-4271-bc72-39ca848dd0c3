// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PaymentCardResponse extends PaymentCardResponse {
  @override
  final PaymentInfo? paymentInfo;

  factory _$PaymentCardResponse(
          [void Function(PaymentCardResponseBuilder)? updates]) =>
      (PaymentCardResponseBuilder()..update(updates))._build();

  _$PaymentCardResponse._({this.paymentInfo}) : super._();
  @override
  PaymentCardResponse rebuild(
          void Function(PaymentCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PaymentCardResponseBuilder toBuilder() =>
      PaymentCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentCardResponse && paymentInfo == other.paymentInfo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, paymentInfo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PaymentCardResponse')
          ..add('paymentInfo', paymentInfo))
        .toString();
  }
}

class PaymentCardResponseBuilder
    implements Builder<PaymentCardResponse, PaymentCardResponseBuilder> {
  _$PaymentCardResponse? _$v;

  PaymentInfoBuilder? _paymentInfo;
  PaymentInfoBuilder get paymentInfo =>
      _$this._paymentInfo ??= PaymentInfoBuilder();
  set paymentInfo(PaymentInfoBuilder? paymentInfo) =>
      _$this._paymentInfo = paymentInfo;

  PaymentCardResponseBuilder() {
    PaymentCardResponse._defaults(this);
  }

  PaymentCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _paymentInfo = $v.paymentInfo?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PaymentCardResponse other) {
    _$v = other as _$PaymentCardResponse;
  }

  @override
  void update(void Function(PaymentCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PaymentCardResponse build() => _build();

  _$PaymentCardResponse _build() {
    _$PaymentCardResponse _$result;
    try {
      _$result = _$v ??
          _$PaymentCardResponse._(
            paymentInfo: _paymentInfo?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'paymentInfo';
        _paymentInfo?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'PaymentCardResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
