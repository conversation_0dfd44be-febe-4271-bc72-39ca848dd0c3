// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_count_pin_card_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ResetCountPINCardResponse extends ResetCountPINCardResponse {
  @override
  final String? message;

  factory _$ResetCountPINCardResponse(
          [void Function(ResetCountPINCardResponseBuilder)? updates]) =>
      (ResetCountPINCardResponseBuilder()..update(updates))._build();

  _$ResetCountPINCardResponse._({this.message}) : super._();
  @override
  ResetCountPINCardResponse rebuild(
          void Function(ResetCountPINCardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ResetCountPINCardResponseBuilder toBuilder() =>
      ResetCountPINCardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ResetCountPINCardResponse && message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ResetCountPINCardResponse')
          ..add('message', message))
        .toString();
  }
}

class ResetCountPINCardResponseBuilder
    implements
        Builder<ResetCountPINCardResponse, ResetCountPINCardResponseBuilder> {
  _$ResetCountPINCardResponse? _$v;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  ResetCountPINCardResponseBuilder() {
    ResetCountPINCardResponse._defaults(this);
  }

  ResetCountPINCardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ResetCountPINCardResponse other) {
    _$v = other as _$ResetCountPINCardResponse;
  }

  @override
  void update(void Function(ResetCountPINCardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ResetCountPINCardResponse build() => _build();

  _$ResetCountPINCardResponse _build() {
    final _$result = _$v ??
        _$ResetCountPINCardResponse._(
          message: message,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
