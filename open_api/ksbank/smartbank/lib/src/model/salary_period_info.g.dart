// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salary_period_info.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SalaryPeriodInfo extends SalaryPeriodInfo {
  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? startDate;
  @override
  final String? endDate;
  @override
  final int? workingDays;

  factory _$SalaryPeriodInfo(
          [void Function(SalaryPeriodInfoBuilder)? updates]) =>
      (SalaryPeriodInfoBuilder()..update(updates))._build();

  _$SalaryPeriodInfo._(
      {this.id, this.name, this.startDate, this.endDate, this.workingDays})
      : super._();
  @override
  SalaryPeriodInfo rebuild(void Function(SalaryPeriodInfoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SalaryPeriodInfoBuilder toBuilder() =>
      SalaryPeriodInfoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SalaryPeriodInfo &&
        id == other.id &&
        name == other.name &&
        startDate == other.startDate &&
        endDate == other.endDate &&
        workingDays == other.workingDays;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, startDate.hashCode);
    _$hash = $jc(_$hash, endDate.hashCode);
    _$hash = $jc(_$hash, workingDays.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SalaryPeriodInfo')
          ..add('id', id)
          ..add('name', name)
          ..add('startDate', startDate)
          ..add('endDate', endDate)
          ..add('workingDays', workingDays))
        .toString();
  }
}

class SalaryPeriodInfoBuilder
    implements Builder<SalaryPeriodInfo, SalaryPeriodInfoBuilder> {
  _$SalaryPeriodInfo? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _startDate;
  String? get startDate => _$this._startDate;
  set startDate(String? startDate) => _$this._startDate = startDate;

  String? _endDate;
  String? get endDate => _$this._endDate;
  set endDate(String? endDate) => _$this._endDate = endDate;

  int? _workingDays;
  int? get workingDays => _$this._workingDays;
  set workingDays(int? workingDays) => _$this._workingDays = workingDays;

  SalaryPeriodInfoBuilder() {
    SalaryPeriodInfo._defaults(this);
  }

  SalaryPeriodInfoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _startDate = $v.startDate;
      _endDate = $v.endDate;
      _workingDays = $v.workingDays;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SalaryPeriodInfo other) {
    _$v = other as _$SalaryPeriodInfo;
  }

  @override
  void update(void Function(SalaryPeriodInfoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SalaryPeriodInfo build() => _build();

  _$SalaryPeriodInfo _build() {
    final _$result = _$v ??
        _$SalaryPeriodInfo._(
          id: id,
          name: name,
          startDate: startDate,
          endDate: endDate,
          workingDays: workingDays,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
