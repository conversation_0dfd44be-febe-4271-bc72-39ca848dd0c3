// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_payment_method_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const LoanPaymentMethodResponseLoanPaymentMethodEnum
    _$loanPaymentMethodResponseLoanPaymentMethodEnum_DEBT_DECREASING =
    const LoanPaymentMethodResponseLoanPaymentMethodEnum._('DEBT_DECREASING');
const LoanPaymentMethodResponseLoanPaymentMethodEnum
    _$loanPaymentMethodResponseLoanPaymentMethodEnum_FIXED_ANNUITY =
    const LoanPaymentMethodResponseLoanPaymentMethodEnum._('FIXED_ANNUITY');

LoanPaymentMethodResponseLoanPaymentMethodEnum
    _$loanPaymentMethodResponseLoanPaymentMethodEnumValueOf(String name) {
  switch (name) {
    case 'DEBT_DECREASING':
      return _$loanPaymentMethodResponseLoanPaymentMethodEnum_DEBT_DECREASING;
    case 'FIXED_ANNUITY':
      return _$loanPaymentMethodResponseLoanPaymentMethodEnum_FIXED_ANNUITY;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<LoanPaymentMethodResponseLoanPaymentMethodEnum>
    _$loanPaymentMethodResponseLoanPaymentMethodEnumValues = BuiltSet<
        LoanPaymentMethodResponseLoanPaymentMethodEnum>(const <LoanPaymentMethodResponseLoanPaymentMethodEnum>[
  _$loanPaymentMethodResponseLoanPaymentMethodEnum_DEBT_DECREASING,
  _$loanPaymentMethodResponseLoanPaymentMethodEnum_FIXED_ANNUITY,
]);

Serializer<LoanPaymentMethodResponseLoanPaymentMethodEnum>
    _$loanPaymentMethodResponseLoanPaymentMethodEnumSerializer =
    _$LoanPaymentMethodResponseLoanPaymentMethodEnumSerializer();

class _$LoanPaymentMethodResponseLoanPaymentMethodEnumSerializer
    implements
        PrimitiveSerializer<LoanPaymentMethodResponseLoanPaymentMethodEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'DEBT_DECREASING': 'DEBT_DECREASING',
    'FIXED_ANNUITY': 'FIXED_ANNUITY',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'DEBT_DECREASING': 'DEBT_DECREASING',
    'FIXED_ANNUITY': 'FIXED_ANNUITY',
  };

  @override
  final Iterable<Type> types = const <Type>[
    LoanPaymentMethodResponseLoanPaymentMethodEnum
  ];
  @override
  final String wireName = 'LoanPaymentMethodResponseLoanPaymentMethodEnum';

  @override
  Object serialize(Serializers serializers,
          LoanPaymentMethodResponseLoanPaymentMethodEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  LoanPaymentMethodResponseLoanPaymentMethodEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      LoanPaymentMethodResponseLoanPaymentMethodEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$LoanPaymentMethodResponse extends LoanPaymentMethodResponse {
  @override
  final LoanPaymentMethodResponseLoanPaymentMethodEnum? loanPaymentMethod;
  @override
  final String? name;

  factory _$LoanPaymentMethodResponse(
          [void Function(LoanPaymentMethodResponseBuilder)? updates]) =>
      (LoanPaymentMethodResponseBuilder()..update(updates))._build();

  _$LoanPaymentMethodResponse._({this.loanPaymentMethod, this.name})
      : super._();
  @override
  LoanPaymentMethodResponse rebuild(
          void Function(LoanPaymentMethodResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  LoanPaymentMethodResponseBuilder toBuilder() =>
      LoanPaymentMethodResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LoanPaymentMethodResponse &&
        loanPaymentMethod == other.loanPaymentMethod &&
        name == other.name;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, loanPaymentMethod.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'LoanPaymentMethodResponse')
          ..add('loanPaymentMethod', loanPaymentMethod)
          ..add('name', name))
        .toString();
  }
}

class LoanPaymentMethodResponseBuilder
    implements
        Builder<LoanPaymentMethodResponse, LoanPaymentMethodResponseBuilder> {
  _$LoanPaymentMethodResponse? _$v;

  LoanPaymentMethodResponseLoanPaymentMethodEnum? _loanPaymentMethod;
  LoanPaymentMethodResponseLoanPaymentMethodEnum? get loanPaymentMethod =>
      _$this._loanPaymentMethod;
  set loanPaymentMethod(
          LoanPaymentMethodResponseLoanPaymentMethodEnum? loanPaymentMethod) =>
      _$this._loanPaymentMethod = loanPaymentMethod;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  LoanPaymentMethodResponseBuilder() {
    LoanPaymentMethodResponse._defaults(this);
  }

  LoanPaymentMethodResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _loanPaymentMethod = $v.loanPaymentMethod;
      _name = $v.name;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(LoanPaymentMethodResponse other) {
    _$v = other as _$LoanPaymentMethodResponse;
  }

  @override
  void update(void Function(LoanPaymentMethodResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  LoanPaymentMethodResponse build() => _build();

  _$LoanPaymentMethodResponse _build() {
    final _$result = _$v ??
        _$LoanPaymentMethodResponse._(
          loanPaymentMethod: loanPaymentMethod,
          name: name,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
