// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pay_invoice_lotus_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$PayInvoiceLotusRequest extends PayInvoiceLotusRequest {
  @override
  final String? supplierId;
  @override
  final String? customerCode;
  @override
  final String? customerName;
  @override
  final String? customerAddress;
  @override
  final bool? isSavedNextTime;
  @override
  final String? bankCif;
  @override
  final String? accountNo;
  @override
  final num? amount;
  @override
  final String? billIds;
  @override
  final num? fee;

  factory _$PayInvoiceLotusRequest(
          [void Function(PayInvoiceLotusRequestBuilder)? updates]) =>
      (PayInvoiceLotusRequestBuilder()..update(updates))._build();

  _$PayInvoiceLotusRequest._(
      {this.supplierId,
      this.customerCode,
      this.customerName,
      this.customerAddress,
      this.isSavedNextTime,
      this.bankCif,
      this.accountNo,
      this.amount,
      this.billIds,
      this.fee})
      : super._();
  @override
  PayInvoiceLotusRequest rebuild(
          void Function(PayInvoiceLotusRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  PayInvoiceLotusRequestBuilder toBuilder() =>
      PayInvoiceLotusRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PayInvoiceLotusRequest &&
        supplierId == other.supplierId &&
        customerCode == other.customerCode &&
        customerName == other.customerName &&
        customerAddress == other.customerAddress &&
        isSavedNextTime == other.isSavedNextTime &&
        bankCif == other.bankCif &&
        accountNo == other.accountNo &&
        amount == other.amount &&
        billIds == other.billIds &&
        fee == other.fee;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, supplierId.hashCode);
    _$hash = $jc(_$hash, customerCode.hashCode);
    _$hash = $jc(_$hash, customerName.hashCode);
    _$hash = $jc(_$hash, customerAddress.hashCode);
    _$hash = $jc(_$hash, isSavedNextTime.hashCode);
    _$hash = $jc(_$hash, bankCif.hashCode);
    _$hash = $jc(_$hash, accountNo.hashCode);
    _$hash = $jc(_$hash, amount.hashCode);
    _$hash = $jc(_$hash, billIds.hashCode);
    _$hash = $jc(_$hash, fee.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'PayInvoiceLotusRequest')
          ..add('supplierId', supplierId)
          ..add('customerCode', customerCode)
          ..add('customerName', customerName)
          ..add('customerAddress', customerAddress)
          ..add('isSavedNextTime', isSavedNextTime)
          ..add('bankCif', bankCif)
          ..add('accountNo', accountNo)
          ..add('amount', amount)
          ..add('billIds', billIds)
          ..add('fee', fee))
        .toString();
  }
}

class PayInvoiceLotusRequestBuilder
    implements Builder<PayInvoiceLotusRequest, PayInvoiceLotusRequestBuilder> {
  _$PayInvoiceLotusRequest? _$v;

  String? _supplierId;
  String? get supplierId => _$this._supplierId;
  set supplierId(String? supplierId) => _$this._supplierId = supplierId;

  String? _customerCode;
  String? get customerCode => _$this._customerCode;
  set customerCode(String? customerCode) => _$this._customerCode = customerCode;

  String? _customerName;
  String? get customerName => _$this._customerName;
  set customerName(String? customerName) => _$this._customerName = customerName;

  String? _customerAddress;
  String? get customerAddress => _$this._customerAddress;
  set customerAddress(String? customerAddress) =>
      _$this._customerAddress = customerAddress;

  bool? _isSavedNextTime;
  bool? get isSavedNextTime => _$this._isSavedNextTime;
  set isSavedNextTime(bool? isSavedNextTime) =>
      _$this._isSavedNextTime = isSavedNextTime;

  String? _bankCif;
  String? get bankCif => _$this._bankCif;
  set bankCif(String? bankCif) => _$this._bankCif = bankCif;

  String? _accountNo;
  String? get accountNo => _$this._accountNo;
  set accountNo(String? accountNo) => _$this._accountNo = accountNo;

  num? _amount;
  num? get amount => _$this._amount;
  set amount(num? amount) => _$this._amount = amount;

  String? _billIds;
  String? get billIds => _$this._billIds;
  set billIds(String? billIds) => _$this._billIds = billIds;

  num? _fee;
  num? get fee => _$this._fee;
  set fee(num? fee) => _$this._fee = fee;

  PayInvoiceLotusRequestBuilder() {
    PayInvoiceLotusRequest._defaults(this);
  }

  PayInvoiceLotusRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _supplierId = $v.supplierId;
      _customerCode = $v.customerCode;
      _customerName = $v.customerName;
      _customerAddress = $v.customerAddress;
      _isSavedNextTime = $v.isSavedNextTime;
      _bankCif = $v.bankCif;
      _accountNo = $v.accountNo;
      _amount = $v.amount;
      _billIds = $v.billIds;
      _fee = $v.fee;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(PayInvoiceLotusRequest other) {
    _$v = other as _$PayInvoiceLotusRequest;
  }

  @override
  void update(void Function(PayInvoiceLotusRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  PayInvoiceLotusRequest build() => _build();

  _$PayInvoiceLotusRequest _build() {
    final _$result = _$v ??
        _$PayInvoiceLotusRequest._(
          supplierId: supplierId,
          customerCode: customerCode,
          customerName: customerName,
          customerAddress: customerAddress,
          isSavedNextTime: isSavedNextTime,
          bankCif: bankCif,
          accountNo: accountNo,
          amount: amount,
          billIds: billIds,
          fee: fee,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
