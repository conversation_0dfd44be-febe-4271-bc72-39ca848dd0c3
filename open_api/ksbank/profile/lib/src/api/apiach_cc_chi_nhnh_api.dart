//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:dio/dio.dart';

import 'package:ksbank_api_profile/src/api_util.dart';
import 'package:ksbank_api_profile/src/model/base_response_district_response.dart';
import 'package:ksbank_api_profile/src/model/base_response_page_support_branch_response.dart';
import 'package:ksbank_api_profile/src/model/base_response_page_support_district_response.dart';
import 'package:ksbank_api_profile/src/model/base_response_page_support_network_response.dart';
import 'package:ksbank_api_profile/src/model/base_response_page_support_province_response.dart';
import 'package:ksbank_api_profile/src/model/base_response_page_support_ward_network_response.dart';
import 'package:ksbank_api_profile/src/model/base_response_page_support_ward_response.dart';
import 'package:ksbank_api_profile/src/model/base_response_province_response.dart';
import 'package:ksbank_api_profile/src/model/get_branch_request.dart';

class APIAChCcChiNhnhApi {
  final Dio _dio;

  final Serializers _serializers;

  const APIAChCcChiNhnhApi(this._dio, this._serializers);

  /// API tra cứu toàn bộ danh sách các tỉnh
  /// Sử dụng để tra cứu danh sách thông tin tỉnh theo mã.   Thông tin trả về danh sách gồm:   provinceCode: Mã tỉnh  provinceNameVn: Tên tỉnh bằng tiếng việt  provinceNameEn: Tên tỉnh bằng tiếng anh  coreProvinceCode: Mã tỉnh trong core
  ///
  /// Parameters:
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportProvinceResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportProvinceResponse>> getAllProvinces({
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/v1/province/getAllProvinces';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportProvinceResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportProvinceResponse),
            ) as BaseResponsePageSupportProvinceResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportProvinceResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách chi nhánh
  /// Sử dụng để tra cứu thông tin danh sách chi nhánh  Thông tin trả về danh sách gồm:   branchCode: Mã chi nhánh  branchName: Tên chi nhánh  districtCode: Mã huyện/ thị xã  address: Địa chỉ chi nhánh
  ///
  /// Parameters:
  /// * [getBranchRequest]
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportBranchResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportBranchResponse>> getBranches({
    required GetBranchRequest getBranchRequest,
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/branch/getBranches';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
    };

    dynamic _bodyData;

    try {
      const _type = FullType(GetBranchRequest);
      _bodyData =
          _serializers.serialize(getBranchRequest, specifiedType: _type);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _options.compose(
          _dio.options,
          _path,
          queryParameters: _queryParameters,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportBranchResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportBranchResponse),
            ) as BaseResponsePageSupportBranchResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportBranchResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách chi nhánh theo theo mã huyện/ thị xã
  /// Sử dụng để tra cứu thông tin danh sách chi nhánh theo theo mã huyện/ thị xã  Thông tin trả về danh sách gồm:   branchCode: Mã chi nhánh  branchName: Tên chi nhánh  districtCode: Mã huyện/ thị xã  address: Địa chỉ chi nhánh
  ///
  /// Parameters:
  /// * [districtCode]
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportBranchResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportBranchResponse>>
      getBranchesByDistrictCode({
    required String districtCode,
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/branch/getBranchesByDistrictCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
      r'districtCode': encodeQueryParameter(
          _serializers, districtCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportBranchResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportBranchResponse),
            ) as BaseResponsePageSupportBranchResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportBranchResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin huyện/ thị xã theo mã
  /// Sử dụng để tra cứu thông tin huyện/ thị xã theo mã.   Thông tin trả về gồm:   districtCode: Mã huyện  districtNameVn: Tên huyện bằng tiếng việt  districtNameEn: Tên huyện bằng tiếng anh  provinceCode: Mã tỉnh mà huyện trực thuộc
  ///
  /// Parameters:
  /// * [districtCode]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponseDistrictResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponseDistrictResponse>> getDistrictByCode({
    required String districtCode,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/district/getDistrictByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'districtCode': encodeQueryParameter(
          _serializers, districtCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponseDistrictResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType: const FullType(BaseResponseDistrictResponse),
            ) as BaseResponseDistrictResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponseDistrictResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin huyện/ thị xã có chi nhánh theo mã
  /// Sử dụng để tra cứu thông tin huyện/ thị xã có chi nhánh theo mã.   Thông tin trả về gồm:   districtCode: Mã huyện  districtNameVn: Tên huyện bằng tiếng việt  districtNameEn: Tên huyện bằng tiếng anh  provinceCode: Mã tỉnh mà huyện trực thuộc
  ///
  /// Parameters:
  /// * [districtCode]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponseDistrictResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponseDistrictResponse>> getDistrictWithBranchesByCode({
    required String districtCode,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/district/getDistrictWithBranchesByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'districtCode': encodeQueryParameter(
          _serializers, districtCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponseDistrictResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType: const FullType(BaseResponseDistrictResponse),
            ) as BaseResponseDistrictResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponseDistrictResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách huyện/ thị xã theo mã
  /// Sử dụng để tra cứu thông tin danh sách huyện/ thị xã theo mã.   Thông tin trả về gồm:   districtCode: Mã huyện  districtNameVn: Tên huyện bằng tiếng việt  districtNameEn: Tên huyện bằng tiếng anh  provinceCode: Mã tỉnh mà huyện trực thuộc
  ///
  /// Parameters:
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportDistrictResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportDistrictResponse>> getDistrictsByCode({
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/district/getDistrictsByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportDistrictResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportDistrictResponse),
            ) as BaseResponsePageSupportDistrictResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportDistrictResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách quận/ huyện/ thị xã theo mã tỉnh/thành phố
  /// Sử dụng để tra cứu thông tin danh sách huyện/ thị xã theo mã.   Thông tin trả về gồm:   districtCode: Mã huyện  districtNameVn: Tên huyện bằng tiếng việt  districtNameEn: Tên huyện bằng tiếng anh  provinceCode: Mã tỉnh mà huyện trực thuộc
  ///
  /// Parameters:
  /// * [provinceCode]
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportDistrictResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportDistrictResponse>>
      getDistrictsByProvinceCode({
    required String provinceCode,
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/v1/district/getDistrictsByProvinceCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'provinceCode': encodeQueryParameter(
          _serializers, provinceCode, const FullType(String)),
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportDistrictResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportDistrictResponse),
            ) as BaseResponsePageSupportDistrictResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportDistrictResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã
  /// Sử dụng để tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã.   Thông tin trả về gồm:   districtCode: Mã huyện  districtNameVn: Tên huyện bằng tiếng việt  districtNameEn: Tên huyện bằng tiếng anh  provinceCode: Mã tỉnh mà huyện trực thuộc
  ///
  /// Parameters:
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportDistrictResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportDistrictResponse>>
      getDistrictsWithBranchesByCode({
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/district/getDistrictsWithBranchesByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportDistrictResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportDistrictResponse),
            ) as BaseResponsePageSupportDistrictResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportDistrictResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã tỉnh
  /// Sử dụng để tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã tỉnh  Thông tin trả về gồm:   districtCode: Mã huyện  districtNameVn: Tên huyện bằng tiếng việt  districtNameEn: Tên huyện bằng tiếng anh  provinceCode: Mã tỉnh mà huyện trực thuộc
  ///
  /// Parameters:
  /// * [provinceCode]
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportDistrictResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportDistrictResponse>>
      getDistrictsWithBranchesByProvinceCode({
    required String provinceCode,
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/district/getDistrictsWithBranchesByProvinceCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
      r'provinceCode': encodeQueryParameter(
          _serializers, provinceCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportDistrictResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportDistrictResponse),
            ) as BaseResponsePageSupportDistrictResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportDistrictResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getNetworksByProvinceCodeAndDistrictCode
  ///
  ///
  /// Parameters:
  /// * [provinceCode]
  /// * [districtCode]
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportNetworkResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportNetworkResponse>>
      getNetworksByProvinceCodeAndDistrictCode({
    required String provinceCode,
    required String districtCode,
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/v1/wards/getNetworksByProvinceCodeAndDistrictCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
      r'provinceCode': encodeQueryParameter(
          _serializers, provinceCode, const FullType(String)),
      r'districtCode': encodeQueryParameter(
          _serializers, districtCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportNetworkResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportNetworkResponse),
            ) as BaseResponsePageSupportNetworkResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportNetworkResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin tỉnh theo mã
  /// Sử dụng để tra cứu thông tin tỉnh theo mã.   Thông tin trả về gồm:   provinceCode: Mã tỉnh  provinceNameVn: Tên tỉnh bằng tiếng việt  provinceNameEn: Tên tỉnh bằng tiếng anh  coreProvinceCode: Mã tỉnh trong core
  ///
  /// Parameters:
  /// * [provinceCode]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponseProvinceResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponseProvinceResponse>> getProvinceByCode({
    required String provinceCode,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/province/getProvinceByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'provinceCode': encodeQueryParameter(
          _serializers, provinceCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponseProvinceResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType: const FullType(BaseResponseProvinceResponse),
            ) as BaseResponseProvinceResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponseProvinceResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin tỉnh có chi nhánh theo mã
  /// Sử dụng để tra cứu thông tin tỉnh có chi nhánh theo mã.   Thông tin trả về gồm:   provinceCode: Mã tỉnh  provinceNameVn: Tên tỉnh bằng tiếng việt  provinceNameEn: Tên tỉnh bằng tiếng anh  coreProvinceCode: Mã tỉnh trong core
  ///
  /// Parameters:
  /// * [provinceCode]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponseProvinceResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponseProvinceResponse>> getProvinceWithBranchesByCode({
    required String provinceCode,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/province/getProvinceWithBranchesByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      r'provinceCode': encodeQueryParameter(
          _serializers, provinceCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponseProvinceResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType: const FullType(BaseResponseProvinceResponse),
            ) as BaseResponseProvinceResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponseProvinceResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu danh sách thông tin tỉnh theo mã
  /// Sử dụng để tra cứu danh sách thông tin tỉnh theo mã.   Thông tin trả về danh sách gồm:   provinceCode: Mã tỉnh  provinceNameVn: Tên tỉnh bằng tiếng việt  provinceNameEn: Tên tỉnh bằng tiếng anh  coreProvinceCode: Mã tỉnh trong core
  ///
  /// Parameters:
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportProvinceResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportProvinceResponse>> getProvincesByCode({
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/province/getProvincesByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportProvinceResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportProvinceResponse),
            ) as BaseResponsePageSupportProvinceResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportProvinceResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách tỉnh có chi nhánh theo mã
  /// Sử dụng để tra cứu thông tin danh sách tỉnh có chi nhánh theo mã.   Thông tin trả về danh sách gồm:   provinceCode: Mã tỉnh  provinceNameVn: Tên tỉnh bằng tiếng việt  provinceNameEn: Tên tỉnh bằng tiếng anh  coreProvinceCode: Mã tỉnh trong core
  ///
  /// Parameters:
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportProvinceResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportProvinceResponse>>
      getProvincesWithBranchesByCode({
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/province/getProvincesWithBranchesByCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportProvinceResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportProvinceResponse),
            ) as BaseResponsePageSupportProvinceResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportProvinceResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// getWardsAndNetworkByProvinceCodeAndDistrictCode
  ///
  ///
  /// Parameters:
  /// * [provinceCode]
  /// * [districtCode]
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportWardNetworkResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportWardNetworkResponse>>
      getWardsAndNetworkByProvinceCodeAndDistrictCode({
    required String provinceCode,
    required String districtCode,
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path =
        r'/address/v1/wards/getWardsAndNetworkByProvinceCodeAndDistrictCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
      r'provinceCode': encodeQueryParameter(
          _serializers, provinceCode, const FullType(String)),
      r'districtCode': encodeQueryParameter(
          _serializers, districtCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportWardNetworkResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportWardNetworkResponse),
            ) as BaseResponsePageSupportWardNetworkResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportWardNetworkResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// API tra cứu thông tin danh sách Phường/ Xã theo mã tỉnh/ thành phố và quận/huyện
  /// Sử dụng để tra cứu thông tin danh sách Phường/ Xã  Thông tin trả về danh sách gồm:   wardCode: Mã Phường/ Xã wardNameVi: Tên Phường/ Xã bằng tiếng việt  wardNameEn: Tên Phường/ Xã bằng tiếng anh  provinceCode: Mã tỉnh/ thành phố  districtCode: Mã quận/ huyện pho
  ///
  /// Parameters:
  /// * [provinceCode]
  /// * [districtCode]
  /// * [page]
  /// * [size]
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [BaseResponsePageSupportWardResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<BaseResponsePageSupportWardResponse>>
      getWardsByProvinceCodeAndDistrictCode({
    required String provinceCode,
    required String districtCode,
    int? page = 0,
    int? size = 20,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/address/v1/wards/getWardsByProvinceCodeAndDistrictCode';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (page != null)
        r'page': encodeQueryParameter(_serializers, page, const FullType(int)),
      if (size != null)
        r'size': encodeQueryParameter(_serializers, size, const FullType(int)),
      r'provinceCode': encodeQueryParameter(
          _serializers, provinceCode, const FullType(String)),
      r'districtCode': encodeQueryParameter(
          _serializers, districtCode, const FullType(String)),
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    BaseResponsePageSupportWardResponse? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null
          ? null
          : _serializers.deserialize(
              rawResponse,
              specifiedType:
                  const FullType(BaseResponsePageSupportWardResponse),
            ) as BaseResponsePageSupportWardResponse;
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<BaseResponsePageSupportWardResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }
}
