//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'create_quick_verify_response.g.dart';

/// CreateQuickVerifyResponse
///
/// Properties:
/// * [id]
/// * [userId]
/// * [identity]
/// * [status]
@BuiltValue()
abstract class CreateQuickVerifyResponse
    implements
        Built<CreateQuickVerifyResponse, CreateQuickVerifyResponseBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  @BuiltValueField(wireName: r'userId')
  String? get userId;

  @BuiltValueField(wireName: r'identity')
  String? get identity;

  @BuiltValueField(wireName: r'status')
  CreateQuickVerifyResponseStatusEnum? get status;
  // enum statusEnum {  PROCESSING,  SMS_OTP_VERIFIED,  EMAIL_OTP_VERIFIED,  SUCCESS,  FAILURE,  CANCEL,  CONFIRMED,  REJECTED,  PENDING,  };

  CreateQuickVerifyResponse._();

  factory CreateQuickVerifyResponse(
          [void updates(CreateQuickVerifyResponseBuilder b)]) =
      _$CreateQuickVerifyResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CreateQuickVerifyResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CreateQuickVerifyResponse> get serializer =>
      _$CreateQuickVerifyResponseSerializer();
}

class _$CreateQuickVerifyResponseSerializer
    implements PrimitiveSerializer<CreateQuickVerifyResponse> {
  @override
  final Iterable<Type> types = const [
    CreateQuickVerifyResponse,
    _$CreateQuickVerifyResponse
  ];

  @override
  final String wireName = r'CreateQuickVerifyResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CreateQuickVerifyResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.userId != null) {
      yield r'userId';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.identity != null) {
      yield r'identity';
      yield serializers.serialize(
        object.identity,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.status != null) {
      yield r'status';
      yield serializers.serialize(
        object.status,
        specifiedType:
            const FullType.nullable(CreateQuickVerifyResponseStatusEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CreateQuickVerifyResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CreateQuickVerifyResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.id = valueDes;
          break;
        case r'userId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.userId = valueDes;
          break;
        case r'identity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.identity = valueDes;
          break;
        case r'status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(CreateQuickVerifyResponseStatusEnum),
          ) as CreateQuickVerifyResponseStatusEnum?;
          if (valueDes == null) continue;
          result.status = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CreateQuickVerifyResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CreateQuickVerifyResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class CreateQuickVerifyResponseStatusEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PROCESSING')
  static const CreateQuickVerifyResponseStatusEnum PROCESSING =
      _$createQuickVerifyResponseStatusEnum_PROCESSING;
  @BuiltValueEnumConst(wireName: r'SMS_OTP_VERIFIED')
  static const CreateQuickVerifyResponseStatusEnum SMS_OTP_VERIFIED =
      _$createQuickVerifyResponseStatusEnum_SMS_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'EMAIL_OTP_VERIFIED')
  static const CreateQuickVerifyResponseStatusEnum EMAIL_OTP_VERIFIED =
      _$createQuickVerifyResponseStatusEnum_EMAIL_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'SUCCESS')
  static const CreateQuickVerifyResponseStatusEnum SUCCESS =
      _$createQuickVerifyResponseStatusEnum_SUCCESS;
  @BuiltValueEnumConst(wireName: r'FAILURE')
  static const CreateQuickVerifyResponseStatusEnum FAILURE =
      _$createQuickVerifyResponseStatusEnum_FAILURE;
  @BuiltValueEnumConst(wireName: r'CANCEL')
  static const CreateQuickVerifyResponseStatusEnum CANCEL =
      _$createQuickVerifyResponseStatusEnum_CANCEL;
  @BuiltValueEnumConst(wireName: r'CONFIRMED')
  static const CreateQuickVerifyResponseStatusEnum CONFIRMED =
      _$createQuickVerifyResponseStatusEnum_CONFIRMED;
  @BuiltValueEnumConst(wireName: r'REJECTED')
  static const CreateQuickVerifyResponseStatusEnum REJECTED =
      _$createQuickVerifyResponseStatusEnum_REJECTED;
  @BuiltValueEnumConst(wireName: r'PENDING')
  static const CreateQuickVerifyResponseStatusEnum PENDING =
      _$createQuickVerifyResponseStatusEnum_PENDING;

  static Serializer<CreateQuickVerifyResponseStatusEnum> get serializer =>
      _$createQuickVerifyResponseStatusEnumSerializer;

  const CreateQuickVerifyResponseStatusEnum._(String name) : super(name);

  static BuiltSet<CreateQuickVerifyResponseStatusEnum> get values =>
      _$createQuickVerifyResponseStatusEnumValues;
  static CreateQuickVerifyResponseStatusEnum valueOf(String name) =>
      _$createQuickVerifyResponseStatusEnumValueOf(name);
}
