// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_quick_verify_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_PROCESSING =
    const CreateQuickVerifyRequestStatusEnum._('PROCESSING');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED =
    const CreateQuickVerifyRequestStatusEnum._('SMS_OTP_VERIFIED');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED =
    const CreateQuickVerifyRequestStatusEnum._('EMAIL_OTP_VERIFIED');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_SUCCESS =
    const CreateQuickVerifyRequestStatusEnum._('SUCCESS');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_FAILURE =
    const CreateQuickVerifyRequestStatusEnum._('FAILURE');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_CANCEL =
    const CreateQuickVerifyRequestStatusEnum._('CANCEL');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_CONFIRMED =
    const CreateQuickVerifyRequestStatusEnum._('CONFIRMED');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_REJECTED =
    const CreateQuickVerifyRequestStatusEnum._('REJECTED');
const CreateQuickVerifyRequestStatusEnum
    _$createQuickVerifyRequestStatusEnum_PENDING =
    const CreateQuickVerifyRequestStatusEnum._('PENDING');

CreateQuickVerifyRequestStatusEnum _$createQuickVerifyRequestStatusEnumValueOf(
    String name) {
  switch (name) {
    case 'PROCESSING':
      return _$createQuickVerifyRequestStatusEnum_PROCESSING;
    case 'SMS_OTP_VERIFIED':
      return _$createQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED;
    case 'EMAIL_OTP_VERIFIED':
      return _$createQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED;
    case 'SUCCESS':
      return _$createQuickVerifyRequestStatusEnum_SUCCESS;
    case 'FAILURE':
      return _$createQuickVerifyRequestStatusEnum_FAILURE;
    case 'CANCEL':
      return _$createQuickVerifyRequestStatusEnum_CANCEL;
    case 'CONFIRMED':
      return _$createQuickVerifyRequestStatusEnum_CONFIRMED;
    case 'REJECTED':
      return _$createQuickVerifyRequestStatusEnum_REJECTED;
    case 'PENDING':
      return _$createQuickVerifyRequestStatusEnum_PENDING;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<CreateQuickVerifyRequestStatusEnum>
    _$createQuickVerifyRequestStatusEnumValues = new BuiltSet<
        CreateQuickVerifyRequestStatusEnum>(const <CreateQuickVerifyRequestStatusEnum>[
  _$createQuickVerifyRequestStatusEnum_PROCESSING,
  _$createQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED,
  _$createQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED,
  _$createQuickVerifyRequestStatusEnum_SUCCESS,
  _$createQuickVerifyRequestStatusEnum_FAILURE,
  _$createQuickVerifyRequestStatusEnum_CANCEL,
  _$createQuickVerifyRequestStatusEnum_CONFIRMED,
  _$createQuickVerifyRequestStatusEnum_REJECTED,
  _$createQuickVerifyRequestStatusEnum_PENDING,
]);

Serializer<CreateQuickVerifyRequestStatusEnum>
    _$createQuickVerifyRequestStatusEnumSerializer =
    new _$CreateQuickVerifyRequestStatusEnumSerializer();

class _$CreateQuickVerifyRequestStatusEnumSerializer
    implements PrimitiveSerializer<CreateQuickVerifyRequestStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };

  @override
  final Iterable<Type> types = const <Type>[CreateQuickVerifyRequestStatusEnum];
  @override
  final String wireName = 'CreateQuickVerifyRequestStatusEnum';

  @override
  Object serialize(
          Serializers serializers, CreateQuickVerifyRequestStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  CreateQuickVerifyRequestStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      CreateQuickVerifyRequestStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$CreateQuickVerifyRequest extends CreateQuickVerifyRequest {
  @override
  final String? id;
  @override
  final String? identity;
  @override
  final CreateQuickVerifyRequestStatusEnum? status;

  factory _$CreateQuickVerifyRequest(
          [void Function(CreateQuickVerifyRequestBuilder)? updates]) =>
      (new CreateQuickVerifyRequestBuilder()..update(updates))._build();

  _$CreateQuickVerifyRequest._({this.id, this.identity, this.status})
      : super._();

  @override
  CreateQuickVerifyRequest rebuild(
          void Function(CreateQuickVerifyRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateQuickVerifyRequestBuilder toBuilder() =>
      new CreateQuickVerifyRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateQuickVerifyRequest &&
        id == other.id &&
        identity == other.identity &&
        status == other.status;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, identity.hashCode);
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateQuickVerifyRequest')
          ..add('id', id)
          ..add('identity', identity)
          ..add('status', status))
        .toString();
  }
}

class CreateQuickVerifyRequestBuilder
    implements
        Builder<CreateQuickVerifyRequest, CreateQuickVerifyRequestBuilder> {
  _$CreateQuickVerifyRequest? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _identity;
  String? get identity => _$this._identity;
  set identity(String? identity) => _$this._identity = identity;

  CreateQuickVerifyRequestStatusEnum? _status;
  CreateQuickVerifyRequestStatusEnum? get status => _$this._status;
  set status(CreateQuickVerifyRequestStatusEnum? status) =>
      _$this._status = status;

  CreateQuickVerifyRequestBuilder() {
    CreateQuickVerifyRequest._defaults(this);
  }

  CreateQuickVerifyRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _identity = $v.identity;
      _status = $v.status;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateQuickVerifyRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CreateQuickVerifyRequest;
  }

  @override
  void update(void Function(CreateQuickVerifyRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateQuickVerifyRequest build() => _build();

  _$CreateQuickVerifyRequest _build() {
    final _$result = _$v ??
        new _$CreateQuickVerifyRequest._(
            id: id, identity: identity, status: status);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
