//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'check_user_klb_request.g.dart';

/// CheckUserKlbRequest
///
/// Properties:
/// * [identityType]
/// * [identity]
@BuiltValue()
abstract class CheckUserKlbRequest
    implements Built<CheckUserKlbRequest, CheckUserKlbRequestBuilder> {
  @BuiltValueField(wireName: r'identityType')
  CheckUserKlbRequestIdentityTypeEnum? get identityType;
  // enum identityTypeEnum {  PHONE,  ID_CARD,  };

  @BuiltValueField(wireName: r'identity')
  String? get identity;

  CheckUserKlbRequest._();

  factory CheckUserKlbRequest([void updates(CheckUserKlbRequestBuilder b)]) =
      _$CheckUserKlbRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CheckUserKlbRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CheckUserKlbRequest> get serializer =>
      _$CheckUserKlbRequestSerializer();
}

class _$CheckUserKlbRequestSerializer
    implements PrimitiveSerializer<CheckUserKlbRequest> {
  @override
  final Iterable<Type> types = const [
    CheckUserKlbRequest,
    _$CheckUserKlbRequest
  ];

  @override
  final String wireName = r'CheckUserKlbRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CheckUserKlbRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.identityType != null) {
      yield r'identityType';
      yield serializers.serialize(
        object.identityType,
        specifiedType:
            const FullType.nullable(CheckUserKlbRequestIdentityTypeEnum),
      );
    }
    if (object.identity != null) {
      yield r'identity';
      yield serializers.serialize(
        object.identity,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CheckUserKlbRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CheckUserKlbRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'identityType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(CheckUserKlbRequestIdentityTypeEnum),
          ) as CheckUserKlbRequestIdentityTypeEnum?;
          if (valueDes == null) continue;
          result.identityType = valueDes;
          break;
        case r'identity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.identity = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CheckUserKlbRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CheckUserKlbRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class CheckUserKlbRequestIdentityTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PHONE')
  static const CheckUserKlbRequestIdentityTypeEnum PHONE =
      _$checkUserKlbRequestIdentityTypeEnum_PHONE;
  @BuiltValueEnumConst(wireName: r'ID_CARD')
  static const CheckUserKlbRequestIdentityTypeEnum ID_CARD =
      _$checkUserKlbRequestIdentityTypeEnum_ID_CARD;

  static Serializer<CheckUserKlbRequestIdentityTypeEnum> get serializer =>
      _$checkUserKlbRequestIdentityTypeEnumSerializer;

  const CheckUserKlbRequestIdentityTypeEnum._(String name) : super(name);

  static BuiltSet<CheckUserKlbRequestIdentityTypeEnum> get values =>
      _$checkUserKlbRequestIdentityTypeEnumValues;
  static CheckUserKlbRequestIdentityTypeEnum valueOf(String name) =>
      _$checkUserKlbRequestIdentityTypeEnumValueOf(name);
}
