//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'base_response_etoken_status.g.dart';

/// BaseResponseEtokenStatus
///
/// Properties:
/// * [success]
/// * [code]
/// * [data]
/// * [message]
@BuiltValue()
abstract class BaseResponseEtokenStatus
    implements
        Built<BaseResponseEtokenStatus, BaseResponseEtokenStatusBuilder> {
  @BuiltValueField(wireName: r'success')
  bool? get success;

  @BuiltValueField(wireName: r'code')
  int? get code;

  @BuiltValueField(wireName: r'data')
  BaseResponseEtokenStatusDataEnum? get data;
  // enum dataEnum {  ACTIVATED,  UNACTIVATED,  UNREGISTERED,  ACTIVE_ON_ANOTHER_DEVICE,  };

  @BuiltValueField(wireName: r'message')
  String? get message;

  BaseResponseEtokenStatus._();

  factory BaseResponseEtokenStatus(
          [void updates(BaseResponseEtokenStatusBuilder b)]) =
      _$BaseResponseEtokenStatus;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BaseResponseEtokenStatusBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BaseResponseEtokenStatus> get serializer =>
      _$BaseResponseEtokenStatusSerializer();
}

class _$BaseResponseEtokenStatusSerializer
    implements PrimitiveSerializer<BaseResponseEtokenStatus> {
  @override
  final Iterable<Type> types = const [
    BaseResponseEtokenStatus,
    _$BaseResponseEtokenStatus
  ];

  @override
  final String wireName = r'BaseResponseEtokenStatus';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BaseResponseEtokenStatus object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.success != null) {
      yield r'success';
      yield serializers.serialize(
        object.success,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.data != null) {
      yield r'data';
      yield serializers.serialize(
        object.data,
        specifiedType:
            const FullType.nullable(BaseResponseEtokenStatusDataEnum),
      );
    }
    if (object.message != null) {
      yield r'message';
      yield serializers.serialize(
        object.message,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    BaseResponseEtokenStatus object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BaseResponseEtokenStatusBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.success = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.code = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(BaseResponseEtokenStatusDataEnum),
          ) as BaseResponseEtokenStatusDataEnum?;
          if (valueDes == null) continue;
          result.data = valueDes;
          break;
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.message = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BaseResponseEtokenStatus deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BaseResponseEtokenStatusBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class BaseResponseEtokenStatusDataEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'ACTIVATED')
  static const BaseResponseEtokenStatusDataEnum ACTIVATED =
      _$baseResponseEtokenStatusDataEnum_ACTIVATED;
  @BuiltValueEnumConst(wireName: r'UNACTIVATED')
  static const BaseResponseEtokenStatusDataEnum UNACTIVATED =
      _$baseResponseEtokenStatusDataEnum_UNACTIVATED;
  @BuiltValueEnumConst(wireName: r'UNREGISTERED')
  static const BaseResponseEtokenStatusDataEnum UNREGISTERED =
      _$baseResponseEtokenStatusDataEnum_UNREGISTERED;
  @BuiltValueEnumConst(wireName: r'ACTIVE_ON_ANOTHER_DEVICE')
  static const BaseResponseEtokenStatusDataEnum ACTIVE_ON_ANOTHER_DEVICE =
      _$baseResponseEtokenStatusDataEnum_ACTIVE_ON_ANOTHER_DEVICE;

  static Serializer<BaseResponseEtokenStatusDataEnum> get serializer =>
      _$baseResponseEtokenStatusDataEnumSerializer;

  const BaseResponseEtokenStatusDataEnum._(String name) : super(name);

  static BuiltSet<BaseResponseEtokenStatusDataEnum> get values =>
      _$baseResponseEtokenStatusDataEnumValues;
  static BaseResponseEtokenStatusDataEnum valueOf(String name) =>
      _$baseResponseEtokenStatusDataEnumValueOf(name);
}
