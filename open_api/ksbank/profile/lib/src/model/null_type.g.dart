// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'null_type.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

NullType _$valueOf(String name) {
  switch (name) {
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<NullType> _$values = new BuiltSet<NullType>(const <NullType>[]);

class _$NullTypeMeta {
  const _$NullTypeMeta();
  NullType valueOf(String name) => _$valueOf(name);
  BuiltSet<NullType> get values => _$values;
}

abstract class _$NullTypeMixin {
  // ignore: non_constant_identifier_names
  _$NullTypeMeta get NullType => const _$NullTypeMeta();
}

Serializer<NullType> _$nullTypeSerializer = new _$NullTypeSerializer();

class _$NullTypeSerializer implements PrimitiveSerializer<NullType> {
  @override
  final Iterable<Type> types = const <Type>[NullType];
  @override
  final String wireName = 'NullType';

  @override
  Object serialize(Serializers serializers, NullType object,
          {FullType specifiedType = FullType.unspecified}) =>
      object.name;

  @override
  NullType deserialize(Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      NullType.valueOf(serialized as String);
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
