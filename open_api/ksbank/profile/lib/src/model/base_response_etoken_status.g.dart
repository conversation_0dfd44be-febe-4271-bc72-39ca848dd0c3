// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_etoken_status.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const BaseResponseEtokenStatusDataEnum
    _$baseResponseEtokenStatusDataEnum_ACTIVATED =
    const BaseResponseEtokenStatusDataEnum._('ACTIVATED');
const BaseResponseEtokenStatusDataEnum
    _$baseResponseEtokenStatusDataEnum_UNACTIVATED =
    const BaseResponseEtokenStatusDataEnum._('UNACTIVATED');
const BaseResponseEtokenStatusDataEnum
    _$baseResponseEtokenStatusDataEnum_UNREGISTERED =
    const BaseResponseEtokenStatusDataEnum._('UNREGISTERED');
const BaseResponseEtokenStatusDataEnum
    _$baseResponseEtokenStatusDataEnum_ACTIVE_ON_ANOTHER_DEVICE =
    const BaseResponseEtokenStatusDataEnum._('ACTIVE_ON_ANOTHER_DEVICE');

BaseResponseEtokenStatusDataEnum _$baseResponseEtokenStatusDataEnumValueOf(
    String name) {
  switch (name) {
    case 'ACTIVATED':
      return _$baseResponseEtokenStatusDataEnum_ACTIVATED;
    case 'UNACTIVATED':
      return _$baseResponseEtokenStatusDataEnum_UNACTIVATED;
    case 'UNREGISTERED':
      return _$baseResponseEtokenStatusDataEnum_UNREGISTERED;
    case 'ACTIVE_ON_ANOTHER_DEVICE':
      return _$baseResponseEtokenStatusDataEnum_ACTIVE_ON_ANOTHER_DEVICE;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<BaseResponseEtokenStatusDataEnum>
    _$baseResponseEtokenStatusDataEnumValues = new BuiltSet<
        BaseResponseEtokenStatusDataEnum>(const <BaseResponseEtokenStatusDataEnum>[
  _$baseResponseEtokenStatusDataEnum_ACTIVATED,
  _$baseResponseEtokenStatusDataEnum_UNACTIVATED,
  _$baseResponseEtokenStatusDataEnum_UNREGISTERED,
  _$baseResponseEtokenStatusDataEnum_ACTIVE_ON_ANOTHER_DEVICE,
]);

Serializer<BaseResponseEtokenStatusDataEnum>
    _$baseResponseEtokenStatusDataEnumSerializer =
    new _$BaseResponseEtokenStatusDataEnumSerializer();

class _$BaseResponseEtokenStatusDataEnumSerializer
    implements PrimitiveSerializer<BaseResponseEtokenStatusDataEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'ACTIVATED': 'ACTIVATED',
    'UNACTIVATED': 'UNACTIVATED',
    'UNREGISTERED': 'UNREGISTERED',
    'ACTIVE_ON_ANOTHER_DEVICE': 'ACTIVE_ON_ANOTHER_DEVICE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'ACTIVATED': 'ACTIVATED',
    'UNACTIVATED': 'UNACTIVATED',
    'UNREGISTERED': 'UNREGISTERED',
    'ACTIVE_ON_ANOTHER_DEVICE': 'ACTIVE_ON_ANOTHER_DEVICE',
  };

  @override
  final Iterable<Type> types = const <Type>[BaseResponseEtokenStatusDataEnum];
  @override
  final String wireName = 'BaseResponseEtokenStatusDataEnum';

  @override
  Object serialize(
          Serializers serializers, BaseResponseEtokenStatusDataEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  BaseResponseEtokenStatusDataEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      BaseResponseEtokenStatusDataEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$BaseResponseEtokenStatus extends BaseResponseEtokenStatus {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final BaseResponseEtokenStatusDataEnum? data;
  @override
  final String? message;

  factory _$BaseResponseEtokenStatus(
          [void Function(BaseResponseEtokenStatusBuilder)? updates]) =>
      (new BaseResponseEtokenStatusBuilder()..update(updates))._build();

  _$BaseResponseEtokenStatus._(
      {this.success, this.code, this.data, this.message})
      : super._();

  @override
  BaseResponseEtokenStatus rebuild(
          void Function(BaseResponseEtokenStatusBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseEtokenStatusBuilder toBuilder() =>
      new BaseResponseEtokenStatusBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseEtokenStatus &&
        success == other.success &&
        code == other.code &&
        data == other.data &&
        message == other.message;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseEtokenStatus')
          ..add('success', success)
          ..add('code', code)
          ..add('data', data)
          ..add('message', message))
        .toString();
  }
}

class BaseResponseEtokenStatusBuilder
    implements
        Builder<BaseResponseEtokenStatus, BaseResponseEtokenStatusBuilder> {
  _$BaseResponseEtokenStatus? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  BaseResponseEtokenStatusDataEnum? _data;
  BaseResponseEtokenStatusDataEnum? get data => _$this._data;
  set data(BaseResponseEtokenStatusDataEnum? data) => _$this._data = data;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  BaseResponseEtokenStatusBuilder() {
    BaseResponseEtokenStatus._defaults(this);
  }

  BaseResponseEtokenStatusBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _data = $v.data;
      _message = $v.message;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseEtokenStatus other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$BaseResponseEtokenStatus;
  }

  @override
  void update(void Function(BaseResponseEtokenStatusBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseEtokenStatus build() => _build();

  _$BaseResponseEtokenStatus _build() {
    final _$result = _$v ??
        new _$BaseResponseEtokenStatus._(
            success: success, code: code, data: data, message: message);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
