//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'verify_otp_and_get_user_info_request.g.dart';

/// VerifyOtpAndGetUserInfoRequest
///
/// Properties:
/// * [identityType]
/// * [identity]
/// * [otp]
@BuiltValue()
abstract class VerifyOtpAndGetUserInfoRequest
    implements
        Built<VerifyOtpAndGetUserInfoRequest,
            VerifyOtpAndGetUserInfoRequestBuilder> {
  @BuiltValueField(wireName: r'identityType')
  VerifyOtpAndGetUserInfoRequestIdentityTypeEnum? get identityType;
  // enum identityTypeEnum {  PHONE,  ID_CARD,  };

  @BuiltValueField(wireName: r'identity')
  String? get identity;

  @BuiltValueField(wireName: r'otp')
  String? get otp;

  VerifyOtpAndGetUserInfoRequest._();

  factory VerifyOtpAndGetUserInfoRequest(
          [void updates(VerifyOtpAndGetUserInfoRequestBuilder b)]) =
      _$VerifyOtpAndGetUserInfoRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(VerifyOtpAndGetUserInfoRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<VerifyOtpAndGetUserInfoRequest> get serializer =>
      _$VerifyOtpAndGetUserInfoRequestSerializer();
}

class _$VerifyOtpAndGetUserInfoRequestSerializer
    implements PrimitiveSerializer<VerifyOtpAndGetUserInfoRequest> {
  @override
  final Iterable<Type> types = const [
    VerifyOtpAndGetUserInfoRequest,
    _$VerifyOtpAndGetUserInfoRequest
  ];

  @override
  final String wireName = r'VerifyOtpAndGetUserInfoRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    VerifyOtpAndGetUserInfoRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.identityType != null) {
      yield r'identityType';
      yield serializers.serialize(
        object.identityType,
        specifiedType: const FullType.nullable(
            VerifyOtpAndGetUserInfoRequestIdentityTypeEnum),
      );
    }
    if (object.identity != null) {
      yield r'identity';
      yield serializers.serialize(
        object.identity,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.otp != null) {
      yield r'otp';
      yield serializers.serialize(
        object.otp,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    VerifyOtpAndGetUserInfoRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required VerifyOtpAndGetUserInfoRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'identityType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                VerifyOtpAndGetUserInfoRequestIdentityTypeEnum),
          ) as VerifyOtpAndGetUserInfoRequestIdentityTypeEnum?;
          if (valueDes == null) continue;
          result.identityType = valueDes;
          break;
        case r'identity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.identity = valueDes;
          break;
        case r'otp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.otp = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  VerifyOtpAndGetUserInfoRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = VerifyOtpAndGetUserInfoRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class VerifyOtpAndGetUserInfoRequestIdentityTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PHONE')
  static const VerifyOtpAndGetUserInfoRequestIdentityTypeEnum PHONE =
      _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_PHONE;
  @BuiltValueEnumConst(wireName: r'ID_CARD')
  static const VerifyOtpAndGetUserInfoRequestIdentityTypeEnum ID_CARD =
      _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_ID_CARD;

  static Serializer<VerifyOtpAndGetUserInfoRequestIdentityTypeEnum>
      get serializer =>
          _$verifyOtpAndGetUserInfoRequestIdentityTypeEnumSerializer;

  const VerifyOtpAndGetUserInfoRequestIdentityTypeEnum._(String name)
      : super(name);

  static BuiltSet<VerifyOtpAndGetUserInfoRequestIdentityTypeEnum> get values =>
      _$verifyOtpAndGetUserInfoRequestIdentityTypeEnumValues;
  static VerifyOtpAndGetUserInfoRequestIdentityTypeEnum valueOf(String name) =>
      _$verifyOtpAndGetUserInfoRequestIdentityTypeEnumValueOf(name);
}
