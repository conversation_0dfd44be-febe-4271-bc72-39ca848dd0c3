//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_theme_detail_response.g.dart';

/// GetThemeDetailResponse
///
/// Properties:
/// * [id] - id
/// * [name] - Tên theme
/// * [description] - mô tả
/// * [category] - Danh mục
/// * [customerRank] - <PERSON><PERSON><PERSON> khách hàng(bình thường, DIAMOND,...)
/// * [startTime] - Thời điểm bắt đầu
/// * [endTime] - Thời điểm kết thúc
/// * [thumbnailUrl] - Link tới file thumbnail
/// * [thumbnailSize] - Size thumbnail
/// * [thumbnailType] - Loại file
/// * [bannerUrl] - Link tới file banner
/// * [bannerSize] - Size banner
/// * [bannerType] - Loại file
/// * [introUrl] - Flash screen
/// * [introSize] - Flash screen size
/// * [introType] - Loại file
/// * [loginUrl] - Link tới file màn login
/// * [loginSize] - Size file màn login
/// * [loginType] - Loại file màn login
/// * [logoUrl] - Link tới file logo
/// * [logoSize] - Size file logo
/// * [logoType] - Loại file logo
/// * [logoRankUrl] - Logo phân hạng
/// * [logoRankSize] - Logo rank size
/// * [logoRankType] - Loại file
/// * [bannerRankUrl] - Banner phân hạng
/// * [bannerRankSize] - Banner rank size
/// * [bannerRankType] - Loại file
/// * [logoShortUrl] - Logo ngắn không có chữ
/// * [logoShortSize] - Logo short size
/// * [logoShortType] - Loại file
/// * [logoLoginUrl] - Logo ở màn hình login
/// * [logoLoginSize] - Logo login size
/// * [logoLoginType] - Loại file
/// * [faceIdUrl] - Link tới file ảnh của button faceId
/// * [faceIdSize] - Size file ảnh faceId
/// * [faceIdType] - Loại file ảnh faceId
/// * [backgroundColor] - Mã màu background
/// * [textColor] - Mã màu text
/// * [iconColorLogin] - Mã màu icon và text trong màn hình Login
/// * [iconColorMain] - Mã màu icon và text trong màn hình chính
/// * [brightLogo] - Sử dụng logo sáng màu hay không?
/// * [active] - Đang được sử dụng hay không?
/// * [defaultTheme] - Có phải theme mặc định hay không?
/// * [deleted] - Đã xóa/ngưng hoạt động hay chưa?
/// * [lastModifiedBy] - Cập nhật gần nhất bởi
/// * [lastModifiedDate] - Thời gian cập nhật gần nhất
/// * [createdBy] - Được tạo bởi
/// * [createdDate] - Thời gian tạo
@BuiltValue()
abstract class GetThemeDetailResponse
    implements Built<GetThemeDetailResponse, GetThemeDetailResponseBuilder> {
  /// id
  @BuiltValueField(wireName: r'id')
  String? get id;

  /// Tên theme
  @BuiltValueField(wireName: r'name')
  String? get name;

  /// mô tả
  @BuiltValueField(wireName: r'description')
  String? get description;

  /// Danh mục
  @BuiltValueField(wireName: r'category')
  String? get category;

  /// Loại khách hàng(bình thường, DIAMOND,...)
  @BuiltValueField(wireName: r'customerRank')
  GetThemeDetailResponseCustomerRankEnum? get customerRank;
  // enum customerRankEnum {  NORMAL,  DIAMOND,  RUBY,  SAPPHIRE,  DIAMOND_ELITE,  };

  /// Thời điểm bắt đầu
  @BuiltValueField(wireName: r'startTime')
  DateTime? get startTime;

  /// Thời điểm kết thúc
  @BuiltValueField(wireName: r'endTime')
  DateTime? get endTime;

  /// Link tới file thumbnail
  @BuiltValueField(wireName: r'thumbnailUrl')
  String? get thumbnailUrl;

  /// Size thumbnail
  @BuiltValueField(wireName: r'thumbnailSize')
  String? get thumbnailSize;

  /// Loại file
  @BuiltValueField(wireName: r'thumbnailType')
  String? get thumbnailType;

  /// Link tới file banner
  @BuiltValueField(wireName: r'bannerUrl')
  String? get bannerUrl;

  /// Size banner
  @BuiltValueField(wireName: r'bannerSize')
  String? get bannerSize;

  /// Loại file
  @BuiltValueField(wireName: r'bannerType')
  String? get bannerType;

  /// Flash screen
  @BuiltValueField(wireName: r'introUrl')
  String? get introUrl;

  /// Flash screen size
  @BuiltValueField(wireName: r'introSize')
  String? get introSize;

  /// Loại file
  @BuiltValueField(wireName: r'introType')
  String? get introType;

  /// Link tới file màn login
  @BuiltValueField(wireName: r'loginUrl')
  String? get loginUrl;

  /// Size file màn login
  @BuiltValueField(wireName: r'loginSize')
  String? get loginSize;

  /// Loại file màn login
  @BuiltValueField(wireName: r'loginType')
  String? get loginType;

  /// Link tới file logo
  @BuiltValueField(wireName: r'logoUrl')
  String? get logoUrl;

  /// Size file logo
  @BuiltValueField(wireName: r'logoSize')
  String? get logoSize;

  /// Loại file logo
  @BuiltValueField(wireName: r'logoType')
  String? get logoType;

  /// Logo phân hạng
  @BuiltValueField(wireName: r'logoRankUrl')
  String? get logoRankUrl;

  /// Logo rank size
  @BuiltValueField(wireName: r'logoRankSize')
  String? get logoRankSize;

  /// Loại file
  @BuiltValueField(wireName: r'logoRankType')
  String? get logoRankType;

  /// Banner phân hạng
  @BuiltValueField(wireName: r'bannerRankUrl')
  String? get bannerRankUrl;

  /// Banner rank size
  @BuiltValueField(wireName: r'bannerRankSize')
  String? get bannerRankSize;

  /// Loại file
  @BuiltValueField(wireName: r'bannerRankType')
  String? get bannerRankType;

  /// Logo ngắn không có chữ
  @BuiltValueField(wireName: r'logoShortUrl')
  String? get logoShortUrl;

  /// Logo short size
  @BuiltValueField(wireName: r'logoShortSize')
  String? get logoShortSize;

  /// Loại file
  @BuiltValueField(wireName: r'logoShortType')
  String? get logoShortType;

  /// Logo ở màn hình login
  @BuiltValueField(wireName: r'logoLoginUrl')
  String? get logoLoginUrl;

  /// Logo login size
  @BuiltValueField(wireName: r'logoLoginSize')
  String? get logoLoginSize;

  /// Loại file
  @BuiltValueField(wireName: r'logoLoginType')
  String? get logoLoginType;

  /// Link tới file ảnh của button faceId
  @BuiltValueField(wireName: r'faceIdUrl')
  String? get faceIdUrl;

  /// Size file ảnh faceId
  @BuiltValueField(wireName: r'faceIdSize')
  String? get faceIdSize;

  /// Loại file ảnh faceId
  @BuiltValueField(wireName: r'faceIdType')
  String? get faceIdType;

  /// Mã màu background
  @BuiltValueField(wireName: r'backgroundColor')
  String? get backgroundColor;

  /// Mã màu text
  @BuiltValueField(wireName: r'textColor')
  String? get textColor;

  /// Mã màu icon và text trong màn hình Login
  @BuiltValueField(wireName: r'iconColorLogin')
  String? get iconColorLogin;

  /// Mã màu icon và text trong màn hình chính
  @BuiltValueField(wireName: r'iconColorMain')
  String? get iconColorMain;

  /// Sử dụng logo sáng màu hay không?
  @BuiltValueField(wireName: r'brightLogo')
  bool? get brightLogo;

  /// Đang được sử dụng hay không?
  @BuiltValueField(wireName: r'active')
  bool? get active;

  /// Có phải theme mặc định hay không?
  @BuiltValueField(wireName: r'defaultTheme')
  bool? get defaultTheme;

  /// Đã xóa/ngưng hoạt động hay chưa?
  @BuiltValueField(wireName: r'deleted')
  bool? get deleted;

  /// Cập nhật gần nhất bởi
  @BuiltValueField(wireName: r'lastModifiedBy')
  String? get lastModifiedBy;

  /// Thời gian cập nhật gần nhất
  @BuiltValueField(wireName: r'lastModifiedDate')
  DateTime? get lastModifiedDate;

  /// Được tạo bởi
  @BuiltValueField(wireName: r'createdBy')
  String? get createdBy;

  /// Thời gian tạo
  @BuiltValueField(wireName: r'createdDate')
  DateTime? get createdDate;

  GetThemeDetailResponse._();

  factory GetThemeDetailResponse(
          [void updates(GetThemeDetailResponseBuilder b)]) =
      _$GetThemeDetailResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetThemeDetailResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetThemeDetailResponse> get serializer =>
      _$GetThemeDetailResponseSerializer();
}

class _$GetThemeDetailResponseSerializer
    implements PrimitiveSerializer<GetThemeDetailResponse> {
  @override
  final Iterable<Type> types = const [
    GetThemeDetailResponse,
    _$GetThemeDetailResponse
  ];

  @override
  final String wireName = r'GetThemeDetailResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetThemeDetailResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.category != null) {
      yield r'category';
      yield serializers.serialize(
        object.category,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.customerRank != null) {
      yield r'customerRank';
      yield serializers.serialize(
        object.customerRank,
        specifiedType:
            const FullType.nullable(GetThemeDetailResponseCustomerRankEnum),
      );
    }
    if (object.startTime != null) {
      yield r'startTime';
      yield serializers.serialize(
        object.startTime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.endTime != null) {
      yield r'endTime';
      yield serializers.serialize(
        object.endTime,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.thumbnailUrl != null) {
      yield r'thumbnailUrl';
      yield serializers.serialize(
        object.thumbnailUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.thumbnailSize != null) {
      yield r'thumbnailSize';
      yield serializers.serialize(
        object.thumbnailSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.thumbnailType != null) {
      yield r'thumbnailType';
      yield serializers.serialize(
        object.thumbnailType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.bannerUrl != null) {
      yield r'bannerUrl';
      yield serializers.serialize(
        object.bannerUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.bannerSize != null) {
      yield r'bannerSize';
      yield serializers.serialize(
        object.bannerSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.bannerType != null) {
      yield r'bannerType';
      yield serializers.serialize(
        object.bannerType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.introUrl != null) {
      yield r'introUrl';
      yield serializers.serialize(
        object.introUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.introSize != null) {
      yield r'introSize';
      yield serializers.serialize(
        object.introSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.introType != null) {
      yield r'introType';
      yield serializers.serialize(
        object.introType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.loginUrl != null) {
      yield r'loginUrl';
      yield serializers.serialize(
        object.loginUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.loginSize != null) {
      yield r'loginSize';
      yield serializers.serialize(
        object.loginSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.loginType != null) {
      yield r'loginType';
      yield serializers.serialize(
        object.loginType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoUrl != null) {
      yield r'logoUrl';
      yield serializers.serialize(
        object.logoUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoSize != null) {
      yield r'logoSize';
      yield serializers.serialize(
        object.logoSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoType != null) {
      yield r'logoType';
      yield serializers.serialize(
        object.logoType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoRankUrl != null) {
      yield r'logoRankUrl';
      yield serializers.serialize(
        object.logoRankUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoRankSize != null) {
      yield r'logoRankSize';
      yield serializers.serialize(
        object.logoRankSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoRankType != null) {
      yield r'logoRankType';
      yield serializers.serialize(
        object.logoRankType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.bannerRankUrl != null) {
      yield r'bannerRankUrl';
      yield serializers.serialize(
        object.bannerRankUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.bannerRankSize != null) {
      yield r'bannerRankSize';
      yield serializers.serialize(
        object.bannerRankSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.bannerRankType != null) {
      yield r'bannerRankType';
      yield serializers.serialize(
        object.bannerRankType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoShortUrl != null) {
      yield r'logoShortUrl';
      yield serializers.serialize(
        object.logoShortUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoShortSize != null) {
      yield r'logoShortSize';
      yield serializers.serialize(
        object.logoShortSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoShortType != null) {
      yield r'logoShortType';
      yield serializers.serialize(
        object.logoShortType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoLoginUrl != null) {
      yield r'logoLoginUrl';
      yield serializers.serialize(
        object.logoLoginUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoLoginSize != null) {
      yield r'logoLoginSize';
      yield serializers.serialize(
        object.logoLoginSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.logoLoginType != null) {
      yield r'logoLoginType';
      yield serializers.serialize(
        object.logoLoginType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.faceIdUrl != null) {
      yield r'faceIdUrl';
      yield serializers.serialize(
        object.faceIdUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.faceIdSize != null) {
      yield r'faceIdSize';
      yield serializers.serialize(
        object.faceIdSize,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.faceIdType != null) {
      yield r'faceIdType';
      yield serializers.serialize(
        object.faceIdType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.backgroundColor != null) {
      yield r'backgroundColor';
      yield serializers.serialize(
        object.backgroundColor,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.textColor != null) {
      yield r'textColor';
      yield serializers.serialize(
        object.textColor,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.iconColorLogin != null) {
      yield r'iconColorLogin';
      yield serializers.serialize(
        object.iconColorLogin,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.iconColorMain != null) {
      yield r'iconColorMain';
      yield serializers.serialize(
        object.iconColorMain,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.brightLogo != null) {
      yield r'brightLogo';
      yield serializers.serialize(
        object.brightLogo,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.active != null) {
      yield r'active';
      yield serializers.serialize(
        object.active,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.defaultTheme != null) {
      yield r'defaultTheme';
      yield serializers.serialize(
        object.defaultTheme,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.deleted != null) {
      yield r'deleted';
      yield serializers.serialize(
        object.deleted,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.lastModifiedBy != null) {
      yield r'lastModifiedBy';
      yield serializers.serialize(
        object.lastModifiedBy,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lastModifiedDate != null) {
      yield r'lastModifiedDate';
      yield serializers.serialize(
        object.lastModifiedDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.createdBy != null) {
      yield r'createdBy';
      yield serializers.serialize(
        object.createdBy,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.createdDate != null) {
      yield r'createdDate';
      yield serializers.serialize(
        object.createdDate,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GetThemeDetailResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetThemeDetailResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.description = valueDes;
          break;
        case r'category':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.category = valueDes;
          break;
        case r'customerRank':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(GetThemeDetailResponseCustomerRankEnum),
          ) as GetThemeDetailResponseCustomerRankEnum?;
          if (valueDes == null) continue;
          result.customerRank = valueDes;
          break;
        case r'startTime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.startTime = valueDes;
          break;
        case r'endTime':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.endTime = valueDes;
          break;
        case r'thumbnailUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.thumbnailUrl = valueDes;
          break;
        case r'thumbnailSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.thumbnailSize = valueDes;
          break;
        case r'thumbnailType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.thumbnailType = valueDes;
          break;
        case r'bannerUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bannerUrl = valueDes;
          break;
        case r'bannerSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bannerSize = valueDes;
          break;
        case r'bannerType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bannerType = valueDes;
          break;
        case r'introUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.introUrl = valueDes;
          break;
        case r'introSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.introSize = valueDes;
          break;
        case r'introType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.introType = valueDes;
          break;
        case r'loginUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.loginUrl = valueDes;
          break;
        case r'loginSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.loginSize = valueDes;
          break;
        case r'loginType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.loginType = valueDes;
          break;
        case r'logoUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoUrl = valueDes;
          break;
        case r'logoSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoSize = valueDes;
          break;
        case r'logoType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoType = valueDes;
          break;
        case r'logoRankUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoRankUrl = valueDes;
          break;
        case r'logoRankSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoRankSize = valueDes;
          break;
        case r'logoRankType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoRankType = valueDes;
          break;
        case r'bannerRankUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bannerRankUrl = valueDes;
          break;
        case r'bannerRankSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bannerRankSize = valueDes;
          break;
        case r'bannerRankType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.bannerRankType = valueDes;
          break;
        case r'logoShortUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoShortUrl = valueDes;
          break;
        case r'logoShortSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoShortSize = valueDes;
          break;
        case r'logoShortType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoShortType = valueDes;
          break;
        case r'logoLoginUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoLoginUrl = valueDes;
          break;
        case r'logoLoginSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoLoginSize = valueDes;
          break;
        case r'logoLoginType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.logoLoginType = valueDes;
          break;
        case r'faceIdUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.faceIdUrl = valueDes;
          break;
        case r'faceIdSize':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.faceIdSize = valueDes;
          break;
        case r'faceIdType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.faceIdType = valueDes;
          break;
        case r'backgroundColor':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.backgroundColor = valueDes;
          break;
        case r'textColor':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.textColor = valueDes;
          break;
        case r'iconColorLogin':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.iconColorLogin = valueDes;
          break;
        case r'iconColorMain':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.iconColorMain = valueDes;
          break;
        case r'brightLogo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.brightLogo = valueDes;
          break;
        case r'active':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.active = valueDes;
          break;
        case r'defaultTheme':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.defaultTheme = valueDes;
          break;
        case r'deleted':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.deleted = valueDes;
          break;
        case r'lastModifiedBy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastModifiedBy = valueDes;
          break;
        case r'lastModifiedDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.lastModifiedDate = valueDes;
          break;
        case r'createdBy':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.createdBy = valueDes;
          break;
        case r'createdDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.createdDate = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetThemeDetailResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetThemeDetailResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class GetThemeDetailResponseCustomerRankEnum extends EnumClass {
  /// Loại khách hàng(bình thường, DIAMOND,...)
  @BuiltValueEnumConst(wireName: r'NORMAL')
  static const GetThemeDetailResponseCustomerRankEnum NORMAL =
      _$getThemeDetailResponseCustomerRankEnum_NORMAL;

  /// Loại khách hàng(bình thường, DIAMOND,...)
  @BuiltValueEnumConst(wireName: r'DIAMOND')
  static const GetThemeDetailResponseCustomerRankEnum DIAMOND =
      _$getThemeDetailResponseCustomerRankEnum_DIAMOND;

  /// Loại khách hàng(bình thường, DIAMOND,...)
  @BuiltValueEnumConst(wireName: r'RUBY')
  static const GetThemeDetailResponseCustomerRankEnum RUBY =
      _$getThemeDetailResponseCustomerRankEnum_RUBY;

  /// Loại khách hàng(bình thường, DIAMOND,...)
  @BuiltValueEnumConst(wireName: r'SAPPHIRE')
  static const GetThemeDetailResponseCustomerRankEnum SAPPHIRE =
      _$getThemeDetailResponseCustomerRankEnum_SAPPHIRE;

  /// Loại khách hàng(bình thường, DIAMOND,...)
  @BuiltValueEnumConst(wireName: r'DIAMOND_ELITE')
  static const GetThemeDetailResponseCustomerRankEnum DIAMOND_ELITE =
      _$getThemeDetailResponseCustomerRankEnum_DIAMOND_ELITE;

  static Serializer<GetThemeDetailResponseCustomerRankEnum> get serializer =>
      _$getThemeDetailResponseCustomerRankEnumSerializer;

  const GetThemeDetailResponseCustomerRankEnum._(String name) : super(name);

  static BuiltSet<GetThemeDetailResponseCustomerRankEnum> get values =>
      _$getThemeDetailResponseCustomerRankEnumValues;
  static GetThemeDetailResponseCustomerRankEnum valueOf(String name) =>
      _$getThemeDetailResponseCustomerRankEnumValueOf(name);
}
