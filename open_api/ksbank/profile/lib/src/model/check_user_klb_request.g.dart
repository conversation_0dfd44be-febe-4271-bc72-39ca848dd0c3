// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_user_klb_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const CheckUserKlbRequestIdentityTypeEnum
    _$checkUserKlbRequestIdentityTypeEnum_PHONE =
    const CheckUserKlbRequestIdentityTypeEnum._('PHONE');
const CheckUserKlbRequestIdentityTypeEnum
    _$checkUserKlbRequestIdentityTypeEnum_ID_CARD =
    const CheckUserKlbRequestIdentityTypeEnum._('ID_CARD');

CheckUserKlbRequestIdentityTypeEnum
    _$checkUserKlbRequestIdentityTypeEnumValueOf(String name) {
  switch (name) {
    case 'PHONE':
      return _$checkUserKlbRequestIdentityTypeEnum_PHONE;
    case 'ID_CARD':
      return _$checkUserKlbRequestIdentityTypeEnum_ID_CARD;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<CheckUserKlbRequestIdentityTypeEnum>
    _$checkUserKlbRequestIdentityTypeEnumValues = new BuiltSet<
        CheckUserKlbRequestIdentityTypeEnum>(const <CheckUserKlbRequestIdentityTypeEnum>[
  _$checkUserKlbRequestIdentityTypeEnum_PHONE,
  _$checkUserKlbRequestIdentityTypeEnum_ID_CARD,
]);

Serializer<CheckUserKlbRequestIdentityTypeEnum>
    _$checkUserKlbRequestIdentityTypeEnumSerializer =
    new _$CheckUserKlbRequestIdentityTypeEnumSerializer();

class _$CheckUserKlbRequestIdentityTypeEnumSerializer
    implements PrimitiveSerializer<CheckUserKlbRequestIdentityTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PHONE': 'PHONE',
    'ID_CARD': 'ID_CARD',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PHONE': 'PHONE',
    'ID_CARD': 'ID_CARD',
  };

  @override
  final Iterable<Type> types = const <Type>[
    CheckUserKlbRequestIdentityTypeEnum
  ];
  @override
  final String wireName = 'CheckUserKlbRequestIdentityTypeEnum';

  @override
  Object serialize(
          Serializers serializers, CheckUserKlbRequestIdentityTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  CheckUserKlbRequestIdentityTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      CheckUserKlbRequestIdentityTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$CheckUserKlbRequest extends CheckUserKlbRequest {
  @override
  final CheckUserKlbRequestIdentityTypeEnum? identityType;
  @override
  final String? identity;

  factory _$CheckUserKlbRequest(
          [void Function(CheckUserKlbRequestBuilder)? updates]) =>
      (new CheckUserKlbRequestBuilder()..update(updates))._build();

  _$CheckUserKlbRequest._({this.identityType, this.identity}) : super._();

  @override
  CheckUserKlbRequest rebuild(
          void Function(CheckUserKlbRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CheckUserKlbRequestBuilder toBuilder() =>
      new CheckUserKlbRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CheckUserKlbRequest &&
        identityType == other.identityType &&
        identity == other.identity;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, identityType.hashCode);
    _$hash = $jc(_$hash, identity.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CheckUserKlbRequest')
          ..add('identityType', identityType)
          ..add('identity', identity))
        .toString();
  }
}

class CheckUserKlbRequestBuilder
    implements Builder<CheckUserKlbRequest, CheckUserKlbRequestBuilder> {
  _$CheckUserKlbRequest? _$v;

  CheckUserKlbRequestIdentityTypeEnum? _identityType;
  CheckUserKlbRequestIdentityTypeEnum? get identityType => _$this._identityType;
  set identityType(CheckUserKlbRequestIdentityTypeEnum? identityType) =>
      _$this._identityType = identityType;

  String? _identity;
  String? get identity => _$this._identity;
  set identity(String? identity) => _$this._identity = identity;

  CheckUserKlbRequestBuilder() {
    CheckUserKlbRequest._defaults(this);
  }

  CheckUserKlbRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _identityType = $v.identityType;
      _identity = $v.identity;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CheckUserKlbRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CheckUserKlbRequest;
  }

  @override
  void update(void Function(CheckUserKlbRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CheckUserKlbRequest build() => _build();

  _$CheckUserKlbRequest _build() {
    final _$result = _$v ??
        new _$CheckUserKlbRequest._(
            identityType: identityType, identity: identity);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
