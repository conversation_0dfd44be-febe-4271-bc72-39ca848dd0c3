// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'query_info_response_v2.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const QueryInfoResponseV2OrganizationEnum
    _$queryInfoResponseV2OrganizationEnum_VNPOST =
    const QueryInfoResponseV2OrganizationEnum._('VNPOST');
const QueryInfoResponseV2OrganizationEnum
    _$queryInfoResponseV2OrganizationEnum_KL =
    const QueryInfoResponseV2OrganizationEnum._('KL');
const QueryInfoResponseV2OrganizationEnum
    _$queryInfoResponseV2OrganizationEnum_OTHER =
    const QueryInfoResponseV2OrganizationEnum._('OTHER');

QueryInfoResponseV2OrganizationEnum
    _$queryInfoResponseV2OrganizationEnumValueOf(String name) {
  switch (name) {
    case 'VNPOST':
      return _$queryInfoResponseV2OrganizationEnum_VNPOST;
    case 'KL':
      return _$queryInfoResponseV2OrganizationEnum_KL;
    case 'OTHER':
      return _$queryInfoResponseV2OrganizationEnum_OTHER;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<QueryInfoResponseV2OrganizationEnum>
    _$queryInfoResponseV2OrganizationEnumValues = new BuiltSet<
        QueryInfoResponseV2OrganizationEnum>(const <QueryInfoResponseV2OrganizationEnum>[
  _$queryInfoResponseV2OrganizationEnum_VNPOST,
  _$queryInfoResponseV2OrganizationEnum_KL,
  _$queryInfoResponseV2OrganizationEnum_OTHER,
]);

Serializer<QueryInfoResponseV2OrganizationEnum>
    _$queryInfoResponseV2OrganizationEnumSerializer =
    new _$QueryInfoResponseV2OrganizationEnumSerializer();

class _$QueryInfoResponseV2OrganizationEnumSerializer
    implements PrimitiveSerializer<QueryInfoResponseV2OrganizationEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'VNPOST': 'VNPOST',
    'KL': 'KL',
    'OTHER': 'OTHER',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'VNPOST': 'VNPOST',
    'KL': 'KL',
    'OTHER': 'OTHER',
  };

  @override
  final Iterable<Type> types = const <Type>[
    QueryInfoResponseV2OrganizationEnum
  ];
  @override
  final String wireName = 'QueryInfoResponseV2OrganizationEnum';

  @override
  Object serialize(
          Serializers serializers, QueryInfoResponseV2OrganizationEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  QueryInfoResponseV2OrganizationEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      QueryInfoResponseV2OrganizationEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$QueryInfoResponseV2 extends QueryInfoResponseV2 {
  @override
  final int? loginType;
  @override
  final String? fullName;
  @override
  final String? avatarUrl;
  @override
  final String? phoneNumber;
  @override
  final String? otpMethod;
  @override
  final String? email;
  @override
  final bool? verifiedEmail;
  @override
  final String? aliasName;
  @override
  final String? username;
  @override
  final bool? facebookLinked;
  @override
  final String? facebookId;
  @override
  final String? facebookName;
  @override
  final bool? googleLinked;
  @override
  final String? googleId;
  @override
  final String? googleName;
  @override
  final String? sex;
  @override
  final Date? birthday;
  @override
  final String? idCardType;
  @override
  final String? idCardNo;
  @override
  final Date? idCardIssueDate;
  @override
  final Date? idCardExpireDate;
  @override
  final String? idCardIssuePlace;
  @override
  final String? resAddr;
  @override
  final String? resCity;
  @override
  final String? nationCode;
  @override
  final String? taxCode;
  @override
  final String? provinceCode;
  @override
  final String? districtCode;
  @override
  final String? wardVlg;
  @override
  final String? street;
  @override
  final String? no123;
  @override
  final String? branchCode;
  @override
  final String? fullAddress;
  @override
  final bool? enabled2fa;
  @override
  final bool? bankVerified;
  @override
  final String? createdFrom;
  @override
  final bool? lockedOldApp;
  @override
  final String? ekycId;
  @override
  final String? suspiciousEkycStatus;
  @override
  final String? customerGroupUserId;
  @override
  final QueryInfoResponseV2OrganizationEnum? organization;
  @override
  final String? identityStatus;
  @override
  final bool? isVerifiedSTH;
  @override
  final bool? isNobleEmployee;
  @override
  final bool? passEkyc;

  factory _$QueryInfoResponseV2(
          [void Function(QueryInfoResponseV2Builder)? updates]) =>
      (new QueryInfoResponseV2Builder()..update(updates))._build();

  _$QueryInfoResponseV2._(
      {this.loginType,
      this.fullName,
      this.avatarUrl,
      this.phoneNumber,
      this.otpMethod,
      this.email,
      this.verifiedEmail,
      this.aliasName,
      this.username,
      this.facebookLinked,
      this.facebookId,
      this.facebookName,
      this.googleLinked,
      this.googleId,
      this.googleName,
      this.sex,
      this.birthday,
      this.idCardType,
      this.idCardNo,
      this.idCardIssueDate,
      this.idCardExpireDate,
      this.idCardIssuePlace,
      this.resAddr,
      this.resCity,
      this.nationCode,
      this.taxCode,
      this.provinceCode,
      this.districtCode,
      this.wardVlg,
      this.street,
      this.no123,
      this.branchCode,
      this.fullAddress,
      this.enabled2fa,
      this.bankVerified,
      this.createdFrom,
      this.lockedOldApp,
      this.ekycId,
      this.suspiciousEkycStatus,
      this.customerGroupUserId,
      this.organization,
      this.identityStatus,
      this.isVerifiedSTH,
      this.isNobleEmployee,
      this.passEkyc})
      : super._();

  @override
  QueryInfoResponseV2 rebuild(
          void Function(QueryInfoResponseV2Builder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  QueryInfoResponseV2Builder toBuilder() =>
      new QueryInfoResponseV2Builder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is QueryInfoResponseV2 &&
        loginType == other.loginType &&
        fullName == other.fullName &&
        avatarUrl == other.avatarUrl &&
        phoneNumber == other.phoneNumber &&
        otpMethod == other.otpMethod &&
        email == other.email &&
        verifiedEmail == other.verifiedEmail &&
        aliasName == other.aliasName &&
        username == other.username &&
        facebookLinked == other.facebookLinked &&
        facebookId == other.facebookId &&
        facebookName == other.facebookName &&
        googleLinked == other.googleLinked &&
        googleId == other.googleId &&
        googleName == other.googleName &&
        sex == other.sex &&
        birthday == other.birthday &&
        idCardType == other.idCardType &&
        idCardNo == other.idCardNo &&
        idCardIssueDate == other.idCardIssueDate &&
        idCardExpireDate == other.idCardExpireDate &&
        idCardIssuePlace == other.idCardIssuePlace &&
        resAddr == other.resAddr &&
        resCity == other.resCity &&
        nationCode == other.nationCode &&
        taxCode == other.taxCode &&
        provinceCode == other.provinceCode &&
        districtCode == other.districtCode &&
        wardVlg == other.wardVlg &&
        street == other.street &&
        no123 == other.no123 &&
        branchCode == other.branchCode &&
        fullAddress == other.fullAddress &&
        enabled2fa == other.enabled2fa &&
        bankVerified == other.bankVerified &&
        createdFrom == other.createdFrom &&
        lockedOldApp == other.lockedOldApp &&
        ekycId == other.ekycId &&
        suspiciousEkycStatus == other.suspiciousEkycStatus &&
        customerGroupUserId == other.customerGroupUserId &&
        organization == other.organization &&
        identityStatus == other.identityStatus &&
        isVerifiedSTH == other.isVerifiedSTH &&
        isNobleEmployee == other.isNobleEmployee &&
        passEkyc == other.passEkyc;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, loginType.hashCode);
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, avatarUrl.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, otpMethod.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, verifiedEmail.hashCode);
    _$hash = $jc(_$hash, aliasName.hashCode);
    _$hash = $jc(_$hash, username.hashCode);
    _$hash = $jc(_$hash, facebookLinked.hashCode);
    _$hash = $jc(_$hash, facebookId.hashCode);
    _$hash = $jc(_$hash, facebookName.hashCode);
    _$hash = $jc(_$hash, googleLinked.hashCode);
    _$hash = $jc(_$hash, googleId.hashCode);
    _$hash = $jc(_$hash, googleName.hashCode);
    _$hash = $jc(_$hash, sex.hashCode);
    _$hash = $jc(_$hash, birthday.hashCode);
    _$hash = $jc(_$hash, idCardType.hashCode);
    _$hash = $jc(_$hash, idCardNo.hashCode);
    _$hash = $jc(_$hash, idCardIssueDate.hashCode);
    _$hash = $jc(_$hash, idCardExpireDate.hashCode);
    _$hash = $jc(_$hash, idCardIssuePlace.hashCode);
    _$hash = $jc(_$hash, resAddr.hashCode);
    _$hash = $jc(_$hash, resCity.hashCode);
    _$hash = $jc(_$hash, nationCode.hashCode);
    _$hash = $jc(_$hash, taxCode.hashCode);
    _$hash = $jc(_$hash, provinceCode.hashCode);
    _$hash = $jc(_$hash, districtCode.hashCode);
    _$hash = $jc(_$hash, wardVlg.hashCode);
    _$hash = $jc(_$hash, street.hashCode);
    _$hash = $jc(_$hash, no123.hashCode);
    _$hash = $jc(_$hash, branchCode.hashCode);
    _$hash = $jc(_$hash, fullAddress.hashCode);
    _$hash = $jc(_$hash, enabled2fa.hashCode);
    _$hash = $jc(_$hash, bankVerified.hashCode);
    _$hash = $jc(_$hash, createdFrom.hashCode);
    _$hash = $jc(_$hash, lockedOldApp.hashCode);
    _$hash = $jc(_$hash, ekycId.hashCode);
    _$hash = $jc(_$hash, suspiciousEkycStatus.hashCode);
    _$hash = $jc(_$hash, customerGroupUserId.hashCode);
    _$hash = $jc(_$hash, organization.hashCode);
    _$hash = $jc(_$hash, identityStatus.hashCode);
    _$hash = $jc(_$hash, isVerifiedSTH.hashCode);
    _$hash = $jc(_$hash, isNobleEmployee.hashCode);
    _$hash = $jc(_$hash, passEkyc.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'QueryInfoResponseV2')
          ..add('loginType', loginType)
          ..add('fullName', fullName)
          ..add('avatarUrl', avatarUrl)
          ..add('phoneNumber', phoneNumber)
          ..add('otpMethod', otpMethod)
          ..add('email', email)
          ..add('verifiedEmail', verifiedEmail)
          ..add('aliasName', aliasName)
          ..add('username', username)
          ..add('facebookLinked', facebookLinked)
          ..add('facebookId', facebookId)
          ..add('facebookName', facebookName)
          ..add('googleLinked', googleLinked)
          ..add('googleId', googleId)
          ..add('googleName', googleName)
          ..add('sex', sex)
          ..add('birthday', birthday)
          ..add('idCardType', idCardType)
          ..add('idCardNo', idCardNo)
          ..add('idCardIssueDate', idCardIssueDate)
          ..add('idCardExpireDate', idCardExpireDate)
          ..add('idCardIssuePlace', idCardIssuePlace)
          ..add('resAddr', resAddr)
          ..add('resCity', resCity)
          ..add('nationCode', nationCode)
          ..add('taxCode', taxCode)
          ..add('provinceCode', provinceCode)
          ..add('districtCode', districtCode)
          ..add('wardVlg', wardVlg)
          ..add('street', street)
          ..add('no123', no123)
          ..add('branchCode', branchCode)
          ..add('fullAddress', fullAddress)
          ..add('enabled2fa', enabled2fa)
          ..add('bankVerified', bankVerified)
          ..add('createdFrom', createdFrom)
          ..add('lockedOldApp', lockedOldApp)
          ..add('ekycId', ekycId)
          ..add('suspiciousEkycStatus', suspiciousEkycStatus)
          ..add('customerGroupUserId', customerGroupUserId)
          ..add('organization', organization)
          ..add('identityStatus', identityStatus)
          ..add('isVerifiedSTH', isVerifiedSTH)
          ..add('isNobleEmployee', isNobleEmployee)
          ..add('passEkyc', passEkyc))
        .toString();
  }
}

class QueryInfoResponseV2Builder
    implements Builder<QueryInfoResponseV2, QueryInfoResponseV2Builder> {
  _$QueryInfoResponseV2? _$v;

  int? _loginType;
  int? get loginType => _$this._loginType;
  set loginType(int? loginType) => _$this._loginType = loginType;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _avatarUrl;
  String? get avatarUrl => _$this._avatarUrl;
  set avatarUrl(String? avatarUrl) => _$this._avatarUrl = avatarUrl;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _otpMethod;
  String? get otpMethod => _$this._otpMethod;
  set otpMethod(String? otpMethod) => _$this._otpMethod = otpMethod;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  bool? _verifiedEmail;
  bool? get verifiedEmail => _$this._verifiedEmail;
  set verifiedEmail(bool? verifiedEmail) =>
      _$this._verifiedEmail = verifiedEmail;

  String? _aliasName;
  String? get aliasName => _$this._aliasName;
  set aliasName(String? aliasName) => _$this._aliasName = aliasName;

  String? _username;
  String? get username => _$this._username;
  set username(String? username) => _$this._username = username;

  bool? _facebookLinked;
  bool? get facebookLinked => _$this._facebookLinked;
  set facebookLinked(bool? facebookLinked) =>
      _$this._facebookLinked = facebookLinked;

  String? _facebookId;
  String? get facebookId => _$this._facebookId;
  set facebookId(String? facebookId) => _$this._facebookId = facebookId;

  String? _facebookName;
  String? get facebookName => _$this._facebookName;
  set facebookName(String? facebookName) => _$this._facebookName = facebookName;

  bool? _googleLinked;
  bool? get googleLinked => _$this._googleLinked;
  set googleLinked(bool? googleLinked) => _$this._googleLinked = googleLinked;

  String? _googleId;
  String? get googleId => _$this._googleId;
  set googleId(String? googleId) => _$this._googleId = googleId;

  String? _googleName;
  String? get googleName => _$this._googleName;
  set googleName(String? googleName) => _$this._googleName = googleName;

  String? _sex;
  String? get sex => _$this._sex;
  set sex(String? sex) => _$this._sex = sex;

  Date? _birthday;
  Date? get birthday => _$this._birthday;
  set birthday(Date? birthday) => _$this._birthday = birthday;

  String? _idCardType;
  String? get idCardType => _$this._idCardType;
  set idCardType(String? idCardType) => _$this._idCardType = idCardType;

  String? _idCardNo;
  String? get idCardNo => _$this._idCardNo;
  set idCardNo(String? idCardNo) => _$this._idCardNo = idCardNo;

  Date? _idCardIssueDate;
  Date? get idCardIssueDate => _$this._idCardIssueDate;
  set idCardIssueDate(Date? idCardIssueDate) =>
      _$this._idCardIssueDate = idCardIssueDate;

  Date? _idCardExpireDate;
  Date? get idCardExpireDate => _$this._idCardExpireDate;
  set idCardExpireDate(Date? idCardExpireDate) =>
      _$this._idCardExpireDate = idCardExpireDate;

  String? _idCardIssuePlace;
  String? get idCardIssuePlace => _$this._idCardIssuePlace;
  set idCardIssuePlace(String? idCardIssuePlace) =>
      _$this._idCardIssuePlace = idCardIssuePlace;

  String? _resAddr;
  String? get resAddr => _$this._resAddr;
  set resAddr(String? resAddr) => _$this._resAddr = resAddr;

  String? _resCity;
  String? get resCity => _$this._resCity;
  set resCity(String? resCity) => _$this._resCity = resCity;

  String? _nationCode;
  String? get nationCode => _$this._nationCode;
  set nationCode(String? nationCode) => _$this._nationCode = nationCode;

  String? _taxCode;
  String? get taxCode => _$this._taxCode;
  set taxCode(String? taxCode) => _$this._taxCode = taxCode;

  String? _provinceCode;
  String? get provinceCode => _$this._provinceCode;
  set provinceCode(String? provinceCode) => _$this._provinceCode = provinceCode;

  String? _districtCode;
  String? get districtCode => _$this._districtCode;
  set districtCode(String? districtCode) => _$this._districtCode = districtCode;

  String? _wardVlg;
  String? get wardVlg => _$this._wardVlg;
  set wardVlg(String? wardVlg) => _$this._wardVlg = wardVlg;

  String? _street;
  String? get street => _$this._street;
  set street(String? street) => _$this._street = street;

  String? _no123;
  String? get no123 => _$this._no123;
  set no123(String? no123) => _$this._no123 = no123;

  String? _branchCode;
  String? get branchCode => _$this._branchCode;
  set branchCode(String? branchCode) => _$this._branchCode = branchCode;

  String? _fullAddress;
  String? get fullAddress => _$this._fullAddress;
  set fullAddress(String? fullAddress) => _$this._fullAddress = fullAddress;

  bool? _enabled2fa;
  bool? get enabled2fa => _$this._enabled2fa;
  set enabled2fa(bool? enabled2fa) => _$this._enabled2fa = enabled2fa;

  bool? _bankVerified;
  bool? get bankVerified => _$this._bankVerified;
  set bankVerified(bool? bankVerified) => _$this._bankVerified = bankVerified;

  String? _createdFrom;
  String? get createdFrom => _$this._createdFrom;
  set createdFrom(String? createdFrom) => _$this._createdFrom = createdFrom;

  bool? _lockedOldApp;
  bool? get lockedOldApp => _$this._lockedOldApp;
  set lockedOldApp(bool? lockedOldApp) => _$this._lockedOldApp = lockedOldApp;

  String? _ekycId;
  String? get ekycId => _$this._ekycId;
  set ekycId(String? ekycId) => _$this._ekycId = ekycId;

  String? _suspiciousEkycStatus;
  String? get suspiciousEkycStatus => _$this._suspiciousEkycStatus;
  set suspiciousEkycStatus(String? suspiciousEkycStatus) =>
      _$this._suspiciousEkycStatus = suspiciousEkycStatus;

  String? _customerGroupUserId;
  String? get customerGroupUserId => _$this._customerGroupUserId;
  set customerGroupUserId(String? customerGroupUserId) =>
      _$this._customerGroupUserId = customerGroupUserId;

  QueryInfoResponseV2OrganizationEnum? _organization;
  QueryInfoResponseV2OrganizationEnum? get organization => _$this._organization;
  set organization(QueryInfoResponseV2OrganizationEnum? organization) =>
      _$this._organization = organization;

  String? _identityStatus;
  String? get identityStatus => _$this._identityStatus;
  set identityStatus(String? identityStatus) =>
      _$this._identityStatus = identityStatus;

  bool? _isVerifiedSTH;
  bool? get isVerifiedSTH => _$this._isVerifiedSTH;
  set isVerifiedSTH(bool? isVerifiedSTH) =>
      _$this._isVerifiedSTH = isVerifiedSTH;

  bool? _isNobleEmployee;
  bool? get isNobleEmployee => _$this._isNobleEmployee;
  set isNobleEmployee(bool? isNobleEmployee) =>
      _$this._isNobleEmployee = isNobleEmployee;

  bool? _passEkyc;
  bool? get passEkyc => _$this._passEkyc;
  set passEkyc(bool? passEkyc) => _$this._passEkyc = passEkyc;

  QueryInfoResponseV2Builder() {
    QueryInfoResponseV2._defaults(this);
  }

  QueryInfoResponseV2Builder get _$this {
    final $v = _$v;
    if ($v != null) {
      _loginType = $v.loginType;
      _fullName = $v.fullName;
      _avatarUrl = $v.avatarUrl;
      _phoneNumber = $v.phoneNumber;
      _otpMethod = $v.otpMethod;
      _email = $v.email;
      _verifiedEmail = $v.verifiedEmail;
      _aliasName = $v.aliasName;
      _username = $v.username;
      _facebookLinked = $v.facebookLinked;
      _facebookId = $v.facebookId;
      _facebookName = $v.facebookName;
      _googleLinked = $v.googleLinked;
      _googleId = $v.googleId;
      _googleName = $v.googleName;
      _sex = $v.sex;
      _birthday = $v.birthday;
      _idCardType = $v.idCardType;
      _idCardNo = $v.idCardNo;
      _idCardIssueDate = $v.idCardIssueDate;
      _idCardExpireDate = $v.idCardExpireDate;
      _idCardIssuePlace = $v.idCardIssuePlace;
      _resAddr = $v.resAddr;
      _resCity = $v.resCity;
      _nationCode = $v.nationCode;
      _taxCode = $v.taxCode;
      _provinceCode = $v.provinceCode;
      _districtCode = $v.districtCode;
      _wardVlg = $v.wardVlg;
      _street = $v.street;
      _no123 = $v.no123;
      _branchCode = $v.branchCode;
      _fullAddress = $v.fullAddress;
      _enabled2fa = $v.enabled2fa;
      _bankVerified = $v.bankVerified;
      _createdFrom = $v.createdFrom;
      _lockedOldApp = $v.lockedOldApp;
      _ekycId = $v.ekycId;
      _suspiciousEkycStatus = $v.suspiciousEkycStatus;
      _customerGroupUserId = $v.customerGroupUserId;
      _organization = $v.organization;
      _identityStatus = $v.identityStatus;
      _isVerifiedSTH = $v.isVerifiedSTH;
      _isNobleEmployee = $v.isNobleEmployee;
      _passEkyc = $v.passEkyc;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(QueryInfoResponseV2 other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$QueryInfoResponseV2;
  }

  @override
  void update(void Function(QueryInfoResponseV2Builder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  QueryInfoResponseV2 build() => _build();

  _$QueryInfoResponseV2 _build() {
    final _$result = _$v ??
        new _$QueryInfoResponseV2._(
            loginType: loginType,
            fullName: fullName,
            avatarUrl: avatarUrl,
            phoneNumber: phoneNumber,
            otpMethod: otpMethod,
            email: email,
            verifiedEmail: verifiedEmail,
            aliasName: aliasName,
            username: username,
            facebookLinked: facebookLinked,
            facebookId: facebookId,
            facebookName: facebookName,
            googleLinked: googleLinked,
            googleId: googleId,
            googleName: googleName,
            sex: sex,
            birthday: birthday,
            idCardType: idCardType,
            idCardNo: idCardNo,
            idCardIssueDate: idCardIssueDate,
            idCardExpireDate: idCardExpireDate,
            idCardIssuePlace: idCardIssuePlace,
            resAddr: resAddr,
            resCity: resCity,
            nationCode: nationCode,
            taxCode: taxCode,
            provinceCode: provinceCode,
            districtCode: districtCode,
            wardVlg: wardVlg,
            street: street,
            no123: no123,
            branchCode: branchCode,
            fullAddress: fullAddress,
            enabled2fa: enabled2fa,
            bankVerified: bankVerified,
            createdFrom: createdFrom,
            lockedOldApp: lockedOldApp,
            ekycId: ekycId,
            suspiciousEkycStatus: suspiciousEkycStatus,
            customerGroupUserId: customerGroupUserId,
            organization: organization,
            identityStatus: identityStatus,
            isVerifiedSTH: isVerifiedSTH,
            isNobleEmployee: isNobleEmployee,
            passEkyc: passEkyc);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
