// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ekyc_status_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const EkycStatusResponseUserLevelTypeEnum
    _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_ACCOUNT =
    const EkycStatusResponseUserLevelTypeEnum._('COUNTERS_ACCOUNT');
const EkycStatusResponseUserLevelTypeEnum
    _$ekycStatusResponseUserLevelTypeEnum_VIDEO_COUNTERS_KYC =
    const EkycStatusResponseUserLevelTypeEnum._('VIDEO_COUNTERS_KYC');
const EkycStatusResponseUserLevelTypeEnum
    _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_KYC =
    const EkycStatusResponseUserLevelTypeEnum._('COUNTERS_KYC');
const EkycStatusResponseUserLevelTypeEnum
    _$ekycStatusResponseUserLevelTypeEnum_VIDEO_KYC =
    const EkycStatusResponseUserLevelTypeEnum._('VIDEO_KYC');
const EkycStatusResponseUserLevelTypeEnum
    _$ekycStatusResponseUserLevelTypeEnum_E_KYC =
    const EkycStatusResponseUserLevelTypeEnum._('E_KYC');
const EkycStatusResponseUserLevelTypeEnum
    _$ekycStatusResponseUserLevelTypeEnum_NO_KYC =
    const EkycStatusResponseUserLevelTypeEnum._('NO_KYC');

EkycStatusResponseUserLevelTypeEnum
    _$ekycStatusResponseUserLevelTypeEnumValueOf(String name) {
  switch (name) {
    case 'COUNTERS_ACCOUNT':
      return _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_ACCOUNT;
    case 'VIDEO_COUNTERS_KYC':
      return _$ekycStatusResponseUserLevelTypeEnum_VIDEO_COUNTERS_KYC;
    case 'COUNTERS_KYC':
      return _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_KYC;
    case 'VIDEO_KYC':
      return _$ekycStatusResponseUserLevelTypeEnum_VIDEO_KYC;
    case 'E_KYC':
      return _$ekycStatusResponseUserLevelTypeEnum_E_KYC;
    case 'NO_KYC':
      return _$ekycStatusResponseUserLevelTypeEnum_NO_KYC;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<EkycStatusResponseUserLevelTypeEnum>
    _$ekycStatusResponseUserLevelTypeEnumValues = new BuiltSet<
        EkycStatusResponseUserLevelTypeEnum>(const <EkycStatusResponseUserLevelTypeEnum>[
  _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_ACCOUNT,
  _$ekycStatusResponseUserLevelTypeEnum_VIDEO_COUNTERS_KYC,
  _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_KYC,
  _$ekycStatusResponseUserLevelTypeEnum_VIDEO_KYC,
  _$ekycStatusResponseUserLevelTypeEnum_E_KYC,
  _$ekycStatusResponseUserLevelTypeEnum_NO_KYC,
]);

Serializer<EkycStatusResponseUserLevelTypeEnum>
    _$ekycStatusResponseUserLevelTypeEnumSerializer =
    new _$EkycStatusResponseUserLevelTypeEnumSerializer();

class _$EkycStatusResponseUserLevelTypeEnumSerializer
    implements PrimitiveSerializer<EkycStatusResponseUserLevelTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'COUNTERS_ACCOUNT': 'COUNTERS_ACCOUNT',
    'VIDEO_COUNTERS_KYC': 'VIDEO_COUNTERS_KYC',
    'COUNTERS_KYC': 'COUNTERS_KYC',
    'VIDEO_KYC': 'VIDEO_KYC',
    'E_KYC': 'E_KYC',
    'NO_KYC': 'NO_KYC',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'COUNTERS_ACCOUNT': 'COUNTERS_ACCOUNT',
    'VIDEO_COUNTERS_KYC': 'VIDEO_COUNTERS_KYC',
    'COUNTERS_KYC': 'COUNTERS_KYC',
    'VIDEO_KYC': 'VIDEO_KYC',
    'E_KYC': 'E_KYC',
    'NO_KYC': 'NO_KYC',
  };

  @override
  final Iterable<Type> types = const <Type>[
    EkycStatusResponseUserLevelTypeEnum
  ];
  @override
  final String wireName = 'EkycStatusResponseUserLevelTypeEnum';

  @override
  Object serialize(
          Serializers serializers, EkycStatusResponseUserLevelTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  EkycStatusResponseUserLevelTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      EkycStatusResponseUserLevelTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$EkycStatusResponse extends EkycStatusResponse {
  @override
  final EkycStatusResponseUserLevelTypeEnum? userLevelType;

  factory _$EkycStatusResponse(
          [void Function(EkycStatusResponseBuilder)? updates]) =>
      (new EkycStatusResponseBuilder()..update(updates))._build();

  _$EkycStatusResponse._({this.userLevelType}) : super._();

  @override
  EkycStatusResponse rebuild(
          void Function(EkycStatusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  EkycStatusResponseBuilder toBuilder() =>
      new EkycStatusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EkycStatusResponse && userLevelType == other.userLevelType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userLevelType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'EkycStatusResponse')
          ..add('userLevelType', userLevelType))
        .toString();
  }
}

class EkycStatusResponseBuilder
    implements Builder<EkycStatusResponse, EkycStatusResponseBuilder> {
  _$EkycStatusResponse? _$v;

  EkycStatusResponseUserLevelTypeEnum? _userLevelType;
  EkycStatusResponseUserLevelTypeEnum? get userLevelType =>
      _$this._userLevelType;
  set userLevelType(EkycStatusResponseUserLevelTypeEnum? userLevelType) =>
      _$this._userLevelType = userLevelType;

  EkycStatusResponseBuilder() {
    EkycStatusResponse._defaults(this);
  }

  EkycStatusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userLevelType = $v.userLevelType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(EkycStatusResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$EkycStatusResponse;
  }

  @override
  void update(void Function(EkycStatusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  EkycStatusResponse build() => _build();

  _$EkycStatusResponse _build() {
    final _$result =
        _$v ?? new _$EkycStatusResponse._(userLevelType: userLevelType);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
