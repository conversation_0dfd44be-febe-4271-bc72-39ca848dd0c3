//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'otp_token.g.dart';

/// OTPToken
///
/// Properties:
/// * [id]
/// * [userId]
/// * [deviceId]
/// * [tokenType]
/// * [activated]
/// * [description]
/// * [shareSecret]
/// * [createAt]
/// * [deviceName]
/// * [brand]
/// * [os]
/// * [imei]
@BuiltValue()
abstract class OTPToken implements Built<OTPToken, OTPTokenBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  @BuiltValueField(wireName: r'userId')
  String? get userId;

  @BuiltValueField(wireName: r'deviceId')
  String? get deviceId;

  @BuiltValueField(wireName: r'tokenType')
  OTPTokenTokenTypeEnum? get tokenType;
  // enum tokenTypeEnum {  SOFT_TOKEN,  SMS_OTP,  HARD_TOKEN,  };

  @BuiltValueField(wireName: r'activated')
  bool? get activated;

  @BuiltValueField(wireName: r'description')
  String? get description;

  @BuiltValueField(wireName: r'shareSecret')
  String? get shareSecret;

  @BuiltValueField(wireName: r'createAt')
  DateTime? get createAt;

  @BuiltValueField(wireName: r'deviceName')
  String? get deviceName;

  @BuiltValueField(wireName: r'brand')
  String? get brand;

  @BuiltValueField(wireName: r'os')
  String? get os;

  @BuiltValueField(wireName: r'imei')
  String? get imei;

  OTPToken._();

  factory OTPToken([void updates(OTPTokenBuilder b)]) = _$OTPToken;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(OTPTokenBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<OTPToken> get serializer => _$OTPTokenSerializer();
}

class _$OTPTokenSerializer implements PrimitiveSerializer<OTPToken> {
  @override
  final Iterable<Type> types = const [OTPToken, _$OTPToken];

  @override
  final String wireName = r'OTPToken';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    OTPToken object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.userId != null) {
      yield r'userId';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.deviceId != null) {
      yield r'deviceId';
      yield serializers.serialize(
        object.deviceId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.tokenType != null) {
      yield r'tokenType';
      yield serializers.serialize(
        object.tokenType,
        specifiedType: const FullType.nullable(OTPTokenTokenTypeEnum),
      );
    }
    if (object.activated != null) {
      yield r'activated';
      yield serializers.serialize(
        object.activated,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.shareSecret != null) {
      yield r'shareSecret';
      yield serializers.serialize(
        object.shareSecret,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.createAt != null) {
      yield r'createAt';
      yield serializers.serialize(
        object.createAt,
        specifiedType: const FullType.nullable(DateTime),
      );
    }
    if (object.deviceName != null) {
      yield r'deviceName';
      yield serializers.serialize(
        object.deviceName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.brand != null) {
      yield r'brand';
      yield serializers.serialize(
        object.brand,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.os != null) {
      yield r'os';
      yield serializers.serialize(
        object.os,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.imei != null) {
      yield r'imei';
      yield serializers.serialize(
        object.imei,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    OTPToken object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required OTPTokenBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.id = valueDes;
          break;
        case r'userId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.userId = valueDes;
          break;
        case r'deviceId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.deviceId = valueDes;
          break;
        case r'tokenType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(OTPTokenTokenTypeEnum),
          ) as OTPTokenTokenTypeEnum?;
          if (valueDes == null) continue;
          result.tokenType = valueDes;
          break;
        case r'activated':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.activated = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.description = valueDes;
          break;
        case r'shareSecret':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.shareSecret = valueDes;
          break;
        case r'createAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(DateTime),
          ) as DateTime?;
          if (valueDes == null) continue;
          result.createAt = valueDes;
          break;
        case r'deviceName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.deviceName = valueDes;
          break;
        case r'brand':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.brand = valueDes;
          break;
        case r'os':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.os = valueDes;
          break;
        case r'imei':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.imei = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  OTPToken deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = OTPTokenBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class OTPTokenTokenTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'SOFT_TOKEN')
  static const OTPTokenTokenTypeEnum SOFT_TOKEN =
      _$oTPTokenTokenTypeEnum_SOFT_TOKEN;
  @BuiltValueEnumConst(wireName: r'SMS_OTP')
  static const OTPTokenTokenTypeEnum SMS_OTP = _$oTPTokenTokenTypeEnum_SMS_OTP;
  @BuiltValueEnumConst(wireName: r'HARD_TOKEN')
  static const OTPTokenTokenTypeEnum HARD_TOKEN =
      _$oTPTokenTokenTypeEnum_HARD_TOKEN;

  static Serializer<OTPTokenTokenTypeEnum> get serializer =>
      _$oTPTokenTokenTypeEnumSerializer;

  const OTPTokenTokenTypeEnum._(String name) : super(name);

  static BuiltSet<OTPTokenTokenTypeEnum> get values =>
      _$oTPTokenTokenTypeEnumValues;
  static OTPTokenTokenTypeEnum valueOf(String name) =>
      _$oTPTokenTokenTypeEnumValueOf(name);
}
