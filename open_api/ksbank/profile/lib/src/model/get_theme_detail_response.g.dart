// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_theme_detail_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const GetThemeDetailResponseCustomerRankEnum
    _$getThemeDetailResponseCustomerRankEnum_NORMAL =
    const GetThemeDetailResponseCustomerRankEnum._('NORMAL');
const GetThemeDetailResponseCustomerRankEnum
    _$getThemeDetailResponseCustomerRankEnum_DIAMOND =
    const GetThemeDetailResponseCustomerRankEnum._('DIAMOND');
const GetThemeDetailResponseCustomerRankEnum
    _$getThemeDetailResponseCustomerRankEnum_RUBY =
    const GetThemeDetailResponseCustomerRankEnum._('RUBY');
const GetThemeDetailResponseCustomerRankEnum
    _$getThemeDetailResponseCustomerRankEnum_SAPPHIRE =
    const GetThemeDetailResponseCustomerRankEnum._('SAPPHIRE');
const GetThemeDetailResponseCustomerRankEnum
    _$getThemeDetailResponseCustomerRankEnum_DIAMOND_ELITE =
    const GetThemeDetailResponseCustomerRankEnum._('DIAMOND_ELITE');

GetThemeDetailResponseCustomerRankEnum
    _$getThemeDetailResponseCustomerRankEnumValueOf(String name) {
  switch (name) {
    case 'NORMAL':
      return _$getThemeDetailResponseCustomerRankEnum_NORMAL;
    case 'DIAMOND':
      return _$getThemeDetailResponseCustomerRankEnum_DIAMOND;
    case 'RUBY':
      return _$getThemeDetailResponseCustomerRankEnum_RUBY;
    case 'SAPPHIRE':
      return _$getThemeDetailResponseCustomerRankEnum_SAPPHIRE;
    case 'DIAMOND_ELITE':
      return _$getThemeDetailResponseCustomerRankEnum_DIAMOND_ELITE;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<GetThemeDetailResponseCustomerRankEnum>
    _$getThemeDetailResponseCustomerRankEnumValues = new BuiltSet<
        GetThemeDetailResponseCustomerRankEnum>(const <GetThemeDetailResponseCustomerRankEnum>[
  _$getThemeDetailResponseCustomerRankEnum_NORMAL,
  _$getThemeDetailResponseCustomerRankEnum_DIAMOND,
  _$getThemeDetailResponseCustomerRankEnum_RUBY,
  _$getThemeDetailResponseCustomerRankEnum_SAPPHIRE,
  _$getThemeDetailResponseCustomerRankEnum_DIAMOND_ELITE,
]);

Serializer<GetThemeDetailResponseCustomerRankEnum>
    _$getThemeDetailResponseCustomerRankEnumSerializer =
    new _$GetThemeDetailResponseCustomerRankEnumSerializer();

class _$GetThemeDetailResponseCustomerRankEnumSerializer
    implements PrimitiveSerializer<GetThemeDetailResponseCustomerRankEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'NORMAL': 'NORMAL',
    'DIAMOND': 'DIAMOND',
    'RUBY': 'RUBY',
    'SAPPHIRE': 'SAPPHIRE',
    'DIAMOND_ELITE': 'DIAMOND_ELITE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'NORMAL': 'NORMAL',
    'DIAMOND': 'DIAMOND',
    'RUBY': 'RUBY',
    'SAPPHIRE': 'SAPPHIRE',
    'DIAMOND_ELITE': 'DIAMOND_ELITE',
  };

  @override
  final Iterable<Type> types = const <Type>[
    GetThemeDetailResponseCustomerRankEnum
  ];
  @override
  final String wireName = 'GetThemeDetailResponseCustomerRankEnum';

  @override
  Object serialize(Serializers serializers,
          GetThemeDetailResponseCustomerRankEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  GetThemeDetailResponseCustomerRankEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      GetThemeDetailResponseCustomerRankEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$GetThemeDetailResponse extends GetThemeDetailResponse {
  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? category;
  @override
  final GetThemeDetailResponseCustomerRankEnum? customerRank;
  @override
  final DateTime? startTime;
  @override
  final DateTime? endTime;
  @override
  final String? thumbnailUrl;
  @override
  final String? thumbnailSize;
  @override
  final String? thumbnailType;
  @override
  final String? bannerUrl;
  @override
  final String? bannerSize;
  @override
  final String? bannerType;
  @override
  final String? introUrl;
  @override
  final String? introSize;
  @override
  final String? introType;
  @override
  final String? loginUrl;
  @override
  final String? loginSize;
  @override
  final String? loginType;
  @override
  final String? logoUrl;
  @override
  final String? logoSize;
  @override
  final String? logoType;
  @override
  final String? logoRankUrl;
  @override
  final String? logoRankSize;
  @override
  final String? logoRankType;
  @override
  final String? bannerRankUrl;
  @override
  final String? bannerRankSize;
  @override
  final String? bannerRankType;
  @override
  final String? logoShortUrl;
  @override
  final String? logoShortSize;
  @override
  final String? logoShortType;
  @override
  final String? logoLoginUrl;
  @override
  final String? logoLoginSize;
  @override
  final String? logoLoginType;
  @override
  final String? faceIdUrl;
  @override
  final String? faceIdSize;
  @override
  final String? faceIdType;
  @override
  final String? backgroundColor;
  @override
  final String? textColor;
  @override
  final String? iconColorLogin;
  @override
  final String? iconColorMain;
  @override
  final bool? brightLogo;
  @override
  final bool? active;
  @override
  final bool? defaultTheme;
  @override
  final bool? deleted;
  @override
  final String? lastModifiedBy;
  @override
  final DateTime? lastModifiedDate;
  @override
  final String? createdBy;
  @override
  final DateTime? createdDate;

  factory _$GetThemeDetailResponse(
          [void Function(GetThemeDetailResponseBuilder)? updates]) =>
      (new GetThemeDetailResponseBuilder()..update(updates))._build();

  _$GetThemeDetailResponse._(
      {this.id,
      this.name,
      this.description,
      this.category,
      this.customerRank,
      this.startTime,
      this.endTime,
      this.thumbnailUrl,
      this.thumbnailSize,
      this.thumbnailType,
      this.bannerUrl,
      this.bannerSize,
      this.bannerType,
      this.introUrl,
      this.introSize,
      this.introType,
      this.loginUrl,
      this.loginSize,
      this.loginType,
      this.logoUrl,
      this.logoSize,
      this.logoType,
      this.logoRankUrl,
      this.logoRankSize,
      this.logoRankType,
      this.bannerRankUrl,
      this.bannerRankSize,
      this.bannerRankType,
      this.logoShortUrl,
      this.logoShortSize,
      this.logoShortType,
      this.logoLoginUrl,
      this.logoLoginSize,
      this.logoLoginType,
      this.faceIdUrl,
      this.faceIdSize,
      this.faceIdType,
      this.backgroundColor,
      this.textColor,
      this.iconColorLogin,
      this.iconColorMain,
      this.brightLogo,
      this.active,
      this.defaultTheme,
      this.deleted,
      this.lastModifiedBy,
      this.lastModifiedDate,
      this.createdBy,
      this.createdDate})
      : super._();

  @override
  GetThemeDetailResponse rebuild(
          void Function(GetThemeDetailResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetThemeDetailResponseBuilder toBuilder() =>
      new GetThemeDetailResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetThemeDetailResponse &&
        id == other.id &&
        name == other.name &&
        description == other.description &&
        category == other.category &&
        customerRank == other.customerRank &&
        startTime == other.startTime &&
        endTime == other.endTime &&
        thumbnailUrl == other.thumbnailUrl &&
        thumbnailSize == other.thumbnailSize &&
        thumbnailType == other.thumbnailType &&
        bannerUrl == other.bannerUrl &&
        bannerSize == other.bannerSize &&
        bannerType == other.bannerType &&
        introUrl == other.introUrl &&
        introSize == other.introSize &&
        introType == other.introType &&
        loginUrl == other.loginUrl &&
        loginSize == other.loginSize &&
        loginType == other.loginType &&
        logoUrl == other.logoUrl &&
        logoSize == other.logoSize &&
        logoType == other.logoType &&
        logoRankUrl == other.logoRankUrl &&
        logoRankSize == other.logoRankSize &&
        logoRankType == other.logoRankType &&
        bannerRankUrl == other.bannerRankUrl &&
        bannerRankSize == other.bannerRankSize &&
        bannerRankType == other.bannerRankType &&
        logoShortUrl == other.logoShortUrl &&
        logoShortSize == other.logoShortSize &&
        logoShortType == other.logoShortType &&
        logoLoginUrl == other.logoLoginUrl &&
        logoLoginSize == other.logoLoginSize &&
        logoLoginType == other.logoLoginType &&
        faceIdUrl == other.faceIdUrl &&
        faceIdSize == other.faceIdSize &&
        faceIdType == other.faceIdType &&
        backgroundColor == other.backgroundColor &&
        textColor == other.textColor &&
        iconColorLogin == other.iconColorLogin &&
        iconColorMain == other.iconColorMain &&
        brightLogo == other.brightLogo &&
        active == other.active &&
        defaultTheme == other.defaultTheme &&
        deleted == other.deleted &&
        lastModifiedBy == other.lastModifiedBy &&
        lastModifiedDate == other.lastModifiedDate &&
        createdBy == other.createdBy &&
        createdDate == other.createdDate;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, category.hashCode);
    _$hash = $jc(_$hash, customerRank.hashCode);
    _$hash = $jc(_$hash, startTime.hashCode);
    _$hash = $jc(_$hash, endTime.hashCode);
    _$hash = $jc(_$hash, thumbnailUrl.hashCode);
    _$hash = $jc(_$hash, thumbnailSize.hashCode);
    _$hash = $jc(_$hash, thumbnailType.hashCode);
    _$hash = $jc(_$hash, bannerUrl.hashCode);
    _$hash = $jc(_$hash, bannerSize.hashCode);
    _$hash = $jc(_$hash, bannerType.hashCode);
    _$hash = $jc(_$hash, introUrl.hashCode);
    _$hash = $jc(_$hash, introSize.hashCode);
    _$hash = $jc(_$hash, introType.hashCode);
    _$hash = $jc(_$hash, loginUrl.hashCode);
    _$hash = $jc(_$hash, loginSize.hashCode);
    _$hash = $jc(_$hash, loginType.hashCode);
    _$hash = $jc(_$hash, logoUrl.hashCode);
    _$hash = $jc(_$hash, logoSize.hashCode);
    _$hash = $jc(_$hash, logoType.hashCode);
    _$hash = $jc(_$hash, logoRankUrl.hashCode);
    _$hash = $jc(_$hash, logoRankSize.hashCode);
    _$hash = $jc(_$hash, logoRankType.hashCode);
    _$hash = $jc(_$hash, bannerRankUrl.hashCode);
    _$hash = $jc(_$hash, bannerRankSize.hashCode);
    _$hash = $jc(_$hash, bannerRankType.hashCode);
    _$hash = $jc(_$hash, logoShortUrl.hashCode);
    _$hash = $jc(_$hash, logoShortSize.hashCode);
    _$hash = $jc(_$hash, logoShortType.hashCode);
    _$hash = $jc(_$hash, logoLoginUrl.hashCode);
    _$hash = $jc(_$hash, logoLoginSize.hashCode);
    _$hash = $jc(_$hash, logoLoginType.hashCode);
    _$hash = $jc(_$hash, faceIdUrl.hashCode);
    _$hash = $jc(_$hash, faceIdSize.hashCode);
    _$hash = $jc(_$hash, faceIdType.hashCode);
    _$hash = $jc(_$hash, backgroundColor.hashCode);
    _$hash = $jc(_$hash, textColor.hashCode);
    _$hash = $jc(_$hash, iconColorLogin.hashCode);
    _$hash = $jc(_$hash, iconColorMain.hashCode);
    _$hash = $jc(_$hash, brightLogo.hashCode);
    _$hash = $jc(_$hash, active.hashCode);
    _$hash = $jc(_$hash, defaultTheme.hashCode);
    _$hash = $jc(_$hash, deleted.hashCode);
    _$hash = $jc(_$hash, lastModifiedBy.hashCode);
    _$hash = $jc(_$hash, lastModifiedDate.hashCode);
    _$hash = $jc(_$hash, createdBy.hashCode);
    _$hash = $jc(_$hash, createdDate.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetThemeDetailResponse')
          ..add('id', id)
          ..add('name', name)
          ..add('description', description)
          ..add('category', category)
          ..add('customerRank', customerRank)
          ..add('startTime', startTime)
          ..add('endTime', endTime)
          ..add('thumbnailUrl', thumbnailUrl)
          ..add('thumbnailSize', thumbnailSize)
          ..add('thumbnailType', thumbnailType)
          ..add('bannerUrl', bannerUrl)
          ..add('bannerSize', bannerSize)
          ..add('bannerType', bannerType)
          ..add('introUrl', introUrl)
          ..add('introSize', introSize)
          ..add('introType', introType)
          ..add('loginUrl', loginUrl)
          ..add('loginSize', loginSize)
          ..add('loginType', loginType)
          ..add('logoUrl', logoUrl)
          ..add('logoSize', logoSize)
          ..add('logoType', logoType)
          ..add('logoRankUrl', logoRankUrl)
          ..add('logoRankSize', logoRankSize)
          ..add('logoRankType', logoRankType)
          ..add('bannerRankUrl', bannerRankUrl)
          ..add('bannerRankSize', bannerRankSize)
          ..add('bannerRankType', bannerRankType)
          ..add('logoShortUrl', logoShortUrl)
          ..add('logoShortSize', logoShortSize)
          ..add('logoShortType', logoShortType)
          ..add('logoLoginUrl', logoLoginUrl)
          ..add('logoLoginSize', logoLoginSize)
          ..add('logoLoginType', logoLoginType)
          ..add('faceIdUrl', faceIdUrl)
          ..add('faceIdSize', faceIdSize)
          ..add('faceIdType', faceIdType)
          ..add('backgroundColor', backgroundColor)
          ..add('textColor', textColor)
          ..add('iconColorLogin', iconColorLogin)
          ..add('iconColorMain', iconColorMain)
          ..add('brightLogo', brightLogo)
          ..add('active', active)
          ..add('defaultTheme', defaultTheme)
          ..add('deleted', deleted)
          ..add('lastModifiedBy', lastModifiedBy)
          ..add('lastModifiedDate', lastModifiedDate)
          ..add('createdBy', createdBy)
          ..add('createdDate', createdDate))
        .toString();
  }
}

class GetThemeDetailResponseBuilder
    implements Builder<GetThemeDetailResponse, GetThemeDetailResponseBuilder> {
  _$GetThemeDetailResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _category;
  String? get category => _$this._category;
  set category(String? category) => _$this._category = category;

  GetThemeDetailResponseCustomerRankEnum? _customerRank;
  GetThemeDetailResponseCustomerRankEnum? get customerRank =>
      _$this._customerRank;
  set customerRank(GetThemeDetailResponseCustomerRankEnum? customerRank) =>
      _$this._customerRank = customerRank;

  DateTime? _startTime;
  DateTime? get startTime => _$this._startTime;
  set startTime(DateTime? startTime) => _$this._startTime = startTime;

  DateTime? _endTime;
  DateTime? get endTime => _$this._endTime;
  set endTime(DateTime? endTime) => _$this._endTime = endTime;

  String? _thumbnailUrl;
  String? get thumbnailUrl => _$this._thumbnailUrl;
  set thumbnailUrl(String? thumbnailUrl) => _$this._thumbnailUrl = thumbnailUrl;

  String? _thumbnailSize;
  String? get thumbnailSize => _$this._thumbnailSize;
  set thumbnailSize(String? thumbnailSize) =>
      _$this._thumbnailSize = thumbnailSize;

  String? _thumbnailType;
  String? get thumbnailType => _$this._thumbnailType;
  set thumbnailType(String? thumbnailType) =>
      _$this._thumbnailType = thumbnailType;

  String? _bannerUrl;
  String? get bannerUrl => _$this._bannerUrl;
  set bannerUrl(String? bannerUrl) => _$this._bannerUrl = bannerUrl;

  String? _bannerSize;
  String? get bannerSize => _$this._bannerSize;
  set bannerSize(String? bannerSize) => _$this._bannerSize = bannerSize;

  String? _bannerType;
  String? get bannerType => _$this._bannerType;
  set bannerType(String? bannerType) => _$this._bannerType = bannerType;

  String? _introUrl;
  String? get introUrl => _$this._introUrl;
  set introUrl(String? introUrl) => _$this._introUrl = introUrl;

  String? _introSize;
  String? get introSize => _$this._introSize;
  set introSize(String? introSize) => _$this._introSize = introSize;

  String? _introType;
  String? get introType => _$this._introType;
  set introType(String? introType) => _$this._introType = introType;

  String? _loginUrl;
  String? get loginUrl => _$this._loginUrl;
  set loginUrl(String? loginUrl) => _$this._loginUrl = loginUrl;

  String? _loginSize;
  String? get loginSize => _$this._loginSize;
  set loginSize(String? loginSize) => _$this._loginSize = loginSize;

  String? _loginType;
  String? get loginType => _$this._loginType;
  set loginType(String? loginType) => _$this._loginType = loginType;

  String? _logoUrl;
  String? get logoUrl => _$this._logoUrl;
  set logoUrl(String? logoUrl) => _$this._logoUrl = logoUrl;

  String? _logoSize;
  String? get logoSize => _$this._logoSize;
  set logoSize(String? logoSize) => _$this._logoSize = logoSize;

  String? _logoType;
  String? get logoType => _$this._logoType;
  set logoType(String? logoType) => _$this._logoType = logoType;

  String? _logoRankUrl;
  String? get logoRankUrl => _$this._logoRankUrl;
  set logoRankUrl(String? logoRankUrl) => _$this._logoRankUrl = logoRankUrl;

  String? _logoRankSize;
  String? get logoRankSize => _$this._logoRankSize;
  set logoRankSize(String? logoRankSize) => _$this._logoRankSize = logoRankSize;

  String? _logoRankType;
  String? get logoRankType => _$this._logoRankType;
  set logoRankType(String? logoRankType) => _$this._logoRankType = logoRankType;

  String? _bannerRankUrl;
  String? get bannerRankUrl => _$this._bannerRankUrl;
  set bannerRankUrl(String? bannerRankUrl) =>
      _$this._bannerRankUrl = bannerRankUrl;

  String? _bannerRankSize;
  String? get bannerRankSize => _$this._bannerRankSize;
  set bannerRankSize(String? bannerRankSize) =>
      _$this._bannerRankSize = bannerRankSize;

  String? _bannerRankType;
  String? get bannerRankType => _$this._bannerRankType;
  set bannerRankType(String? bannerRankType) =>
      _$this._bannerRankType = bannerRankType;

  String? _logoShortUrl;
  String? get logoShortUrl => _$this._logoShortUrl;
  set logoShortUrl(String? logoShortUrl) => _$this._logoShortUrl = logoShortUrl;

  String? _logoShortSize;
  String? get logoShortSize => _$this._logoShortSize;
  set logoShortSize(String? logoShortSize) =>
      _$this._logoShortSize = logoShortSize;

  String? _logoShortType;
  String? get logoShortType => _$this._logoShortType;
  set logoShortType(String? logoShortType) =>
      _$this._logoShortType = logoShortType;

  String? _logoLoginUrl;
  String? get logoLoginUrl => _$this._logoLoginUrl;
  set logoLoginUrl(String? logoLoginUrl) => _$this._logoLoginUrl = logoLoginUrl;

  String? _logoLoginSize;
  String? get logoLoginSize => _$this._logoLoginSize;
  set logoLoginSize(String? logoLoginSize) =>
      _$this._logoLoginSize = logoLoginSize;

  String? _logoLoginType;
  String? get logoLoginType => _$this._logoLoginType;
  set logoLoginType(String? logoLoginType) =>
      _$this._logoLoginType = logoLoginType;

  String? _faceIdUrl;
  String? get faceIdUrl => _$this._faceIdUrl;
  set faceIdUrl(String? faceIdUrl) => _$this._faceIdUrl = faceIdUrl;

  String? _faceIdSize;
  String? get faceIdSize => _$this._faceIdSize;
  set faceIdSize(String? faceIdSize) => _$this._faceIdSize = faceIdSize;

  String? _faceIdType;
  String? get faceIdType => _$this._faceIdType;
  set faceIdType(String? faceIdType) => _$this._faceIdType = faceIdType;

  String? _backgroundColor;
  String? get backgroundColor => _$this._backgroundColor;
  set backgroundColor(String? backgroundColor) =>
      _$this._backgroundColor = backgroundColor;

  String? _textColor;
  String? get textColor => _$this._textColor;
  set textColor(String? textColor) => _$this._textColor = textColor;

  String? _iconColorLogin;
  String? get iconColorLogin => _$this._iconColorLogin;
  set iconColorLogin(String? iconColorLogin) =>
      _$this._iconColorLogin = iconColorLogin;

  String? _iconColorMain;
  String? get iconColorMain => _$this._iconColorMain;
  set iconColorMain(String? iconColorMain) =>
      _$this._iconColorMain = iconColorMain;

  bool? _brightLogo;
  bool? get brightLogo => _$this._brightLogo;
  set brightLogo(bool? brightLogo) => _$this._brightLogo = brightLogo;

  bool? _active;
  bool? get active => _$this._active;
  set active(bool? active) => _$this._active = active;

  bool? _defaultTheme;
  bool? get defaultTheme => _$this._defaultTheme;
  set defaultTheme(bool? defaultTheme) => _$this._defaultTheme = defaultTheme;

  bool? _deleted;
  bool? get deleted => _$this._deleted;
  set deleted(bool? deleted) => _$this._deleted = deleted;

  String? _lastModifiedBy;
  String? get lastModifiedBy => _$this._lastModifiedBy;
  set lastModifiedBy(String? lastModifiedBy) =>
      _$this._lastModifiedBy = lastModifiedBy;

  DateTime? _lastModifiedDate;
  DateTime? get lastModifiedDate => _$this._lastModifiedDate;
  set lastModifiedDate(DateTime? lastModifiedDate) =>
      _$this._lastModifiedDate = lastModifiedDate;

  String? _createdBy;
  String? get createdBy => _$this._createdBy;
  set createdBy(String? createdBy) => _$this._createdBy = createdBy;

  DateTime? _createdDate;
  DateTime? get createdDate => _$this._createdDate;
  set createdDate(DateTime? createdDate) => _$this._createdDate = createdDate;

  GetThemeDetailResponseBuilder() {
    GetThemeDetailResponse._defaults(this);
  }

  GetThemeDetailResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _name = $v.name;
      _description = $v.description;
      _category = $v.category;
      _customerRank = $v.customerRank;
      _startTime = $v.startTime;
      _endTime = $v.endTime;
      _thumbnailUrl = $v.thumbnailUrl;
      _thumbnailSize = $v.thumbnailSize;
      _thumbnailType = $v.thumbnailType;
      _bannerUrl = $v.bannerUrl;
      _bannerSize = $v.bannerSize;
      _bannerType = $v.bannerType;
      _introUrl = $v.introUrl;
      _introSize = $v.introSize;
      _introType = $v.introType;
      _loginUrl = $v.loginUrl;
      _loginSize = $v.loginSize;
      _loginType = $v.loginType;
      _logoUrl = $v.logoUrl;
      _logoSize = $v.logoSize;
      _logoType = $v.logoType;
      _logoRankUrl = $v.logoRankUrl;
      _logoRankSize = $v.logoRankSize;
      _logoRankType = $v.logoRankType;
      _bannerRankUrl = $v.bannerRankUrl;
      _bannerRankSize = $v.bannerRankSize;
      _bannerRankType = $v.bannerRankType;
      _logoShortUrl = $v.logoShortUrl;
      _logoShortSize = $v.logoShortSize;
      _logoShortType = $v.logoShortType;
      _logoLoginUrl = $v.logoLoginUrl;
      _logoLoginSize = $v.logoLoginSize;
      _logoLoginType = $v.logoLoginType;
      _faceIdUrl = $v.faceIdUrl;
      _faceIdSize = $v.faceIdSize;
      _faceIdType = $v.faceIdType;
      _backgroundColor = $v.backgroundColor;
      _textColor = $v.textColor;
      _iconColorLogin = $v.iconColorLogin;
      _iconColorMain = $v.iconColorMain;
      _brightLogo = $v.brightLogo;
      _active = $v.active;
      _defaultTheme = $v.defaultTheme;
      _deleted = $v.deleted;
      _lastModifiedBy = $v.lastModifiedBy;
      _lastModifiedDate = $v.lastModifiedDate;
      _createdBy = $v.createdBy;
      _createdDate = $v.createdDate;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetThemeDetailResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetThemeDetailResponse;
  }

  @override
  void update(void Function(GetThemeDetailResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetThemeDetailResponse build() => _build();

  _$GetThemeDetailResponse _build() {
    final _$result = _$v ??
        new _$GetThemeDetailResponse._(
            id: id,
            name: name,
            description: description,
            category: category,
            customerRank: customerRank,
            startTime: startTime,
            endTime: endTime,
            thumbnailUrl: thumbnailUrl,
            thumbnailSize: thumbnailSize,
            thumbnailType: thumbnailType,
            bannerUrl: bannerUrl,
            bannerSize: bannerSize,
            bannerType: bannerType,
            introUrl: introUrl,
            introSize: introSize,
            introType: introType,
            loginUrl: loginUrl,
            loginSize: loginSize,
            loginType: loginType,
            logoUrl: logoUrl,
            logoSize: logoSize,
            logoType: logoType,
            logoRankUrl: logoRankUrl,
            logoRankSize: logoRankSize,
            logoRankType: logoRankType,
            bannerRankUrl: bannerRankUrl,
            bannerRankSize: bannerRankSize,
            bannerRankType: bannerRankType,
            logoShortUrl: logoShortUrl,
            logoShortSize: logoShortSize,
            logoShortType: logoShortType,
            logoLoginUrl: logoLoginUrl,
            logoLoginSize: logoLoginSize,
            logoLoginType: logoLoginType,
            faceIdUrl: faceIdUrl,
            faceIdSize: faceIdSize,
            faceIdType: faceIdType,
            backgroundColor: backgroundColor,
            textColor: textColor,
            iconColorLogin: iconColorLogin,
            iconColorMain: iconColorMain,
            brightLogo: brightLogo,
            active: active,
            defaultTheme: defaultTheme,
            deleted: deleted,
            lastModifiedBy: lastModifiedBy,
            lastModifiedDate: lastModifiedDate,
            createdBy: createdBy,
            createdDate: createdDate);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
