// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user2345_verify_type.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const User2345VerifyType _$CHIP_ID = const User2345VerifyType._('CHIP_ID');
const User2345VerifyType _$FACE_ID = const User2345VerifyType._('FACE_ID');
const User2345VerifyType _$NONE = const User2345VerifyType._('NONE');
const User2345VerifyType _$GTTT_EXPIRE =
    const User2345VerifyType._('GTTT_EXPIRE');
const User2345VerifyType _$GTTT_EXPIRING_SOON =
    const User2345VerifyType._('GTTT_EXPIRING_SOON');

User2345VerifyType _$valueOf(String name) {
  switch (name) {
    case 'CHIP_ID':
      return _$CHIP_ID;
    case 'FACE_ID':
      return _$FACE_ID;
    case 'NONE':
      return _$NONE;
    case 'GTTT_EXPIRE':
      return _$GTTT_EXPIRE;
    case 'GTTT_EXPIRING_SOON':
      return _$GTTT_EXPIRING_SOON;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<User2345VerifyType> _$values =
    new BuiltSet<User2345VerifyType>(const <User2345VerifyType>[
  _$CHIP_ID,
  _$FACE_ID,
  _$NONE,
  _$GTTT_EXPIRE,
  _$GTTT_EXPIRING_SOON,
]);

class _$User2345VerifyTypeMeta {
  const _$User2345VerifyTypeMeta();
  User2345VerifyType get CHIP_ID => _$CHIP_ID;
  User2345VerifyType get FACE_ID => _$FACE_ID;
  User2345VerifyType get NONE => _$NONE;
  User2345VerifyType get GTTT_EXPIRE => _$GTTT_EXPIRE;
  User2345VerifyType get GTTT_EXPIRING_SOON => _$GTTT_EXPIRING_SOON;
  User2345VerifyType valueOf(String name) => _$valueOf(name);
  BuiltSet<User2345VerifyType> get values => _$values;
}

abstract class _$User2345VerifyTypeMixin {
  // ignore: non_constant_identifier_names
  _$User2345VerifyTypeMeta get User2345VerifyType =>
      const _$User2345VerifyTypeMeta();
}

Serializer<User2345VerifyType> _$user2345VerifyTypeSerializer =
    new _$User2345VerifyTypeSerializer();

class _$User2345VerifyTypeSerializer
    implements PrimitiveSerializer<User2345VerifyType> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'CHIP_ID': 'CHIP_ID',
    'FACE_ID': 'FACE_ID',
    'NONE': 'NONE',
    'GTTT_EXPIRE': 'GTTT_EXPIRE',
    'GTTT_EXPIRING_SOON': 'GTTT_EXPIRING_SOON',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'CHIP_ID': 'CHIP_ID',
    'FACE_ID': 'FACE_ID',
    'NONE': 'NONE',
    'GTTT_EXPIRE': 'GTTT_EXPIRE',
    'GTTT_EXPIRING_SOON': 'GTTT_EXPIRING_SOON',
  };

  @override
  final Iterable<Type> types = const <Type>[User2345VerifyType];
  @override
  final String wireName = 'User2345VerifyType';

  @override
  Object serialize(Serializers serializers, User2345VerifyType object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  User2345VerifyType deserialize(Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      User2345VerifyType.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
