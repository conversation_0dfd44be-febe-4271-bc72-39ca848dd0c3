//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'create_quick_verify_request.g.dart';

/// CreateQuickVerifyRequest
///
/// Properties:
/// * [id]
/// * [identity]
/// * [status]
@BuiltValue()
abstract class CreateQuickVerifyRequest
    implements
        Built<CreateQuickVerifyRequest, CreateQuickVerifyRequestBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  @BuiltValueField(wireName: r'identity')
  String? get identity;

  @BuiltValueField(wireName: r'status')
  CreateQuickVerifyRequestStatusEnum? get status;
  // enum statusEnum {  PROCESSING,  SMS_OTP_VERIFIED,  EMAIL_OTP_VERIFIED,  SUCCESS,  FAILURE,  CANCEL,  CONFIRMED,  REJECTED,  PENDING,  };

  CreateQuickVerifyRequest._();

  factory CreateQuickVerifyRequest(
          [void updates(CreateQuickVerifyRequestBuilder b)]) =
      _$CreateQuickVerifyRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CreateQuickVerifyRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CreateQuickVerifyRequest> get serializer =>
      _$CreateQuickVerifyRequestSerializer();
}

class _$CreateQuickVerifyRequestSerializer
    implements PrimitiveSerializer<CreateQuickVerifyRequest> {
  @override
  final Iterable<Type> types = const [
    CreateQuickVerifyRequest,
    _$CreateQuickVerifyRequest
  ];

  @override
  final String wireName = r'CreateQuickVerifyRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CreateQuickVerifyRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.identity != null) {
      yield r'identity';
      yield serializers.serialize(
        object.identity,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.status != null) {
      yield r'status';
      yield serializers.serialize(
        object.status,
        specifiedType:
            const FullType.nullable(CreateQuickVerifyRequestStatusEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CreateQuickVerifyRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CreateQuickVerifyRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.id = valueDes;
          break;
        case r'identity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.identity = valueDes;
          break;
        case r'status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(CreateQuickVerifyRequestStatusEnum),
          ) as CreateQuickVerifyRequestStatusEnum?;
          if (valueDes == null) continue;
          result.status = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CreateQuickVerifyRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CreateQuickVerifyRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class CreateQuickVerifyRequestStatusEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PROCESSING')
  static const CreateQuickVerifyRequestStatusEnum PROCESSING =
      _$createQuickVerifyRequestStatusEnum_PROCESSING;
  @BuiltValueEnumConst(wireName: r'SMS_OTP_VERIFIED')
  static const CreateQuickVerifyRequestStatusEnum SMS_OTP_VERIFIED =
      _$createQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'EMAIL_OTP_VERIFIED')
  static const CreateQuickVerifyRequestStatusEnum EMAIL_OTP_VERIFIED =
      _$createQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'SUCCESS')
  static const CreateQuickVerifyRequestStatusEnum SUCCESS =
      _$createQuickVerifyRequestStatusEnum_SUCCESS;
  @BuiltValueEnumConst(wireName: r'FAILURE')
  static const CreateQuickVerifyRequestStatusEnum FAILURE =
      _$createQuickVerifyRequestStatusEnum_FAILURE;
  @BuiltValueEnumConst(wireName: r'CANCEL')
  static const CreateQuickVerifyRequestStatusEnum CANCEL =
      _$createQuickVerifyRequestStatusEnum_CANCEL;
  @BuiltValueEnumConst(wireName: r'CONFIRMED')
  static const CreateQuickVerifyRequestStatusEnum CONFIRMED =
      _$createQuickVerifyRequestStatusEnum_CONFIRMED;
  @BuiltValueEnumConst(wireName: r'REJECTED')
  static const CreateQuickVerifyRequestStatusEnum REJECTED =
      _$createQuickVerifyRequestStatusEnum_REJECTED;
  @BuiltValueEnumConst(wireName: r'PENDING')
  static const CreateQuickVerifyRequestStatusEnum PENDING =
      _$createQuickVerifyRequestStatusEnum_PENDING;

  static Serializer<CreateQuickVerifyRequestStatusEnum> get serializer =>
      _$createQuickVerifyRequestStatusEnumSerializer;

  const CreateQuickVerifyRequestStatusEnum._(String name) : super(name);

  static BuiltSet<CreateQuickVerifyRequestStatusEnum> get values =>
      _$createQuickVerifyRequestStatusEnumValues;
  static CreateQuickVerifyRequestStatusEnum valueOf(String name) =>
      _$createQuickVerifyRequestStatusEnumValueOf(name);
}
