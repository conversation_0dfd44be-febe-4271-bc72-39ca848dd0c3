//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'user2345_verify_type.g.dart';

class User2345VerifyType extends EnumClass {
  @BuiltValueEnumConst(wireName: r'CHIP_ID')
  static const User2345VerifyType CHIP_ID = _$CHIP_ID;
  @BuiltValueEnumConst(wireName: r'FACE_ID')
  static const User2345VerifyType FACE_ID = _$FACE_ID;
  @BuiltValueEnumConst(wireName: r'NONE')
  static const User2345VerifyType NONE = _$NONE;
  @BuiltValueEnumConst(wireName: r'GTTT_EXPIRE')
  static const User2345VerifyType GTTT_EXPIRE = _$GTTT_EXPIRE;
  @BuiltValueEnumConst(wireName: r'GTTT_EXPIRING_SOON')
  static const User2345VerifyType GTTT_EXPIRING_SOON = _$GTTT_EXPIRING_SOON;

  static Serializer<User2345VerifyType> get serializer =>
      _$user2345VerifyTypeSerializer;

  const User2345VerifyType._(String name) : super(name);

  static BuiltSet<User2345VerifyType> get values => _$values;
  static User2345VerifyType valueOf(String name) => _$valueOf(name);
}

/// Optionally, enum_class can generate a mixin to go with your enum for use
/// with Angular. It exposes your enum constants as getters. So, if you mix it
/// in to your Dart component class, the values become available to the
/// corresponding Angular template.
///
/// Trigger mixin generation by writing a line like this one next to your enum.
abstract class User2345VerifyTypeMixin = Object with _$User2345VerifyTypeMixin;
