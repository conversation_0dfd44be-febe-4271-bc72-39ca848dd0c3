// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_user_and_send_otp_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const CheckUserAndSendOtpRequestIdentityTypeEnum
    _$checkUserAndSendOtpRequestIdentityTypeEnum_PHONE =
    const CheckUserAndSendOtpRequestIdentityTypeEnum._('PHONE');
const CheckUserAndSendOtpRequestIdentityTypeEnum
    _$checkUserAndSendOtpRequestIdentityTypeEnum_ID_CARD =
    const CheckUserAndSendOtpRequestIdentityTypeEnum._('ID_CARD');

CheckUserAndSendOtpRequestIdentityTypeEnum
    _$checkUserAndSendOtpRequestIdentityTypeEnumValueOf(String name) {
  switch (name) {
    case 'PHONE':
      return _$checkUserAndSendOtpRequestIdentityTypeEnum_PHONE;
    case 'ID_CARD':
      return _$checkUserAndSendOtpRequestIdentityTypeEnum_ID_CARD;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<CheckUserAndSendOtpRequestIdentityTypeEnum>
    _$checkUserAndSendOtpRequestIdentityTypeEnumValues = new BuiltSet<
        CheckUserAndSendOtpRequestIdentityTypeEnum>(const <CheckUserAndSendOtpRequestIdentityTypeEnum>[
  _$checkUserAndSendOtpRequestIdentityTypeEnum_PHONE,
  _$checkUserAndSendOtpRequestIdentityTypeEnum_ID_CARD,
]);

Serializer<CheckUserAndSendOtpRequestIdentityTypeEnum>
    _$checkUserAndSendOtpRequestIdentityTypeEnumSerializer =
    new _$CheckUserAndSendOtpRequestIdentityTypeEnumSerializer();

class _$CheckUserAndSendOtpRequestIdentityTypeEnumSerializer
    implements PrimitiveSerializer<CheckUserAndSendOtpRequestIdentityTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PHONE': 'PHONE',
    'ID_CARD': 'ID_CARD',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PHONE': 'PHONE',
    'ID_CARD': 'ID_CARD',
  };

  @override
  final Iterable<Type> types = const <Type>[
    CheckUserAndSendOtpRequestIdentityTypeEnum
  ];
  @override
  final String wireName = 'CheckUserAndSendOtpRequestIdentityTypeEnum';

  @override
  Object serialize(Serializers serializers,
          CheckUserAndSendOtpRequestIdentityTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  CheckUserAndSendOtpRequestIdentityTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      CheckUserAndSendOtpRequestIdentityTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$CheckUserAndSendOtpRequest extends CheckUserAndSendOtpRequest {
  @override
  final CheckUserAndSendOtpRequestIdentityTypeEnum? identityType;
  @override
  final String? identity;

  factory _$CheckUserAndSendOtpRequest(
          [void Function(CheckUserAndSendOtpRequestBuilder)? updates]) =>
      (new CheckUserAndSendOtpRequestBuilder()..update(updates))._build();

  _$CheckUserAndSendOtpRequest._({this.identityType, this.identity})
      : super._();

  @override
  CheckUserAndSendOtpRequest rebuild(
          void Function(CheckUserAndSendOtpRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CheckUserAndSendOtpRequestBuilder toBuilder() =>
      new CheckUserAndSendOtpRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CheckUserAndSendOtpRequest &&
        identityType == other.identityType &&
        identity == other.identity;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, identityType.hashCode);
    _$hash = $jc(_$hash, identity.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CheckUserAndSendOtpRequest')
          ..add('identityType', identityType)
          ..add('identity', identity))
        .toString();
  }
}

class CheckUserAndSendOtpRequestBuilder
    implements
        Builder<CheckUserAndSendOtpRequest, CheckUserAndSendOtpRequestBuilder> {
  _$CheckUserAndSendOtpRequest? _$v;

  CheckUserAndSendOtpRequestIdentityTypeEnum? _identityType;
  CheckUserAndSendOtpRequestIdentityTypeEnum? get identityType =>
      _$this._identityType;
  set identityType(CheckUserAndSendOtpRequestIdentityTypeEnum? identityType) =>
      _$this._identityType = identityType;

  String? _identity;
  String? get identity => _$this._identity;
  set identity(String? identity) => _$this._identity = identity;

  CheckUserAndSendOtpRequestBuilder() {
    CheckUserAndSendOtpRequest._defaults(this);
  }

  CheckUserAndSendOtpRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _identityType = $v.identityType;
      _identity = $v.identity;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CheckUserAndSendOtpRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CheckUserAndSendOtpRequest;
  }

  @override
  void update(void Function(CheckUserAndSendOtpRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CheckUserAndSendOtpRequest build() => _build();

  _$CheckUserAndSendOtpRequest _build() {
    final _$result = _$v ??
        new _$CheckUserAndSendOtpRequest._(
            identityType: identityType, identity: identity);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
