//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'check_user_and_send_otp_request.g.dart';

/// CheckUserAndSendOtpRequest
///
/// Properties:
/// * [identityType]
/// * [identity]
@BuiltValue()
abstract class CheckUserAndSendOtpRequest
    implements
        Built<CheckUserAndSendOtpRequest, CheckUserAndSendOtpRequestBuilder> {
  @BuiltValueField(wireName: r'identityType')
  CheckUserAndSendOtpRequestIdentityTypeEnum? get identityType;
  // enum identityTypeEnum {  PHONE,  ID_CARD,  };

  @BuiltValueField(wireName: r'identity')
  String? get identity;

  CheckUserAndSendOtpRequest._();

  factory CheckUserAndSendOtpRequest(
          [void updates(CheckUserAndSendOtpRequestBuilder b)]) =
      _$CheckUserAndSendOtpRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CheckUserAndSendOtpRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CheckUserAndSendOtpRequest> get serializer =>
      _$CheckUserAndSendOtpRequestSerializer();
}

class _$CheckUserAndSendOtpRequestSerializer
    implements PrimitiveSerializer<CheckUserAndSendOtpRequest> {
  @override
  final Iterable<Type> types = const [
    CheckUserAndSendOtpRequest,
    _$CheckUserAndSendOtpRequest
  ];

  @override
  final String wireName = r'CheckUserAndSendOtpRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CheckUserAndSendOtpRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.identityType != null) {
      yield r'identityType';
      yield serializers.serialize(
        object.identityType,
        specifiedType:
            const FullType.nullable(CheckUserAndSendOtpRequestIdentityTypeEnum),
      );
    }
    if (object.identity != null) {
      yield r'identity';
      yield serializers.serialize(
        object.identity,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CheckUserAndSendOtpRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CheckUserAndSendOtpRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'identityType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                CheckUserAndSendOtpRequestIdentityTypeEnum),
          ) as CheckUserAndSendOtpRequestIdentityTypeEnum?;
          if (valueDes == null) continue;
          result.identityType = valueDes;
          break;
        case r'identity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.identity = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CheckUserAndSendOtpRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CheckUserAndSendOtpRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class CheckUserAndSendOtpRequestIdentityTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PHONE')
  static const CheckUserAndSendOtpRequestIdentityTypeEnum PHONE =
      _$checkUserAndSendOtpRequestIdentityTypeEnum_PHONE;
  @BuiltValueEnumConst(wireName: r'ID_CARD')
  static const CheckUserAndSendOtpRequestIdentityTypeEnum ID_CARD =
      _$checkUserAndSendOtpRequestIdentityTypeEnum_ID_CARD;

  static Serializer<CheckUserAndSendOtpRequestIdentityTypeEnum>
      get serializer => _$checkUserAndSendOtpRequestIdentityTypeEnumSerializer;

  const CheckUserAndSendOtpRequestIdentityTypeEnum._(String name) : super(name);

  static BuiltSet<CheckUserAndSendOtpRequestIdentityTypeEnum> get values =>
      _$checkUserAndSendOtpRequestIdentityTypeEnumValues;
  static CheckUserAndSendOtpRequestIdentityTypeEnum valueOf(String name) =>
      _$checkUserAndSendOtpRequestIdentityTypeEnumValueOf(name);
}
