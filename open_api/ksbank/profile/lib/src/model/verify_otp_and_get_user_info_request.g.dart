// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verify_otp_and_get_user_info_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const VerifyOtpAndGetUserInfoRequestIdentityTypeEnum
    _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_PHONE =
    const VerifyOtpAndGetUserInfoRequestIdentityTypeEnum._('PHONE');
const VerifyOtpAndGetUserInfoRequestIdentityTypeEnum
    _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_ID_CARD =
    const VerifyOtpAndGetUserInfoRequestIdentityTypeEnum._('ID_CARD');

VerifyOtpAndGetUserInfoRequestIdentityTypeEnum
    _$verifyOtpAndGetUserInfoRequestIdentityTypeEnumValueOf(String name) {
  switch (name) {
    case 'PHONE':
      return _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_PHONE;
    case 'ID_CARD':
      return _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_ID_CARD;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<VerifyOtpAndGetUserInfoRequestIdentityTypeEnum>
    _$verifyOtpAndGetUserInfoRequestIdentityTypeEnumValues = new BuiltSet<
        VerifyOtpAndGetUserInfoRequestIdentityTypeEnum>(const <VerifyOtpAndGetUserInfoRequestIdentityTypeEnum>[
  _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_PHONE,
  _$verifyOtpAndGetUserInfoRequestIdentityTypeEnum_ID_CARD,
]);

Serializer<VerifyOtpAndGetUserInfoRequestIdentityTypeEnum>
    _$verifyOtpAndGetUserInfoRequestIdentityTypeEnumSerializer =
    new _$VerifyOtpAndGetUserInfoRequestIdentityTypeEnumSerializer();

class _$VerifyOtpAndGetUserInfoRequestIdentityTypeEnumSerializer
    implements
        PrimitiveSerializer<VerifyOtpAndGetUserInfoRequestIdentityTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PHONE': 'PHONE',
    'ID_CARD': 'ID_CARD',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PHONE': 'PHONE',
    'ID_CARD': 'ID_CARD',
  };

  @override
  final Iterable<Type> types = const <Type>[
    VerifyOtpAndGetUserInfoRequestIdentityTypeEnum
  ];
  @override
  final String wireName = 'VerifyOtpAndGetUserInfoRequestIdentityTypeEnum';

  @override
  Object serialize(Serializers serializers,
          VerifyOtpAndGetUserInfoRequestIdentityTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  VerifyOtpAndGetUserInfoRequestIdentityTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      VerifyOtpAndGetUserInfoRequestIdentityTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$VerifyOtpAndGetUserInfoRequest extends VerifyOtpAndGetUserInfoRequest {
  @override
  final VerifyOtpAndGetUserInfoRequestIdentityTypeEnum? identityType;
  @override
  final String? identity;
  @override
  final String? otp;

  factory _$VerifyOtpAndGetUserInfoRequest(
          [void Function(VerifyOtpAndGetUserInfoRequestBuilder)? updates]) =>
      (new VerifyOtpAndGetUserInfoRequestBuilder()..update(updates))._build();

  _$VerifyOtpAndGetUserInfoRequest._(
      {this.identityType, this.identity, this.otp})
      : super._();

  @override
  VerifyOtpAndGetUserInfoRequest rebuild(
          void Function(VerifyOtpAndGetUserInfoRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  VerifyOtpAndGetUserInfoRequestBuilder toBuilder() =>
      new VerifyOtpAndGetUserInfoRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is VerifyOtpAndGetUserInfoRequest &&
        identityType == other.identityType &&
        identity == other.identity &&
        otp == other.otp;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, identityType.hashCode);
    _$hash = $jc(_$hash, identity.hashCode);
    _$hash = $jc(_$hash, otp.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'VerifyOtpAndGetUserInfoRequest')
          ..add('identityType', identityType)
          ..add('identity', identity)
          ..add('otp', otp))
        .toString();
  }
}

class VerifyOtpAndGetUserInfoRequestBuilder
    implements
        Builder<VerifyOtpAndGetUserInfoRequest,
            VerifyOtpAndGetUserInfoRequestBuilder> {
  _$VerifyOtpAndGetUserInfoRequest? _$v;

  VerifyOtpAndGetUserInfoRequestIdentityTypeEnum? _identityType;
  VerifyOtpAndGetUserInfoRequestIdentityTypeEnum? get identityType =>
      _$this._identityType;
  set identityType(
          VerifyOtpAndGetUserInfoRequestIdentityTypeEnum? identityType) =>
      _$this._identityType = identityType;

  String? _identity;
  String? get identity => _$this._identity;
  set identity(String? identity) => _$this._identity = identity;

  String? _otp;
  String? get otp => _$this._otp;
  set otp(String? otp) => _$this._otp = otp;

  VerifyOtpAndGetUserInfoRequestBuilder() {
    VerifyOtpAndGetUserInfoRequest._defaults(this);
  }

  VerifyOtpAndGetUserInfoRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _identityType = $v.identityType;
      _identity = $v.identity;
      _otp = $v.otp;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(VerifyOtpAndGetUserInfoRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$VerifyOtpAndGetUserInfoRequest;
  }

  @override
  void update(void Function(VerifyOtpAndGetUserInfoRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  VerifyOtpAndGetUserInfoRequest build() => _build();

  _$VerifyOtpAndGetUserInfoRequest _build() {
    final _$result = _$v ??
        new _$VerifyOtpAndGetUserInfoRequest._(
            identityType: identityType, identity: identity, otp: otp);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
