// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_user_and_send_otp_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_PROCESSING =
    const CheckUserAndSendOtpResponseStatusEnum._('PROCESSING');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_SMS_OTP_VERIFIED =
    const CheckUserAndSendOtpResponseStatusEnum._('SMS_OTP_VERIFIED');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_EMAIL_OTP_VERIFIED =
    const CheckUserAndSendOtpResponseStatusEnum._('EMAIL_OTP_VERIFIED');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_SUCCESS =
    const CheckUserAndSendOtpResponseStatusEnum._('SUCCESS');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_FAILURE =
    const CheckUserAndSendOtpResponseStatusEnum._('FAILURE');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_CANCEL =
    const CheckUserAndSendOtpResponseStatusEnum._('CANCEL');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_CONFIRMED =
    const CheckUserAndSendOtpResponseStatusEnum._('CONFIRMED');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_REJECTED =
    const CheckUserAndSendOtpResponseStatusEnum._('REJECTED');
const CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnum_PENDING =
    const CheckUserAndSendOtpResponseStatusEnum._('PENDING');

CheckUserAndSendOtpResponseStatusEnum
    _$checkUserAndSendOtpResponseStatusEnumValueOf(String name) {
  switch (name) {
    case 'PROCESSING':
      return _$checkUserAndSendOtpResponseStatusEnum_PROCESSING;
    case 'SMS_OTP_VERIFIED':
      return _$checkUserAndSendOtpResponseStatusEnum_SMS_OTP_VERIFIED;
    case 'EMAIL_OTP_VERIFIED':
      return _$checkUserAndSendOtpResponseStatusEnum_EMAIL_OTP_VERIFIED;
    case 'SUCCESS':
      return _$checkUserAndSendOtpResponseStatusEnum_SUCCESS;
    case 'FAILURE':
      return _$checkUserAndSendOtpResponseStatusEnum_FAILURE;
    case 'CANCEL':
      return _$checkUserAndSendOtpResponseStatusEnum_CANCEL;
    case 'CONFIRMED':
      return _$checkUserAndSendOtpResponseStatusEnum_CONFIRMED;
    case 'REJECTED':
      return _$checkUserAndSendOtpResponseStatusEnum_REJECTED;
    case 'PENDING':
      return _$checkUserAndSendOtpResponseStatusEnum_PENDING;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<CheckUserAndSendOtpResponseStatusEnum>
    _$checkUserAndSendOtpResponseStatusEnumValues = new BuiltSet<
        CheckUserAndSendOtpResponseStatusEnum>(const <CheckUserAndSendOtpResponseStatusEnum>[
  _$checkUserAndSendOtpResponseStatusEnum_PROCESSING,
  _$checkUserAndSendOtpResponseStatusEnum_SMS_OTP_VERIFIED,
  _$checkUserAndSendOtpResponseStatusEnum_EMAIL_OTP_VERIFIED,
  _$checkUserAndSendOtpResponseStatusEnum_SUCCESS,
  _$checkUserAndSendOtpResponseStatusEnum_FAILURE,
  _$checkUserAndSendOtpResponseStatusEnum_CANCEL,
  _$checkUserAndSendOtpResponseStatusEnum_CONFIRMED,
  _$checkUserAndSendOtpResponseStatusEnum_REJECTED,
  _$checkUserAndSendOtpResponseStatusEnum_PENDING,
]);

Serializer<CheckUserAndSendOtpResponseStatusEnum>
    _$checkUserAndSendOtpResponseStatusEnumSerializer =
    new _$CheckUserAndSendOtpResponseStatusEnumSerializer();

class _$CheckUserAndSendOtpResponseStatusEnumSerializer
    implements PrimitiveSerializer<CheckUserAndSendOtpResponseStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };

  @override
  final Iterable<Type> types = const <Type>[
    CheckUserAndSendOtpResponseStatusEnum
  ];
  @override
  final String wireName = 'CheckUserAndSendOtpResponseStatusEnum';

  @override
  Object serialize(
          Serializers serializers, CheckUserAndSendOtpResponseStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  CheckUserAndSendOtpResponseStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      CheckUserAndSendOtpResponseStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$CheckUserAndSendOtpResponse extends CheckUserAndSendOtpResponse {
  @override
  final CheckUserAndSendOtpResponseStatusEnum? status;

  factory _$CheckUserAndSendOtpResponse(
          [void Function(CheckUserAndSendOtpResponseBuilder)? updates]) =>
      (new CheckUserAndSendOtpResponseBuilder()..update(updates))._build();

  _$CheckUserAndSendOtpResponse._({this.status}) : super._();

  @override
  CheckUserAndSendOtpResponse rebuild(
          void Function(CheckUserAndSendOtpResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CheckUserAndSendOtpResponseBuilder toBuilder() =>
      new CheckUserAndSendOtpResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CheckUserAndSendOtpResponse && status == other.status;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CheckUserAndSendOtpResponse')
          ..add('status', status))
        .toString();
  }
}

class CheckUserAndSendOtpResponseBuilder
    implements
        Builder<CheckUserAndSendOtpResponse,
            CheckUserAndSendOtpResponseBuilder> {
  _$CheckUserAndSendOtpResponse? _$v;

  CheckUserAndSendOtpResponseStatusEnum? _status;
  CheckUserAndSendOtpResponseStatusEnum? get status => _$this._status;
  set status(CheckUserAndSendOtpResponseStatusEnum? status) =>
      _$this._status = status;

  CheckUserAndSendOtpResponseBuilder() {
    CheckUserAndSendOtpResponse._defaults(this);
  }

  CheckUserAndSendOtpResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _status = $v.status;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CheckUserAndSendOtpResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CheckUserAndSendOtpResponse;
  }

  @override
  void update(void Function(CheckUserAndSendOtpResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CheckUserAndSendOtpResponse build() => _build();

  _$CheckUserAndSendOtpResponse _build() {
    final _$result = _$v ?? new _$CheckUserAndSendOtpResponse._(status: status);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
