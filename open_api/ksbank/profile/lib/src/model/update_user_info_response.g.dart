// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_user_info_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const UpdateUserInfoResponseOtpTypeEnum
    _$updateUserInfoResponseOtpTypeEnum_EMAIL =
    const UpdateUserInfoResponseOtpTypeEnum._('EMAIL');
const UpdateUserInfoResponseOtpTypeEnum
    _$updateUserInfoResponseOtpTypeEnum_SMS =
    const UpdateUserInfoResponseOtpTypeEnum._('SMS');
const UpdateUserInfoResponseOtpTypeEnum
    _$updateUserInfoResponseOtpTypeEnum_SOLF =
    const UpdateUserInfoResponseOtpTypeEnum._('SOLF');
const UpdateUserInfoResponseOtpTypeEnum
    _$updateUserInfoResponseOtpTypeEnum_AUTO =
    const UpdateUserInfoResponseOtpTypeEnum._('AUTO');

UpdateUserInfoResponseOtpTypeEnum _$updateUserInfoResponseOtpTypeEnumValueOf(
    String name) {
  switch (name) {
    case 'EMAIL':
      return _$updateUserInfoResponseOtpTypeEnum_EMAIL;
    case 'SMS':
      return _$updateUserInfoResponseOtpTypeEnum_SMS;
    case 'SOLF':
      return _$updateUserInfoResponseOtpTypeEnum_SOLF;
    case 'AUTO':
      return _$updateUserInfoResponseOtpTypeEnum_AUTO;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<UpdateUserInfoResponseOtpTypeEnum>
    _$updateUserInfoResponseOtpTypeEnumValues = new BuiltSet<
        UpdateUserInfoResponseOtpTypeEnum>(const <UpdateUserInfoResponseOtpTypeEnum>[
  _$updateUserInfoResponseOtpTypeEnum_EMAIL,
  _$updateUserInfoResponseOtpTypeEnum_SMS,
  _$updateUserInfoResponseOtpTypeEnum_SOLF,
  _$updateUserInfoResponseOtpTypeEnum_AUTO,
]);

Serializer<UpdateUserInfoResponseOtpTypeEnum>
    _$updateUserInfoResponseOtpTypeEnumSerializer =
    new _$UpdateUserInfoResponseOtpTypeEnumSerializer();

class _$UpdateUserInfoResponseOtpTypeEnumSerializer
    implements PrimitiveSerializer<UpdateUserInfoResponseOtpTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'EMAIL': 'EMAIL',
    'SMS': 'SMS',
    'SOLF': 'SOLF',
    'AUTO': 'AUTO',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'EMAIL': 'EMAIL',
    'SMS': 'SMS',
    'SOLF': 'SOLF',
    'AUTO': 'AUTO',
  };

  @override
  final Iterable<Type> types = const <Type>[UpdateUserInfoResponseOtpTypeEnum];
  @override
  final String wireName = 'UpdateUserInfoResponseOtpTypeEnum';

  @override
  Object serialize(
          Serializers serializers, UpdateUserInfoResponseOtpTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  UpdateUserInfoResponseOtpTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      UpdateUserInfoResponseOtpTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$UpdateUserInfoResponse extends UpdateUserInfoResponse {
  @override
  final UpdateUserInfoResponseOtpTypeEnum? otpType;

  factory _$UpdateUserInfoResponse(
          [void Function(UpdateUserInfoResponseBuilder)? updates]) =>
      (new UpdateUserInfoResponseBuilder()..update(updates))._build();

  _$UpdateUserInfoResponse._({this.otpType}) : super._();

  @override
  UpdateUserInfoResponse rebuild(
          void Function(UpdateUserInfoResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  UpdateUserInfoResponseBuilder toBuilder() =>
      new UpdateUserInfoResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UpdateUserInfoResponse && otpType == other.otpType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, otpType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'UpdateUserInfoResponse')
          ..add('otpType', otpType))
        .toString();
  }
}

class UpdateUserInfoResponseBuilder
    implements Builder<UpdateUserInfoResponse, UpdateUserInfoResponseBuilder> {
  _$UpdateUserInfoResponse? _$v;

  UpdateUserInfoResponseOtpTypeEnum? _otpType;
  UpdateUserInfoResponseOtpTypeEnum? get otpType => _$this._otpType;
  set otpType(UpdateUserInfoResponseOtpTypeEnum? otpType) =>
      _$this._otpType = otpType;

  UpdateUserInfoResponseBuilder() {
    UpdateUserInfoResponse._defaults(this);
  }

  UpdateUserInfoResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _otpType = $v.otpType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(UpdateUserInfoResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$UpdateUserInfoResponse;
  }

  @override
  void update(void Function(UpdateUserInfoResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  UpdateUserInfoResponse build() => _build();

  _$UpdateUserInfoResponse _build() {
    final _$result = _$v ?? new _$UpdateUserInfoResponse._(otpType: otpType);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
