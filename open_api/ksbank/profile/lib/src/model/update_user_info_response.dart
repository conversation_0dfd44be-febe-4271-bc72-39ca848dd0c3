//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'update_user_info_response.g.dart';

/// UpdateUserInfoResponse
///
/// Properties:
/// * [otpType]
@BuiltValue()
abstract class UpdateUserInfoResponse
    implements Built<UpdateUserInfoResponse, UpdateUserInfoResponseBuilder> {
  @BuiltValueField(wireName: r'otpType')
  UpdateUserInfoResponseOtpTypeEnum? get otpType;
  // enum otpTypeEnum {  EMAIL,  SMS,  SOLF,  AUTO,  };

  UpdateUserInfoResponse._();

  factory UpdateUserInfoResponse(
          [void updates(UpdateUserInfoResponseBuilder b)]) =
      _$UpdateUserInfoResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UpdateUserInfoResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UpdateUserInfoResponse> get serializer =>
      _$UpdateUserInfoResponseSerializer();
}

class _$UpdateUserInfoResponseSerializer
    implements PrimitiveSerializer<UpdateUserInfoResponse> {
  @override
  final Iterable<Type> types = const [
    UpdateUserInfoResponse,
    _$UpdateUserInfoResponse
  ];

  @override
  final String wireName = r'UpdateUserInfoResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UpdateUserInfoResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.otpType != null) {
      yield r'otpType';
      yield serializers.serialize(
        object.otpType,
        specifiedType:
            const FullType.nullable(UpdateUserInfoResponseOtpTypeEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    UpdateUserInfoResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UpdateUserInfoResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'otpType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(UpdateUserInfoResponseOtpTypeEnum),
          ) as UpdateUserInfoResponseOtpTypeEnum?;
          if (valueDes == null) continue;
          result.otpType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UpdateUserInfoResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UpdateUserInfoResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class UpdateUserInfoResponseOtpTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'EMAIL')
  static const UpdateUserInfoResponseOtpTypeEnum EMAIL =
      _$updateUserInfoResponseOtpTypeEnum_EMAIL;
  @BuiltValueEnumConst(wireName: r'SMS')
  static const UpdateUserInfoResponseOtpTypeEnum SMS =
      _$updateUserInfoResponseOtpTypeEnum_SMS;
  @BuiltValueEnumConst(wireName: r'SOLF')
  static const UpdateUserInfoResponseOtpTypeEnum SOLF =
      _$updateUserInfoResponseOtpTypeEnum_SOLF;
  @BuiltValueEnumConst(wireName: r'AUTO')
  static const UpdateUserInfoResponseOtpTypeEnum AUTO =
      _$updateUserInfoResponseOtpTypeEnum_AUTO;

  static Serializer<UpdateUserInfoResponseOtpTypeEnum> get serializer =>
      _$updateUserInfoResponseOtpTypeEnumSerializer;

  const UpdateUserInfoResponseOtpTypeEnum._(String name) : super(name);

  static BuiltSet<UpdateUserInfoResponseOtpTypeEnum> get values =>
      _$updateUserInfoResponseOtpTypeEnumValues;
  static UpdateUserInfoResponseOtpTypeEnum valueOf(String name) =>
      _$updateUserInfoResponseOtpTypeEnumValueOf(name);
}
