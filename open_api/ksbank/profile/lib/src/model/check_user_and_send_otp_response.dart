//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'check_user_and_send_otp_response.g.dart';

/// CheckUserAndSendOtpResponse
///
/// Properties:
/// * [status]
@BuiltValue()
abstract class CheckUserAndSendOtpResponse
    implements
        Built<CheckUserAndSendOtpResponse, CheckUserAndSendOtpResponseBuilder> {
  @BuiltValueField(wireName: r'status')
  CheckUserAndSendOtpResponseStatusEnum? get status;
  // enum statusEnum {  PROCESSING,  SMS_OTP_VERIFIED,  EMAIL_OTP_VERIFIED,  SUCCESS,  FAILURE,  CANCEL,  CONFIRMED,  REJECTED,  PENDING,  };

  CheckUserAndSendOtpResponse._();

  factory CheckUserAndSendOtpResponse(
          [void updates(CheckUserAndSendOtpResponseBuilder b)]) =
      _$CheckUserAndSendOtpResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(CheckUserAndSendOtpResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<CheckUserAndSendOtpResponse> get serializer =>
      _$CheckUserAndSendOtpResponseSerializer();
}

class _$CheckUserAndSendOtpResponseSerializer
    implements PrimitiveSerializer<CheckUserAndSendOtpResponse> {
  @override
  final Iterable<Type> types = const [
    CheckUserAndSendOtpResponse,
    _$CheckUserAndSendOtpResponse
  ];

  @override
  final String wireName = r'CheckUserAndSendOtpResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    CheckUserAndSendOtpResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.status != null) {
      yield r'status';
      yield serializers.serialize(
        object.status,
        specifiedType:
            const FullType.nullable(CheckUserAndSendOtpResponseStatusEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    CheckUserAndSendOtpResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required CheckUserAndSendOtpResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(CheckUserAndSendOtpResponseStatusEnum),
          ) as CheckUserAndSendOtpResponseStatusEnum?;
          if (valueDes == null) continue;
          result.status = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  CheckUserAndSendOtpResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = CheckUserAndSendOtpResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class CheckUserAndSendOtpResponseStatusEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PROCESSING')
  static const CheckUserAndSendOtpResponseStatusEnum PROCESSING =
      _$checkUserAndSendOtpResponseStatusEnum_PROCESSING;
  @BuiltValueEnumConst(wireName: r'SMS_OTP_VERIFIED')
  static const CheckUserAndSendOtpResponseStatusEnum SMS_OTP_VERIFIED =
      _$checkUserAndSendOtpResponseStatusEnum_SMS_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'EMAIL_OTP_VERIFIED')
  static const CheckUserAndSendOtpResponseStatusEnum EMAIL_OTP_VERIFIED =
      _$checkUserAndSendOtpResponseStatusEnum_EMAIL_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'SUCCESS')
  static const CheckUserAndSendOtpResponseStatusEnum SUCCESS =
      _$checkUserAndSendOtpResponseStatusEnum_SUCCESS;
  @BuiltValueEnumConst(wireName: r'FAILURE')
  static const CheckUserAndSendOtpResponseStatusEnum FAILURE =
      _$checkUserAndSendOtpResponseStatusEnum_FAILURE;
  @BuiltValueEnumConst(wireName: r'CANCEL')
  static const CheckUserAndSendOtpResponseStatusEnum CANCEL =
      _$checkUserAndSendOtpResponseStatusEnum_CANCEL;
  @BuiltValueEnumConst(wireName: r'CONFIRMED')
  static const CheckUserAndSendOtpResponseStatusEnum CONFIRMED =
      _$checkUserAndSendOtpResponseStatusEnum_CONFIRMED;
  @BuiltValueEnumConst(wireName: r'REJECTED')
  static const CheckUserAndSendOtpResponseStatusEnum REJECTED =
      _$checkUserAndSendOtpResponseStatusEnum_REJECTED;
  @BuiltValueEnumConst(wireName: r'PENDING')
  static const CheckUserAndSendOtpResponseStatusEnum PENDING =
      _$checkUserAndSendOtpResponseStatusEnum_PENDING;

  static Serializer<CheckUserAndSendOtpResponseStatusEnum> get serializer =>
      _$checkUserAndSendOtpResponseStatusEnumSerializer;

  const CheckUserAndSendOtpResponseStatusEnum._(String name) : super(name);

  static BuiltSet<CheckUserAndSendOtpResponseStatusEnum> get values =>
      _$checkUserAndSendOtpResponseStatusEnumValues;
  static CheckUserAndSendOtpResponseStatusEnum valueOf(String name) =>
      _$checkUserAndSendOtpResponseStatusEnumValueOf(name);
}
