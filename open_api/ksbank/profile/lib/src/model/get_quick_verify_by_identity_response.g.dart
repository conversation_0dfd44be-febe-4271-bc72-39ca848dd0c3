// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_quick_verify_by_identity_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_PROCESSING =
    const GetQuickVerifyByIdentityResponseStatusEnum._('PROCESSING');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_SMS_OTP_VERIFIED =
    const GetQuickVerifyByIdentityResponseStatusEnum._('SMS_OTP_VERIFIED');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_EMAIL_OTP_VERIFIED =
    const GetQuickVerifyByIdentityResponseStatusEnum._('EMAIL_OTP_VERIFIED');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_SUCCESS =
    const GetQuickVerifyByIdentityResponseStatusEnum._('SUCCESS');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_FAILURE =
    const GetQuickVerifyByIdentityResponseStatusEnum._('FAILURE');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_CANCEL =
    const GetQuickVerifyByIdentityResponseStatusEnum._('CANCEL');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_CONFIRMED =
    const GetQuickVerifyByIdentityResponseStatusEnum._('CONFIRMED');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_REJECTED =
    const GetQuickVerifyByIdentityResponseStatusEnum._('REJECTED');
const GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnum_PENDING =
    const GetQuickVerifyByIdentityResponseStatusEnum._('PENDING');

GetQuickVerifyByIdentityResponseStatusEnum
    _$getQuickVerifyByIdentityResponseStatusEnumValueOf(String name) {
  switch (name) {
    case 'PROCESSING':
      return _$getQuickVerifyByIdentityResponseStatusEnum_PROCESSING;
    case 'SMS_OTP_VERIFIED':
      return _$getQuickVerifyByIdentityResponseStatusEnum_SMS_OTP_VERIFIED;
    case 'EMAIL_OTP_VERIFIED':
      return _$getQuickVerifyByIdentityResponseStatusEnum_EMAIL_OTP_VERIFIED;
    case 'SUCCESS':
      return _$getQuickVerifyByIdentityResponseStatusEnum_SUCCESS;
    case 'FAILURE':
      return _$getQuickVerifyByIdentityResponseStatusEnum_FAILURE;
    case 'CANCEL':
      return _$getQuickVerifyByIdentityResponseStatusEnum_CANCEL;
    case 'CONFIRMED':
      return _$getQuickVerifyByIdentityResponseStatusEnum_CONFIRMED;
    case 'REJECTED':
      return _$getQuickVerifyByIdentityResponseStatusEnum_REJECTED;
    case 'PENDING':
      return _$getQuickVerifyByIdentityResponseStatusEnum_PENDING;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<GetQuickVerifyByIdentityResponseStatusEnum>
    _$getQuickVerifyByIdentityResponseStatusEnumValues = new BuiltSet<
        GetQuickVerifyByIdentityResponseStatusEnum>(const <GetQuickVerifyByIdentityResponseStatusEnum>[
  _$getQuickVerifyByIdentityResponseStatusEnum_PROCESSING,
  _$getQuickVerifyByIdentityResponseStatusEnum_SMS_OTP_VERIFIED,
  _$getQuickVerifyByIdentityResponseStatusEnum_EMAIL_OTP_VERIFIED,
  _$getQuickVerifyByIdentityResponseStatusEnum_SUCCESS,
  _$getQuickVerifyByIdentityResponseStatusEnum_FAILURE,
  _$getQuickVerifyByIdentityResponseStatusEnum_CANCEL,
  _$getQuickVerifyByIdentityResponseStatusEnum_CONFIRMED,
  _$getQuickVerifyByIdentityResponseStatusEnum_REJECTED,
  _$getQuickVerifyByIdentityResponseStatusEnum_PENDING,
]);

Serializer<GetQuickVerifyByIdentityResponseStatusEnum>
    _$getQuickVerifyByIdentityResponseStatusEnumSerializer =
    new _$GetQuickVerifyByIdentityResponseStatusEnumSerializer();

class _$GetQuickVerifyByIdentityResponseStatusEnumSerializer
    implements PrimitiveSerializer<GetQuickVerifyByIdentityResponseStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };

  @override
  final Iterable<Type> types = const <Type>[
    GetQuickVerifyByIdentityResponseStatusEnum
  ];
  @override
  final String wireName = 'GetQuickVerifyByIdentityResponseStatusEnum';

  @override
  Object serialize(Serializers serializers,
          GetQuickVerifyByIdentityResponseStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  GetQuickVerifyByIdentityResponseStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      GetQuickVerifyByIdentityResponseStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$GetQuickVerifyByIdentityResponse
    extends GetQuickVerifyByIdentityResponse {
  @override
  final String? id;
  @override
  final String? identity;
  @override
  final GetQuickVerifyByIdentityResponseStatusEnum? status;

  factory _$GetQuickVerifyByIdentityResponse(
          [void Function(GetQuickVerifyByIdentityResponseBuilder)? updates]) =>
      (new GetQuickVerifyByIdentityResponseBuilder()..update(updates))._build();

  _$GetQuickVerifyByIdentityResponse._({this.id, this.identity, this.status})
      : super._();

  @override
  GetQuickVerifyByIdentityResponse rebuild(
          void Function(GetQuickVerifyByIdentityResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetQuickVerifyByIdentityResponseBuilder toBuilder() =>
      new GetQuickVerifyByIdentityResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetQuickVerifyByIdentityResponse &&
        id == other.id &&
        identity == other.identity &&
        status == other.status;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, identity.hashCode);
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetQuickVerifyByIdentityResponse')
          ..add('id', id)
          ..add('identity', identity)
          ..add('status', status))
        .toString();
  }
}

class GetQuickVerifyByIdentityResponseBuilder
    implements
        Builder<GetQuickVerifyByIdentityResponse,
            GetQuickVerifyByIdentityResponseBuilder> {
  _$GetQuickVerifyByIdentityResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _identity;
  String? get identity => _$this._identity;
  set identity(String? identity) => _$this._identity = identity;

  GetQuickVerifyByIdentityResponseStatusEnum? _status;
  GetQuickVerifyByIdentityResponseStatusEnum? get status => _$this._status;
  set status(GetQuickVerifyByIdentityResponseStatusEnum? status) =>
      _$this._status = status;

  GetQuickVerifyByIdentityResponseBuilder() {
    GetQuickVerifyByIdentityResponse._defaults(this);
  }

  GetQuickVerifyByIdentityResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _identity = $v.identity;
      _status = $v.status;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetQuickVerifyByIdentityResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetQuickVerifyByIdentityResponse;
  }

  @override
  void update(void Function(GetQuickVerifyByIdentityResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetQuickVerifyByIdentityResponse build() => _build();

  _$GetQuickVerifyByIdentityResponse _build() {
    final _$result = _$v ??
        new _$GetQuickVerifyByIdentityResponse._(
            id: id, identity: identity, status: status);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
