// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_liveness_type_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const GetLivenessTypeResponseLivenessTypeEnum
    _$getLivenessTypeResponseLivenessTypeEnum_FACE =
    const GetLivenessTypeResponseLivenessTypeEnum._('FACE');
const GetLivenessTypeResponseLivenessTypeEnum
    _$getLivenessTypeResponseLivenessTypeEnum_NOSE =
    const GetLivenessTypeResponseLivenessTypeEnum._('NOSE');

GetLivenessTypeResponseLivenessTypeEnum
    _$getLivenessTypeResponseLivenessTypeEnumValueOf(String name) {
  switch (name) {
    case 'FACE':
      return _$getLivenessTypeResponseLivenessTypeEnum_FACE;
    case 'NOSE':
      return _$getLivenessTypeResponseLivenessTypeEnum_NOSE;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<GetLivenessTypeResponseLivenessTypeEnum>
    _$getLivenessTypeResponseLivenessTypeEnumValues = new BuiltSet<
        GetLivenessTypeResponseLivenessTypeEnum>(const <GetLivenessTypeResponseLivenessTypeEnum>[
  _$getLivenessTypeResponseLivenessTypeEnum_FACE,
  _$getLivenessTypeResponseLivenessTypeEnum_NOSE,
]);

Serializer<GetLivenessTypeResponseLivenessTypeEnum>
    _$getLivenessTypeResponseLivenessTypeEnumSerializer =
    new _$GetLivenessTypeResponseLivenessTypeEnumSerializer();

class _$GetLivenessTypeResponseLivenessTypeEnumSerializer
    implements PrimitiveSerializer<GetLivenessTypeResponseLivenessTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'FACE': 'TURN_FACE',
    'NOSE': 'TURN_NOSE',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'TURN_FACE': 'FACE',
    'TURN_NOSE': 'NOSE',
  };

  @override
  final Iterable<Type> types = const <Type>[
    GetLivenessTypeResponseLivenessTypeEnum
  ];
  @override
  final String wireName = 'GetLivenessTypeResponseLivenessTypeEnum';

  @override
  Object serialize(Serializers serializers,
          GetLivenessTypeResponseLivenessTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  GetLivenessTypeResponseLivenessTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      GetLivenessTypeResponseLivenessTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$GetLivenessTypeResponse extends GetLivenessTypeResponse {
  @override
  final String? phone;
  @override
  final GetLivenessTypeResponseLivenessTypeEnum? livenessType;

  factory _$GetLivenessTypeResponse(
          [void Function(GetLivenessTypeResponseBuilder)? updates]) =>
      (new GetLivenessTypeResponseBuilder()..update(updates))._build();

  _$GetLivenessTypeResponse._({this.phone, this.livenessType}) : super._();

  @override
  GetLivenessTypeResponse rebuild(
          void Function(GetLivenessTypeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetLivenessTypeResponseBuilder toBuilder() =>
      new GetLivenessTypeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetLivenessTypeResponse &&
        phone == other.phone &&
        livenessType == other.livenessType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, phone.hashCode);
    _$hash = $jc(_$hash, livenessType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetLivenessTypeResponse')
          ..add('phone', phone)
          ..add('livenessType', livenessType))
        .toString();
  }
}

class GetLivenessTypeResponseBuilder
    implements
        Builder<GetLivenessTypeResponse, GetLivenessTypeResponseBuilder> {
  _$GetLivenessTypeResponse? _$v;

  String? _phone;
  String? get phone => _$this._phone;
  set phone(String? phone) => _$this._phone = phone;

  GetLivenessTypeResponseLivenessTypeEnum? _livenessType;
  GetLivenessTypeResponseLivenessTypeEnum? get livenessType =>
      _$this._livenessType;
  set livenessType(GetLivenessTypeResponseLivenessTypeEnum? livenessType) =>
      _$this._livenessType = livenessType;

  GetLivenessTypeResponseBuilder() {
    GetLivenessTypeResponse._defaults(this);
  }

  GetLivenessTypeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _phone = $v.phone;
      _livenessType = $v.livenessType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetLivenessTypeResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$GetLivenessTypeResponse;
  }

  @override
  void update(void Function(GetLivenessTypeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetLivenessTypeResponse build() => _build();

  _$GetLivenessTypeResponse _build() {
    final _$result = _$v ??
        new _$GetLivenessTypeResponse._(
            phone: phone, livenessType: livenessType);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
