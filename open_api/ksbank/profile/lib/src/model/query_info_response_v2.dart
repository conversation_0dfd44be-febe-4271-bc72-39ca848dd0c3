//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:ksbank_api_profile/src/model/date.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'query_info_response_v2.g.dart';

/// QueryInfoResponseV2
///
/// Properties:
/// * [loginType]
/// * [fullName]
/// * [avatarUrl]
/// * [phoneNumber]
/// * [otpMethod]
/// * [email]
/// * [verifiedEmail]
/// * [aliasName]
/// * [username]
/// * [facebookLinked]
/// * [facebookId]
/// * [facebookName]
/// * [googleLinked]
/// * [googleId]
/// * [googleName]
/// * [sex]
/// * [birthday]
/// * [idCardType]
/// * [idCardNo]
/// * [idCardIssueDate]
/// * [idCardExpireDate]
/// * [idCardIssuePlace]
/// * [resAddr]
/// * [resCity]
/// * [nationCode]
/// * [taxCode]
/// * [provinceCode]
/// * [districtCode]
/// * [wardVlg]
/// * [street]
/// * [no123]
/// * [branchCode]
/// * [fullAddress]
/// * [enabled2fa]
/// * [bankVerified]
/// * [createdFrom]
/// * [lockedOldApp]
/// * [ekycId]
/// * [suspiciousEkycStatus]
/// * [customerGroupUserId]
/// * [organization]
/// * [identityStatus]
/// * [isVerifiedSTH] - Trạng thái xác thực STH. Lấy theo trạng thái xác thực BCA.
/// * [isNobleEmployee] - Có phải CBBH hay không.
/// * [passEkyc]
@BuiltValue()
abstract class QueryInfoResponseV2
    implements Built<QueryInfoResponseV2, QueryInfoResponseV2Builder> {
  @BuiltValueField(wireName: r'loginType')
  int? get loginType;

  @BuiltValueField(wireName: r'fullName')
  String? get fullName;

  @BuiltValueField(wireName: r'avatarUrl')
  String? get avatarUrl;

  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  @BuiltValueField(wireName: r'otpMethod')
  String? get otpMethod;

  @BuiltValueField(wireName: r'email')
  String? get email;

  @BuiltValueField(wireName: r'verifiedEmail')
  bool? get verifiedEmail;

  @BuiltValueField(wireName: r'aliasName')
  String? get aliasName;

  @BuiltValueField(wireName: r'username')
  String? get username;

  @BuiltValueField(wireName: r'facebookLinked')
  bool? get facebookLinked;

  @BuiltValueField(wireName: r'facebookId')
  String? get facebookId;

  @BuiltValueField(wireName: r'facebookName')
  String? get facebookName;

  @BuiltValueField(wireName: r'googleLinked')
  bool? get googleLinked;

  @BuiltValueField(wireName: r'googleId')
  String? get googleId;

  @BuiltValueField(wireName: r'googleName')
  String? get googleName;

  @BuiltValueField(wireName: r'sex')
  String? get sex;

  @BuiltValueField(wireName: r'birthday')
  Date? get birthday;

  @BuiltValueField(wireName: r'idCardType')
  String? get idCardType;

  @BuiltValueField(wireName: r'idCardNo')
  String? get idCardNo;

  @BuiltValueField(wireName: r'idCardIssueDate')
  Date? get idCardIssueDate;

  @BuiltValueField(wireName: r'idCardExpireDate')
  Date? get idCardExpireDate;

  @BuiltValueField(wireName: r'idCardIssuePlace')
  String? get idCardIssuePlace;

  @BuiltValueField(wireName: r'resAddr')
  String? get resAddr;

  @BuiltValueField(wireName: r'resCity')
  String? get resCity;

  @BuiltValueField(wireName: r'nationCode')
  String? get nationCode;

  @BuiltValueField(wireName: r'taxCode')
  String? get taxCode;

  @BuiltValueField(wireName: r'provinceCode')
  String? get provinceCode;

  @BuiltValueField(wireName: r'districtCode')
  String? get districtCode;

  @BuiltValueField(wireName: r'wardVlg')
  String? get wardVlg;

  @BuiltValueField(wireName: r'street')
  String? get street;

  @BuiltValueField(wireName: r'no123')
  String? get no123;

  @BuiltValueField(wireName: r'branchCode')
  String? get branchCode;

  @BuiltValueField(wireName: r'fullAddress')
  String? get fullAddress;

  @BuiltValueField(wireName: r'enabled2fa')
  bool? get enabled2fa;

  @BuiltValueField(wireName: r'bankVerified')
  bool? get bankVerified;

  @BuiltValueField(wireName: r'createdFrom')
  String? get createdFrom;

  @BuiltValueField(wireName: r'lockedOldApp')
  bool? get lockedOldApp;

  @BuiltValueField(wireName: r'ekycId')
  String? get ekycId;

  @BuiltValueField(wireName: r'suspiciousEkycStatus')
  String? get suspiciousEkycStatus;

  @BuiltValueField(wireName: r'customerGroupUserId')
  String? get customerGroupUserId;

  @BuiltValueField(wireName: r'organization')
  QueryInfoResponseV2OrganizationEnum? get organization;
  // enum organizationEnum {  VNPOST,  KL,  OTHER,  };

  @BuiltValueField(wireName: r'identityStatus')
  String? get identityStatus;

  /// Trạng thái xác thực STH. Lấy theo trạng thái xác thực BCA.
  @BuiltValueField(wireName: r'isVerifiedSTH')
  bool? get isVerifiedSTH;

  /// Có phải CBBH hay không.
  @BuiltValueField(wireName: r'isNobleEmployee')
  bool? get isNobleEmployee;

  @BuiltValueField(wireName: r'passEkyc')
  bool? get passEkyc;

  QueryInfoResponseV2._();

  factory QueryInfoResponseV2([void updates(QueryInfoResponseV2Builder b)]) =
      _$QueryInfoResponseV2;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(QueryInfoResponseV2Builder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<QueryInfoResponseV2> get serializer =>
      _$QueryInfoResponseV2Serializer();
}

class _$QueryInfoResponseV2Serializer
    implements PrimitiveSerializer<QueryInfoResponseV2> {
  @override
  final Iterable<Type> types = const [
    QueryInfoResponseV2,
    _$QueryInfoResponseV2
  ];

  @override
  final String wireName = r'QueryInfoResponseV2';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    QueryInfoResponseV2 object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.loginType != null) {
      yield r'loginType';
      yield serializers.serialize(
        object.loginType,
        specifiedType: const FullType.nullable(int),
      );
    }
    if (object.fullName != null) {
      yield r'fullName';
      yield serializers.serialize(
        object.fullName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.avatarUrl != null) {
      yield r'avatarUrl';
      yield serializers.serialize(
        object.avatarUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.otpMethod != null) {
      yield r'otpMethod';
      yield serializers.serialize(
        object.otpMethod,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.verifiedEmail != null) {
      yield r'verifiedEmail';
      yield serializers.serialize(
        object.verifiedEmail,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.aliasName != null) {
      yield r'aliasName';
      yield serializers.serialize(
        object.aliasName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.username != null) {
      yield r'username';
      yield serializers.serialize(
        object.username,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.facebookLinked != null) {
      yield r'facebookLinked';
      yield serializers.serialize(
        object.facebookLinked,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.facebookId != null) {
      yield r'facebookId';
      yield serializers.serialize(
        object.facebookId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.facebookName != null) {
      yield r'facebookName';
      yield serializers.serialize(
        object.facebookName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.googleLinked != null) {
      yield r'googleLinked';
      yield serializers.serialize(
        object.googleLinked,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.googleId != null) {
      yield r'googleId';
      yield serializers.serialize(
        object.googleId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.googleName != null) {
      yield r'googleName';
      yield serializers.serialize(
        object.googleName,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.sex != null) {
      yield r'sex';
      yield serializers.serialize(
        object.sex,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.birthday != null) {
      yield r'birthday';
      yield serializers.serialize(
        object.birthday,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.idCardType != null) {
      yield r'idCardType';
      yield serializers.serialize(
        object.idCardType,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardNo != null) {
      yield r'idCardNo';
      yield serializers.serialize(
        object.idCardNo,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.idCardIssueDate != null) {
      yield r'idCardIssueDate';
      yield serializers.serialize(
        object.idCardIssueDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.idCardExpireDate != null) {
      yield r'idCardExpireDate';
      yield serializers.serialize(
        object.idCardExpireDate,
        specifiedType: const FullType.nullable(Date),
      );
    }
    if (object.idCardIssuePlace != null) {
      yield r'idCardIssuePlace';
      yield serializers.serialize(
        object.idCardIssuePlace,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.resAddr != null) {
      yield r'resAddr';
      yield serializers.serialize(
        object.resAddr,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.resCity != null) {
      yield r'resCity';
      yield serializers.serialize(
        object.resCity,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.nationCode != null) {
      yield r'nationCode';
      yield serializers.serialize(
        object.nationCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.taxCode != null) {
      yield r'taxCode';
      yield serializers.serialize(
        object.taxCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.provinceCode != null) {
      yield r'provinceCode';
      yield serializers.serialize(
        object.provinceCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.districtCode != null) {
      yield r'districtCode';
      yield serializers.serialize(
        object.districtCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.wardVlg != null) {
      yield r'wardVlg';
      yield serializers.serialize(
        object.wardVlg,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.street != null) {
      yield r'street';
      yield serializers.serialize(
        object.street,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.no123 != null) {
      yield r'no123';
      yield serializers.serialize(
        object.no123,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.branchCode != null) {
      yield r'branchCode';
      yield serializers.serialize(
        object.branchCode,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.fullAddress != null) {
      yield r'fullAddress';
      yield serializers.serialize(
        object.fullAddress,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.enabled2fa != null) {
      yield r'enabled2fa';
      yield serializers.serialize(
        object.enabled2fa,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.bankVerified != null) {
      yield r'bankVerified';
      yield serializers.serialize(
        object.bankVerified,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.createdFrom != null) {
      yield r'createdFrom';
      yield serializers.serialize(
        object.createdFrom,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.lockedOldApp != null) {
      yield r'lockedOldApp';
      yield serializers.serialize(
        object.lockedOldApp,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.ekycId != null) {
      yield r'ekycId';
      yield serializers.serialize(
        object.ekycId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.suspiciousEkycStatus != null) {
      yield r'suspiciousEkycStatus';
      yield serializers.serialize(
        object.suspiciousEkycStatus,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.customerGroupUserId != null) {
      yield r'customerGroupUserId';
      yield serializers.serialize(
        object.customerGroupUserId,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.organization != null) {
      yield r'organization';
      yield serializers.serialize(
        object.organization,
        specifiedType:
            const FullType.nullable(QueryInfoResponseV2OrganizationEnum),
      );
    }
    if (object.identityStatus != null) {
      yield r'identityStatus';
      yield serializers.serialize(
        object.identityStatus,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.isVerifiedSTH != null) {
      yield r'isVerifiedSTH';
      yield serializers.serialize(
        object.isVerifiedSTH,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.isNobleEmployee != null) {
      yield r'isNobleEmployee';
      yield serializers.serialize(
        object.isNobleEmployee,
        specifiedType: const FullType.nullable(bool),
      );
    }
    if (object.passEkyc != null) {
      yield r'passEkyc';
      yield serializers.serialize(
        object.passEkyc,
        specifiedType: const FullType.nullable(bool),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    QueryInfoResponseV2 object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required QueryInfoResponseV2Builder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'loginType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(int),
          ) as int?;
          if (valueDes == null) continue;
          result.loginType = valueDes;
          break;
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.fullName = valueDes;
          break;
        case r'avatarUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.avatarUrl = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phoneNumber = valueDes;
          break;
        case r'otpMethod':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.otpMethod = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.email = valueDes;
          break;
        case r'verifiedEmail':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.verifiedEmail = valueDes;
          break;
        case r'aliasName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.aliasName = valueDes;
          break;
        case r'username':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.username = valueDes;
          break;
        case r'facebookLinked':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.facebookLinked = valueDes;
          break;
        case r'facebookId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.facebookId = valueDes;
          break;
        case r'facebookName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.facebookName = valueDes;
          break;
        case r'googleLinked':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.googleLinked = valueDes;
          break;
        case r'googleId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.googleId = valueDes;
          break;
        case r'googleName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.googleName = valueDes;
          break;
        case r'sex':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.sex = valueDes;
          break;
        case r'birthday':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.birthday = valueDes;
          break;
        case r'idCardType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardType = valueDes;
          break;
        case r'idCardNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardNo = valueDes;
          break;
        case r'idCardIssueDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.idCardIssueDate = valueDes;
          break;
        case r'idCardExpireDate':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(Date),
          ) as Date?;
          if (valueDes == null) continue;
          result.idCardExpireDate = valueDes;
          break;
        case r'idCardIssuePlace':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.idCardIssuePlace = valueDes;
          break;
        case r'resAddr':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.resAddr = valueDes;
          break;
        case r'resCity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.resCity = valueDes;
          break;
        case r'nationCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.nationCode = valueDes;
          break;
        case r'taxCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.taxCode = valueDes;
          break;
        case r'provinceCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.provinceCode = valueDes;
          break;
        case r'districtCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.districtCode = valueDes;
          break;
        case r'wardVlg':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.wardVlg = valueDes;
          break;
        case r'street':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.street = valueDes;
          break;
        case r'no123':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.no123 = valueDes;
          break;
        case r'branchCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.branchCode = valueDes;
          break;
        case r'fullAddress':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.fullAddress = valueDes;
          break;
        case r'enabled2fa':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.enabled2fa = valueDes;
          break;
        case r'bankVerified':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.bankVerified = valueDes;
          break;
        case r'createdFrom':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.createdFrom = valueDes;
          break;
        case r'lockedOldApp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.lockedOldApp = valueDes;
          break;
        case r'ekycId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.ekycId = valueDes;
          break;
        case r'suspiciousEkycStatus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.suspiciousEkycStatus = valueDes;
          break;
        case r'customerGroupUserId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.customerGroupUserId = valueDes;
          break;
        case r'organization':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(QueryInfoResponseV2OrganizationEnum),
          ) as QueryInfoResponseV2OrganizationEnum?;
          if (valueDes == null) continue;
          result.organization = valueDes;
          break;
        case r'identityStatus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.identityStatus = valueDes;
          break;
        case r'isVerifiedSTH':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.isVerifiedSTH = valueDes;
          break;
        case r'isNobleEmployee':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.isNobleEmployee = valueDes;
          break;
        case r'passEkyc':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(bool),
          ) as bool?;
          if (valueDes == null) continue;
          result.passEkyc = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  QueryInfoResponseV2 deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = QueryInfoResponseV2Builder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class QueryInfoResponseV2OrganizationEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'VNPOST')
  static const QueryInfoResponseV2OrganizationEnum VNPOST =
      _$queryInfoResponseV2OrganizationEnum_VNPOST;
  @BuiltValueEnumConst(wireName: r'KL')
  static const QueryInfoResponseV2OrganizationEnum KL =
      _$queryInfoResponseV2OrganizationEnum_KL;
  @BuiltValueEnumConst(wireName: r'OTHER')
  static const QueryInfoResponseV2OrganizationEnum OTHER =
      _$queryInfoResponseV2OrganizationEnum_OTHER;

  static Serializer<QueryInfoResponseV2OrganizationEnum> get serializer =>
      _$queryInfoResponseV2OrganizationEnumSerializer;

  const QueryInfoResponseV2OrganizationEnum._(String name) : super(name);

  static BuiltSet<QueryInfoResponseV2OrganizationEnum> get values =>
      _$queryInfoResponseV2OrganizationEnumValues;
  static QueryInfoResponseV2OrganizationEnum valueOf(String name) =>
      _$queryInfoResponseV2OrganizationEnumValueOf(name);
}
