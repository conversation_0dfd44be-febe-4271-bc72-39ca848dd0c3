//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'confirm_quick_verify_request.g.dart';

/// ConfirmQuickVerifyRequest
///
/// Properties:
/// * [status]
@BuiltValue()
abstract class ConfirmQuickVerifyRequest
    implements
        Built<ConfirmQuickVerifyRequest, ConfirmQuickVerifyRequestBuilder> {
  @BuiltValueField(wireName: r'status')
  ConfirmQuickVerifyRequestStatusEnum? get status;
  // enum statusEnum {  PROCESSING,  SMS_OTP_VERIFIED,  EMAIL_OTP_VERIFIED,  SUCCESS,  FAILURE,  CANCEL,  CONFIRMED,  REJECTED,  PENDING,  };

  ConfirmQuickVerifyRequest._();

  factory ConfirmQuickVerifyRequest(
          [void updates(ConfirmQuickVerifyRequestBuilder b)]) =
      _$ConfirmQuickVerifyRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ConfirmQuickVerifyRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ConfirmQuickVerifyRequest> get serializer =>
      _$ConfirmQuickVerifyRequestSerializer();
}

class _$ConfirmQuickVerifyRequestSerializer
    implements PrimitiveSerializer<ConfirmQuickVerifyRequest> {
  @override
  final Iterable<Type> types = const [
    ConfirmQuickVerifyRequest,
    _$ConfirmQuickVerifyRequest
  ];

  @override
  final String wireName = r'ConfirmQuickVerifyRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ConfirmQuickVerifyRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.status != null) {
      yield r'status';
      yield serializers.serialize(
        object.status,
        specifiedType:
            const FullType.nullable(ConfirmQuickVerifyRequestStatusEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ConfirmQuickVerifyRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ConfirmQuickVerifyRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(ConfirmQuickVerifyRequestStatusEnum),
          ) as ConfirmQuickVerifyRequestStatusEnum?;
          if (valueDes == null) continue;
          result.status = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ConfirmQuickVerifyRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ConfirmQuickVerifyRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class ConfirmQuickVerifyRequestStatusEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PROCESSING')
  static const ConfirmQuickVerifyRequestStatusEnum PROCESSING =
      _$confirmQuickVerifyRequestStatusEnum_PROCESSING;
  @BuiltValueEnumConst(wireName: r'SMS_OTP_VERIFIED')
  static const ConfirmQuickVerifyRequestStatusEnum SMS_OTP_VERIFIED =
      _$confirmQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'EMAIL_OTP_VERIFIED')
  static const ConfirmQuickVerifyRequestStatusEnum EMAIL_OTP_VERIFIED =
      _$confirmQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'SUCCESS')
  static const ConfirmQuickVerifyRequestStatusEnum SUCCESS =
      _$confirmQuickVerifyRequestStatusEnum_SUCCESS;
  @BuiltValueEnumConst(wireName: r'FAILURE')
  static const ConfirmQuickVerifyRequestStatusEnum FAILURE =
      _$confirmQuickVerifyRequestStatusEnum_FAILURE;
  @BuiltValueEnumConst(wireName: r'CANCEL')
  static const ConfirmQuickVerifyRequestStatusEnum CANCEL =
      _$confirmQuickVerifyRequestStatusEnum_CANCEL;
  @BuiltValueEnumConst(wireName: r'CONFIRMED')
  static const ConfirmQuickVerifyRequestStatusEnum CONFIRMED =
      _$confirmQuickVerifyRequestStatusEnum_CONFIRMED;
  @BuiltValueEnumConst(wireName: r'REJECTED')
  static const ConfirmQuickVerifyRequestStatusEnum REJECTED =
      _$confirmQuickVerifyRequestStatusEnum_REJECTED;
  @BuiltValueEnumConst(wireName: r'PENDING')
  static const ConfirmQuickVerifyRequestStatusEnum PENDING =
      _$confirmQuickVerifyRequestStatusEnum_PENDING;

  static Serializer<ConfirmQuickVerifyRequestStatusEnum> get serializer =>
      _$confirmQuickVerifyRequestStatusEnumSerializer;

  const ConfirmQuickVerifyRequestStatusEnum._(String name) : super(name);

  static BuiltSet<ConfirmQuickVerifyRequestStatusEnum> get values =>
      _$confirmQuickVerifyRequestStatusEnumValues;
  static ConfirmQuickVerifyRequestStatusEnum valueOf(String name) =>
      _$confirmQuickVerifyRequestStatusEnumValueOf(name);
}
