//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_quick_verify_by_identity_response.g.dart';

/// GetQuickVerifyByIdentityResponse
///
/// Properties:
/// * [id]
/// * [identity]
/// * [status]
@BuiltValue()
abstract class GetQuickVerifyByIdentityResponse
    implements
        Built<GetQuickVerifyByIdentityResponse,
            GetQuickVerifyByIdentityResponseBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  @BuiltValueField(wireName: r'identity')
  String? get identity;

  @BuiltValueField(wireName: r'status')
  GetQuickVerifyByIdentityResponseStatusEnum? get status;
  // enum statusEnum {  PROCESSING,  SMS_OTP_VERIFIED,  EMAIL_OTP_VERIFIED,  SUCCESS,  FAILURE,  CANCEL,  CONFIRMED,  REJECTED,  PENDING,  };

  GetQuickVerifyByIdentityResponse._();

  factory GetQuickVerifyByIdentityResponse(
          [void updates(GetQuickVerifyByIdentityResponseBuilder b)]) =
      _$GetQuickVerifyByIdentityResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetQuickVerifyByIdentityResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetQuickVerifyByIdentityResponse> get serializer =>
      _$GetQuickVerifyByIdentityResponseSerializer();
}

class _$GetQuickVerifyByIdentityResponseSerializer
    implements PrimitiveSerializer<GetQuickVerifyByIdentityResponse> {
  @override
  final Iterable<Type> types = const [
    GetQuickVerifyByIdentityResponse,
    _$GetQuickVerifyByIdentityResponse
  ];

  @override
  final String wireName = r'GetQuickVerifyByIdentityResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetQuickVerifyByIdentityResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.identity != null) {
      yield r'identity';
      yield serializers.serialize(
        object.identity,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.status != null) {
      yield r'status';
      yield serializers.serialize(
        object.status,
        specifiedType:
            const FullType.nullable(GetQuickVerifyByIdentityResponseStatusEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GetQuickVerifyByIdentityResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetQuickVerifyByIdentityResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.id = valueDes;
          break;
        case r'identity':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.identity = valueDes;
          break;
        case r'status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                GetQuickVerifyByIdentityResponseStatusEnum),
          ) as GetQuickVerifyByIdentityResponseStatusEnum?;
          if (valueDes == null) continue;
          result.status = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetQuickVerifyByIdentityResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetQuickVerifyByIdentityResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class GetQuickVerifyByIdentityResponseStatusEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'PROCESSING')
  static const GetQuickVerifyByIdentityResponseStatusEnum PROCESSING =
      _$getQuickVerifyByIdentityResponseStatusEnum_PROCESSING;
  @BuiltValueEnumConst(wireName: r'SMS_OTP_VERIFIED')
  static const GetQuickVerifyByIdentityResponseStatusEnum SMS_OTP_VERIFIED =
      _$getQuickVerifyByIdentityResponseStatusEnum_SMS_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'EMAIL_OTP_VERIFIED')
  static const GetQuickVerifyByIdentityResponseStatusEnum EMAIL_OTP_VERIFIED =
      _$getQuickVerifyByIdentityResponseStatusEnum_EMAIL_OTP_VERIFIED;
  @BuiltValueEnumConst(wireName: r'SUCCESS')
  static const GetQuickVerifyByIdentityResponseStatusEnum SUCCESS =
      _$getQuickVerifyByIdentityResponseStatusEnum_SUCCESS;
  @BuiltValueEnumConst(wireName: r'FAILURE')
  static const GetQuickVerifyByIdentityResponseStatusEnum FAILURE =
      _$getQuickVerifyByIdentityResponseStatusEnum_FAILURE;
  @BuiltValueEnumConst(wireName: r'CANCEL')
  static const GetQuickVerifyByIdentityResponseStatusEnum CANCEL =
      _$getQuickVerifyByIdentityResponseStatusEnum_CANCEL;
  @BuiltValueEnumConst(wireName: r'CONFIRMED')
  static const GetQuickVerifyByIdentityResponseStatusEnum CONFIRMED =
      _$getQuickVerifyByIdentityResponseStatusEnum_CONFIRMED;
  @BuiltValueEnumConst(wireName: r'REJECTED')
  static const GetQuickVerifyByIdentityResponseStatusEnum REJECTED =
      _$getQuickVerifyByIdentityResponseStatusEnum_REJECTED;
  @BuiltValueEnumConst(wireName: r'PENDING')
  static const GetQuickVerifyByIdentityResponseStatusEnum PENDING =
      _$getQuickVerifyByIdentityResponseStatusEnum_PENDING;

  static Serializer<GetQuickVerifyByIdentityResponseStatusEnum>
      get serializer => _$getQuickVerifyByIdentityResponseStatusEnumSerializer;

  const GetQuickVerifyByIdentityResponseStatusEnum._(String name) : super(name);

  static BuiltSet<GetQuickVerifyByIdentityResponseStatusEnum> get values =>
      _$getQuickVerifyByIdentityResponseStatusEnumValues;
  static GetQuickVerifyByIdentityResponseStatusEnum valueOf(String name) =>
      _$getQuickVerifyByIdentityResponseStatusEnumValueOf(name);
}
