// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_quick_verify_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_PROCESSING =
    const CreateQuickVerifyResponseStatusEnum._('PROCESSING');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_SMS_OTP_VERIFIED =
    const CreateQuickVerifyResponseStatusEnum._('SMS_OTP_VERIFIED');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_EMAIL_OTP_VERIFIED =
    const CreateQuickVerifyResponseStatusEnum._('EMAIL_OTP_VERIFIED');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_SUCCESS =
    const CreateQuickVerifyResponseStatusEnum._('SUCCESS');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_FAILURE =
    const CreateQuickVerifyResponseStatusEnum._('FAILURE');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_CANCEL =
    const CreateQuickVerifyResponseStatusEnum._('CANCEL');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_CONFIRMED =
    const CreateQuickVerifyResponseStatusEnum._('CONFIRMED');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_REJECTED =
    const CreateQuickVerifyResponseStatusEnum._('REJECTED');
const CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnum_PENDING =
    const CreateQuickVerifyResponseStatusEnum._('PENDING');

CreateQuickVerifyResponseStatusEnum
    _$createQuickVerifyResponseStatusEnumValueOf(String name) {
  switch (name) {
    case 'PROCESSING':
      return _$createQuickVerifyResponseStatusEnum_PROCESSING;
    case 'SMS_OTP_VERIFIED':
      return _$createQuickVerifyResponseStatusEnum_SMS_OTP_VERIFIED;
    case 'EMAIL_OTP_VERIFIED':
      return _$createQuickVerifyResponseStatusEnum_EMAIL_OTP_VERIFIED;
    case 'SUCCESS':
      return _$createQuickVerifyResponseStatusEnum_SUCCESS;
    case 'FAILURE':
      return _$createQuickVerifyResponseStatusEnum_FAILURE;
    case 'CANCEL':
      return _$createQuickVerifyResponseStatusEnum_CANCEL;
    case 'CONFIRMED':
      return _$createQuickVerifyResponseStatusEnum_CONFIRMED;
    case 'REJECTED':
      return _$createQuickVerifyResponseStatusEnum_REJECTED;
    case 'PENDING':
      return _$createQuickVerifyResponseStatusEnum_PENDING;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<CreateQuickVerifyResponseStatusEnum>
    _$createQuickVerifyResponseStatusEnumValues = new BuiltSet<
        CreateQuickVerifyResponseStatusEnum>(const <CreateQuickVerifyResponseStatusEnum>[
  _$createQuickVerifyResponseStatusEnum_PROCESSING,
  _$createQuickVerifyResponseStatusEnum_SMS_OTP_VERIFIED,
  _$createQuickVerifyResponseStatusEnum_EMAIL_OTP_VERIFIED,
  _$createQuickVerifyResponseStatusEnum_SUCCESS,
  _$createQuickVerifyResponseStatusEnum_FAILURE,
  _$createQuickVerifyResponseStatusEnum_CANCEL,
  _$createQuickVerifyResponseStatusEnum_CONFIRMED,
  _$createQuickVerifyResponseStatusEnum_REJECTED,
  _$createQuickVerifyResponseStatusEnum_PENDING,
]);

Serializer<CreateQuickVerifyResponseStatusEnum>
    _$createQuickVerifyResponseStatusEnumSerializer =
    new _$CreateQuickVerifyResponseStatusEnumSerializer();

class _$CreateQuickVerifyResponseStatusEnumSerializer
    implements PrimitiveSerializer<CreateQuickVerifyResponseStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };

  @override
  final Iterable<Type> types = const <Type>[
    CreateQuickVerifyResponseStatusEnum
  ];
  @override
  final String wireName = 'CreateQuickVerifyResponseStatusEnum';

  @override
  Object serialize(
          Serializers serializers, CreateQuickVerifyResponseStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  CreateQuickVerifyResponseStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      CreateQuickVerifyResponseStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$CreateQuickVerifyResponse extends CreateQuickVerifyResponse {
  @override
  final String? id;
  @override
  final String? userId;
  @override
  final String? identity;
  @override
  final CreateQuickVerifyResponseStatusEnum? status;

  factory _$CreateQuickVerifyResponse(
          [void Function(CreateQuickVerifyResponseBuilder)? updates]) =>
      (new CreateQuickVerifyResponseBuilder()..update(updates))._build();

  _$CreateQuickVerifyResponse._(
      {this.id, this.userId, this.identity, this.status})
      : super._();

  @override
  CreateQuickVerifyResponse rebuild(
          void Function(CreateQuickVerifyResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  CreateQuickVerifyResponseBuilder toBuilder() =>
      new CreateQuickVerifyResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CreateQuickVerifyResponse &&
        id == other.id &&
        userId == other.userId &&
        identity == other.identity &&
        status == other.status;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, identity.hashCode);
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'CreateQuickVerifyResponse')
          ..add('id', id)
          ..add('userId', userId)
          ..add('identity', identity)
          ..add('status', status))
        .toString();
  }
}

class CreateQuickVerifyResponseBuilder
    implements
        Builder<CreateQuickVerifyResponse, CreateQuickVerifyResponseBuilder> {
  _$CreateQuickVerifyResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  String? _identity;
  String? get identity => _$this._identity;
  set identity(String? identity) => _$this._identity = identity;

  CreateQuickVerifyResponseStatusEnum? _status;
  CreateQuickVerifyResponseStatusEnum? get status => _$this._status;
  set status(CreateQuickVerifyResponseStatusEnum? status) =>
      _$this._status = status;

  CreateQuickVerifyResponseBuilder() {
    CreateQuickVerifyResponse._defaults(this);
  }

  CreateQuickVerifyResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _userId = $v.userId;
      _identity = $v.identity;
      _status = $v.status;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(CreateQuickVerifyResponse other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$CreateQuickVerifyResponse;
  }

  @override
  void update(void Function(CreateQuickVerifyResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  CreateQuickVerifyResponse build() => _build();

  _$CreateQuickVerifyResponse _build() {
    final _$result = _$v ??
        new _$CreateQuickVerifyResponse._(
            id: id, userId: userId, identity: identity, status: status);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
