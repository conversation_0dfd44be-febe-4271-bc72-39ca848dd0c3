// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_token.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const OTPTokenTokenTypeEnum _$oTPTokenTokenTypeEnum_SOFT_TOKEN =
    const OTPTokenTokenTypeEnum._('SOFT_TOKEN');
const OTPTokenTokenTypeEnum _$oTPTokenTokenTypeEnum_SMS_OTP =
    const OTPTokenTokenTypeEnum._('SMS_OTP');
const OTPTokenTokenTypeEnum _$oTPTokenTokenTypeEnum_HARD_TOKEN =
    const OTPTokenTokenTypeEnum._('HARD_TOKEN');

OTPTokenTokenTypeEnum _$oTPTokenTokenTypeEnumValueOf(String name) {
  switch (name) {
    case 'SOFT_TOKEN':
      return _$oTPTokenTokenTypeEnum_SOFT_TOKEN;
    case 'SMS_OTP':
      return _$oTPTokenTokenTypeEnum_SMS_OTP;
    case 'HARD_TOKEN':
      return _$oTPTokenTokenTypeEnum_HARD_TOKEN;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<OTPTokenTokenTypeEnum> _$oTPTokenTokenTypeEnumValues =
    new BuiltSet<OTPTokenTokenTypeEnum>(const <OTPTokenTokenTypeEnum>[
  _$oTPTokenTokenTypeEnum_SOFT_TOKEN,
  _$oTPTokenTokenTypeEnum_SMS_OTP,
  _$oTPTokenTokenTypeEnum_HARD_TOKEN,
]);

Serializer<OTPTokenTokenTypeEnum> _$oTPTokenTokenTypeEnumSerializer =
    new _$OTPTokenTokenTypeEnumSerializer();

class _$OTPTokenTokenTypeEnumSerializer
    implements PrimitiveSerializer<OTPTokenTokenTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'SOFT_TOKEN': 'SOFT_TOKEN',
    'SMS_OTP': 'SMS_OTP',
    'HARD_TOKEN': 'HARD_TOKEN',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'SOFT_TOKEN': 'SOFT_TOKEN',
    'SMS_OTP': 'SMS_OTP',
    'HARD_TOKEN': 'HARD_TOKEN',
  };

  @override
  final Iterable<Type> types = const <Type>[OTPTokenTokenTypeEnum];
  @override
  final String wireName = 'OTPTokenTokenTypeEnum';

  @override
  Object serialize(Serializers serializers, OTPTokenTokenTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  OTPTokenTokenTypeEnum deserialize(Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      OTPTokenTokenTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$OTPToken extends OTPToken {
  @override
  final String? id;
  @override
  final String? userId;
  @override
  final String? deviceId;
  @override
  final OTPTokenTokenTypeEnum? tokenType;
  @override
  final bool? activated;
  @override
  final String? description;
  @override
  final String? shareSecret;
  @override
  final DateTime? createAt;
  @override
  final String? deviceName;
  @override
  final String? brand;
  @override
  final String? os;
  @override
  final String? imei;

  factory _$OTPToken([void Function(OTPTokenBuilder)? updates]) =>
      (new OTPTokenBuilder()..update(updates))._build();

  _$OTPToken._(
      {this.id,
      this.userId,
      this.deviceId,
      this.tokenType,
      this.activated,
      this.description,
      this.shareSecret,
      this.createAt,
      this.deviceName,
      this.brand,
      this.os,
      this.imei})
      : super._();

  @override
  OTPToken rebuild(void Function(OTPTokenBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  OTPTokenBuilder toBuilder() => new OTPTokenBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OTPToken &&
        id == other.id &&
        userId == other.userId &&
        deviceId == other.deviceId &&
        tokenType == other.tokenType &&
        activated == other.activated &&
        description == other.description &&
        shareSecret == other.shareSecret &&
        createAt == other.createAt &&
        deviceName == other.deviceName &&
        brand == other.brand &&
        os == other.os &&
        imei == other.imei;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, deviceId.hashCode);
    _$hash = $jc(_$hash, tokenType.hashCode);
    _$hash = $jc(_$hash, activated.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, shareSecret.hashCode);
    _$hash = $jc(_$hash, createAt.hashCode);
    _$hash = $jc(_$hash, deviceName.hashCode);
    _$hash = $jc(_$hash, brand.hashCode);
    _$hash = $jc(_$hash, os.hashCode);
    _$hash = $jc(_$hash, imei.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'OTPToken')
          ..add('id', id)
          ..add('userId', userId)
          ..add('deviceId', deviceId)
          ..add('tokenType', tokenType)
          ..add('activated', activated)
          ..add('description', description)
          ..add('shareSecret', shareSecret)
          ..add('createAt', createAt)
          ..add('deviceName', deviceName)
          ..add('brand', brand)
          ..add('os', os)
          ..add('imei', imei))
        .toString();
  }
}

class OTPTokenBuilder implements Builder<OTPToken, OTPTokenBuilder> {
  _$OTPToken? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  String? _deviceId;
  String? get deviceId => _$this._deviceId;
  set deviceId(String? deviceId) => _$this._deviceId = deviceId;

  OTPTokenTokenTypeEnum? _tokenType;
  OTPTokenTokenTypeEnum? get tokenType => _$this._tokenType;
  set tokenType(OTPTokenTokenTypeEnum? tokenType) =>
      _$this._tokenType = tokenType;

  bool? _activated;
  bool? get activated => _$this._activated;
  set activated(bool? activated) => _$this._activated = activated;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _shareSecret;
  String? get shareSecret => _$this._shareSecret;
  set shareSecret(String? shareSecret) => _$this._shareSecret = shareSecret;

  DateTime? _createAt;
  DateTime? get createAt => _$this._createAt;
  set createAt(DateTime? createAt) => _$this._createAt = createAt;

  String? _deviceName;
  String? get deviceName => _$this._deviceName;
  set deviceName(String? deviceName) => _$this._deviceName = deviceName;

  String? _brand;
  String? get brand => _$this._brand;
  set brand(String? brand) => _$this._brand = brand;

  String? _os;
  String? get os => _$this._os;
  set os(String? os) => _$this._os = os;

  String? _imei;
  String? get imei => _$this._imei;
  set imei(String? imei) => _$this._imei = imei;

  OTPTokenBuilder() {
    OTPToken._defaults(this);
  }

  OTPTokenBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _userId = $v.userId;
      _deviceId = $v.deviceId;
      _tokenType = $v.tokenType;
      _activated = $v.activated;
      _description = $v.description;
      _shareSecret = $v.shareSecret;
      _createAt = $v.createAt;
      _deviceName = $v.deviceName;
      _brand = $v.brand;
      _os = $v.os;
      _imei = $v.imei;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(OTPToken other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$OTPToken;
  }

  @override
  void update(void Function(OTPTokenBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  OTPToken build() => _build();

  _$OTPToken _build() {
    final _$result = _$v ??
        new _$OTPToken._(
            id: id,
            userId: userId,
            deviceId: deviceId,
            tokenType: tokenType,
            activated: activated,
            description: description,
            shareSecret: shareSecret,
            createAt: createAt,
            deviceName: deviceName,
            brand: brand,
            os: os,
            imei: imei);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
