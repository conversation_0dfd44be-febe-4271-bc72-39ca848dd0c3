//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'ekyc_status_response.g.dart';

/// EkycStatusResponse
///
/// Properties:
/// * [userLevelType]
@BuiltValue()
abstract class EkycStatusResponse
    implements Built<EkycStatusResponse, EkycStatusResponseBuilder> {
  @BuiltValueField(wireName: r'userLevelType')
  EkycStatusResponseUserLevelTypeEnum? get userLevelType;
  // enum userLevelTypeEnum {  COUNTERS_ACCOUNT,  VIDEO_COUNTERS_KYC,  COUNTERS_KYC,  VIDEO_KYC,  E_KYC,  NO_KYC,  };

  EkycStatusResponse._();

  factory EkycStatusResponse([void updates(EkycStatusResponseBuilder b)]) =
      _$EkycStatusResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(EkycStatusResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<EkycStatusResponse> get serializer =>
      _$EkycStatusResponseSerializer();
}

class _$EkycStatusResponseSerializer
    implements PrimitiveSerializer<EkycStatusResponse> {
  @override
  final Iterable<Type> types = const [EkycStatusResponse, _$EkycStatusResponse];

  @override
  final String wireName = r'EkycStatusResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    EkycStatusResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.userLevelType != null) {
      yield r'userLevelType';
      yield serializers.serialize(
        object.userLevelType,
        specifiedType:
            const FullType.nullable(EkycStatusResponseUserLevelTypeEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    EkycStatusResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required EkycStatusResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'userLevelType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType.nullable(EkycStatusResponseUserLevelTypeEnum),
          ) as EkycStatusResponseUserLevelTypeEnum?;
          if (valueDes == null) continue;
          result.userLevelType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  EkycStatusResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = EkycStatusResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class EkycStatusResponseUserLevelTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'COUNTERS_ACCOUNT')
  static const EkycStatusResponseUserLevelTypeEnum COUNTERS_ACCOUNT =
      _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_ACCOUNT;
  @BuiltValueEnumConst(wireName: r'VIDEO_COUNTERS_KYC')
  static const EkycStatusResponseUserLevelTypeEnum VIDEO_COUNTERS_KYC =
      _$ekycStatusResponseUserLevelTypeEnum_VIDEO_COUNTERS_KYC;
  @BuiltValueEnumConst(wireName: r'COUNTERS_KYC')
  static const EkycStatusResponseUserLevelTypeEnum COUNTERS_KYC =
      _$ekycStatusResponseUserLevelTypeEnum_COUNTERS_KYC;
  @BuiltValueEnumConst(wireName: r'VIDEO_KYC')
  static const EkycStatusResponseUserLevelTypeEnum VIDEO_KYC =
      _$ekycStatusResponseUserLevelTypeEnum_VIDEO_KYC;
  @BuiltValueEnumConst(wireName: r'E_KYC')
  static const EkycStatusResponseUserLevelTypeEnum E_KYC =
      _$ekycStatusResponseUserLevelTypeEnum_E_KYC;
  @BuiltValueEnumConst(wireName: r'NO_KYC')
  static const EkycStatusResponseUserLevelTypeEnum NO_KYC =
      _$ekycStatusResponseUserLevelTypeEnum_NO_KYC;

  static Serializer<EkycStatusResponseUserLevelTypeEnum> get serializer =>
      _$ekycStatusResponseUserLevelTypeEnumSerializer;

  const EkycStatusResponseUserLevelTypeEnum._(String name) : super(name);

  static BuiltSet<EkycStatusResponseUserLevelTypeEnum> get values =>
      _$ekycStatusResponseUserLevelTypeEnumValues;
  static EkycStatusResponseUserLevelTypeEnum valueOf(String name) =>
      _$ekycStatusResponseUserLevelTypeEnumValueOf(name);
}
