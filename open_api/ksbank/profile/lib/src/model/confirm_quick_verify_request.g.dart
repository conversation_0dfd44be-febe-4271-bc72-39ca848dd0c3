// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'confirm_quick_verify_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_PROCESSING =
    const ConfirmQuickVerifyRequestStatusEnum._('PROCESSING');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED =
    const ConfirmQuickVerifyRequestStatusEnum._('SMS_OTP_VERIFIED');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED =
    const ConfirmQuickVerifyRequestStatusEnum._('EMAIL_OTP_VERIFIED');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_SUCCESS =
    const ConfirmQuickVerifyRequestStatusEnum._('SUCCESS');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_FAILURE =
    const ConfirmQuickVerifyRequestStatusEnum._('FAILURE');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_CANCEL =
    const ConfirmQuickVerifyRequestStatusEnum._('CANCEL');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_CONFIRMED =
    const ConfirmQuickVerifyRequestStatusEnum._('CONFIRMED');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_REJECTED =
    const ConfirmQuickVerifyRequestStatusEnum._('REJECTED');
const ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnum_PENDING =
    const ConfirmQuickVerifyRequestStatusEnum._('PENDING');

ConfirmQuickVerifyRequestStatusEnum
    _$confirmQuickVerifyRequestStatusEnumValueOf(String name) {
  switch (name) {
    case 'PROCESSING':
      return _$confirmQuickVerifyRequestStatusEnum_PROCESSING;
    case 'SMS_OTP_VERIFIED':
      return _$confirmQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED;
    case 'EMAIL_OTP_VERIFIED':
      return _$confirmQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED;
    case 'SUCCESS':
      return _$confirmQuickVerifyRequestStatusEnum_SUCCESS;
    case 'FAILURE':
      return _$confirmQuickVerifyRequestStatusEnum_FAILURE;
    case 'CANCEL':
      return _$confirmQuickVerifyRequestStatusEnum_CANCEL;
    case 'CONFIRMED':
      return _$confirmQuickVerifyRequestStatusEnum_CONFIRMED;
    case 'REJECTED':
      return _$confirmQuickVerifyRequestStatusEnum_REJECTED;
    case 'PENDING':
      return _$confirmQuickVerifyRequestStatusEnum_PENDING;
    default:
      throw new ArgumentError(name);
  }
}

final BuiltSet<ConfirmQuickVerifyRequestStatusEnum>
    _$confirmQuickVerifyRequestStatusEnumValues = new BuiltSet<
        ConfirmQuickVerifyRequestStatusEnum>(const <ConfirmQuickVerifyRequestStatusEnum>[
  _$confirmQuickVerifyRequestStatusEnum_PROCESSING,
  _$confirmQuickVerifyRequestStatusEnum_SMS_OTP_VERIFIED,
  _$confirmQuickVerifyRequestStatusEnum_EMAIL_OTP_VERIFIED,
  _$confirmQuickVerifyRequestStatusEnum_SUCCESS,
  _$confirmQuickVerifyRequestStatusEnum_FAILURE,
  _$confirmQuickVerifyRequestStatusEnum_CANCEL,
  _$confirmQuickVerifyRequestStatusEnum_CONFIRMED,
  _$confirmQuickVerifyRequestStatusEnum_REJECTED,
  _$confirmQuickVerifyRequestStatusEnum_PENDING,
]);

Serializer<ConfirmQuickVerifyRequestStatusEnum>
    _$confirmQuickVerifyRequestStatusEnumSerializer =
    new _$ConfirmQuickVerifyRequestStatusEnumSerializer();

class _$ConfirmQuickVerifyRequestStatusEnumSerializer
    implements PrimitiveSerializer<ConfirmQuickVerifyRequestStatusEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'PROCESSING': 'PROCESSING',
    'SMS_OTP_VERIFIED': 'SMS_OTP_VERIFIED',
    'EMAIL_OTP_VERIFIED': 'EMAIL_OTP_VERIFIED',
    'SUCCESS': 'SUCCESS',
    'FAILURE': 'FAILURE',
    'CANCEL': 'CANCEL',
    'CONFIRMED': 'CONFIRMED',
    'REJECTED': 'REJECTED',
    'PENDING': 'PENDING',
  };

  @override
  final Iterable<Type> types = const <Type>[
    ConfirmQuickVerifyRequestStatusEnum
  ];
  @override
  final String wireName = 'ConfirmQuickVerifyRequestStatusEnum';

  @override
  Object serialize(
          Serializers serializers, ConfirmQuickVerifyRequestStatusEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  ConfirmQuickVerifyRequestStatusEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      ConfirmQuickVerifyRequestStatusEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$ConfirmQuickVerifyRequest extends ConfirmQuickVerifyRequest {
  @override
  final ConfirmQuickVerifyRequestStatusEnum? status;

  factory _$ConfirmQuickVerifyRequest(
          [void Function(ConfirmQuickVerifyRequestBuilder)? updates]) =>
      (new ConfirmQuickVerifyRequestBuilder()..update(updates))._build();

  _$ConfirmQuickVerifyRequest._({this.status}) : super._();

  @override
  ConfirmQuickVerifyRequest rebuild(
          void Function(ConfirmQuickVerifyRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ConfirmQuickVerifyRequestBuilder toBuilder() =>
      new ConfirmQuickVerifyRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ConfirmQuickVerifyRequest && status == other.status;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, status.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ConfirmQuickVerifyRequest')
          ..add('status', status))
        .toString();
  }
}

class ConfirmQuickVerifyRequestBuilder
    implements
        Builder<ConfirmQuickVerifyRequest, ConfirmQuickVerifyRequestBuilder> {
  _$ConfirmQuickVerifyRequest? _$v;

  ConfirmQuickVerifyRequestStatusEnum? _status;
  ConfirmQuickVerifyRequestStatusEnum? get status => _$this._status;
  set status(ConfirmQuickVerifyRequestStatusEnum? status) =>
      _$this._status = status;

  ConfirmQuickVerifyRequestBuilder() {
    ConfirmQuickVerifyRequest._defaults(this);
  }

  ConfirmQuickVerifyRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _status = $v.status;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ConfirmQuickVerifyRequest other) {
    ArgumentError.checkNotNull(other, 'other');
    _$v = other as _$ConfirmQuickVerifyRequest;
  }

  @override
  void update(void Function(ConfirmQuickVerifyRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ConfirmQuickVerifyRequest build() => _build();

  _$ConfirmQuickVerifyRequest _build() {
    final _$result = _$v ?? new _$ConfirmQuickVerifyRequest._(status: status);
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
