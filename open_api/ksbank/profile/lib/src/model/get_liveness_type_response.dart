//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_liveness_type_response.g.dart';

/// GetLivenessTypeResponse
///
/// Properties:
/// * [phone]
/// * [livenessType]
@BuiltValue()
abstract class GetLivenessTypeResponse
    implements Built<GetLivenessTypeResponse, GetLivenessTypeResponseBuilder> {
  @BuiltValueField(wireName: r'phone')
  String? get phone;

  @BuiltValueField(wireName: r'livenessType')
  GetLivenessTypeResponseLivenessTypeEnum? get livenessType;
  // enum livenessTypeEnum {  TURN_FACE,  TURN_NOSE,  };

  GetLivenessTypeResponse._();

  factory GetLivenessTypeResponse(
          [void updates(GetLivenessTypeResponseBuilder b)]) =
      _$GetLivenessTypeResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetLivenessTypeResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetLivenessTypeResponse> get serializer =>
      _$GetLivenessTypeResponseSerializer();
}

class _$GetLivenessTypeResponseSerializer
    implements PrimitiveSerializer<GetLivenessTypeResponse> {
  @override
  final Iterable<Type> types = const [
    GetLivenessTypeResponse,
    _$GetLivenessTypeResponse
  ];

  @override
  final String wireName = r'GetLivenessTypeResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetLivenessTypeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.phone != null) {
      yield r'phone';
      yield serializers.serialize(
        object.phone,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.livenessType != null) {
      yield r'livenessType';
      yield serializers.serialize(
        object.livenessType,
        specifiedType:
            const FullType.nullable(GetLivenessTypeResponseLivenessTypeEnum),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GetLivenessTypeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetLivenessTypeResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'phone':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.phone = valueDes;
          break;
        case r'livenessType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(
                GetLivenessTypeResponseLivenessTypeEnum),
          ) as GetLivenessTypeResponseLivenessTypeEnum?;
          if (valueDes == null) continue;
          result.livenessType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetLivenessTypeResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetLivenessTypeResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class GetLivenessTypeResponseLivenessTypeEnum extends EnumClass {
  @BuiltValueEnumConst(wireName: r'TURN_FACE')
  static const GetLivenessTypeResponseLivenessTypeEnum FACE =
      _$getLivenessTypeResponseLivenessTypeEnum_FACE;
  @BuiltValueEnumConst(wireName: r'TURN_NOSE')
  static const GetLivenessTypeResponseLivenessTypeEnum NOSE =
      _$getLivenessTypeResponseLivenessTypeEnum_NOSE;

  static Serializer<GetLivenessTypeResponseLivenessTypeEnum> get serializer =>
      _$getLivenessTypeResponseLivenessTypeEnumSerializer;

  const GetLivenessTypeResponseLivenessTypeEnum._(String name) : super(name);

  static BuiltSet<GetLivenessTypeResponseLivenessTypeEnum> get values =>
      _$getLivenessTypeResponseLivenessTypeEnumValues;
  static GetLivenessTypeResponseLivenessTypeEnum valueOf(String name) =>
      _$getLivenessTypeResponseLivenessTypeEnumValueOf(name);
}
