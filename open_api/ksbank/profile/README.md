# ksbank_api_profile (EXPERIMENTAL)
Documentation Profile API v1.0

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0
- Generator version: 7.9.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  ksbank_api_profile: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  ksbank_api_profile:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  ksbank_api_profile:
    path: /path/to/ksbank_api_profile
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:ksbank_api_profile/ksbank_api_profile.dart';


final api = KsbankApiProfile().getAPIAChCcChiNhnhApi();
final int page = 56; // int | 
final int size = 56; // int | 

try {
    final response = await api.getAllProvinces(page, size);
    print(response);
} catch on DioException (e) {
    print("Exception when calling APIAChCcChiNhnhApi->getAllProvinces: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *https://dev-ksapi.ssf.vn/profile*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getAllProvinces**](doc/APIAChCcChiNhnhApi.md#getallprovinces) | **GET** /address/v1/province/getAllProvinces | API tra cứu toàn bộ danh sách các tỉnh
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getBranches**](doc/APIAChCcChiNhnhApi.md#getbranches) | **POST** /address/branch/getBranches | API tra cứu thông tin danh sách chi nhánh
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getBranchesByDistrictCode**](doc/APIAChCcChiNhnhApi.md#getbranchesbydistrictcode) | **GET** /address/branch/getBranchesByDistrictCode | API tra cứu thông tin danh sách chi nhánh theo theo mã huyện/ thị xã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getDistrictByCode**](doc/APIAChCcChiNhnhApi.md#getdistrictbycode) | **GET** /address/district/getDistrictByCode | API tra cứu thông tin huyện/ thị xã theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getDistrictWithBranchesByCode**](doc/APIAChCcChiNhnhApi.md#getdistrictwithbranchesbycode) | **GET** /address/district/getDistrictWithBranchesByCode | API tra cứu thông tin huyện/ thị xã có chi nhánh theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getDistrictsByCode**](doc/APIAChCcChiNhnhApi.md#getdistrictsbycode) | **GET** /address/district/getDistrictsByCode | API tra cứu thông tin danh sách huyện/ thị xã theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getDistrictsByProvinceCode**](doc/APIAChCcChiNhnhApi.md#getdistrictsbyprovincecode) | **GET** /address/v1/district/getDistrictsByProvinceCode | API tra cứu thông tin danh sách quận/ huyện/ thị xã theo mã tỉnh/thành phố
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getDistrictsWithBranchesByCode**](doc/APIAChCcChiNhnhApi.md#getdistrictswithbranchesbycode) | **GET** /address/district/getDistrictsWithBranchesByCode | API tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getDistrictsWithBranchesByProvinceCode**](doc/APIAChCcChiNhnhApi.md#getdistrictswithbranchesbyprovincecode) | **GET** /address/district/getDistrictsWithBranchesByProvinceCode | API tra cứu thông tin danh sách huyện/ thị xã có chi nhánh theo mã tỉnh
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getNetworksByProvinceCodeAndDistrictCode**](doc/APIAChCcChiNhnhApi.md#getnetworksbyprovincecodeanddistrictcode) | **GET** /address/v1/wards/getNetworksByProvinceCodeAndDistrictCode | 
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getProvinceByCode**](doc/APIAChCcChiNhnhApi.md#getprovincebycode) | **GET** /address/province/getProvinceByCode | API tra cứu thông tin tỉnh theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getProvinceWithBranchesByCode**](doc/APIAChCcChiNhnhApi.md#getprovincewithbranchesbycode) | **GET** /address/province/getProvinceWithBranchesByCode | API tra cứu thông tin tỉnh có chi nhánh theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getProvincesByCode**](doc/APIAChCcChiNhnhApi.md#getprovincesbycode) | **GET** /address/province/getProvincesByCode | API tra cứu danh sách thông tin tỉnh theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getProvincesWithBranchesByCode**](doc/APIAChCcChiNhnhApi.md#getprovinceswithbranchesbycode) | **GET** /address/province/getProvincesWithBranchesByCode | API tra cứu thông tin danh sách tỉnh có chi nhánh theo mã
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getWardsAndNetworkByProvinceCodeAndDistrictCode**](doc/APIAChCcChiNhnhApi.md#getwardsandnetworkbyprovincecodeanddistrictcode) | **GET** /address/v1/wards/getWardsAndNetworkByProvinceCodeAndDistrictCode | 
[*APIAChCcChiNhnhApi*](doc/APIAChCcChiNhnhApi.md) | [**getWardsByProvinceCodeAndDistrictCode**](doc/APIAChCcChiNhnhApi.md#getwardsbyprovincecodeanddistrictcode) | **GET** /address/v1/wards/getWardsByProvinceCodeAndDistrictCode | API tra cứu thông tin danh sách Phường/ Xã theo mã tỉnh/ thành phố và quận/huyện
[*APIDnhChoCcUserTMySTMApi*](doc/APIDnhChoCcUserTMySTMApi.md) | [**createUser1**](doc/APIDnhChoCcUserTMySTMApi.md#createuser1) | **POST** /stm/users/customer/register | API đăng ký cho user thông thường từ máy STM
[*APIDnhChoCcUserTMySTMApi*](doc/APIDnhChoCcUserTMySTMApi.md) | [**validateUser**](doc/APIDnhChoCcUserTMySTMApi.md#validateuser) | **POST** /stm/users/customer/validate | API kiểm tra sự tồn tại của User trước khi mở tài khoản
[*APIXcThcNhanhQuaKLBApi*](doc/APIXcThcNhanhQuaKLBApi.md) | [**confirmQuickVerify**](doc/APIXcThcNhanhQuaKLBApi.md#confirmquickverify) | **POST** /users/v1/confirmQuickVerify | API tạo yêu cầu xác thực nhanh
[*APIXcThcNhanhQuaKLBApi*](doc/APIXcThcNhanhQuaKLBApi.md) | [**createQuickVerify**](doc/APIXcThcNhanhQuaKLBApi.md#createquickverify) | **POST** /users/external/v1/createQuickVerify | API tạo yêu cầu xác thực nhanh
[*APIXcThcNhanhQuaKLBApi*](doc/APIXcThcNhanhQuaKLBApi.md) | [**getQuickVerifyByIdentity**](doc/APIXcThcNhanhQuaKLBApi.md#getquickverifybyidentity) | **POST** /users/external/v1/getQuickVerifyByIdentity | API tạo tìm kiếm yêu cầu xác thực nhanh
[*APIXcThcNhanhQuaKLBApi*](doc/APIXcThcNhanhQuaKLBApi.md) | [**getQuickVerifyByUserId**](doc/APIXcThcNhanhQuaKLBApi.md#getquickverifybyuserid) | **GET** /users/v1/getQuickVerifyByUserId | API tạo tìm kiếm yêu cầu xác thực nhanh
[*APIXcThcNhanhQuaKLBApi*](doc/APIXcThcNhanhQuaKLBApi.md) | [**getUserKlbInfo**](doc/APIXcThcNhanhQuaKLBApi.md#getuserklbinfo) | **POST** /users/external/v1/getUserKlbInfo | API tạo tìm kiếm yêu cầu xác thực nhanh
[*CustomerGroupControllerImplApi*](doc/CustomerGroupControllerImplApi.md) | [**getCustomerGroupList**](doc/CustomerGroupControllerImplApi.md#getcustomergrouplist) | **GET** /customer-group | Lấy thông tin danh sách nhóm khách hàng
[*CustomerGroupUseControllerImplApi*](doc/CustomerGroupUseControllerImplApi.md) | [**getCustomerGroupUseList**](doc/CustomerGroupUseControllerImplApi.md#getcustomergroupuselist) | **GET** /customer-group-use/{customerGroupId}/customer-group | Lấy danh sách đối tượng sử dụng nhóm khách hàng
[*DeviceInfoResourceApi*](doc/DeviceInfoResourceApi.md) | [**getDeviceInfo**](doc/DeviceInfoResourceApi.md#getdeviceinfo) | **GET** /devices/detail | API lấy thông tin chi tiết thiết bị đăng nhập
[*DeviceInfoResourceApi*](doc/DeviceInfoResourceApi.md) | [**unlinkDevice**](doc/DeviceInfoResourceApi.md#unlinkdevice) | **PUT** /devices/unlink | API hủy liên kết thiết bị.
[*LivenessResourceApi*](doc/LivenessResourceApi.md) | [**createTransactionLivenessSession**](doc/LivenessResourceApi.md#createtransactionlivenesssession) | **POST** /api/v1/liveness/transaction/sessions | Create liveness session
[*LivenessResourceApi*](doc/LivenessResourceApi.md) | [**getTransactionLivenessSessionResult**](doc/LivenessResourceApi.md#gettransactionlivenesssessionresult) | **GET** /api/v1/liveness/transaction/{transactionNo}/sessions/{sessionId}/result | Get liveness transaction session result
[*MigrationControllerApi*](doc/MigrationControllerApi.md) | [**migrateCustomerFromCore**](doc/MigrationControllerApi.md#migratecustomerfromcore) | **POST** /migration/v1/old-customer | 
[*MigrationControllerApi*](doc/MigrationControllerApi.md) | [**migrateKsfCustomer**](doc/MigrationControllerApi.md#migrateksfcustomer) | **POST** /migration/v1/ksf-customer | 
[*MigrationControllerApi*](doc/MigrationControllerApi.md) | [**migrateUserCrm**](doc/MigrationControllerApi.md#migrateusercrm) | **POST** /migration/v1/user-crm | 
[*NgKUserQuaVNPOSTApi*](doc/NgKUserQuaVNPOSTApi.md) | [**checkUser**](doc/NgKUserQuaVNPOSTApi.md#checkuser) | **POST** /users/vnpost/checkUser | API kiểm tra thông tin tạo mới user
[*NgKUserQuaVNPOSTApi*](doc/NgKUserQuaVNPOSTApi.md) | [**createUser**](doc/NgKUserQuaVNPOSTApi.md#createuser) | **POST** /users/vnpost/customer/register | API đăng ký cho user thông thường từ VNP
[*NotificationControllerApi*](doc/NotificationControllerApi.md) | [**getNotification**](doc/NotificationControllerApi.md#getnotification) | **GET** /notifications/cif/{cifNumber} | 
[*NotificationControllerApi*](doc/NotificationControllerApi.md) | [**getPlayerIdsByCifNo**](doc/NotificationControllerApi.md#getplayeridsbycifno) | **GET** /notifications/internal/v1/getPlayerIdsByCifNo | API lấy playerId của UserId bằng số cif
[*NotificationControllerApi*](doc/NotificationControllerApi.md) | [**sendNotification**](doc/NotificationControllerApi.md#sendnotification) | **POST** /notifications | 
[*ResourceForManageBanksUserApi*](doc/ResourceForManageBanksUserApi.md) | [**closeCustomer**](doc/ResourceForManageBanksUserApi.md#closecustomer) | **POST** /admin/users/customer/close | API hủy dịch vụ ebanking
[*ResourceForManageBanksUserApi*](doc/ResourceForManageBanksUserApi.md) | [**openCustomer**](doc/ResourceForManageBanksUserApi.md#opencustomer) | **POST** /admin/users/customer/open | API kich hoat lai dịch vụ ebanking
[*ResourceForManageBanksUserApi*](doc/ResourceForManageBanksUserApi.md) | [**registerCustomer**](doc/ResourceForManageBanksUserApi.md#registercustomer) | **POST** /admin/users/customer/register | API đăng ký cho user đã có CIF
[*ResourceForManageBanksUserApi*](doc/ResourceForManageBanksUserApi.md) | [**resetPassword**](doc/ResourceForManageBanksUserApi.md#resetpassword) | **POST** /admin/users/customer/resetPassword | API để reset password
[*SaleAppCollaboratorControllerApi*](doc/SaleAppCollaboratorControllerApi.md) | [**registerSaleAppCollab**](doc/SaleAppCollaboratorControllerApi.md#registersaleappcollab) | **POST** /saleAppCollab/v1/register | API Tạo hồ sơ đăng ký CTV
[*TermsControllerApi*](doc/TermsControllerApi.md) | [**checkTermsAgreed**](doc/TermsControllerApi.md#checktermsagreed) | **GET** /api/terms/v1/check | 
[*TermsControllerApi*](doc/TermsControllerApi.md) | [**createTerms**](doc/TermsControllerApi.md#createterms) | **POST** /api/terms/v1/create | 
[*UserAppSettingResourceApi*](doc/UserAppSettingResourceApi.md) | [**getAuthenTransByBiometricsSetting**](doc/UserAppSettingResourceApi.md#getauthentransbybiometricssetting) | **GET** /users/app-settings/authen-trans-by-biometrics-setting | API lấy thông tin cài đặt xác thực giao dịch nhanh bằng sinh trắc học.
[*UserAppSettingResourceApi*](doc/UserAppSettingResourceApi.md) | [**getLoginByBiometricsSetting**](doc/UserAppSettingResourceApi.md#getloginbybiometricssetting) | **GET** /users/app-settings/login-by-biometrics-setting | API lấy thông tin cài đặt đăng nhập bằng sinh trắc học.
[*UserAppSettingResourceApi*](doc/UserAppSettingResourceApi.md) | [**updateAuthenTransByBiometricsSetting**](doc/UserAppSettingResourceApi.md#updateauthentransbybiometricssetting) | **PUT** /users/app-settings/authen-trans-by-biometrics-setting | API cập nhật thông tin cài đặt xác thực giao dịch nhanh bằng sinh trắc học.
[*UserAppSettingResourceApi*](doc/UserAppSettingResourceApi.md) | [**updateLoginByBiometricsSetting**](doc/UserAppSettingResourceApi.md#updateloginbybiometricssetting) | **PUT** /users/app-settings/login-by-biometrics-setting | API cập nhật thông tin cài đặt đăng nhập bằng sinh trắc học.
[*UserBankInfoResourceApi*](doc/UserBankInfoResourceApi.md) | [**getEkycStatus1**](doc/UserBankInfoResourceApi.md#getekycstatus1) | **GET** /users/ekyc-status | API lấy thông tin trạng thái ekyc của khách hàng.
[*UserBankInfoResourceApi*](doc/UserBankInfoResourceApi.md) | [**getUserBankInfo**](doc/UserBankInfoResourceApi.md#getuserbankinfo) | **GET** /users/bank-info | API lấy thông tin người dùng liên kết tài khoản bank.
[*UserBankInfoResourceApi*](doc/UserBankInfoResourceApi.md) | [**getUserBankInfoViaCif**](doc/UserBankInfoResourceApi.md#getuserbankinfoviacif) | **GET** /users/bank-info/{cifNumber} | API lấy thông tin người dùng liên kết tài khoản bank thông qua số cif.
[*UserBankInfoResourceApi*](doc/UserBankInfoResourceApi.md) | [**updateUserBankPackage**](doc/UserBankInfoResourceApi.md#updateuserbankpackage) | **PUT** /users/bank-info/ebank-package | API cập nhật gói ebanking của khách hàng.
[*UserBankInfoResourceApi*](doc/UserBankInfoResourceApi.md) | [**updateUserBankPackageV1**](doc/UserBankInfoResourceApi.md#updateuserbankpackagev1) | **PUT** /users/v1/bank-info/ebank-package | API cập nhật gói ebanking của khách hàng.
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**changePassword**](doc/UserCommandResourceApi.md#changepassword) | **PUT** /users/password/change/{username} | API đổi mật khẩu
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**checkNeedVerifyType**](doc/UserCommandResourceApi.md#checkneedverifytype) | **GET** /users/2345/v1/checkNeedVerifyType | [2345] API KIỂM TRA KH CẦN UPDATE CCCD CHIP HAY FACE_ID
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**compareCustomerFace2345**](doc/UserCommandResourceApi.md#comparecustomerface2345) | **POST** /users/2345/v1/compare-customer-face | [2345] So sánh ảnh face KH
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**confirmUserIdentity**](doc/UserCommandResourceApi.md#confirmuseridentity) | **POST** /users/register/confirm | Verify to register user
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**createNewUser**](doc/UserCommandResourceApi.md#createnewuser) | **POST** /users/create | API Tạo mới user
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**createNewUserV1**](doc/UserCommandResourceApi.md#createnewuserv1) | **POST** /users/v1/create | API Tạo mới user
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**deActiveSoftOtp**](doc/UserCommandResourceApi.md#deactivesoftotp) | **POST** /users/soft-otp/remove | Hủy đăng kí soft OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**deletePlayerId**](doc/UserCommandResourceApi.md#deleteplayerid) | **DELETE** /users/deletePlayerId | API xóa playerID khi ấn nút đăng xuất
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**finishVerify2345**](doc/UserCommandResourceApi.md#finishverify2345) | **POST** /users/2345/v1/finish-verify | [2345] Bước cuối verify 2345, đẩy dữ liệu vào rà soát
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**forgotPassword**](doc/UserCommandResourceApi.md#forgotpassword) | **POST** /users/password/forgot/request | API quên mật khẩu
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**generateOtp**](doc/UserCommandResourceApi.md#generateotp) | **POST** /users/otp/generate | API gửi tao mã OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**generateOtpForTest**](doc/UserCommandResourceApi.md#generateotpfortest) | **POST** /users/otp-for-auto-test/generate | API gửi tao mã dành cho auto test
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**getEtokenStatus**](doc/UserCommandResourceApi.md#getetokenstatus) | **POST** /users/soft-otp/etoken-status | API lấy trạng thái của ETOKEN
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**getFinalEkycResult**](doc/UserCommandResourceApi.md#getfinalekycresult) | **POST** /users/ekyc/result/{userIdentity} | Final result for ekyc
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**getLivenessType**](doc/UserCommandResourceApi.md#getlivenesstype) | **POST** /users/ekyc/getLivenessType | GET LIVENESS TYPE
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**isPasswordCorrect**](doc/UserCommandResourceApi.md#ispasswordcorrect) | **POST** /users/password/verify | Xác thực mật khẩu
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**isPasswordCorrectV1**](doc/UserCommandResourceApi.md#ispasswordcorrectv1) | **POST** /users/password/v1/verify | Xác thực mật khẩu
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**isPasswordCorrectV2**](doc/UserCommandResourceApi.md#ispasswordcorrectv2) | **POST** /users/password/v2/verify | Xác thực mật khẩu
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**klbCancelMobileVNP**](doc/UserCommandResourceApi.md#klbcancelmobilevnp) | **POST** /users/v1/klbCancelMobileVNP | Hủy dịch vụ trên app Kien long cũ
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**login**](doc/UserCommandResourceApi.md#login) | **POST** /users/login | API đăng nhập
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**loginOpenApi**](doc/UserCommandResourceApi.md#loginopenapi) | **POST** /users/open/v1/login | API đăng nhập
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**logoutAllSession**](doc/UserCommandResourceApi.md#logoutallsession) | **DELETE** /users/logout/all-devices | API đăng xuất tất cả các thiết bị
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**logoutSingleSession**](doc/UserCommandResourceApi.md#logoutsinglesession) | **DELETE** /users/logout/current-device | API đăng xuất trên thiết bị hiện tại
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**pinEtokenVerify**](doc/UserCommandResourceApi.md#pinetokenverify) | **POST** /users/soft-otp/pin-verified | 
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**reInstallSoftOTP**](doc/UserCommandResourceApi.md#reinstallsoftotp) | **POST** /users/re-install/etoken | [Lấy Etoken khi đã cài trên thiết bị khác] - 4. Api trả về secret mới
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**recoverNewPassword**](doc/UserCommandResourceApi.md#recovernewpassword) | **POST** /users/password/forgot/new-password | 
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**refreshToken**](doc/UserCommandResourceApi.md#refreshtoken) | **POST** /users/refresh-token/{refreshToken} | API lấy lại token mới
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**registerSoftOtp**](doc/UserCommandResourceApi.md#registersoftotp) | **POST** /users/soft-otp/register | 
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveAndReadIdCardBy2345**](doc/UserCommandResourceApi.md#saveandreadidcardby2345) | **POST** /users/2345/v1/save-id-card | [2345] API save id card
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveAndReadIdCardByEkyc**](doc/UserCommandResourceApi.md#saveandreadidcardbyekyc) | **POST** /users/ekyc/save-id-card | Verify id card by ekyc
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveAndReadIdCardByEkycV1**](doc/UserCommandResourceApi.md#saveandreadidcardbyekycv1) | **POST** /users/ekyc/v1/save-id-card | Verify id card by ekyc
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveFaceId**](doc/UserCommandResourceApi.md#savefaceid) | **POST** /users/ekyc/save-face-id | Save face id by ekyc
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveLiveNess**](doc/UserCommandResourceApi.md#saveliveness) | **POST** /users/ekyc/save-live-ness | Save live ness
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveProfileLiveNess**](doc/UserCommandResourceApi.md#saveprofileliveness) | **POST** /users/ekyc/profileLiveness/save-live-ness | SAVE PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveProfileLiveNess2345**](doc/UserCommandResourceApi.md#saveprofileliveness2345) | **POST** /users/2345/v1/profileLiveness/save-live-ness | [2345] SAVE PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveProfileLiveNessTransaction2345**](doc/UserCommandResourceApi.md#saveprofilelivenesstransaction2345) | **POST** /users/2345/transaction/v1/profileLiveness/save-live-ness | [MOBILE_CALL] 2. SAVE PROFILE LIVENESS TRANSACTION
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveVideoProfileLiveNessTransaction2345**](doc/UserCommandResourceApi.md#savevideoprofilelivenesstransaction2345) | **POST** /users/2345/transaction/v1/profileLiveness/save-video-liveness | [MOBILE_CALL] 4. SAVE VIDEO PROFILE LIVENESS TRANSACTION
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveVideoProfileLiveness**](doc/UserCommandResourceApi.md#savevideoprofileliveness) | **POST** /users/ekyc/profileLiveness/save-video-liveness | SAVE VIDEO PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**saveVideoProfileLiveness2345**](doc/UserCommandResourceApi.md#savevideoprofileliveness2345) | **POST** /users/2345/v1/profileLiveness/save-video-liveness | [2345] SAVE VIDEO PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**startLiveNess**](doc/UserCommandResourceApi.md#startliveness) | **POST** /users/ekyc/start-live-ness | Start live ness
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**startLiveNess1**](doc/UserCommandResourceApi.md#startliveness1) | **POST** /users/ekyc/profileLiveness/start-live-ness | START PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**startProfileLiveNess2345**](doc/UserCommandResourceApi.md#startprofileliveness2345) | **POST** /users/2345/v1/profileLiveness/start-live-ness | [2345] START PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**startProfileLiveNessTransaction2345**](doc/UserCommandResourceApi.md#startprofilelivenesstransaction2345) | **POST** /users/2345/transaction/v1/profileLiveness/start-live-ness | [2345] START PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**updateCustomerGroup**](doc/UserCommandResourceApi.md#updatecustomergroup) | **PUT** /users/{userId}/customer-group/{customerGroupId} | Cập nhật thông tin nhóm khách hàng
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**updateNotificationInfo**](doc/UserCommandResourceApi.md#updatenotificationinfo) | **POST** /users/notificationInfo | API update thông tin user
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**updateOcrByNFC2345**](doc/UserCommandResourceApi.md#updateocrbynfc2345) | **POST** /users/2345/v1/update-ocr | [2345] Update info by mobile NFC
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**updateOcrByUser**](doc/UserCommandResourceApi.md#updateocrbyuser) | **POST** /users/ekyc/update-ocr | Update info by user
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**updateUserAddress**](doc/UserCommandResourceApi.md#updateuseraddress) | **PUT** /users/updateAddress | API cập nhật thông tin địa chỉ người dùng
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**updateUserInfo**](doc/UserCommandResourceApi.md#updateuserinfo) | **PUT** /users/update | API cập nhật thông tin người dùng
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**updateUserInfo1**](doc/UserCommandResourceApi.md#updateuserinfo1) | **POST** /users/update-info | [Thay đổi thông tin người dùng] - 2. Nhập vào thông tin cần thay đổi
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyAdvanceSoftOtp**](doc/UserCommandResourceApi.md#verifyadvancesoftotp) | **POST** /users/soft-otp/verify-advance-otp | API xác thực OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyEmailOtpToUpdateInfo**](doc/UserCommandResourceApi.md#verifyemailotptoupdateinfo) | **POST** /users/update-info/verify-email-otp | [Thay đổi thông tin người dùng] - 4. Verify SMS OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyIdCardSide**](doc/UserCommandResourceApi.md#verifyidcardside) | **POST** /users/ekyc/verifyIdCardSide | VERIFY ID CARD SIDE
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyIdCardSide1**](doc/UserCommandResourceApi.md#verifyidcardside1) | **POST** /users/2345/v1/verifyIdCardSide | [2345] VERIFY ID CARD SIDE
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyIdCardWithFaceId**](doc/UserCommandResourceApi.md#verifyidcardwithfaceid) | **POST** /users/ekyc/verify-id-card | Save face id by ekyc
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyIdCardWithFaceId2345**](doc/UserCommandResourceApi.md#verifyidcardwithfaceid2345) | **POST** /users/2345/v1/verify-id-card | [2345] VERIFY ID CARD
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyImportantInfo**](doc/UserCommandResourceApi.md#verifyimportantinfo) | **POST** /users/update/important-request | Yêu cầu thay đổi thông tin quan trong
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyLiveNessByEkyc**](doc/UserCommandResourceApi.md#verifylivenessbyekyc) | **POST** /users/ekyc/verify-live-ness | Verify live ness by ekyc
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyOtp**](doc/UserCommandResourceApi.md#verifyotp) | **POST** /users/otp/verify | API xác thực OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyPasswordToResetEtoken**](doc/UserCommandResourceApi.md#verifypasswordtoresetetoken) | **POST** /users/re-install/etoken/verify-password | [Lấy Etoken khi đã cài trên thiết bị khác] - 1. Kiểm tra mật khẩu
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyPasswordToUpdateInfo**](doc/UserCommandResourceApi.md#verifypasswordtoupdateinfo) | **POST** /users/update-info/verify-password | [Thay đổi thông tin người dùng] - 1. Xác nhận mật khẩu
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyProfileLiveNess**](doc/UserCommandResourceApi.md#verifyprofileliveness) | **POST** /users/ekyc/profileLiveness/verify-live-ness | VERIFY PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyProfileLiveNess2345**](doc/UserCommandResourceApi.md#verifyprofileliveness2345) | **POST** /users/2345/v1/profileLiveness/verify-live-ness | [2345] VERIFY PROFILE LIVENESS
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyProfileLiveNessTransaction2345**](doc/UserCommandResourceApi.md#verifyprofilelivenesstransaction2345) | **POST** /users/2345/transaction/v1/profileLiveness/verify-live-ness | [MOBILE_CALL] 3. VERIFY PROFILE LIVENESS TRANSACTION
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifySmsOtpToResetEtoken**](doc/UserCommandResourceApi.md#verifysmsotptoresetetoken) | **POST** /users/re-install/etoken/verify-sms-otp | [Lấy Etoken khi đã cài trên thiết bị khác] - 3. Verify SMS OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifySmsOtpToUpdateInfo**](doc/UserCommandResourceApi.md#verifysmsotptoupdateinfo) | **POST** /users/update-info/verify-sms-otp | [Thay đổi thông tin người dùng] - 3. Verify SMS OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifySoftOtp**](doc/UserCommandResourceApi.md#verifysoftotp) | **POST** /users/soft-otp/verify | API xác thực OTP
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyUserIdentity**](doc/UserCommandResourceApi.md#verifyuseridentity) | **POST** /users/register/verify | Verify to register user
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyUserIdentityV1**](doc/UserCommandResourceApi.md#verifyuseridentityv1) | **POST** /users/register/v1/verify | Verify to register user
[*UserCommandResourceApi*](doc/UserCommandResourceApi.md) | [**verifyUserInfoToResetEtoken**](doc/UserCommandResourceApi.md#verifyuserinfotoresetetoken) | **POST** /users/re-install/etoken/verify-user-info | [Lấy Etoken khi đã cài trên thiết bị khác] - 2. Kiểm tra thông tin thẻ của người dùng
[*UserNotificationSettingResourceApi*](doc/UserNotificationSettingResourceApi.md) | [**getNotificationSetting**](doc/UserNotificationSettingResourceApi.md#getnotificationsetting) | **GET** /users/setting/notification | 
[*UserNotificationSettingResourceApi*](doc/UserNotificationSettingResourceApi.md) | [**updateNotification**](doc/UserNotificationSettingResourceApi.md#updatenotification) | **PUT** /users/setting/notification | 
[*UserNotificationSettingResourceApi*](doc/UserNotificationSettingResourceApi.md) | [**updateNotificationSetting**](doc/UserNotificationSettingResourceApi.md#updatenotificationsetting) | **POST** /users/setting/notification | Thay đổi trạng thái đăng kí nhận notification của người dùng
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**checkUserAndSendOtp**](doc/UserQueryResourceApi.md#checkuserandsendotp) | **POST** /users/external/v1/checkUserAndSendOtp | API lấy kiểm ta xem user đã có tài khoản klb chưa
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**checkUserKlb**](doc/UserQueryResourceApi.md#checkuserklb) | **POST** /users/external/v1/checkUserKlb | API lấy kiểm ta xem user đã có tài khoản klb chưa
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**generateToken**](doc/UserQueryResourceApi.md#generatetoken) | **GET** /users/generate-token | API sinh token mã hóa thông tin người dùng
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getBranchDetailByCode**](doc/UserQueryResourceApi.md#getbranchdetailbycode) | **GET** /users/branch/{branchCode} | API lấy thông tin chi nhánh theo branch code
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getCustomerRankInfo**](doc/UserQueryResourceApi.md#getcustomerrankinfo) | **GET** /users/customer_rank | API lấy thông tin phân hạng của người dùng
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getEkycStatus**](doc/UserQueryResourceApi.md#getekycstatus) | **GET** /users/v1/getEkycStatus | API lấy thông tin trạng thái ekyc
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getListUserIds**](doc/UserQueryResourceApi.md#getlistuserids) | **GET** /users/internal/v1/getListUserId | 
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getListUserInfoByListUserId**](doc/UserQueryResourceApi.md#getlistuserinfobylistuserid) | **POST** /users/internal/v1/getListUserInfoByListUserId | API lấy danh sách UserInfo bằng danh sách userId
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getListUserInfoByListUserIdAndBranch**](doc/UserQueryResourceApi.md#getlistuserinfobylistuseridandbranch) | **POST** /users/internal/v1/getListUserInfoByListUserIdAndBranch | API lấy danh sách UserInfo bằng danh sách userId
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserBranch**](doc/UserQueryResourceApi.md#getuserbranch) | **GET** /users/v1/branchOpenCif | API lấy thông tin chi nhánh đăng ký dịch vụ ngân hàng điện tử
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserByBirthday**](doc/UserQueryResourceApi.md#getuserbybirthday) | **GET** /users/get-by-birthday | API lấy danh sách cif có ngày sinh vào ngày sinh truyền vào
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserInfo**](doc/UserQueryResourceApi.md#getuserinfo) | **GET** /users/detail | API lấy thông tin cho tiết người dùng
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserInfoByCif**](doc/UserQueryResourceApi.md#getuserinfobycif) | **GET** /users/internal/v1/getUserInfoByCifNo | API lấy thông tin user không ở trạng thái khóa hoặc hủy bằng số cif
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserInfoByFaceId**](doc/UserQueryResourceApi.md#getuserinfobyfaceid) | **GET** /users/internal/v1/getUserInfoByFaceId | API lấy thông tin user không ở trạng thái khóa hoặc hủy bằng FaceId
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserInfoByUserId**](doc/UserQueryResourceApi.md#getuserinfobyuserid) | **GET** /users/detail/v1/{user_id} | API lấy thông tin cho tiết người dùng
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserInfoOpen**](doc/UserQueryResourceApi.md#getuserinfoopen) | **GET** /users/open/v1/detail | 
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserInfoV1**](doc/UserQueryResourceApi.md#getuserinfov1) | **GET** /users/v1/detail | API lấy thông tin cho tiết người dùng
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**getUserInfoV2**](doc/UserQueryResourceApi.md#getuserinfov2) | **GET** /users/v2/detail | API lấy thông tin cho tiết người dùng
[*UserQueryResourceApi*](doc/UserQueryResourceApi.md) | [**verifyOtpAndGetUserInfo**](doc/UserQueryResourceApi.md#verifyotpandgetuserinfo) | **POST** /users/external/v1/verifyOtpAndGetUserInfo | API lấy kiểm ta xem user đã có tài khoản klb chưa
[*UserSettingResourceApi*](doc/UserSettingResourceApi.md) | [**getListTheme**](doc/UserSettingResourceApi.md#getlisttheme) | **GET** /users/setting/theme | Lấy danh sách tất cả các theme của App KienLongBank Plus
[*UserSettingResourceApi*](doc/UserSettingResourceApi.md) | [**getSetting**](doc/UserSettingResourceApi.md#getsetting) | **GET** /users/setting/sync | 
[*UserSettingResourceApi*](doc/UserSettingResourceApi.md) | [**getThemeDetail**](doc/UserSettingResourceApi.md#getthemedetail) | **GET** /users/setting/theme/detail | Lấy một theme chỉ định của App KienLongBank Plus
[*UserSettingResourceApi*](doc/UserSettingResourceApi.md) | [**syncSetting**](doc/UserSettingResourceApi.md#syncsetting) | **POST** /users/setting/sync | 
[*UserSettingResourceApi*](doc/UserSettingResourceApi.md) | [**updateThemeSchedule**](doc/UserSettingResourceApi.md#updatethemeschedule) | **POST** /users/setting/theme/schedule | Update schedule cho một theme, đc gọi bởi CMS


## Documentation For Models

 - [AccessTokenResponse](doc/AccessTokenResponse.md)
 - [AdvanceOTPRequest](doc/AdvanceOTPRequest.md)
 - [AuthenTransByBiometricsSettingResponse](doc/AuthenTransByBiometricsSettingResponse.md)
 - [BaseResponse](doc/BaseResponse.md)
 - [BaseResponseAuthenTransByBiometricsSettingResponse](doc/BaseResponseAuthenTransByBiometricsSettingResponse.md)
 - [BaseResponseBirthdayUserResponse](doc/BaseResponseBirthdayUserResponse.md)
 - [BaseResponseBoolean](doc/BaseResponseBoolean.md)
 - [BaseResponseCheckNeedVerifyTypeResponse](doc/BaseResponseCheckNeedVerifyTypeResponse.md)
 - [BaseResponseCheckUserAndSendOtpResponse](doc/BaseResponseCheckUserAndSendOtpResponse.md)
 - [BaseResponseCheckUserKlbResponse](doc/BaseResponseCheckUserKlbResponse.md)
 - [BaseResponseCheckUserProfileExistResponse](doc/BaseResponseCheckUserProfileExistResponse.md)
 - [BaseResponseCompareCustomerFace2345Response](doc/BaseResponseCompareCustomerFace2345Response.md)
 - [BaseResponseCreateNotificationResponse](doc/BaseResponseCreateNotificationResponse.md)
 - [BaseResponseCreateQuickVerifyResponse](doc/BaseResponseCreateQuickVerifyResponse.md)
 - [BaseResponseCreateTransactionLivenessSessionResponse](doc/BaseResponseCreateTransactionLivenessSessionResponse.md)
 - [BaseResponseCreateUserFinalResponse](doc/BaseResponseCreateUserFinalResponse.md)
 - [BaseResponseCreateUserFromSTMResponse](doc/BaseResponseCreateUserFromSTMResponse.md)
 - [BaseResponseCreateUserFromVNPostResponse](doc/BaseResponseCreateUserFromVNPostResponse.md)
 - [BaseResponseDistrictResponse](doc/BaseResponseDistrictResponse.md)
 - [BaseResponseEkycFinalResponse](doc/BaseResponseEkycFinalResponse.md)
 - [BaseResponseEkycStatusResponse](doc/BaseResponseEkycStatusResponse.md)
 - [BaseResponseEtokenStatus](doc/BaseResponseEtokenStatus.md)
 - [BaseResponseFinishVerify2345Response](doc/BaseResponseFinishVerify2345Response.md)
 - [BaseResponseGenerateTokenResponse](doc/BaseResponseGenerateTokenResponse.md)
 - [BaseResponseGetBranchDetailByCodeResponse](doc/BaseResponseGetBranchDetailByCodeResponse.md)
 - [BaseResponseGetCustomerGroupListResponse](doc/BaseResponseGetCustomerGroupListResponse.md)
 - [BaseResponseGetCustomerGroupUseListResponse](doc/BaseResponseGetCustomerGroupUseListResponse.md)
 - [BaseResponseGetCustomerRankResponse](doc/BaseResponseGetCustomerRankResponse.md)
 - [BaseResponseGetListThemeResponse](doc/BaseResponseGetListThemeResponse.md)
 - [BaseResponseGetListUserIdResponse](doc/BaseResponseGetListUserIdResponse.md)
 - [BaseResponseGetListUserInfoByListUserIdResponse](doc/BaseResponseGetListUserInfoByListUserIdResponse.md)
 - [BaseResponseGetLivenessTypeResponse](doc/BaseResponseGetLivenessTypeResponse.md)
 - [BaseResponseGetNotificationInfoResponse](doc/BaseResponseGetNotificationInfoResponse.md)
 - [BaseResponseGetPlayerIdsByCifNoResponse](doc/BaseResponseGetPlayerIdsByCifNoResponse.md)
 - [BaseResponseGetQuickVerifyByIdentityResponse](doc/BaseResponseGetQuickVerifyByIdentityResponse.md)
 - [BaseResponseGetThemeDetailResponse](doc/BaseResponseGetThemeDetailResponse.md)
 - [BaseResponseGetTransactionLivenessSessionResultResponse](doc/BaseResponseGetTransactionLivenessSessionResultResponse.md)
 - [BaseResponseGetUserBranchResponse](doc/BaseResponseGetUserBranchResponse.md)
 - [BaseResponseGetUserInfoByCifResponse](doc/BaseResponseGetUserInfoByCifResponse.md)
 - [BaseResponseGetUserKlbInfoResponse](doc/BaseResponseGetUserKlbInfoResponse.md)
 - [BaseResponseLoginByBiometricsSettingResponse](doc/BaseResponseLoginByBiometricsSettingResponse.md)
 - [BaseResponseMapStringObject](doc/BaseResponseMapStringObject.md)
 - [BaseResponseMigrateCustomerFromOldSysResponse](doc/BaseResponseMigrateCustomerFromOldSysResponse.md)
 - [BaseResponseMigrateKsfCustomerResponse](doc/BaseResponseMigrateKsfCustomerResponse.md)
 - [BaseResponseMigrateUserCrmResponse](doc/BaseResponseMigrateUserCrmResponse.md)
 - [BaseResponseOTPToken](doc/BaseResponseOTPToken.md)
 - [BaseResponseObject](doc/BaseResponseObject.md)
 - [BaseResponsePageSupportBranchResponse](doc/BaseResponsePageSupportBranchResponse.md)
 - [BaseResponsePageSupportDeviceInfoResponse](doc/BaseResponsePageSupportDeviceInfoResponse.md)
 - [BaseResponsePageSupportDistrictResponse](doc/BaseResponsePageSupportDistrictResponse.md)
 - [BaseResponsePageSupportNetworkResponse](doc/BaseResponsePageSupportNetworkResponse.md)
 - [BaseResponsePageSupportProvinceResponse](doc/BaseResponsePageSupportProvinceResponse.md)
 - [BaseResponsePageSupportWardNetworkResponse](doc/BaseResponsePageSupportWardNetworkResponse.md)
 - [BaseResponsePageSupportWardResponse](doc/BaseResponsePageSupportWardResponse.md)
 - [BaseResponseProvinceResponse](doc/BaseResponseProvinceResponse.md)
 - [BaseResponseQueryInfoResponse](doc/BaseResponseQueryInfoResponse.md)
 - [BaseResponseQueryInfoResponseV1](doc/BaseResponseQueryInfoResponseV1.md)
 - [BaseResponseQueryInfoResponseV2](doc/BaseResponseQueryInfoResponseV2.md)
 - [BaseResponseRegisterSaleAppCollabResponse](doc/BaseResponseRegisterSaleAppCollabResponse.md)
 - [BaseResponseSaveProfileLiveness2345Response](doc/BaseResponseSaveProfileLiveness2345Response.md)
 - [BaseResponseSaveProfileLivenessResponse](doc/BaseResponseSaveProfileLivenessResponse.md)
 - [BaseResponseSaveProfileLivenessTransaction2345Response](doc/BaseResponseSaveProfileLivenessTransaction2345Response.md)
 - [BaseResponseSaveVideoEKYCProfileLiveness2345Response](doc/BaseResponseSaveVideoEKYCProfileLiveness2345Response.md)
 - [BaseResponseSaveVideoEKYCProfileLivenessResponse](doc/BaseResponseSaveVideoEKYCProfileLivenessResponse.md)
 - [BaseResponseSaveVideoProfileLivenessTransaction2345Request](doc/BaseResponseSaveVideoProfileLivenessTransaction2345Request.md)
 - [BaseResponseStartLiveNessResponse](doc/BaseResponseStartLiveNessResponse.md)
 - [BaseResponseStartProfileLiveness2345Response](doc/BaseResponseStartProfileLiveness2345Response.md)
 - [BaseResponseStartProfileLivenessResponse](doc/BaseResponseStartProfileLivenessResponse.md)
 - [BaseResponseStartProfileLivenessTransaction2345Response](doc/BaseResponseStartProfileLivenessTransaction2345Response.md)
 - [BaseResponseString](doc/BaseResponseString.md)
 - [BaseResponseUpdateAuthenTransByBiometricsSettingResponse](doc/BaseResponseUpdateAuthenTransByBiometricsSettingResponse.md)
 - [BaseResponseUpdateCustomerGroupUseResponse](doc/BaseResponseUpdateCustomerGroupUseResponse.md)
 - [BaseResponseUpdateInfoResponse](doc/BaseResponseUpdateInfoResponse.md)
 - [BaseResponseUpdateLoginByBiometricsSettingResponse](doc/BaseResponseUpdateLoginByBiometricsSettingResponse.md)
 - [BaseResponseUpdateOCRByNFC2345Response](doc/BaseResponseUpdateOCRByNFC2345Response.md)
 - [BaseResponseUpdateOcrResponse](doc/BaseResponseUpdateOcrResponse.md)
 - [BaseResponseUpdateThemeScheduleResponse](doc/BaseResponseUpdateThemeScheduleResponse.md)
 - [BaseResponseUpdateUserInfoResponse](doc/BaseResponseUpdateUserInfoResponse.md)
 - [BaseResponseUserBankInfoResponse](doc/BaseResponseUserBankInfoResponse.md)
 - [BaseResponseUserNotificationSettingResponse](doc/BaseResponseUserNotificationSettingResponse.md)
 - [BaseResponseVerifyIdCardFaceId2345Response](doc/BaseResponseVerifyIdCardFaceId2345Response.md)
 - [BaseResponseVerifyIdCardSide2345Response](doc/BaseResponseVerifyIdCardSide2345Response.md)
 - [BaseResponseVerifyIdCardSideResponse](doc/BaseResponseVerifyIdCardSideResponse.md)
 - [BaseResponseVerifyIdentityResponse](doc/BaseResponseVerifyIdentityResponse.md)
 - [BaseResponseVerifyOtpAndGetUserInfoResponse](doc/BaseResponseVerifyOtpAndGetUserInfoResponse.md)
 - [BaseResponseVerifyPasswordResponse](doc/BaseResponseVerifyPasswordResponse.md)
 - [BaseResponseVerifyPasswordToResetEtokenResponse](doc/BaseResponseVerifyPasswordToResetEtokenResponse.md)
 - [BaseResponseVerifyProfileLiveness2345Response](doc/BaseResponseVerifyProfileLiveness2345Response.md)
 - [BaseResponseVerifyProfileLivenessResponse](doc/BaseResponseVerifyProfileLivenessResponse.md)
 - [BaseResponseVerifyProfileLivenessTransaction2345Response](doc/BaseResponseVerifyProfileLivenessTransaction2345Response.md)
 - [BaseResponseVoid](doc/BaseResponseVoid.md)
 - [BirthdayUserResponse](doc/BirthdayUserResponse.md)
 - [BranchImageResponse](doc/BranchImageResponse.md)
 - [BranchResponse](doc/BranchResponse.md)
 - [CheckNeedVerifyTypeResponse](doc/CheckNeedVerifyTypeResponse.md)
 - [CheckUserAndSendOtpRequest](doc/CheckUserAndSendOtpRequest.md)
 - [CheckUserAndSendOtpResponse](doc/CheckUserAndSendOtpResponse.md)
 - [CheckUserKlbRequest](doc/CheckUserKlbRequest.md)
 - [CheckUserKlbResponse](doc/CheckUserKlbResponse.md)
 - [CheckUserProfileExistRequest](doc/CheckUserProfileExistRequest.md)
 - [CheckUserProfileExistResponse](doc/CheckUserProfileExistResponse.md)
 - [CloseCustomerRequest](doc/CloseCustomerRequest.md)
 - [CompareCustomerFace2345Request](doc/CompareCustomerFace2345Request.md)
 - [CompareCustomerFace2345Response](doc/CompareCustomerFace2345Response.md)
 - [ConfirmOtpRequest](doc/ConfirmOtpRequest.md)
 - [ConfirmQuickVerifyRequest](doc/ConfirmQuickVerifyRequest.md)
 - [CreateNotificationResponse](doc/CreateNotificationResponse.md)
 - [CreateQuickVerifyRequest](doc/CreateQuickVerifyRequest.md)
 - [CreateQuickVerifyResponse](doc/CreateQuickVerifyResponse.md)
 - [CreateTermsRequest](doc/CreateTermsRequest.md)
 - [CreateTransactionLivenessSessionRequest](doc/CreateTransactionLivenessSessionRequest.md)
 - [CreateTransactionLivenessSessionResponse](doc/CreateTransactionLivenessSessionResponse.md)
 - [CreateUserFinalResponse](doc/CreateUserFinalResponse.md)
 - [CreateUserFromSTMRequest](doc/CreateUserFromSTMRequest.md)
 - [CreateUserFromSTMResponse](doc/CreateUserFromSTMResponse.md)
 - [CreateUserFromVNPRequest](doc/CreateUserFromVNPRequest.md)
 - [CreateUserFromVNPostResponse](doc/CreateUserFromVNPostResponse.md)
 - [CustomerGroupListDto](doc/CustomerGroupListDto.md)
 - [CustomerGroupUseListDto](doc/CustomerGroupUseListDto.md)
 - [CustomerPasswordChangeRequest](doc/CustomerPasswordChangeRequest.md)
 - [DeviceInfoResponse](doc/DeviceInfoResponse.md)
 - [DistrictResponse](doc/DistrictResponse.md)
 - [EkycFinalResponse](doc/EkycFinalResponse.md)
 - [EkycStatusResponse](doc/EkycStatusResponse.md)
 - [FinishVerify2345Request](doc/FinishVerify2345Request.md)
 - [FinishVerify2345Response](doc/FinishVerify2345Response.md)
 - [ForgotPasswordRequest](doc/ForgotPasswordRequest.md)
 - [GenerateOtpRequest](doc/GenerateOtpRequest.md)
 - [GenerateTokenResponse](doc/GenerateTokenResponse.md)
 - [GetBranchDetailByCodeResponse](doc/GetBranchDetailByCodeResponse.md)
 - [GetBranchRequest](doc/GetBranchRequest.md)
 - [GetCustomerGroupListResponse](doc/GetCustomerGroupListResponse.md)
 - [GetCustomerGroupUseListResponse](doc/GetCustomerGroupUseListResponse.md)
 - [GetCustomerRankResponse](doc/GetCustomerRankResponse.md)
 - [GetListThemeResponse](doc/GetListThemeResponse.md)
 - [GetListUserIdResponse](doc/GetListUserIdResponse.md)
 - [GetListUserInfoByListUserIdAndBranchRequest](doc/GetListUserInfoByListUserIdAndBranchRequest.md)
 - [GetListUserInfoByListUserIdRequest](doc/GetListUserInfoByListUserIdRequest.md)
 - [GetListUserInfoByListUserIdResponse](doc/GetListUserInfoByListUserIdResponse.md)
 - [GetLivenessTypeRequest](doc/GetLivenessTypeRequest.md)
 - [GetLivenessTypeResponse](doc/GetLivenessTypeResponse.md)
 - [GetNotificationInfoResponse](doc/GetNotificationInfoResponse.md)
 - [GetPlayerIdsByCifNoResponse](doc/GetPlayerIdsByCifNoResponse.md)
 - [GetQuickVerifyByIdentityResponse](doc/GetQuickVerifyByIdentityResponse.md)
 - [GetThemeDetailResponse](doc/GetThemeDetailResponse.md)
 - [GetTransactionLivenessSessionResultResponse](doc/GetTransactionLivenessSessionResultResponse.md)
 - [GetUserBranchResponse](doc/GetUserBranchResponse.md)
 - [GetUserInfoByCifResponse](doc/GetUserInfoByCifResponse.md)
 - [GetUserKlbInfoResponse](doc/GetUserKlbInfoResponse.md)
 - [LoginByBiometricsSettingResponse](doc/LoginByBiometricsSettingResponse.md)
 - [MigrateCustomerFromOldSysResponse](doc/MigrateCustomerFromOldSysResponse.md)
 - [MigrateKsfCustomerResponse](doc/MigrateKsfCustomerResponse.md)
 - [MigrateUserCrmResponse](doc/MigrateUserCrmResponse.md)
 - [NetworkResponse](doc/NetworkResponse.md)
 - [NotificationRequest](doc/NotificationRequest.md)
 - [NullType](doc/NullType.md)
 - [OTPToken](doc/OTPToken.md)
 - [OpenCustomerRequest](doc/OpenCustomerRequest.md)
 - [PageSupportBranchResponse](doc/PageSupportBranchResponse.md)
 - [PageSupportDeviceInfoResponse](doc/PageSupportDeviceInfoResponse.md)
 - [PageSupportDistrictResponse](doc/PageSupportDistrictResponse.md)
 - [PageSupportNetworkResponse](doc/PageSupportNetworkResponse.md)
 - [PageSupportProvinceResponse](doc/PageSupportProvinceResponse.md)
 - [PageSupportWardNetworkResponse](doc/PageSupportWardNetworkResponse.md)
 - [PageSupportWardResponse](doc/PageSupportWardResponse.md)
 - [PasswordChangeRequest](doc/PasswordChangeRequest.md)
 - [ProvinceResponse](doc/ProvinceResponse.md)
 - [QueryInfoResponse](doc/QueryInfoResponse.md)
 - [QueryInfoResponseV1](doc/QueryInfoResponseV1.md)
 - [QueryInfoResponseV2](doc/QueryInfoResponseV2.md)
 - [RecoverPasswordRequest](doc/RecoverPasswordRequest.md)
 - [RegisterCifCustomerRequest](doc/RegisterCifCustomerRequest.md)
 - [RegisterSaleAppCollabRequest](doc/RegisterSaleAppCollabRequest.md)
 - [RegisterSaleAppCollabResponse](doc/RegisterSaleAppCollabResponse.md)
 - [RegistrationRequest](doc/RegistrationRequest.md)
 - [RegistrationV1Request](doc/RegistrationV1Request.md)
 - [ReinstallEtokenRequest](doc/ReinstallEtokenRequest.md)
 - [SaveAndReadIdCard2345Request](doc/SaveAndReadIdCard2345Request.md)
 - [SaveLiveNessRequest](doc/SaveLiveNessRequest.md)
 - [SaveProfileLiveness2345Request](doc/SaveProfileLiveness2345Request.md)
 - [SaveProfileLiveness2345Response](doc/SaveProfileLiveness2345Response.md)
 - [SaveProfileLivenessRequest](doc/SaveProfileLivenessRequest.md)
 - [SaveProfileLivenessResponse](doc/SaveProfileLivenessResponse.md)
 - [SaveProfileLivenessTransaction2345Request](doc/SaveProfileLivenessTransaction2345Request.md)
 - [SaveProfileLivenessTransaction2345Response](doc/SaveProfileLivenessTransaction2345Response.md)
 - [SaveVideoEKYCProfileLiveness2345Request](doc/SaveVideoEKYCProfileLiveness2345Request.md)
 - [SaveVideoEKYCProfileLiveness2345Response](doc/SaveVideoEKYCProfileLiveness2345Response.md)
 - [SaveVideoEKYCProfileLivenessRequest](doc/SaveVideoEKYCProfileLivenessRequest.md)
 - [SaveVideoEKYCProfileLivenessResponse](doc/SaveVideoEKYCProfileLivenessResponse.md)
 - [SaveVideoProfileLivenessTransaction2345Request](doc/SaveVideoProfileLivenessTransaction2345Request.md)
 - [SignInRequest](doc/SignInRequest.md)
 - [StartLiveNessRequest](doc/StartLiveNessRequest.md)
 - [StartLiveNessResponse](doc/StartLiveNessResponse.md)
 - [StartProfileLiveness2345Request](doc/StartProfileLiveness2345Request.md)
 - [StartProfileLiveness2345Response](doc/StartProfileLiveness2345Response.md)
 - [StartProfileLivenessRequest](doc/StartProfileLivenessRequest.md)
 - [StartProfileLivenessResponse](doc/StartProfileLivenessResponse.md)
 - [StartProfileLivenessTransaction2345Request](doc/StartProfileLivenessTransaction2345Request.md)
 - [StartProfileLivenessTransaction2345Response](doc/StartProfileLivenessTransaction2345Response.md)
 - [ThemeData](doc/ThemeData.md)
 - [UpdateAuthenTransByBiometricsSettingRequest](doc/UpdateAuthenTransByBiometricsSettingRequest.md)
 - [UpdateAuthenTransByBiometricsSettingResponse](doc/UpdateAuthenTransByBiometricsSettingResponse.md)
 - [UpdateEbankingPackageRequest](doc/UpdateEbankingPackageRequest.md)
 - [UpdateInfoRequest](doc/UpdateInfoRequest.md)
 - [UpdateInfoResponse](doc/UpdateInfoResponse.md)
 - [UpdateLoginByBiometricsSettingRequest](doc/UpdateLoginByBiometricsSettingRequest.md)
 - [UpdateLoginByBiometricsSettingResponse](doc/UpdateLoginByBiometricsSettingResponse.md)
 - [UpdateNotificationInfoRequest](doc/UpdateNotificationInfoRequest.md)
 - [UpdateOCRByNFC2345Request](doc/UpdateOCRByNFC2345Request.md)
 - [UpdateOCRByNFC2345Response](doc/UpdateOCRByNFC2345Response.md)
 - [UpdateOcrRequest](doc/UpdateOcrRequest.md)
 - [UpdateOcrResponse](doc/UpdateOcrResponse.md)
 - [UpdateThemeScheduleRequest](doc/UpdateThemeScheduleRequest.md)
 - [UpdateThemeScheduleResponse](doc/UpdateThemeScheduleResponse.md)
 - [UpdateUserAddressRequest](doc/UpdateUserAddressRequest.md)
 - [UpdateUserInfoRequest](doc/UpdateUserInfoRequest.md)
 - [UpdateUserInfoResponse](doc/UpdateUserInfoResponse.md)
 - [User2345VerifyType](doc/User2345VerifyType.md)
 - [UserBankInfoResponse](doc/UserBankInfoResponse.md)
 - [UserIdAndPhone](doc/UserIdAndPhone.md)
 - [UserInfoData](doc/UserInfoData.md)
 - [UserNotificationSettingRequest](doc/UserNotificationSettingRequest.md)
 - [UserNotificationSettingResponse](doc/UserNotificationSettingResponse.md)
 - [ValidateUserRequest](doc/ValidateUserRequest.md)
 - [VerifyEkycRequest](doc/VerifyEkycRequest.md)
 - [VerifyEkycV1Request](doc/VerifyEkycV1Request.md)
 - [VerifyEmailOtpToUpdateInfoRequest](doc/VerifyEmailOtpToUpdateInfoRequest.md)
 - [VerifyIdCardFaceId2345Request](doc/VerifyIdCardFaceId2345Request.md)
 - [VerifyIdCardFaceId2345Response](doc/VerifyIdCardFaceId2345Response.md)
 - [VerifyIdCardFaceIdRequest](doc/VerifyIdCardFaceIdRequest.md)
 - [VerifyIdCardSide2345Request](doc/VerifyIdCardSide2345Request.md)
 - [VerifyIdCardSide2345Response](doc/VerifyIdCardSide2345Response.md)
 - [VerifyIdCardSideRequest](doc/VerifyIdCardSideRequest.md)
 - [VerifyIdCardSideResponse](doc/VerifyIdCardSideResponse.md)
 - [VerifyIdentityRequest](doc/VerifyIdentityRequest.md)
 - [VerifyIdentityResponse](doc/VerifyIdentityResponse.md)
 - [VerifyImportantInfoRequest](doc/VerifyImportantInfoRequest.md)
 - [VerifyOtpAndGetUserInfoRequest](doc/VerifyOtpAndGetUserInfoRequest.md)
 - [VerifyOtpAndGetUserInfoResponse](doc/VerifyOtpAndGetUserInfoResponse.md)
 - [VerifyOtpForUnActiveUserRequest](doc/VerifyOtpForUnActiveUserRequest.md)
 - [VerifyOtpRequest](doc/VerifyOtpRequest.md)
 - [VerifyOtpToResetEtokenRequest](doc/VerifyOtpToResetEtokenRequest.md)
 - [VerifyPasswordRequest](doc/VerifyPasswordRequest.md)
 - [VerifyPasswordResponse](doc/VerifyPasswordResponse.md)
 - [VerifyPasswordToResetEtokenResponse](doc/VerifyPasswordToResetEtokenResponse.md)
 - [VerifyProfileLiveness2345Request](doc/VerifyProfileLiveness2345Request.md)
 - [VerifyProfileLiveness2345Response](doc/VerifyProfileLiveness2345Response.md)
 - [VerifyProfileLivenessRequest](doc/VerifyProfileLivenessRequest.md)
 - [VerifyProfileLivenessResponse](doc/VerifyProfileLivenessResponse.md)
 - [VerifyProfileLivenessTransaction2345Request](doc/VerifyProfileLivenessTransaction2345Request.md)
 - [VerifyProfileLivenessTransaction2345Response](doc/VerifyProfileLivenessTransaction2345Response.md)
 - [VerifySmSOtpToUpdateUserInfoRequest](doc/VerifySmSOtpToUpdateUserInfoRequest.md)
 - [VerifyUserInfoRequest](doc/VerifyUserInfoRequest.md)
 - [WardNetworkResponse](doc/WardNetworkResponse.md)
 - [WardResponse](doc/WardResponse.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### Authorization

- **Type**: HTTP Bearer Token authentication (Bearer [token])


## Author



