import 'package:ksb_bloc/bloc.dart';

import '../bloc.dart';

mixin BaseAuthBloc on BaseBloc {
  String? _transactionNumber;

  TransNextStep? _step = TransNextStep.NONE;

  bool get showFaceID => _step == TransNextStep.ENABLE_FACE_ID;

  double? _amount;
  TransactionCategoriesType? _transactionType;
  String? _messageDialog;

  double? get amount => _amount;

  String? get transactionNumber => _transactionNumber;

  void setTransactionNumber(String? transactionNumber) {
    _transactionNumber = transactionNumber;
  }

  TransNextStep? get step => _step;

  void setStep(TransNextStep step) {
    _step = step;
  }

  String? get messageDialog => _messageDialog;

  void setAuthData({
    String? transactionNumber,
    TransNextStep? step,
    String? messageDialog,
  }) {
    _transactionNumber = transactionNumber;
    _step = step;
    _messageDialog = messageDialog;
  }

  ///param for review api, called before call review
  void setAmountAndTransactionType({
    double? amount,
    TransactionCategoriesType? transactionType,
  }) {
    _amount = amount;
    _transactionType = transactionType;
  }

  Future callReviewTransactionApi() async {
    final response = await repository.bankApi!
        .getCashLimitationControllerApi()
        .checkCashLimitationVer2(
      checkCashLimitationVers2Request: CheckCashLimitationVers2Request(
        (b) {
          b
            ..amount = _amount
            ..transactionCategoriesType = _transactionType;
        },
      ),
    );
    final result = response.data?.data;
    if (result != null) {
      setAuthData(
        transactionNumber: result.transactionNumber,
        step: result.transNextStep,
        messageDialog: result.content,
      );
      return true;
    } else {
      return false;
    }
  }

  Future reviewTransaction() async {
    showLoading();
    try {
      return await callReviewTransactionApi();
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
    return false;
  }

  Future<AwsLivenessSessionModel?> createTransactionLivenessSession() async {
    showLoading();
    try {
      final token = await session.getToken();
      final response = await repository.profileApi!
          .getLivenessResourceApi()
          .createTransactionLivenessSession(
            headers: getHttpHeader(token: token.accessTokenRequest),
            createTransactionLivenessSessionRequest:
                CreateTransactionLivenessSessionRequest((b) {
              b.transactionNo = _transactionNumber;
            }),
          );
      if (response.data?.data != null) {
        return AwsLivenessSessionModel.fromCreateTransactionResponse(
          response.data!.data!,
        );
      }
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
    return null;
  }

  Future<bool?> getResultLiveness(
    String sessionId,
    String transactionNo,
  ) async {
    showLoading();
    try {
      final token = await session.getToken();
      final response = await repository.profileApi!
          .getLivenessResourceApi()
          .getTransactionLivenessSessionResult(
            headers: getHttpHeader(token: token.accessTokenRequest),
            sessionId: sessionId,
            transactionNo: transactionNo,
          );
      if (response.data?.data != null) {
        return response.data!.data!.verified;
      }
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
    return false;
  }
}
