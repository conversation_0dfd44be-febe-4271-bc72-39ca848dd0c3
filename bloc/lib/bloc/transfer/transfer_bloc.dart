import 'dart:async';

import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/account/transaction_model.dart';
import 'package:ksb_bloc/bloc/transfer/transaction_id_model.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';
import '../base_authenticate_bloc.dart';
import './transfer_extension.dart';

class TransactionType {
  TransactionType._();

  static const TO_MY_ACCOUNT_NUMBER =
      15; //15: Chuyển khoản đến số tài khoản của tôi (nội bộ)
  static const TO_OTHER_ACCOUNT_NUMBER =
      16; //16: Chuyển khoản đến số tài khoản Nội bộ hoặc Liên ngân hàng 247
  static const TO_CARD_NUMBER =
      17; //17: Chuyển khoản đến số số thẻ: Nội bộ hoặc Liên ngân hàng 247
  static const TO_PHONE_NUMBER = 19; //19: Chuyển khoản nội bộ qua số điện thoại
  static const TO_OVERSEA_ACCOUNT_NUMBER = 20; //20: Chuyển tiền quốc tế
  static const TO_CREDIT_CARD_NUMBER = 21; //21: thanh nợ thẻ tín dụng
}

class FeeChargeType {
  FeeChargeType._();

  static final SOURCE = 1;
  static final TARGET = 0;
}

class TARGET_TYPE {
  TARGET_TYPE._();

  static final ACCOUNT = 1;
  static final CARD = 0;
}
/**
 * Card Test: **************** Bank Đông Á
 * Card internal: ****************
 * Điện hồ Chí Minh: PE0300004045, PE03001905112, PE1400098646, PE1400098649
    Nước bến thành: BT07331203, BT210112124, BT6520652
    Mã NCC: MOBI
    Tên dịch vụ: Điện thoại di động
    Mã dịch vụ: DTDDTQ
    mã test: **********, **********
 */

class TransferBloc extends BaseBloc with BaseAuthBloc {
  TransferBloc(Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);
  TransferModel? _transferModel;
  late TransactionManagementApi _api;
  final _model = BehaviorSubject<TransferModel>();
  final _cities = BehaviorSubject<List<BaseItem>>();
  final _branches = BehaviorSubject<List<BaseItem>?>();
  final active = BehaviorSubject<bool>();
  final _bigAmount = BehaviorSubject<BigMoneyNoteModel?>();

  Stream<bool> get btn => active.stream;

  Stream<List<BaseItem>> get citiesStream => _cities.stream;

  Stream<List<BaseItem>?> get branchesStream => _branches.stream;

  Stream<TransferModel> get modelStream => _model.stream;

  Stream<BigMoneyNoteModel?> get bigAmountStream => _bigAmount.stream;

  Sink<TransferModel> get modelSink => _model.sink;

  TransferModel? get model => _transferModel;

  BigMoneyNoteModel? get bigTransaction => _bigAmount.valueOrNull;

  final _canNext = BehaviorSubject<bool>();
  final _clickCall = StreamController<bool>();

  Stream<bool> get clickCall => _clickCall.stream;

  AccountModel? accountModel;

  BaseItem get KLB => BaseBloc.KLB;

  BaseItem get UMEE => BaseBloc.UMEE;

  BaseItem get defaultBank => BaseBloc.KLB;

  @override
  void init() {
    super.init();
    _initDefault();
    _api = repository.bankApi!.getTransactionManagementApi();

    streamSubs.add(citiesStream.listen((event) {
      _updateDefaultCity();
    }));

    streamSubs.add(branchesStream.listen((event) {
      _updateDefaultBranch();
    }));
  }

  Future _initDefault() async {
    _transferModel = TransferModel()
      ..isNow = true
      ..whoCharge = FeeChargeType.SOURCE
      ..isAccount = true
      ..is247 = true;
    setTargetBank(defaultBank);
    _setSourceBank(KLB);

    _transferModel?.sourceAccountAvatar = await getAvatar();
    notifyModelChange();
  }

  Future mergeModel(TransferModel? model) async {
    if (model != null) {
      model.isNow ??= _transferModel?.isNow;
      model.whoCharge ??= FeeChargeType.SOURCE;
      model.isAccount ??= true;
      model.sourceAccountAvatar ??= await getAvatar();
      model.is247 ??= true;
      _transferModel?.channelCode = model.channelCode;

      final scheduleType = model.schedule?.scheduleType ?? '';
      if (ScheduleModel.SCHEDULE_TYPES.indexOf(scheduleType) >= 0) {
        model.isNow = false;
      }
      _transferModel = model;

      if (_transferModel?.isTargetBankInner() == true ||
          _transferModel?.targetBankIdNapas.isNullOrEmpty == false) {
        _transferModel?.is247 = true;
      } else if (_transferModel?.targetBankIdNapas.isNullOrEmpty == true) {
        _transferModel?.is247 = false;
      }

      if (_transferModel?.is247 == false) {
        _getCities();
      }

      if (shouldCheckBigMoney()) {
        checkBigMoney();
      }

      if (model.targetBankCode.isNullOrEmpty &&
          model.targetBankCodeId.isNullOrEmpty) {
        setTargetBank(defaultBank);
      }
      if (model.sourceBankImage == null) {
        _setSourceBank(KLB);
      }
      if (model.amount != null) {
        setBtn(model.amount, data: true);
      }

      if (!model.targetAccountNumber.isNullOrEmpty) {
        setTargetAccount(accountNumber: model.targetAccountNumber);
      }
    }
    notifyModelChange();
  }

  @override
  dispose() {
    super.dispose();
    _model.close();
    _cities.close();
    _branches.close();
    _canNext.close();
    active.close();
    _clickCall.close();
  }

  setBtn(double? value, {bool? data}) {
    if (toInt(value) > 0 && data == true) {
      active.safeAdd(true);
    } else {
      active.safeAdd(false);
    }
  }

  setScheduleBtn(DateTime? fromDate, {DateTime? toDate, String? scheduleType}) {
    logger.t("fromDate $fromDate");
    logger.t("toDate $toDate");
    logger.t("scheduleType $scheduleType");
    var date = DateTime.now();
    if (scheduleType == ScheduleModel.DAY) {
      if (fromDate == null) {
        active.safeAdd(false);
        return;
      }
    } else {
      if (fromDate == null || toDate == null) {
        active.safeAdd(false);
        return;
      }
    }

    if (scheduleType == ScheduleModel.MONTHLY) {
      final _now = DateTime(date.year, date.month);
      if (_now.isAfter(fromDate)) {
        active.safeAdd(false);
      } else
        active.safeAdd(true);
    } else {
      if (date.isAfter(fromDate)) {
        active.safeAdd(false);
      } else
        active.safeAdd(true);
    }
  }

  setSourceAccount(AccountModel? model) {
    logger.t(model);
    accountModel = model;
    _transferModel?.cifNumber = model?.cifNumber;
    _transferModel?.sourceAccountNumber = model?.accountNumber;
    _transferModel?.sourceAccountName = model?.accountName;
    _transferModel?.sourceAccountDisplayName = model?.displayName;
    _transferModel?.sourceAccountBalanceText = model?.availableBalanceText;
    _transferModel?.sourceAccountBalance = model?.availableBalance;
    _transferModel?.sourceCustomerName = model?.customerName;
    notifyModelChange();
  }

  setSourceAccountTransfer(TransferModel model) async {
    await mergeModel(model);
  }

  setTargetBank(BaseItem bank, {bool? clearState, bool? clearCity}) {
    logger.t(bank);
    _transferModel?.targetBankName = bank.subTitle;
    _transferModel?.targetBankCode = bank.bankIdNapas;
    _transferModel?.targetBankShortName = bank.title;
    _transferModel?.targetBankImage = bank.image;
    _transferModel?.targetBankImageType = bank.imageType;
    _transferModel?.targetBankCitad = toInt(bank.data);
    _transferModel?.targetCommonBankName = bank.commonTitle;
    _transferModel?.targetBankCodeId = bank.bankCode;
    _transferModel?.targetBankIdNapas = bank.bankIdNapas;

    if (clearState == true) {
      setAmount(0);
      targetAccountNumberChange('');
      setCity(null, null);
    }

    if (clearCity == true) {
      setCity(null, null);
    }

    if (bank.isInnerBank() || !bank.bankIdNapas.isNullOrEmpty) {
      set247(true);
      _bigAmount.add(null);
    } else if (bank.bankIdNapas.isNullOrEmpty) {
      set247(false);
    }

    if (_transferModel?.is247 == false && _cities.valueOrNull != null) {
      _updateDefaultCity();
      _updateDefaultBranch();
    }
  }

  _setSourceBank(BaseItem bank) {
    _transferModel?.sourceBankName = bank.subTitle;
    _transferModel?.sourceBankImage = bank.image;
    _transferModel?.sourceBankImageType = bank.imageType;
    _transferModel?.sourceCommonBankName = bank.commonTitle;
  }

  setAmount(double? value) {
    logger.t(value);
    if (value != null && value >= 0 && value != _transferModel?.amount) {
      _transferModel?.amount = value;
      if (!shouldCheckBigMoney()) {
        _bigAmount.add(null);
      }
    }
    notifyModelChange();
  }

  setIsAccount(bool value) {
    _transferModel?.isAccount = value;
  }

  BaseItem getTargetBank() {
    return BaseItem(
      id: _transferModel?.targetBankCode,
      title: _transferModel?.targetBankShortName,
      subTitle: _transferModel?.targetBankName,
      image: _transferModel?.targetBankImage,
      imageType: _transferModel?.targetBankImageType,
      commonTitle: _transferModel?.targetCommonBankName,
      data: _transferModel?.targetBankCitad,
      bankCode: _transferModel?.targetBankCodeId,
      bankIdNapas: _transferModel?.targetBankIdNapas,
    );
  }

  setNote(String note) {
    logger.t(note);
    if (!validateNote(note)) {
      active.safeAdd(false);
      _transferModel?.showWarning = true;
    } else {
      _transferModel?.showWarning = false;
      active.safeAdd(true);
    }
    notifyModelChange();
    _transferModel?.note = note;
  }

  setCategory(int? categoryId, String? categoryImage, String? categoryName,
      ImageType? categoryImageType) {
    logger.t(categoryId, error: '$categoryImage/$categoryName');
    _transferModel?.categoryId = categoryId;
    _transferModel?.categoryImage = categoryImage;
    _transferModel?.categoryName = categoryName;
    _transferModel?.categoryImageType = categoryImageType;
  }

  setFeeChargeType(int chargeType) {
    _transferModel?.whoCharge = chargeType;
    notifyModelChange();
  }

  bool shouldCheckBigMoney() {
    return (_transferModel?.is247 == true) &&
        (_transferModel?.amount ?? 0) > 0 &&
        (_transferModel?.isTargetBankInner() == false);
  }

  set247(bool on) async {
    _transferModel?.is247 = on;
    if (shouldCheckBigMoney()) {
      await checkBigMoney();
    } else {
      _bigAmount.add(null);
    }
    notifyModelChange();
    if (!on &&
        (_cities.valueOrNull == null || _cities.valueOrNull?.isEmpty == true)) {
      await _getCities();
    }

    if (on && _transferModel?.targetAccountNumber.isNullOrEmpty == false) {
      await _getBeneficiary();
    }
  }

  checkBank(bool value) {
    if (_transferModel?.targetBankIdNapas.isNullOrEmpty == true) {
      set247(false);
    } else if (_transferModel?.isTargetBankInner() == true) {
      set247(true);
    } else {
      set247(value);
    }
  }

  _updateDefaultCity() {
    final List<BaseItem<dynamic>>? cities = _cities.valueOrNull;
    if (_transferModel?.is247 == false && cities != null && cities.length > 0) {
      var city = cities[0];
      logger.t("_updateDefaultCity = $city");
      if (_transferModel?.cityCode != null) {
        final found = cities.firstWhereOrNull(
          (element) => element.id == _transferModel?.cityCode,
        );
        if (found != null) {
          city = found;
        }
      }
      setCity(city.id, city.title);
    }
  }

  _updateDefaultBranch() {
    final List<BaseItem<dynamic>>? branches = _branches.value;
    if (_transferModel?.is247 == false &&
        branches != null &&
        branches.length > 0) {
      final firstBranch = branches[0];
      setBankBranch(firstBranch.id, firstBranch.title);
    }
  }

  notifyModelChange() {
    safeAddData(_model, _transferModel);
  }

  targetAccountNumberChange(String? value) {
    if (value != null && value != _transferModel?.targetAccountNumber) {
      _transferModel?.targetAccountNumber = value;
      _transferModel?.targetAccountName = '';
      _transferModel?.targetAccountDisplayName = '';
      _transferModel?.targetAccountAvatar = '';
      _transferModel?.targetAccountDisplayNameEdit = null;
    }
    notifyModelChange();
  }

  setCardNo({String? accountNumber}) {
    _transferModel?.targetAccountNumber = accountNumber;
    notifyModelChange();
  }

  setTargetAccount({
    required String? accountNumber,
    String? displayName,
    String? accountName,
    String? avatar,
  }) async {
    var hasChange = false;
    if (accountNumber?.isNotEmpty == true &&
        ((_transferModel?.targetAccountNumber != accountNumber) ||
            accountName.isNullOrEmpty)) {
      hasChange = true;
    }
    _transferModel?.targetAccountNumber = accountNumber;
    if (!accountName.isNullOrEmpty)
      _transferModel?.targetAccountName = accountName;
    if (accountNumber.isNullOrEmpty) _transferModel?.targetAccountName = null;
    _transferModel?.targetAccountDisplayName = displayName;
    _transferModel?.targetAccountAvatar = avatar;
    _transferModel?.targetAccountDisplayNameEdit = null;
    notifyModelChange();

    if (hasChange &&
        _transferModel?.is247 == true &&
        _transferModel?.targetAccountName.isNullOrEmpty == true) {
      await _getBeneficiary();
    }
  }

  setTargetContact(ContactModel contact) async {
    logger.t(contact);
    _transferModel?.targetBankName = contact.bankName;
    _transferModel?.targetBankCode = contact.bankIdNapas;
    _transferModel?.targetBankShortName = contact.bankShortName;
    _transferModel?.targetBankImage = contact.bankUrl;
    _transferModel?.targetBankImageType = contact.bankUrlType;
    _transferModel?.targetBankCitad = toInt(contact.citadBankCode);
    _transferModel?.targetCommonBankName = contact.bankCommonName; //
    _transferModel?.targetBankCodeId = contact.bankCode;
    _transferModel?.targetBankIdNapas = contact.bankIdNapas;

    final accountNumber = contact.accountNo;
    final avatar = contact.avatarUrl;
    final displayName = contact.aliasName;
    final accountName = contact.accountName;

    _transferModel?.targetAccountNumber = accountNumber;
    if (!accountName.isNullOrEmpty)
      _transferModel?.targetAccountName = accountName;
    if (accountNumber.isNullOrEmpty) _transferModel?.targetAccountName = null;
    _transferModel?.targetAccountDisplayName = displayName;
    _transferModel?.targetAccountAvatar = avatar;
    _transferModel?.targetAccountDisplayNameEdit = null;

    if (contact.isInnerBank() || !contact.bankIdNapas.isNullOrEmpty) {
      await set247(true);
    } else if (contact.bankIdNapas.isNullOrEmpty) {
      await set247(false);
    }

    setCity(null, null);
    if (_transferModel?.is247 == false && _cities.valueOrNull != null) {
      _updateDefaultCity();
      _updateDefaultBranch();
    }
  }

  _getBeneficiary() async {
    showLoading();
    if (_transferModel?.cifNumber == null) {
      _transferModel?.cifNumber = await preferences.cifNumber;
    }
    TransactionBeneficiaryRequest transactionBeneficiaryRequest =
        TransactionBeneficiaryRequest((builder) {
      builder
        ..bankCode =
            _transferModel?.targetBankIdNapas ?? _transferModel?.targetBankCode
        ..accountNo = _transferModel?.targetAccountNumber
        ..bankCif = _transferModel?.cifNumber
        ..isAccount = toInt(_transferModel?.isAccount);
    });
    return _api
        .getBeneficiary(
            transactionBeneficiaryRequest: transactionBeneficiaryRequest)
        .then((value) {
      final data = value.data?.data;
      _transferModel?.targetAccountName = data?.beneficiaryName;
      notifyModelChange();
    }).catchError((err) {
      handlerApiError(err);
    }).whenComplete(completeLoading);
  }

  setTargetAccountDisplayName(String name, {bool? is247}) {
    _transferModel?.targetAccountDisplayNameEdit = name.trim();
    if (is247 == null)
      notifyModelChange();
    else
      _transferModel?.targetAccountName = name;
  }

  setTargetAccountName(String? name) {
    _transferModel?.targetAccountName = name;
    logger.t(name);
    if (name.isNullOrEmpty) _transferModel?.targetAccountDisplayNameEdit = null;
    notifyModelChange();
  }

  void setSchedule(ScheduleModel? schedule) {
    _transferModel?.schedule = schedule;
    if (schedule != null) {
      _transferModel?.isNow = false;
    } else {
      _transferModel?.isNow = true;
    }
    notifyModelChange();
  }

  void setSoftOtp(String? value) {
    _transferModel?.softOtp = value;
  }

  setFeeAmount(num value) {
    _transferModel?.chargeAmount = value;
    notifyModelChange();
  }

  Future checkBigMoney({bool isLoading = true, bool handlerError = false}) {
    if (isLoading) {
      if (isProgressVisible) return Future.error("Đang xử lý, vui lòng đợi");
      showLoading();
    }
    return repository.bankApi!
        .getTransactionsApiControllerApi()
        .getAdvanceTransactionNote(amount: _transferModel?.amount ?? 0)
        .then((value) {
      if (value.data?.data != null) {
        _bigAmount.add(BigMoneyNoteModel.fromResponse(value.data!.data!));
      }
    }).catchError((e) {
      _bigAmount.add(null);
      if (handlerError) {
        handlerApiError(e);
      }
    }).whenComplete(() {
      if (isLoading) {
        completeLoading();
      }
    });
  }

  Future validateData() async {
    if (isProgressVisible) return Future.error("Đang xử lý, vui lòng đợi");
    showLoading();
    try {
      if (shouldCheckBigMoney()) {
        await checkBigMoney(isLoading: false);
      }
      final note = _transferModel?.getNote();
      if (!validateNote(note)) {
        throw 'Vui lòng nhập nội dung chuyển tiền tiếng việt không dấu và không có ký tự đặc biệt';
      }

      final beneficiaryName = _transferModel?.targetAccountName?.trim() ?? '';
      if (beneficiaryName.isEmpty) {
        throw 'Vui lòng nhập tên người thụ hưởng';
      }

      if (_transferModel?.targetAccountName.isNullOrEmpty == true &&
          _transferModel?.is247 == false) {
        throw 'Chưa nhập tên người thụ hưởng';
      }
      if (_transferModel?.sourceAccountNumber.isNullOrEmpty == true) {
        throw 'Chưa chọn tài khoản chuyển tiền';
      }
      if (_transferModel?.targetAccountNumber.isNullOrEmpty == true) {
        throw 'Chưa nhập thông tin tài khoản nhận';
      }
      if (_transferModel?.amount == null ||
          (_transferModel?.amount ?? 0) <= 0) {
        throw 'Chưa nhập số tiền';
      }
      if (_transferModel?.is247 == false) {
        if (_transferModel?.cityCode == null) {
          throw 'Bạn chưa chọn Tỉnh/Thành phố';
        }
        if (_transferModel?.targetBranchId == null) {
          throw 'Bạn chưa chọn chi nhánh';
        }
      }
      await reviewTransfer();
    } catch (e) {
      handlerApiError(e);
      throw e;
    } finally {
      completeLoading();
    }
  }

  ///api reviewTransaction for transfer
  Future reviewTransfer() async {
    //Kiểm tra phí chuyển tiền nội bộ
    _canNext.add(false);
    final hasBankId = (_transferModel?.targetBankCode != null) ||
        (_transferModel?.targetBankCitad != null);
    if (!hasBankId ||
        _transferModel?.amount == null ||
        (_transferModel?.amount ?? 0) <= 0 ||
        _transferModel?.targetAccountNumber.isNullOrEmpty == true) {
      setFeeAmount(0);
      return;
    }
    if (_transferModel?.is247 == false) {
      final request = ReviewTransferInterBankCitadV2Request((builder) {
        builder
          ..accountNoTo = _transferModel?.targetAccountNumber
          ..amount = toInt(_transferModel?.amount)
          ..accountNoFrom = _transferModel?.sourceAccountNumber
          ..targetBankCode = _transferModel?.targetBankCitad?.toString() ??
              _transferModel?.targetBankCode
          ..transactionGroup = _transferModel?.categoryId
          ..isAccount = toInt(_transferModel?.isAccount)
          ..description = _transferModel?.getNote()
          ..regionId = _transferModel?.cityCode
          ..bankId = _transferModel?.targetBankCitad
          ..branchId = _transferModel?.targetBranchId
          ..branchName = _transferModel?.targetBranchName
          ..beneficiaryName = _transferModel?.targetAccountName?.trim()
          ..idCard = '';
      });
      final fee = await _api
          .reviewTransferCitadV2(reviewTransferInterBankCitadV2Request: request)
          .then((value) {
        _transferModel?.transactionNumber = value.data?.data?.transactionNumber;
        // _transferModel.targetAccountName = value?.data?.data?.;
        _transferModel?.advanceTransaction = null;
        setAuthData(
          step: value.data?.data?.transNextStep,
          transactionNumber: value.data?.data?.transactionNumber,
          messageDialog: value.data?.data?.content,
        );
        return (value.data?.data?.fee ?? 0) + (value.data?.data?.tax ?? 0);
      });
      _canNext.add(true);
      setFeeAmount(fee);
    } else if (_transferModel?.isTargetBankInner() == true) {
      final request = ReviewTransferIntraBankV2Request((builder) {
        builder
          ..accountNoTo = _transferModel?.targetAccountNumber
          ..amount = toInt(_transferModel?.amount)
          ..accountNoFrom = _transferModel?.sourceAccountNumber
          ..isAccount = toInt(_transferModel?.isAccount)
          ..transactionGroup = _transferModel?.categoryId
          ..targetBankCode = _transferModel?.targetBankCode;
      });
      final fee = await _api
          .reviewTransferIntraBankV2(reviewTransferIntraBankV2Request: request)
          .then((value) {
        _transferModel?.targetAccountName = value.data?.data?.targetAccountName;
        _transferModel?.transactionNumber = value.data?.data?.transactionNumber;
        _transferModel?.advanceTransaction = null;
        setAuthData(
          step: value.data?.data?.transNextStep,
          transactionNumber: value.data?.data?.transactionNumber,
          messageDialog: value.data?.data?.content,
        );
        return (value.data?.data?.fee ?? 0) + (value.data?.data?.tax ?? 0);
      });
      _canNext.add(true);
      setFeeAmount(fee);
    } else {
      final request = ReviewTransferInterBank247Ver4Request((builder) {
        builder
          ..accountNoTo = _transferModel?.targetAccountNumber
          ..amount = toInt(_transferModel?.amount)
          ..accountNoFrom = _transferModel?.sourceAccountNumber
          ..targetBankCode = _transferModel?.targetBankIdNapas ??
              _transferModel?.targetBankCode
          ..transactionGroup = _transferModel?.categoryId
          ..isAccount = toInt(_transferModel?.isAccount);
      });
      final fee = await _api
          .reviewTransfer247V4(reviewTransferInterBank247Ver4Request: request)
          .then((value) {
        _transferModel?.targetAccountName = value.data?.data?.targetAccountName;
        _transferModel?.transactionNumber = value.data?.data?.transactionNumber;
        final advanceTransaction = value.data?.data?.advanceTransaction;
        if (advanceTransaction != null) {
          _transferModel?.advanceTransaction =
              AdvanceTransactionModel.fromReviewAdvanceTransactionResponse(
                  advanceTransaction);
        } else {
          _transferModel?.advanceTransaction = null;
        }
        setAuthData(
          step: value.data?.data?.transNextStep,
          transactionNumber: value.data?.data?.transactionNumber,
          messageDialog: value.data?.data?.content,
        );
        return (value.data?.data?.fee ?? 0) + (value.data?.data?.tax ?? 0);
      });
      _canNext.add(true);
      setFeeAmount(fee);
    }
  }

  Future startTransfer({bool? enableAwsLiveness}) {
    if (isProgressVisible) return Future.error("Đang xử lý giao dịch");
    showLoading();
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    logger.t(_transferModel.toString());

    otpRequestBuilder.otp = _transferModel?.softOtp;
    SchedulingObjectBuilder? scheduleBuilder;
    if (_transferModel?.isNow == false && _transferModel?.schedule != null) {
      scheduleBuilder = SchedulingObjectBuilder()
        ..fromDate = _transferModel?.schedule?.fromDate?.convertDate().formatDMY
        ..toDate = _transferModel?.schedule?.toDate?.convertDate().formatDMY
        ..scheduleType = _transferModel?.schedule?.scheduleType
        ..days = _transferModel?.schedule?.days;
    }

    // Sử dụng API v4 nếu enable AWS liveness, ngược lại sử dụng API v3
    if (enableAwsLiveness == true) {
      final transferBankRequest = TransferBankVer4Request((builder) {
        builder
          ..transactionNo = _transferModel?.transactionNumber
          ..description = _transferModel?.getNote()
          ..transactionCategoryId = _transferModel?.categoryId ?? 8
          ..whoCharge = _transferModel?.whoCharge
          ..chargeAmount = toInt(_transferModel?.chargeAmount)
          ..isAccount = toInt(_transferModel?.isAccount)
          ..is247 = toInt(_transferModel?.is247)
          ..isNow = toInt(_transferModel?.isNow)
          ..verifySoftOtp = otpRequestBuilder
          ..schedule = scheduleBuilder
          ..isSaveToTemplate = toInt(_transferModel?.saveTemplate)
          ..aliasName = _transferModel?.getTargetAccountDisplayName()
          ..regionId = _transferModel?.cityCode
          ..bankId = _transferModel?.targetBankCitad
          ..beneficiaryName = _transferModel?.targetAccountName?.trim()
          ..branchId = _transferModel?.targetBranchId
          ..branchName = _transferModel?.targetBranchName
          ..scheduleId = _transferModel?.scheduleId
          ..channelCode = _transferModel?.channelCode
          ..bankCodeId = _transferModel?.targetBankCodeId ?? '';
      });
      
      if (transferBankRequest.isNow == 0 &&
          !transferBankRequest.scheduleId.isNullOrEmpty) {
        return updateSchedule();
      } else {
        return _api
            .transferVer4(transferBankVer4Request: transferBankRequest)
            .then((value) {
          final data = value.data?.data?.transaction;
          if (data != null) {
            _transferModel?.transactionNumber = data.rtxnNo ?? '';
            _transferModel?.transactionTime = data.transactionTime;
            _transferModel?.amount = toDouble(data.amount ?? 0);
            _transferModel?.chargeAmount = data.fee;
          }
          if (value.data?.data?.advanceTransferResponse?.transactionId != null) {
            _transferModel?.advancedTransactionId =
                value.data?.data?.advanceTransferResponse?.transactionId;
            preferences.setHasAdvancedTransfer(true);
          }
        }).catchError((e) {
          handlerApiError(e);
          throw e;
        }).whenComplete(completeLoading);
      }
    } else {
      // Sử dụng API v3 khi không enable AWS liveness
      final transferBankRequest = TransferBankVer3Request((builder) {
        builder
          ..transactionNo = _transferModel?.transactionNumber
          ..description = _transferModel?.getNote()
          ..transactionCategoryId = _transferModel?.categoryId ?? 8
          ..whoCharge = _transferModel?.whoCharge
          ..chargeAmount = toInt(_transferModel?.chargeAmount)
          ..isAccount = toInt(_transferModel?.isAccount)
          ..is247 = toInt(_transferModel?.is247)
          ..isNow = toInt(_transferModel?.isNow)
          ..verifySoftOtp = otpRequestBuilder
          ..schedule = scheduleBuilder
          ..isSaveToTemplate = toInt(_transferModel?.saveTemplate)
          ..aliasName = _transferModel?.getTargetAccountDisplayName()
          ..regionId = _transferModel?.cityCode
          ..bankId = _transferModel?.targetBankCitad
          ..beneficiaryName = _transferModel?.targetAccountName?.trim()
          ..branchId = _transferModel?.targetBranchId
          ..branchName = _transferModel?.targetBranchName
          ..scheduleId = _transferModel?.scheduleId
          ..channelCode = _transferModel?.channelCode
          ..bankCodeId = _transferModel?.targetBankCodeId ?? '';
      });
      
      if (transferBankRequest.isNow == 0 &&
          !transferBankRequest.scheduleId.isNullOrEmpty) {
        return updateSchedule();
      } else {
        return _api
            .transferVer3(transferBankVer3Request: transferBankRequest)
            .then((value) {
          final data = value.data?.data?.transaction;
          if (data != null) {
            _transferModel?.transactionNumber = data.rtxnNo ?? '';
            _transferModel?.transactionTime = data.transactionTime;
            _transferModel?.amount = toDouble(data.amount ?? 0);
            _transferModel?.chargeAmount = data.fee;
          }
          if (value.data?.data?.advanceTransferResponse?.transactionId != null) {
            _transferModel?.advancedTransactionId =
                value.data?.data?.advanceTransferResponse?.transactionId;
            preferences.setHasAdvancedTransfer(true);
          }
        }).catchError((e) {
          handlerApiError(e);
          throw e;
        }).whenComplete(completeLoading);
      }
    }
  }

  Future updateSchedule() {
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    logger.t(_transferModel.toString());

    otpRequestBuilder.otp = _transferModel?.softOtp;

    SchedulingObjectBuilder? scheduleBuilder;
    if (_transferModel?.isNow == false && _transferModel?.schedule != null) {
      scheduleBuilder = SchedulingObjectBuilder()
        ..fromDate = _transferModel?.schedule?.fromDate?.convertDate().formatDMY
        ..toDate = _transferModel?.schedule?.toDate?.convertDate().formatDMY
        ..scheduleType = _transferModel?.schedule?.scheduleType
        ..days = _transferModel?.schedule?.days;
    }
    final transferBankRequest = TransferBankRequest((builder) {
      builder
        ..bankCif = _transferModel?.cifNumber
        ..accountNoFrom = _transferModel?.sourceAccountNumber
        ..bankCode = _transferModel?.targetBankCode
        ..accountNoTo = _transferModel?.targetAccountNumber
        ..amount = toInt(_transferModel?.amount)
        ..description = _transferModel?.getNote()
        ..transactionCategoryId = _transferModel?.categoryId ?? 8
        ..whoCharge = _transferModel?.whoCharge
        ..chargeAmount = toInt(_transferModel?.chargeAmount)
        ..isAccount = toInt(_transferModel?.isAccount)
        ..is247 = toInt(_transferModel?.is247)
        ..isNow = toInt(_transferModel?.isNow)
        ..verifySoftOtp = otpRequestBuilder
        ..schedule = scheduleBuilder
        ..isSaveToTemplate = toInt(_transferModel?.saveTemplate)
        ..aliasName = _transferModel?.getTargetAccountDisplayName()
        ..regionId = _transferModel?.cityCode
        ..bankId = _transferModel?.targetBankCitad
        ..beneficiaryName = _transferModel?.targetAccountName?.trim()
        ..branchId = _transferModel?.targetBranchId
        ..branchName = _transferModel?.targetBranchName
        ..scheduleId = _transferModel?.scheduleId
        ..channelCode = _transferModel?.channelCode
        ..idCard = ''
        ..bankCodeId = _transferModel?.targetBankCodeId ?? '';
    });
    logger.t("api chinh sua lich chuyen tien");
    return repository.bankApi!
        .getScheduleTransferManagementApi()
        .updateTransactionSchedule(
            scheduleId: transferBankRequest.scheduleId ?? '',
            transferBankRequest: transferBankRequest)
        .then((value) async {
      final data = value.data?.data;
      if (data != null) {
        var result = TransferModel.fromTransferBank(data.scheduleResponse);
        _transferModel?.schedule = result.schedule;
        notifyModelChange();
      }
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(completeLoading);
  }

  Future<bool?> paymentInvest({String? requestId}) {
    if (isProgressVisible) return Future.error("Đang xử lý");
    showLoading();
    final otpRequestBuilder = VerifySoftOtpBuilder();
    otpRequestBuilder.otp = _transferModel?.softOtp;
    final receiveKSBankRequest = ReceiveKSBankRequest((builder) {
      builder.accountNoFrom = _transferModel?.sourceAccountNumber;
      builder.amount = toInt(_transferModel?.amount);
      builder.accountNoTo = _transferModel?.targetAccountNumber;
      builder.narratives = _transferModel?.note;
      builder.companyId = _transferModel?.targetCompanyId;
      builder.transRef = requestId;
      builder.transactionNo = requestId;
      builder.verifySoftOtp = otpRequestBuilder;
    });

    return repository.stocksApi!
        .getV2AppApi()
        .receiveKSBank(
            id: _transferModel?.stockType ?? '',
            receiveKSBankRequest: receiveKSBankRequest)
        .then((value) {
      final data = value.data;
      if (data != null && (data.status != null && data.status == "SUCCESS")) {
        _transferModel!.transactionNumber = data.requestOrgId;
      } else if (data != null &&
          (data.status != null && data.status == "FAILED")) {
        addError("${data.errorDescription} (${data.errorCode})");
      }
      return data?.status != null && data?.status == "SUCCESS";
    }).catchError((e) {
      handlerApiError(e);
      return false;
    }).whenComplete(completeLoading);
  }

  void setSaveTemplate(bool checked) {
    _transferModel?.saveTemplate = checked;
    notifyModelChange();
  }

  /// kiem tra truoc khi thanh toan the no
  /// api reviewTransaction for payment card
  Future<bool> reviewPayment({
    String? sourceAccountNo,
    String? cifNo,
    String? refCardId,
    String? cardNo,
    String? amount,
    String? paymentType,
    String? note,
  }) {
    showScreenLoading(isSubmit: true);
    return repository.bankApi!.getCardApi().reviewPayment1(
        reviewPaymentCardRequest: ReviewPaymentCardRequest((builder) {
      builder
        ..paymentType = paymentType
        ..sourceAccountNo = sourceAccountNo
        ..cifNo = cifNo
        ..refCardId = refCardId ?? ""
        ..cardNo = cardNo
        ..amount = amount
        ..note = note ?? 'Thanh toan the tin dung %';
    })).then((response) {
      final data = response.data?.data;
      setAuthData(
        step: data?.transNextStep,
        transactionNumber: data?.transactionNumber,
        messageDialog: data?.content,
      );
      successResponse(response);
      return true;
    }).catchError((error) {
      handlerApiError(error);
      return false;
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
    });
  }

  /// thanh toan no the (tin dung,...)
  Future<String> paymentCard(
      {String? sourceAccountNo,
      String? cifNo,
      String? refCardId,
      String? cardNo,
      int? amount,
      String? otp,
      String? paymentType,
      String? note}) {
    if (isProgressVisible) return Future.error("Đang xử lý");
    showLoading();
    return repository.bankApi!.getCardApi().payment(
        paymentCardRequest: PaymentCardRequest((builder) {
      builder
        ..paymentType = paymentType
        ..sourceAccountNo = sourceAccountNo
        ..cifNo = cifNo
        ..refCardId = refCardId ?? ""
        ..cardNo = cardNo
        ..amount = amount
        ..note = note
        ..transactionNo = transactionNumber
        ..otpCode = otp;
    })).then((response) {
      ApiResponseDtoPaymentCardResponse? res = response.data;
      PaymentCardResponse? paymentCard = res?.data;
      PaymentInfo? paymentInfo = paymentCard?.paymentInfo;
      return paymentInfo?.refTransactionNo ?? "";
    }).catchError((error) {
      handlerApiError(error);
      return "";
    }).whenComplete(() {
      completeLoading();
    });
  }

  Future _getCities() async {
    if (_transferModel?.cifNumber == null) {
      _transferModel?.cifNumber = await preferences.cifNumber;
    }

    RegionRequest regionRequest = RegionRequest((builder) {
      builder.bankCif = _transferModel?.cifNumber;
    });
    return _api
        .getRegions(regionRequest: regionRequest)
        .then((value) {
          final data = value.data?.data;
          return data
                  ?.map<BaseItem>(
                    (e) => BaseItem(
                      id: e.regionID,
                      title: e.regionName,
                    ),
                  )
                  .toList() ??
              <BaseItem>[];
        })
        .then((value) => safeAddData(_cities, value))
        .catchError((e) {
          handlerApiError(e);
        })
        .whenComplete(completeLoading);
  }

  Future<List<BaseItem>> _getBankBranches() {
    _branches.safeAdd(null);
    try {
      if (_transferModel?.cityCode == null) {
        return Future.value(<BaseItem>[]);
      }
      BranchRequest request = BranchRequest((builder) {
        builder
          ..bankCif = _transferModel?.cifNumber
          ..regionId = toInt(_transferModel?.cityCode)
          ..bankId = toInt(_transferModel?.targetBankCitad);
      });
      return _api.getBankBranchByRegions(branchRequest: request).then((value) {
        final data = value.data?.data;
        return data
                ?.map(
                  (e) => BaseItem(
                    id: e.branchID,
                    title: e.branchName,
                  ),
                )
                .toList() ??
            <BaseItem>[];
      }).then((value) {
        safeAddData(_branches, value);
        return value;
      });
    } catch (e) {
      handlerApiError(e);
      return Future.value(<BaseItem>[]);
    } finally {
      completeLoading();
    }
  }

  void setCity(int? id, String? title) {
    logger.t(id, error: title);
    _transferModel?.cityCode = id;
    _transferModel?.cityName = title;
    _transferModel?.targetBranchId = null;
    _transferModel?.targetBranchName = null;
    notifyModelChange();
    _getBankBranches();
  }

  void setBankBranch(dynamic id, String? title) {
    logger.t(id, error: title);
    _transferModel?.targetBranchId = id?.toString();
    _transferModel?.targetBranchName = title;
    notifyModelChange();
  }

  void resetModel() {
    logger.t('resetModel');
    targetAccountNumberChange('');
    setNote('');
    setTargetAccountName('');
    setTargetBank(defaultBank, clearState: true);
    _transferModel?.isDisableAfScanVietQR = false;
    _transferModel?.channelCode = "";
  }

  void setTransferMiniApp(CommonPayment? payment, double? value) {
    setAmount(value);
    _transferModel?.isAccount = true;
    _transferModel?.targetBankCode = KLB.id;
    _transferModel?.targetAccountNumber = payment?.supplierAccount;
    _transferModel?.targetAccountName = payment?.supplierName;
    _transferModel?.categoryId = 8;
    _transferModel?.note = payment?.content;
    _transferModel?.targetCompanyId = payment?.companyId;
    _transferModel?.stockType = payment?.type;
    _transferModel?.transId = payment?.transId;
  }
}

class TransferModel {
  String? cifNumber;
  String? sourceAccountNumber;
  String? sourceAccountName;

  String? sourceAccountDisplayName;
  String? sourceAccountBalanceText;
  double? sourceAccountBalance;

  String? sourceCustomerName;
  String? sourceBankImage;
  ImageType? sourceBankImageType;

  String? sourceBankName;
  String? sourceCommonBankName;
  String? sourceAccountAvatar;

  String? targetBankShortName;
  String? targetBankCode;
  String? targetBankImage;

  ImageType? targetBankImageType;
  String? targetBankName;
  String? targetCommonBankName;

  String? targetAccountNumber;
  String? targetAccountName;
  String? targetAccountDisplayName;

  String? targetCompanyId;
  String? targetAccountAvatar;
  String? targetAccountDisplayNameEdit;

  String? targetBranchId;
  String? targetBranchName;
  int? targetBankCitad;

  String? targetBankCodeId;
  String? targetBankIdNapas;
  double? amount;

  String? note;
  int? categoryId;
  String? categoryName;

  String? categoryImage;
  ImageType? categoryImageType;
  int? whoCharge;

  num? chargeAmount;
  bool? isAccount; //1: sang tài khoản, 0 sang thẻ
  bool? is247; //1: Chuyển nhanh NAPAS, 0: chuyển chậm CITAD

  bool? isNow; //1: chuyển ngay, 0: đặt lịch
  String? softOtp;
  ScheduleModel? schedule;

  String? transactionNumber;
  String? transactionTime;
  bool? saveTemplate;

  String? templateId;
  String? scheduleId;
  String? cityName;

  int? cityCode;
  String? stockType;
  String? transId;

  bool?
      isClickBankAndTargetAccount; // true: thực hiện call api lấy tên tài khoản
  bool? showWarning;
  String? channelCode;

  String? callbackUrl;
  String? advancedTransactionId;
  AdvanceTransactionModel? advanceTransaction;
  bool?
      isDisableAfScanVietQR; // true: vô hiệu hoá tất cả các field trong màn "transfer_accept" khi quét VietQR có ghi sẵn tiền

  bool isDisableField() {
    // true: vô hiệu hoá field "chọn ngân hàng" và field "chọn tài khoản" trong màn "transfer_accept" khi quét VietQR không cần biết QR có tiền hay không
    return ((isDisableAfScanVietQR ?? false) || channelCode == "2");
  }

  getTargetAccountDisplayName() {
    if (targetAccountDisplayNameEdit != null) {
      return targetAccountDisplayNameEdit;
    }
    return targetAccountDisplayName ?? '';
  }

  bool isTargetBankInner() {
    final isKLB = targetBankCode == BaseBloc.KLB.bankCode ||
        targetBankIdNapas == BaseBloc.KLB.bankIdNapas ||
        targetBankCodeId == BaseBloc.KLB.id;

    final isUmee = targetBankCode == BaseBloc.UMEE.bankCode ||
        targetBankIdNapas == BaseBloc.UMEE.bankIdNapas ||
        targetBankCodeId == BaseBloc.UMEE.id;
    return isKLB || isUmee;
  }

  String getNote() {
    final _note = note?.trim() ?? '';
    return _note.isNullOrEmpty
        ? (sourceCustomerName != null ? "$sourceCustomerName chuyen tien" : "")
        : _note;
  }

  TransferModel({
    this.cifNumber,
    this.sourceAccountNumber,
    this.sourceAccountName,
    this.sourceAccountDisplayName,
    this.sourceAccountBalanceText,
    this.sourceAccountBalance,
    this.sourceCustomerName,
    this.sourceBankImage,
    this.sourceBankImageType,
    this.sourceBankName,
    this.sourceAccountAvatar,
    this.targetBankShortName,
    this.targetBankCode,
    this.targetBankImage,
    this.targetBankImageType,
    this.targetBankName,
    this.targetAccountNumber,
    this.targetAccountName,
    this.targetAccountDisplayName,
    this.targetAccountAvatar,
    this.targetAccountDisplayNameEdit,
    this.amount,
    this.note,
    this.categoryId,
    this.categoryName,
    this.categoryImage,
    this.categoryImageType,
    this.whoCharge,
    this.chargeAmount,
    this.isAccount,
    this.is247,
    this.isNow,
    this.softOtp,
    this.schedule,
    this.transactionNumber,
    this.transactionTime,
    this.saveTemplate,
    this.templateId,
    this.scheduleId,
    this.cityName,
    this.cityCode,
    this.targetBranchId,
    this.targetBranchName,
    this.targetBankCitad,
    this.targetCompanyId,
    this.stockType,
    this.transId,
    this.isClickBankAndTargetAccount,
    this.showWarning,
    this.targetCommonBankName,
    this.channelCode,
    this.callbackUrl,
    this.targetBankCodeId,
    this.targetBankIdNapas,
    this.advanceTransaction,
    this.advancedTransactionId,
    this.isDisableAfScanVietQR = false,
  });

  TransferModel.fromContact(ContactModel? benefit) {
    if (benefit != null) {
      targetBankName = benefit.bankName;
      targetBankCode = benefit.bankIdNapas;
      targetBankShortName = benefit.bankShortName;
      targetBankImage = benefit.bankUrl;
      targetBankImageType = benefit.bankUrlType;
      targetBankCitad = toInt(benefit.citadBankCode);
      targetCommonBankName = benefit.bankName; //
      targetBankCodeId = benefit.bankCode;
      targetBankIdNapas = benefit.bankIdNapas;
      targetAccountNumber = benefit.accountNo;
      targetAccountName = benefit.accountName;
      targetAccountDisplayName = benefit.aliasName;
      targetAccountAvatar = benefit.avatarUrl;
      isAccount = (benefit.groupName == ContactModel.noi_bo ||
          benefit.groupName == ContactModel.lien_ket);
    }
  }

  TransferModel.fromTransaction(TransactionModel? transaction) {
    if (transaction != null) {
      sourceAccountNumber = transaction.accountNo;
      targetBankName = transaction.partnerBankName;
      targetBankCode = transaction.partnerBankIdNapas;
      targetBankIdNapas = transaction.partnerBankIdNapas;
      targetBankCodeId = transaction.partnerBankCodeId;
      targetBankShortName = transaction.partnerBankShortName;
      targetBankImage = transaction.partnerBankUrl;
      targetBankImageType = transaction.partnerBankUrlType;
      targetCommonBankName = transaction.partnerBankCommonName;

      targetBankCitad = transaction.partnerCitadBankCode;
      targetAccountNumber = transaction.partnerAccountNo;
      targetAccountName = transaction.partnerAccountName;

      targetAccountDisplayName = transaction.partnerAccountAlias;
      targetAccountAvatar = transaction.partnerAccountAvatar;
      isAccount = transaction.isAccount;

      amount = transaction.amount;
      note = transaction.description;
    }
  }

  TransferModel.fromTransferBank(TransferScheduleResponse? transaction) {
    if (transaction != null) {
      cifNumber = transaction.cifNo;
      amount = toDouble(transaction.amount);
      categoryId = transaction.transactionCategoryId;
      categoryImage = transaction.categoryImage;
      categoryImageType = getImageTypeFrom(transaction.categoryImageType);
      saveTemplate = toBool(transaction.isSaveToTemplate);
      sourceAccountNumber = transaction.accountNoFrom;
      whoCharge = transaction.whoCharge;
      chargeAmount = transaction.chargeAmount;
      isAccount = toBool(transaction.isAccount);
      targetBankCode = transaction.bankCode;
      targetAccountNumber = transaction.accountNoTo;
      targetAccountDisplayName = transaction.aliasName;
      targetAccountName = transaction.beneficiaryName;
      is247 = toBool(transaction.is247);
      isNow = toBool(transaction.isNow);
      note = transaction.description;
      cityCode = transaction.regionId;
      targetBranchId = transaction.branchId;
      targetBranchName = transaction.branchName;
      targetBankImage = transaction.targetBankImage;
      targetBankImageType = getImageTypeFrom(transaction.targetBankImageType);
      targetBankCitad = toInt(transaction.targetBankCodeCitAd);
      targetBankShortName = transaction.shortName;
      schedule = ScheduleModel(
        scheduleType: transaction.scheduleGroup,
        fromDate: transaction.fromDate,
        toDate: transaction.toDate,
        dateOfMonth: transaction.scheduleGroup == ScheduleModel.MONTHLY
            ? transaction.days
            : '',
        dateOfWeek: transaction.scheduleGroup == ScheduleModel.WEEKLY
            ? transaction.days
            : '',
      );
    }
  }

  TransferModel copyWith({
    String? cifNumber,
    String? sourceAccountNumber,
    String? sourceAccountName,
    String? sourceAccountDisplayName,
    String? sourceAccountBalanceText,
    double? sourceAccountBalance,
    String? sourceCustomerName,
    String? sourceBankImage,
    ImageType? sourceBankImageType,
    String? sourceBankName,
    String? sourceAccountAvatar,
    String? targetBankShortName,
    String? targetBankCode,
    String? targetBankImage,
    ImageType? targetBankImageType,
    String? targetBankName,
    String? targetAccountNumber,
    String? targetAccountName,
    String? targetAccountDisplayName,
    String? targetAccountAvatar,
    String? targetAccountDisplayNameEdit,
    double? amount,
    String? note,
    int? categoryId,
    String? categoryName,
    String? categoryImage,
    ImageType? categoryImageType,
    int? whoCharge,
    num? chargeAmount,
    bool? isAccount,
    bool? is247,
    bool? isNow,
    String? softOtp,
    Schedule? schedule,
    String? transactionNumber,
    String? transactionTime,
    bool? saveTemplate,
    String? templateId,
    String? cityName,
    int? cityCode,
    String? targetBranchId,
    String? targetBranchName,
    int? targetBankCitad,
    AdvanceTransactionModel? advanceTransaction,
  }) {
    return TransferModel(
      cifNumber: cifNumber ?? this.cifNumber,
      sourceAccountNumber: sourceAccountNumber ?? this.sourceAccountNumber,
      sourceAccountName: sourceAccountName ?? this.sourceAccountName,
      sourceAccountDisplayName:
          sourceAccountDisplayName ?? this.sourceAccountDisplayName,
      sourceAccountBalanceText:
          sourceAccountBalanceText ?? this.sourceAccountBalanceText,
      sourceAccountBalance: sourceAccountBalance ?? this.sourceAccountBalance,
      sourceCustomerName: sourceCustomerName ?? this.sourceCustomerName,
      sourceBankImage: sourceBankImage ?? this.sourceBankImage,
      sourceBankImageType: sourceBankImageType ?? this.sourceBankImageType,
      sourceBankName: sourceBankName ?? this.sourceBankName,
      sourceAccountAvatar: sourceAccountAvatar ?? this.sourceAccountAvatar,
      targetBankShortName: targetBankShortName ?? this.targetBankShortName,
      targetBankCode: targetBankCode ?? this.targetBankCode,
      targetBankImage: targetBankImage ?? this.targetBankImage,
      targetBankImageType: targetBankImageType ?? this.targetBankImageType,
      targetBankName: targetBankName ?? this.targetBankName,
      targetAccountNumber: targetAccountNumber ?? this.targetAccountNumber,
      targetAccountName: targetAccountName ?? this.targetAccountName,
      targetAccountDisplayName:
          targetAccountDisplayName ?? this.targetAccountDisplayName,
      targetAccountAvatar: targetAccountAvatar ?? this.targetAccountAvatar,
      targetAccountDisplayNameEdit:
          targetAccountDisplayNameEdit ?? this.targetAccountDisplayNameEdit,
      amount: amount ?? this.amount,
      note: note ?? this.note,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      categoryImage: categoryImage ?? this.categoryImage,
      categoryImageType: categoryImageType ?? this.categoryImageType,
      whoCharge: whoCharge ?? this.whoCharge,
      chargeAmount: chargeAmount ?? this.chargeAmount,
      isAccount: isAccount ?? this.isAccount,
      is247: is247 ?? this.is247,
      isNow: isNow ?? this.isNow,
      softOtp: softOtp ?? this.softOtp,
      schedule: schedule as ScheduleModel? ?? this.schedule?.copyWith(),
      transactionNumber: transactionNumber ?? this.transactionNumber,
      transactionTime: transactionTime ?? this.transactionTime,
      saveTemplate: saveTemplate ?? this.saveTemplate,
      templateId: templateId ?? this.templateId,
      scheduleId: scheduleId ?? this.scheduleId,
      cityName: cityName ?? this.cityName,
      cityCode: cityCode ?? this.cityCode,
      targetBranchId: targetBranchId ?? this.targetBranchId,
      targetBranchName: targetBranchName ?? this.targetBranchName,
      targetBankCitad: targetBankCitad ?? this.targetBankCitad,
      targetBankIdNapas: targetBankIdNapas ?? this.targetBankIdNapas,
      targetBankCodeId: targetBankCodeId ?? this.targetBankCodeId,
      targetCommonBankName: targetCommonBankName ?? this.targetCommonBankName,
      targetCompanyId: targetCompanyId ?? this.targetCompanyId,
      advanceTransaction: advanceTransaction ?? this.advanceTransaction,
    );
  }

  @override
  String toString() {
    return 'TransferModel{cifNumber: $cifNumber, sourceAccountNumber: $sourceAccountNumber, sourceAccountName: $sourceAccountName, sourceAccountDisplayName: $sourceAccountDisplayName, sourceAccountBalanceText: $sourceAccountBalanceText, sourceCustomerName: $sourceCustomerName, sourceAccountAvatar: $sourceAccountAvatar, targetBankShortName: $targetBankShortName, targetBankCode: $targetBankCode, targetBankImage: $targetBankImage, targetBankImageType: $targetBankImageType, targetBankName: $targetBankName, targetAccountNumber: $targetAccountNumber, targetAccountName: $targetAccountName, targetAccountDisplayName: $targetAccountDisplayName, targetAccountAvatar: $targetAccountAvatar, targetAccountDisplayNameEdit: $targetAccountDisplayNameEdit, amount: $amount, note: $note, categoryId: $categoryId, categoryName: $categoryName, categoryImage: $categoryImage, categoryImageType: $categoryImageType, whoCharge: $whoCharge, chargeAmount: $chargeAmount, isAccount: $isAccount, is247: $is247, isNow: $isNow, verifySoftOtp: $softOtp, schedule: $schedule, transactionNumber: $transactionNumber, transactionTime: $transactionTime, saveTemplate: $saveTemplate, templateId: $templateId, cityName: $cityName, cityCode: $cityCode, targetBranchId: $targetBranchId, targetBranchName: $targetBranchName, targetBankCitad: $targetBankCitad}, scheduleId: $scheduleId, bankIdNapas:$targetBankIdNapas}';
  }

  bool canNext() {
    return targetAccountNumber?.isNotEmpty == true &&
        amount != null &&
        amount! > 0 &&
        targetAccountName?.isNotEmpty == true &&
        validateNote(note);
  }
}

class ScheduleModel {
  static const DAY = 'day';
  static const DAILY = 'daily';
  static const WEEKLY = 'weekly';
  static const MONTHLY = 'monthly';

  static const SCHEDULE_TYPES = [DAY, DAILY, WEEKLY, MONTHLY];

  static const SUN = "SUN";
  static const MON = "MON";
  static const TUE = "TUE";
  static const WED = "WED";
  static const THU = "THU";
  static const FRI = "FRI";
  static const SAT = "SAT";

  static const Map<int, String> weekDays = {
    0: ScheduleModel.SUN,
    DateTime.monday: ScheduleModel.MON,
    DateTime.tuesday: ScheduleModel.TUE,
    DateTime.wednesday: ScheduleModel.WED,
    DateTime.thursday: ScheduleModel.THU,
    DateTime.friday: ScheduleModel.FRI,
    DateTime.saturday: ScheduleModel.SAT,
  };

  static const Map<String, int> weekDaysInvert = {
    ScheduleModel.SUN: 0,
    ScheduleModel.MON: DateTime.monday,
    ScheduleModel.TUE: DateTime.tuesday,
    ScheduleModel.WED: DateTime.wednesday,
    ScheduleModel.THU: DateTime.thursday,
    ScheduleModel.FRI: DateTime.friday,
    ScheduleModel.SAT: DateTime.saturday
  };

  String? scheduleType;
  DateTime? fromDate;
  DateTime? toDate;
  String? dateOfWeek;
  String? dateOfMonth;
  String? scheduleName;
  String? scheduleIndexWeek;
  int? scheduleIndexMonth;

  ScheduleModel({
    this.scheduleType,
    this.scheduleName,
    this.fromDate,
    this.toDate,
    this.dateOfWeek,
    this.dateOfMonth,
    this.scheduleIndexWeek,
    this.scheduleIndexMonth,
  });

  ScheduleModel copyWith({
    String? scheduleType,
    String? scheduleName,
    DateTime? fromDate,
    DateTime? toDate,
    String? dateOfWeek,
    String? dateOfMonth,
    String? scheduleIndexWeek,
    int? scheduleIndexMonth,
  }) {
    return new ScheduleModel(
      scheduleType: scheduleType ?? this.scheduleType,
      scheduleName: scheduleName ?? this.scheduleName,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      dateOfWeek: dateOfWeek ?? this.dateOfWeek,
      dateOfMonth: dateOfMonth ?? this.dateOfMonth,
      scheduleIndexWeek: scheduleIndexWeek ?? this.scheduleIndexWeek,
      scheduleIndexMonth: scheduleIndexMonth ?? this.scheduleIndexMonth,
    );
  }

  @override
  String toString() {
    return 'ScheduleModel{scheduleType: $scheduleType, fromDate: $fromDate, toDate: $toDate, dateOfWeek: $dateOfWeek, dateOfMonth: $dateOfMonth}';
  }

  String get days {
    if (scheduleType == WEEKLY) return dateOfWeek ?? SUN;
    if (scheduleType == MONTHLY) return dateOfMonth ?? '1';
    return '';
  }

  String getDisplayScheduleTime(BuildContext context) {
    final result = StringBuffer();
    if (scheduleType == ScheduleModel.DAY || toDate == null) {
      result.write('${fromDate.formatDMY}');
    } else if (scheduleType == ScheduleModel.DAILY) {
      result.write('${fromDate.formatDMY} - ${toDate.formatDMY}');
    } else if (scheduleType == ScheduleModel.WEEKLY &&
        !dateOfWeek.isNullOrEmpty) {
      final seedDate = DateTime(2021, 4, 4); //sunday
      final index = ScheduleModel.weekDaysInvert[dateOfWeek!]!;
      scheduleIndexWeek =
          DateFormat("EEEE").format(seedDate.add(Duration(days: index)));

      result.write('${fromDate.formatDMY} - ${toDate.formatDMY}');
    } else {
      result.write('${fromDate.formatDMY} - ${toDate.formatDMY}');
    }
    return result.toString();
  }
}

class StockType {
  static final String INVEST = "INVEST";
  static final String BOND = "BOND";
  static final String RESIDENCE = "RESIDENCE";
  static final String SECURITY = "SECURITY";
}

String? getPaymentCallBackUrl(scheme, amount, transactionNumber) {
  return scheme +
      "/paymentResponse?amount=$amount&transaction_id=$transactionNumber";
}

bool validateNote(String? note) {
  RegExp exp = new RegExp(r"[^\w\s\-.,]+");
  if (note == null || note.isEmpty) return true;

  if (exp.hasMatch(note)) {
    return false;
  } else {
    return true;
  }
}
