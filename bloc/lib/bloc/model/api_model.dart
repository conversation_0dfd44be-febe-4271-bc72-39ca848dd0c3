import 'package:ksbank_api_media/ksbank_api_media.dart' as ksbankApiMedia
    show standardSerializers;
import 'package:ksbank_api_profile/ksbank_api_profile.dart' as ksbankApiProfile
    show standardSerializers;
import 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart'
    as ksbankApiSmartBank show standardSerializers;
import 'package:saving_vnp_api/saving_vnp_api.dart' as ksbankApiSavingVNP
    show standardSerializers;
import 'package:ksbank_api_notification/ksbank_api_notification.dart'
    as ksbankApiNotification show standardSerializers;
import 'package:ksbank_api_stocks/ksbank_api_stocks.dart' as ksbankApiStock
    show standardSerializers;
import 'package:ksbank_api_stm/ksbank_api_stm.dart' as ksbankApiStm
    show standardSerializers;
import 'package:ksbank_api_maintenance/ksbank_api_maintenance.dart'
    as ksbankApiMaintenance show standardSerializers;
import 'package:ksbank_api_loyalty/ksbank_api_loyalty.dart' as ksbankApiLoyalty
    show standardSerializers;
import 'package:myshop_api/myshop_api.dart' as ksbankApiMyShop
    show standardSerializers;
import 'package:ksbank_api_nickname/ksbank_api_nickname.dart'
    as ksbankApiNickName show standardSerializers;
import 'package:video_ekyc_api/video_ekyc_api.dart' as videoEKyc
    show standardSerializers;
import 'package:vnpay_qr_api/vnpay_qr_api.dart' as vnPayQRApi
    show standardSerializers;
import 'package:lucky_money_api/lucky_money_api.dart' as ksbankApiLuckyMoney
    show standardSerializers;
import 'package:wish_saving_api/wish_saving_api.dart' as ksbankApiWishSaving
    show standardSerializers;
import 'package:ksbank_api_sale_app/ksbank_api_sale_app.dart'
    as ksbankApiSaleApp show standardSerializers;

import '../../repository/try_parse_plugin.dart';

export 'package:ksbank_api_loyalty/ksbank_api_loyalty.dart'
    hide
        VerifySoftOtpRequest,
        VerifySoftOtpRequestBuilder,
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        BasicAuthInterceptor,
        standardSerializers,
        DateTimeToDate,
        Date,
        serializers;
export 'package:ksbank_api_maintenance/ksbank_api_maintenance.dart'
    hide
        BaseResponseObject,
        BaseResponseObjectBuilder,
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        BasicAuthInterceptor,
        standardSerializers,
        DateTimeToDate,
        Date,
        serializers,
        NullType,
        NullTypeMixin;
export 'package:ksbank_api_media/ksbank_api_media.dart'
    hide NullType, NullTypeMixin, Date, DateTimeToDate;
export 'package:ksbank_api_notification/ksbank_api_notification.dart'
    hide
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        standardSerializers,
        serializers,
        DateTimeToDate,
        Date,
        BasicAuthInterceptor,
        NullType,
        NullTypeMixin;
export 'package:ksbank_api_profile/ksbank_api_profile.dart'
    hide
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        standardSerializers,
        serializers,
        BasicAuthInterceptor,
        CreateNotificationResponse,
        CreateNotificationResponseBuilder,
        BaseResponseCreateNotificationResponse,
        BaseResponseCreateNotificationResponseBuilder,
        DistrictResponse,
        DistrictResponseBuilder,
        ThemeData,
        ThemeDataBuilder,
        NotificationControllerApi,
        NullType,
        NullTypeMixin;
export 'package:ksbank_api_smartbank/ksbank_api_smartbank.dart'
    hide
        BasicAuthInfo,
        BaseResponseObject,
        BaseResponseObjectBuilder,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        standardSerializers,
        serializers,
        BasicAuthInterceptor,
        DateTimeToDate,
        Date,
        NullType,
        BearerAuthInterceptor,
        NullTypeMixin;
export 'package:ksbank_api_stm/ksbank_api_stm.dart'
    hide
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        BasicAuthInterceptor,
        DateTimeToDate,
        FileUploadResponseBuilder,
        Date,
        FileUploadResponse,
        standardSerializers,
        serializers,
        NullType,
        NullTypeMixin;
export 'package:ksbank_api_stocks/ksbank_api_stocks.dart'
    hide
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        BasicAuthInterceptor,
        standardSerializers,
        DateTimeToDate,
        Date,
        serializers,
        NullType,
        NullTypeMixin;
export 'package:ksbank_api_nickname/ksbank_api_nickname.dart'
    hide
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        BasicAuthInterceptor,
        standardSerializers,
        DateTimeToDate,
        Date,
        serializers,
        NullType,
        NullTypeMixin;
export 'package:vnpay_qr_api/vnpay_qr_api.dart'
    hide
    ReviewTransferResponse,
    DateTimeToDate,
    ReviewTransferRequest,
    ReviewTransferResponseBuilder,
    NullTypeMixin,
    OAuthInterceptor,
    BasicAuthInterceptor,
    Date,
    ReviewTransferRequestBuilder,
    standardSerializers,
    serializers,
    NullType,
    BasicAuthInfo,
    VerifyOtpRequestBuilder,
    VerifyOtpRequest,
    ApiKeyAuthInterceptor;

export 'package:saving_vnp_api/saving_vnp_api.dart'
    hide
        BasicAuthInfo,
        ApiKeyAuthInterceptor,
        OAuthInterceptor,
        BasicAuthInterceptor,
        standardSerializers,
        DateTimeToDate,
        Date,
        BaseResponseListWithdrawalCodeExpiredResponseBuilder,
        ReviewTransferRequestBuilder,
        TransferResponse,
        BaseResponseListWithdrawalCodeActiveResponse,
        ListWithdrawalCodeActiveResponseBuilder,
        ReviewTransferResponseBuilder,
        ReviewTransferRequest,
        ListWithdrawalCodeExpiredResponse,
        ListWithdrawalCodeExpiredResponseBuilder,
        BaseResponseListWithdrawalCodeExpiredResponse,
        TransferApi,
        TransferRequestBuilder,
        TransferRequest,
        BaseResponseTransferResponseBuilder,
        BaseResponseListWithdrawalCodeActiveResponseBuilder,
        BaseResponseReviewTransferResponse,
        BaseResponseReviewTransferResponseBuilder,
        WithdrawalCodeResponseBuilder,
        WithdrawalCodeResponse,
        BaseResponseTransferResponse,
        ListWithdrawalCodeActiveResponse,
        TransferResponseBuilder,
        ReviewTransferResponse,
        serializers,
        NullType,
        NullTypeMixin;

export 'package:lucky_money_api/lucky_money_api.dart'
    hide
        ApiKeyAuthInterceptor,
        BasicAuthInfo,
        serializers,
        standardSerializers,
        Date,
        BasicAuthInterceptor,
        AccessTokenResponseBuilder,
        OAuthInterceptor,
        AccessTokenResponse,
        VerifySoftOtpRequest,
        DateTimeToDate,
        VerifySoftOtpRequestBuilder,
        NullType,
        NullTypeMixin;
export 'package:ksbank_api_sale_app/ksbank_api_sale_app.dart'
    hide
        ApiKeyAuthInterceptor,
        BasicAuthInfo,
        serializers,
        standardSerializers,
        Date,
        BasicAuthInterceptor,
        OAuthInterceptor,
        DateTimeToDate,
        NullType,
        NullTypeMixin;

export 'package:wish_saving_api/wish_saving_api.dart'
    hide
    ApiKeyAuthInterceptor,
    BasicAuthInfo,
    serializers,
    standardSerializers,
    Date,
    BasicAuthInterceptor,
    OAuthInterceptor,
    VerifySoftOtpRequest,
    DateTimeToDate,
    VerifySoftOtpRequestBuilder,
    NullType,
    TransNextStep,
    TransNextStepMixin,
    NullTypeMixin;

final _ksbankProfileSerializers =
    (ksbankApiProfile.standardSerializers.toBuilder()).build();
final ksbankProfileSerializers = (_ksbankProfileSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankProfileSerializers)))
    .build();

final _ksbankMediaSerializers =
    (ksbankApiMedia.standardSerializers.toBuilder()).build();
final ksbankMediaSerializers = (_ksbankMediaSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankMediaSerializers)))
    .build();

final _ksbankSmartBankSerializers =
    (ksbankApiSmartBank.standardSerializers.toBuilder()).build();
final ksbankSmartBankSerializers = (_ksbankSmartBankSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankSmartBankSerializers)))
    .build();

final _ksbankSavingVnpSerializers =
    (ksbankApiSavingVNP.standardSerializers.toBuilder()).build();
final ksbankSavingVnpSerializers = (_ksbankSavingVnpSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankSmartBankSerializers)))
    .build();

final _vnPayQRSerializers =
    (vnPayQRApi.standardSerializers.toBuilder()).build();
final vnPayQRSerializers = (_vnPayQRSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _vnPayQRSerializers)))
    .build();

final _ksbankNotificationSerializers =
    (ksbankApiNotification.standardSerializers.toBuilder()).build();
final ksbankNotificationSerializers = (_ksbankNotificationSerializers
        .toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankNotificationSerializers)))
    .build();

final _ksbankStocksSerializers =
    (ksbankApiStock.standardSerializers.toBuilder()).build();
final ksbankStocksSerializers = (_ksbankStocksSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankStocksSerializers)))
    .build();

final _ksbankStmSerializers =
    (ksbankApiStm.standardSerializers.toBuilder()).build();
final ksbankStmSerializers = (_ksbankStmSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankStmSerializers)))
    .build();

final _ksbankMaintenanceSerializers =
    (ksbankApiMaintenance.standardSerializers.toBuilder()).build();
final ksbankMaintenanceSerializers = (_ksbankMaintenanceSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankMaintenanceSerializers)))
    .build();

final _ksbankLoyaltySerializers =
    (ksbankApiLoyalty.standardSerializers.toBuilder()).build();
final ksbankLoyaltySerializers = (_ksbankLoyaltySerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankLoyaltySerializers)))
    .build();

final _ksbankMyshopSerializers =
    (ksbankApiMyShop.standardSerializers.toBuilder()).build();
final ksbankMyshopSerializers = (_ksbankMyshopSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankMyshopSerializers)))
    .build();

final _ksbankNicknameSerializers =
    (ksbankApiNickName.standardSerializers.toBuilder()).build();
final ksbankNicknameSerializers = (_ksbankNicknameSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankNicknameSerializers)))
    .build();

final _videoEkycSerializers =
    (videoEKyc.standardSerializers.toBuilder()).build();
final videoEkycSerializers = (_videoEkycSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _videoEkycSerializers)))
    .build();

final _ksbankLuckyMoneySerializers =
    (ksbankApiLuckyMoney.standardSerializers.toBuilder()).build();
final ksbankLuckyMoneySerializers = (_ksbankLuckyMoneySerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankLuckyMoneySerializers)))
    .build();

final _ksbankWishSavingSerializers =
    (ksbankApiWishSaving.standardSerializers.toBuilder()).build();
final ksbankWishSavingSerializers = (_ksbankWishSavingSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankWishSavingSerializers)))
    .build();

final _ksbankSaleappSerializers =
    (ksbankApiSaleApp.standardSerializers.toBuilder()).build();
final ksbankSaleappSerializers = (_ksbankSaleappSerializers.toBuilder()
      ..addPlugin(TryParsePlugin(serializers: _ksbankSaleappSerializers)))
    .build();
