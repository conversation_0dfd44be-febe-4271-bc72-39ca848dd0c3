import 'package:ksbank_api_profile/ksbank_api_profile.dart';

/// Model cho AWS Liveness Session
/// Map từ CreateTransactionLivenessSessionResponse
class AwsLivenessSessionModel {
  /// ID của session liveness
  final String? sessionId;

  /// Token của session
  final String? sessionToken;

  /// Region của AWS
  final String? region;

  /// Access Key ID của AWS
  final String? accessKeyId;

  /// Secret Access Key của AWS
  final String? secretAccessKey;

  const AwsLivenessSessionModel({
    this.sessionId,
    this.sessionToken,
    this.region,
    this.accessKeyId,
    this.secretAccessKey,
  });

  /// Tạo instance từ JSON
  factory AwsLivenessSessionModel.fromJson(Map<String, dynamic> json) {
    return AwsLivenessSessionModel(
      sessionId: json['sessionId'] as String?,
      sessionToken: json['sessionToken'] as String?,
      region: json['region'] as String?,
      accessKeyId: json['accessKeyId'] as String?,
      secretAccessKey: json['secretAccessKey'] as String?,
    );
  }

  /// <PERSON>y<PERSON>n đổi thành JSON
  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'sessionToken': sessionToken,
      'region': region,
      'accessKeyId': accessKeyId,
      'secretAccessKey': secretAccessKey,
    };
  }

  /// Tạo instance từ CreateTransactionLivenessSessionResponse
  factory AwsLivenessSessionModel.fromCreateTransactionResponse(
      CreateTransactionLivenessSessionResponse res) {
    return AwsLivenessSessionModel(
      sessionId: res.sessionId,
      sessionToken: res.sessionToken,
      region: res.region,
      accessKeyId: res.accessKeyId,
      secretAccessKey: res.secretAccessKey,
    );
  }

  /// Copy với các thay đổi
  AwsLivenessSessionModel copyWith({
    String? sessionId,
    String? sessionToken,
    String? region,
    String? accessKeyId,
    String? secretAccessKey,
  }) {
    return AwsLivenessSessionModel(
      sessionId: sessionId ?? this.sessionId,
      sessionToken: sessionToken ?? this.sessionToken,
      region: region ?? this.region,
      accessKeyId: accessKeyId ?? this.accessKeyId,
      secretAccessKey: secretAccessKey ?? this.secretAccessKey,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AwsLivenessSessionModel &&
        other.sessionId == sessionId &&
        other.sessionToken == sessionToken &&
        other.region == region &&
        other.accessKeyId == accessKeyId &&
        other.secretAccessKey == secretAccessKey;
  }

  @override
  int get hashCode {
    return sessionId.hashCode ^
        sessionToken.hashCode ^
        region.hashCode ^
        accessKeyId.hashCode ^
        secretAccessKey.hashCode;
  }

  @override
  String toString() {
    return 'AwsLivenessSessionModel('
        'sessionId: $sessionId, '
        'sessionToken: $sessionToken, '
        'region: $region, '
        'accessKeyId: $accessKeyId, '
        'secretAccessKey: $secretAccessKey)';
  }
}
