/// Model cho AWS Liveness Session Result
/// Map từ GetTransactionLivenessSessionResultResponse
class AwsLivenessSessionResult {
  /// ID của session liveness
  final String? sessionId;

  /// Trạng thái xác thực (verified)
  final bool? verified;

  const AwsLivenessSessionResult({
    this.sessionId,
    this.verified,
  });

  /// Tạo instance từ JSON
  factory AwsLivenessSessionResult.fromJson(Map<String, dynamic> json) {
    return AwsLivenessSessionResult(
      sessionId: json['sessionId'] as String?,
      verified: json['verified'] as bool?,
    );
  }

  /// Chuyển đổi thành JSON
  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'verified': verified,
    };
  }

  /// Tạo instance từ GetTransactionLivenessSessionResultResponse
  factory AwsLivenessSessionResult.fromGetTransactionResultResponse(
      dynamic getTransactionResultResponse) {
    if (getTransactionResultResponse == null) {
      return const AwsLivenessSessionResult();
    }

    return AwsLivenessSessionResult(
      sessionId: getTransactionResultResponse.sessionId,
      verified: getTransactionResultResponse.verified,
    );
  }

  /// Copy với các thay đổi
  AwsLivenessSessionResult copyWith({
    String? sessionId,
    bool? verified,
  }) {
    return AwsLivenessSessionResult(
      sessionId: sessionId ?? this.sessionId,
      verified: verified ?? this.verified,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AwsLivenessSessionResult &&
        other.sessionId == sessionId &&
        other.verified == verified;
  }

  @override
  int get hashCode {
    return sessionId.hashCode ^ verified.hashCode;
  }

  @override
  String toString() {
    return 'AwsLivenessSessionResult('
        'sessionId: $sessionId, '
        'verified: $verified)';
  }
} 