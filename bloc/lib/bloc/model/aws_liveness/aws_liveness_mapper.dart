import 'aws_liveness_session_model.dart';
import 'aws_liveness_session_result.dart';

/// Utility class để map giữa AWS Liveness models và OpenAPI models
class AwsLivenessMapper {
  /// Map từ BaseResponseCreateTransactionLivenessSessionResponse sang AwsLivenessSessionModel
  static AwsLivenessSessionModel? mapFromBaseResponse(
      dynamic baseResponse) {
    if (baseResponse == null) return null;
    
    // Kiểm tra nếu có data trong base response
    if (baseResponse.data != null) {
      return AwsLivenessSessionModel.fromCreateTransactionResponse(
          baseResponse.data);
    }
    
    return null;
  }

  /// Map từ BaseResponseGetTransactionLivenessSessionResultResponse sang AwsLivenessSessionResult
  static AwsLivenessSessionResult? mapResultFromBaseResponse(
      dynamic baseResponse) {
    if (baseResponse == null) return null;
    
    // Kiểm tra nếu có data trong base response
    if (baseResponse.data != null) {
      return AwsLivenessSessionResult.fromGetTransactionResultResponse(
          baseResponse.data);
    }
    
    return null;
  }

  /// Map từ Map<String, dynamic> sang AwsLivenessSessionModel
  static AwsLivenessSessionModel? mapFromJson(Map<String, dynamic>? json) {
    if (json == null) return null;
    return AwsLivenessSessionModel.fromJson(json);
  }

  /// Map từ Map<String, dynamic> sang AwsLivenessSessionResult
  static AwsLivenessSessionResult? mapResultFromJson(Map<String, dynamic>? json) {
    if (json == null) return null;
    return AwsLivenessSessionResult.fromJson(json);
  }

  /// Kiểm tra xem base response có thành công không
  static bool isSuccess(dynamic baseResponse) {
    if (baseResponse == null) return false;
    return baseResponse.success == true;
  }

  /// Lấy message từ base response
  static String? getMessage(dynamic baseResponse) {
    if (baseResponse == null) return null;
    return baseResponse.message;
  }

  /// Lấy code từ base response
  static int? getCode(dynamic baseResponse) {
    if (baseResponse == null) return null;
    return baseResponse.code;
  }
} 