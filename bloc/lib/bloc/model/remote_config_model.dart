class PlanUpdateModel {
  bool? active;
  String? fromDate;
  String? toDate;
  String? message;
  Reference? reference;

  PlanUpdateModel(
      {this.active, this.fromDate, this.toDate, this.message, this.reference});

  PlanUpdateModel.fromJson(Map<String, dynamic> json) {
    active = json['active'] ?? false;
    fromDate = json['from_date'];
    toDate = json['to_date'];
    message = json['message'];
    reference = json['reference'] != null
        ? new Reference.fromJson(json['reference'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['active'] = this.active;
    data['from_date'] = this.fromDate;
    data['to_date'] = this.toDate;
    data['message'] = this.message;
    if (this.reference != null) {
      data['reference'] = this.reference!.toJson();
    }
    return data;
  }

  String getMessage() {
    String from = message!.replaceAll('{from_date}', fromDate!);
    String to = from.replaceAll('{to_date}', toDate!);
    return to;
  }
}

class Reference {
  String? title;
  String? link;

  Reference({this.title, this.link});

  Reference.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    link = json['link'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['link'] = this.link;
    return data;
  }
}

/*
{
  "enable_edit_ekyc": false, //cài đặt hiển thị chức năng chỉnh sửa eKYC tại luồng đăng ký
  "enable_nick_name": false, //cài đặt hiển thị chức năng đặt tên nick name
  "enable_collab": false, //cài đặt hiển thị chức năng cộng tác viên
  "enable_setting_transfer_limit": false, //cài đặt giới hạn chuyển khoản
  "enable_setting_push_notification": false,  //cài đặt hiển thị thông báo đẩy
  "enable_setting_sms_banking": true, //cài đặt hiển thị sms banking
  "enable_setting_third_party": false, //cài đặt hiển thị ứng dụng bên thứ 3
  "enable_choose_custom_ebank_package": false, //chọn gói ebank tuỳ chỉnh
  "enable_tuition_payment": true, //thanh toan hoc phi
  "enable_settlement_saving_vnpost": true, //tất toán tiết kiệm VNPost
  "enable_create_saving_vnpost": true, //mở tài khoản tiết kiệm VNPost
  "module_loan_version": 2,//vay cong tac vien
  "liveness_type": "face|nose", //chế độ liveness khuôn mặt hay mũi
  "liveness_save_video": true, //lưu video liveness
  "ssl_primary_fingerprints": "HASH1,HASH2", //SSL certificate fingerprints chính (comma-separated)
  "enable_aws_liveness": false, //cài đặt enable/disable aws liveness
  "limit_amount_show_aws_liveness": 1000000 //giới hạn số tiền để cho phép show aws liveness
}
 */

class KlbModuleConfig {
  bool? enableEditEkyc;
  bool? enableNickName;
  bool? enableCollab;

  bool? enableSettingTransferLimit;
  bool? enableSettingPushNotification;
  bool? enableCustomEbankPackage;

  bool? enableTuitionPayment;
  num? moduleLoanVersion;
  String? livenessType;

  bool? livenessSaveVideo;
  bool? enableSettingThirdParty;
  bool? enableSettingSmsBanking;

  bool? enableSettlementSavingVnpost;
  bool? enableCreateSavingVnpost;
  bool? enableVETCPayment;
  bool? enableSalaryAdvance;
  List<String>? supportIdentifyCards;

  ///enable/disable check accessibility services
  bool? enableCheckSecurity;

  // SSL Certificate Pinning fields
  List<String>? sslPrimaryFingerprints;

  bool? enableAwsLiveness;
  double? limitAmountShowAwsLiveness;

  KlbModuleConfig({
    this.enableEditEkyc,
    this.enableNickName,
    this.enableCollab,
    this.enableSettingTransferLimit,
    this.enableSettingPushNotification,
    this.enableCustomEbankPackage,
    this.enableTuitionPayment,
    this.moduleLoanVersion,
    this.livenessType,
    this.livenessSaveVideo,
    this.enableSettingThirdParty,
    this.enableSettingSmsBanking,
    this.enableSettlementSavingVnpost,
    this.enableCreateSavingVnpost,
    this.enableVETCPayment,
    this.enableSalaryAdvance,
    this.supportIdentifyCards,
    this.enableCheckSecurity,
    this.sslPrimaryFingerprints,
    this.enableAwsLiveness,
    this.limitAmountShowAwsLiveness,
  });

  KlbModuleConfig.fromJson(Map<String, dynamic> json) {
    enableEditEkyc = json['enable_edit_ekyc'];
    enableNickName = json['enable_nick_name'];
    enableCollab = json['enable_collab'];

    enableSettingTransferLimit = json['enable_setting_transfer_limit'];
    enableSettingPushNotification = json['enable_setting_push_notification'];
    enableCustomEbankPackage = json['enable_choose_custom_ebank_package'];

    enableTuitionPayment = json['enable_tuition_payment'];
    moduleLoanVersion = json['module_loan_version'];
    livenessType = json['liveness_type'];

    livenessSaveVideo = json['liveness_save_video'];
    enableSettingThirdParty = json['enable_setting_third_party'];
    enableSettingSmsBanking = json['enable_setting_sms_banking'];

    enableSettlementSavingVnpost = json['enable_settlement_saving_vnpost'];
    enableCreateSavingVnpost = json['enable_create_saving_vnpost'];
    enableVETCPayment = json['enable_vetc_payment'];
    enableSalaryAdvance = json['enable_salary_advance'];
    supportIdentifyCards = json["support_identity_cards"] == null
        ? null
        : List<String>.from(json["support_identity_cards"].map((x) => x));
    enableCheckSecurity = json['enable_check_security'];

    // Parse SSL certificate fingerprints
    sslPrimaryFingerprints = json["ssl_primary_fingerprints"] == null
        ? null
        : List<String>.from(json["ssl_primary_fingerprints"].map((x) => x));

    // AWS Liveness fields
    enableAwsLiveness = json['enable_aws_liveness'];
    limitAmountShowAwsLiveness =
        _parseNumber(json['limit_amount_show_aws_liveness']);
  }



  /// Parse number from Firebase Remote Config (int or double)
  double? _parseNumber(dynamic value) {
    if (value == null) return null;

    if (value is int) {
      return value.toDouble();
    } else if (value is double) {
      return value;
    } else if (value is String) {
      return double.tryParse(value);
    }

    return null;
  }

  /// Get all fingerprints (primary + backup) as a single list
  List<String> getAllFingerprints() {
    List<String> allFingerprints = [];

    if (sslPrimaryFingerprints != null) {
      allFingerprints.addAll(sslPrimaryFingerprints!);
    }

    return allFingerprints;
  }

  /// Check if SSL pinning is enabled (has any fingerprints)
  bool get isSSLPinningEnabled {
    return (sslPrimaryFingerprints?.isNotEmpty == true);
  }

  /// Check if AWS liveness should be shown based on amount
  /// Returns true if enableAwsLiveness = true AND amount <= limitAmountShowAwsLiveness
  bool shouldShowAwsLiveness(double amount) {
    if (enableAwsLiveness == null && limitAmountShowAwsLiveness == null) {
      return true;
    }
    if (enableAwsLiveness != true) return false;
    if (limitAmountShowAwsLiveness == null) return false;

    return amount >= limitAmountShowAwsLiveness!;
  }
}
