import 'dart:isolate';
import 'dart:ui';

import 'package:common/model/server_config.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/money_widget.dart';
import 'package:ekyc/main.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:ks_chat/bloc/bloc/chat_base_bloc.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/remote_config_model.dart';
import 'package:ksb_bloc/onesignal.dart';
import 'package:ksb_bloc/repository/firebase_config_app.dart';
import 'package:mobile_banking/bloc_impl/repository/app_interceptor.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/sunshine_app.dart';
import 'package:mobile_banking/utils/screen_size.dart';
import 'package:mobile_banking/utils/utils.dart';
import 'package:mobile_banking/vvn_sdk.dart';
import 'package:rxdart/rxdart.dart';
import 'package:umee_shop/di/injection.dart';
import 'package:umee_shop/environment.dart' as $shop;
import 'package:umee_shop/repository/src/umee_shop_repository_impl.dart';
import 'package:ekyc_bloc/environment.dart' as ekyc;
import 'package:salary_advance/environment/environment.dart' as $salary_advance;
import 'package:salary_advance/di/environment_injector.dart';
import 'package:salary_advance/data/salary_advance/salary_advance_impl.dart';

import 'firebase_options.dart';

import 'bloc_impl/repository/preferences_impl.dart';
import 'utils/firebase/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await initFirebaseCrashlytics();
  await FlutterDownloader.initialize();
  var _ev = Environment.newcore();
  Preferences preferences = PreferencesImpl();
  DialogUtil.defaultTitle = "KienlongBank";
  ChatBaseBloc.OAUTH_SERVICE_NAME = 'keycloak';
  await preferences.initPreferences();
  if (preferences.userName.isNullOrEmpty ||
      preferences.fullName.isNullOrEmpty) {
    await preferences.clearSecure();
  } else {
    try {
      await preferences.allValues;
    } catch (e) {
      await preferences.clearSecure();
    }
  }

  Environment? savedEnv = await preferences.getEnvironment();
  if (savedEnv != null && savedEnv.endPoint.isNotEmpty == true) {
    _ev = savedEnv;
  } else {
    preferences.setEnvironment(_ev.toJson());
  }
  setShowLog(_ev.isDevelopment);
  MoneyWidget.unitDefault = ' VND';
  final moduleConfig = await _getModuleConfig();
  await Injection.initInjection(_ev, preferences, remoteConfig: moduleConfig);
  await initModuleEKyc(
    env: ekyc.Environment.ksFinance(
      serverConfig: ServerConfig(apiAI: _ev.apiAi),
    ),
  );
  NotificationService.init(_ev);
  VvnSdk.instance.init(
    apiKey: 'Z9Gsi9B0sOK1QVZLayoAMZ2wJPi89Es9',
    host: 'https://api.cloudekyc.com',
  );
  VvnSdk.instance.packageName = null;
  SystemChrome.setSystemUIOverlayStyle((ScreenMode.isLight == true
      ? SystemUiOverlayStyle.light
      : SystemUiOverlayStyle.dark));

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  await initMyShop(_ev);

  await initSalaryAdvance(_ev);

  runApp(const SunshineApp());

  _bindBackgroundIsolate();
  FlutterDownloader.registerCallback(downloadCallback);
}

 Future<KlbModuleConfig?> _getModuleConfig() async {
    try {
     return await getModuleConfig();
    } catch (e) {
      logger.e(e);
      return null;
    }
  }

void downloadCallback(String id, int status, int progress) {
  final SendPort? send =
      IsolateNameServer.lookupPortByName('downloader_send_port');
  send?.send([id, status, progress]);
}

ReceivePort _receivePort = ReceivePort();

final _downloadStream = BehaviorSubject();

Stream get downloadStream => _downloadStream.stream;

void _bindBackgroundIsolate() {
  IsolateNameServer.removePortNameMapping('downloader_send_port');
  IsolateNameServer.registerPortWithName(
      _receivePort.sendPort, 'downloader_send_port');
  _receivePort.pipe(_downloadStream);
}

initFirebaseCrashlytics() async {
  // await Firebase.initializeApp();
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
  await FirebaseCrashlytics.instance
      .setCrashlyticsCollectionEnabled(!kDebugMode);
}

initMyShop(Environment env, {bool changeEnv = false}) async {
  final Map<String, String> endPoints = {
    "apiOAuth": "https://api.sunshinegroup.vn:5000",
    "apiCore": "https://apicore.sunshinetech.com.vn",
    "strApi": env.strApiUrl,
    "apiIdentity": "https://apiidentity.sunshinegroup.vn",
    "apiChat": env.chatServerUrl.toString(),
    "socketChat": env.chatSocketServer,
    "serverUrl": env.endPoint,
    "language": env.language,
    "proxy": env.proxy,
    "payboxConfigUrl": env.myShopPayboxConfigUrl
  };
  $shop.Environment environment = $shop.Environment.fromJson(endPoints);
  if (changeEnv) {
    repository.setUp(environment, AppInterceptors());
  } else {
    await configureDependencies(environment, AppInterceptors());
  }
}

initSalaryAdvance(Environment env, {bool changeEnv = false}) async {
  final Map<String, String> endPoints = {
    "apiOAuth": "https://api.sunshinegroup.vn:5000",
    "apiCore": "https://apicore.sunshinetech.com.vn",
    "strApi": env.strApiUrl,
    "apiIdentity": "https://apiidentity.sunshinegroup.vn",
    "apiChat": env.chatServerUrl.toString(),
    "socketChat": env.chatSocketServer,
    "serverUrl": env.endPoint,
    "language": env.language,
    "proxy": env.proxy,
    "payboxConfigUrl": env.myShopPayboxConfigUrl
  };
  $salary_advance.Environment environment =
      $salary_advance.Environment.fromJson(endPoints);
  if (changeEnv) {
    salaryAdvanceRepository.setUp(environment, AppInterceptors());
  } else {
    await SalaryAdvanceConfigEnvironment.configureDependencies(
        environment, AppInterceptors());
  }
}
