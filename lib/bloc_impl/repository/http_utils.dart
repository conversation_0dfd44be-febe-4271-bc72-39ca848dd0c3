import 'dart:convert';
import 'dart:io';

import 'package:ksb_bloc/bloc.dart';
import 'package:dio/io.dart';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:mobile_banking/bloc_impl/repository/ssl_certificate_pinning_interceptor.dart';
// Xoá import model config vì không cần thiết

Dio initDio(
  String baseUrl, {
  String? proxy,
  int connectTimeout = 60000,
  int receiveTimeout = 60000,
  List<String>? sslFingerprints,
}) {
  Dio dio;
  BaseOptions options = BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: Duration(milliseconds: connectTimeout),
    receiveTimeout: Duration(milliseconds: receiveTimeout),
  );
  dio = Dio(options);

  if (proxy != null && proxy.isNotEmpty) {
    dio.httpClientAdapter = IOHttpClientAdapter(
      createHttpClient: () {
        final client = HttpClient();
        client.findProxy = (url) {
          return 'PROXY $proxy';
        };
        client.badCertificateCallback =
            (X509Certificate cert, String host, int port) => true;
        return client;
      },
    );
  }

  // Thêm interceptor SSL pinning nếu có fingerprints
  if (sslFingerprints != null && sslFingerprints.isNotEmpty) {
    dio.interceptors.add(
      SSLCertificatePinningInterceptor(sslFingerprints),
    );
  }

  return dio;
}

Map<String, String> getHttpHeader({
  String contentType = 'application/json',
  String? token,
}) {
  final header = Map<String, String>();
  if (contentType.isNotEmpty == true) {
    header[HttpHeaders.contentTypeHeader] = contentType;
  }

  if (token?.isNotEmpty == true) {
    header[HttpHeaders.authorizationHeader] = token!;
  }
  return header;
}

handlerJsonResponse(http.Response response) {
  logger.e('=> statusCode  [32m${response.statusCode} [0m');
  if (response.statusCode == HttpStatus.ok ||
      response.statusCode == HttpStatus.badRequest) {
    return json.decode(response.body);
  }
  if (response.statusCode == HttpStatus.unauthorized) {
    throw Exception(response.statusCode);
  } else {
    throw Exception('${response.body}');
  }
}

toBaseResponse(baseResponse) {
  final response = BaseResponseModel.fromMap(baseResponse);
  if (response.data != null) {
    return response.data;
  }
  if (response.statusCode != 0) {
    throw Exception(response.message);
  }
}

bool toBoolResponse(baseResponse) {
  final response = BaseResponseModel.fromMap(baseResponse);
  if (response.statusCode != 0) {
    throw Exception(response.message);
  }
  return response.statusCode == 0;
}

Map getPagingParameters([int? offSet = 0, int? pageSize = 20, String? filter]) {
  return {
    "offSet": offSet ?? 0,
    "pageSize": pageSize ?? 20,
    if (filter?.isNotEmpty == true) "filter": filter,
  };
}

String buildUrl(String url, [Map? parameters]) {
  final stringBuilder = StringBuffer(url);
  if (parameters?.isNotEmpty == true) {
    stringBuilder.write('?');
    parameters?.forEach((key, value) => stringBuilder.write('$key=$value&'));
  }
  final result = stringBuilder.toString();
  logger.v(result);
  return result;
}

String toJsonString(data) {
  String encoded = json.encode(data);
  logger.v('ENCODED - $encoded');
  return encoded;
}
