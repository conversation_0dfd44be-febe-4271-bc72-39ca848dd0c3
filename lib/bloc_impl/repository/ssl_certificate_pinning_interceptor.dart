import 'dart:async';

import 'package:ksb_bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:http_certificate_pinning/http_certificate_pinning.dart';

class SSLCertificatePinningInterceptor extends Interceptor {
  final List<String> _allowedSHAFingerprints;

  SSLCertificatePinningInterceptor(this._allowedSHAFingerprints);

  @override
  Future onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    var dioError =
        DioException(requestOptions: options, error: 'CONNECTION_NOT_SECURE');
    try {
      final secure = await HttpCertificatePinning.check(
        serverURL: options.baseUrl,
        headerHttp: options.headers.map((a, b) => MapEntry(a, b.toString())),
        sha: SHA.SHA256,
        allowedSHAFingerprints: _allowedSHAFingerprints,
        timeout: 50,
      ).timeout(const Duration(seconds: 2), onTimeout: () => 'CONNECTION_SECURE');
      if (secure.contains("CONNECTION_SECURE")) {
        return super.onRequest(options, handler);
      } else {
        handler.reject(dioError);
      }
    } catch (e) {
      logger.e(e);
      handler.reject(dioError);
    }
  }
}
