import 'dart:convert';
import 'dart:io';

import 'package:common/model/base_response.dart' as base_response_common;
import 'package:common/global_callback.dart';
import 'package:connectivity/connectivity.dart';
import 'package:ekyc/ui/auth_transaction_ekyc_sunshine.dart';
import 'package:face_liveness_detector/face_liveness_detect.dart';
import 'package:face_liveness_detector/face_liveness_error.dart';
import 'package:app_settings/app_settings.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:home_widget/home_widget.dart';
import 'package:ks_chat/bloc/model/base_item.dart' as baseItem;
import 'package:ks_chat/bloc/model/message_model.dart';
import 'package:ks_chat/ks_chat.dart' as $ksChat;
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/device_info.dart';
import 'package:ksb_bloc/bloc/model/remote_config_model.dart';
import 'package:ksb_bloc/bloc/model/theme/theme_model.dart';
import 'package:ksb_bloc/deep_link.dart';
import 'package:ksb_bloc/repository/firebase_config_app.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/image/image_from_type.dart';
import 'package:ksb_common/umee_common_factory.dart';
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/multil_localizations.dart';
import 'package:mobile_banking/route.dart';
import 'package:mobile_banking/ui_v2/auth/login_page_v2.dart';
import 'package:mobile_banking/ui_v2/chat/v2/chat_visitor_page.dart';
import 'package:mobile_banking/ui_v2/message/v2/departments_choose_page.dart';
import 'package:mobile_banking/ui_v2/message/v2/quick_message_page.dart';
import 'package:mobile_banking/ui_v2/qr_scanner/qr_scanner.dart';
import 'package:mobile_banking/ui_v2/splash_page.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_overlay.dart';
import 'package:mobile_banking/ui_v2/widget/permission/permission.dart';
import 'package:mobile_banking/ui_v2/widget/referral_code_confirm_sheet.dart';
import 'package:mobile_banking/ui_v2/widget/referral_widget.dart';
import 'package:mobile_banking/utils/otp_utils.dart';
import 'package:mobile_banking/utils/utils.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ksb_common/ksb_common.dart' as ksb_common;

import 'ui_v2/auth/widget/view_capture_warning_widget.dart';
import 'ui_v2/home/<USER>/successful_authentication_page.dart';
import 'ui_v2/home/<USER>/update_identification_card_page.dart';
import 'ui_v2/request_money_qr/request_money_list_support_bank_viet_q_r.dart';

part "global_callback_for_auth_transaction.dart";

final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

KlbModuleConfig? _moduleConfig;

KlbModuleConfig? get moduleConfig => _moduleConfig;

final GlobalKey<NavigatorState> navigator = GlobalKey();

BuildContext get globalContext => navigator.currentState!.context;

class SunshineApp extends StatefulWidget {
  const SunshineApp({Key? key}) : super(key: key);

  @override
  State<SunshineApp> createState() => _SunshineAppState();
}

class _SunshineAppState extends State<SunshineApp>
    with RepeatCountdown, WidgetsBindingObserver {
  late Preferences _prefs;
  final _env = Injection.injector.get<Environment>();
  final _session = Injection.injector.get<Session>();
  final _loginBloc = Injection.injector.get<LoginBloc>();
  final _chatBloc = Injection.injector.get<$ksChat.RocketChatRoomsBloc>();
  final _chatVisitorBloc =
      Injection.injector.get<$ksChat.RocketChatVisitorRoomsBloc>();
  late CompositeSubscription _subs;
  final _settingsChange = BehaviorSubject<bool>();
  final sleepAfterSeconds = 300;
  bool isAutologin = true;

  // retry connect Socket
  int numberTryConnect = 0;
  bool isThrottling = false;
  ThemeDataLocal? theme;
  final _preferences = Injection.preferences;

  void _loadPriorityImages() async {
    try {
      if (!_preferences.getNameThemeIntro().isNullOrEmpty) {
        theme = ThemeDataLocal.fromJson(
          jsonDecode(_preferences.getNameThemeIntro()),
        );
      }
      preCacheSvg(
        theme?.imageUrl ?? "",
        type: ksb_common.getImageTypeFrom(theme?.type),
      );
    } catch (e) {
      logger.e(e);
    }
  }

  @override
  void initState() {
    super.initState();
    HomeWidget.setAppGroupId('group.com.sunshine.mobileBanking.homeWidget');
    _loadPriorityImages();
    _subs = CompositeSubscription();
    WidgetsBinding.instance.addObserver(this);
    _initSettings();

    ///handle value being clicked from home screen widget to start app
    HomeWidget.initiallyLaunchedFromHomeWidget().then(
      (value) {
        _prefs.setUriHomeWidget(value.toString());
        if ((value?.host ?? "") == "scanqr") {
          setState(() {
            isAutologin = false;
          });
        }
      },
    );
    _subs.add(_session.actionStatus.stream.distinct().listen((event) {
      if (event?.state == ActionState.RESTART) {
        goToLogin(navigator.currentState?.overlay?.context);
      }
    }));
    _subs.add(_session.networkStatus.stream.distinct().listen((event) {
      if (event == NetworkStatus.none) {
        DialogUtil.alert(
            navigator.currentState!.context,
            S
                .of(navigator.currentState!.context)
                .common_network_connection_error_des,
            title: S
                .of(navigator.currentState!.context)
                .common_network_connection_error_title,
            onSubmit: () => _session.networkStatus.add(NetworkStatus.others));
      }
    }));

    DeepLink.instance.init();
    _initCountdown();
    _listen();
    checkRealTimePermission();
    _listenChat();
    _ksbCommonCallback();
    _callBackOtp();

    _getModuleConfig();
    _initGlobalCallbackFor2345();
  }

  _getModuleConfig() async {
    try {
      _moduleConfig = await getModuleConfig();
    } catch (e) {
      logger.e(e);
    }
  }

  BuildContext _getContext() {
    return navigator.currentState!.context;
  }

  _ksbCommonCallback() {
    UmeeCommonFactory.instance.getChooseAccountWidget =
        ([GetChooseAccountModel? model]) {
      return ChooseAccountCard(
        accountNumberSelected: model?.accountNumberSelected,
        onSelectAccount: model?.onSelectAccount,
        selectMin: model?.selectMin,
        title: model?.title,
        currentNoSelectedAccount: model?.currentNoSelectedAccount,
        onTap: model?.onTap,
        firstInit: model?.firstInit,
        backgroundColor: model?.backgroundColor,
        dividerColor: model?.dividerColor,
        selectedDividerColor: model?.selectedDividerColor,
        textColor: model?.textColor,
        icon: model?.icon,
        onlyTTGT: model?.onlyTTGT,
        margin: model?.margin,
        hasNext: model?.hasNext,
      );
    };
    UmeeCommonFactory.instance.getReferralWidget = (GetReferralModel model) {
      return ReferralWidget(
        textEditingController: model.textEditingController,
        hintText: model.hintText,
        focusNode: model.focusNode,
        labelStyle: model.labelStyle,
        labelText: model.labelText,
        messageTooltip: model.messageTooltip,
        onBlur: model.onBlur,
        onChange: model.onChange,
        onParseQr: model.onParseQr,
        tooltipKey: model.tooltipKey,
        unFocusColor: model.unFocusColor,
      );
    };

    UmeeCommonFactory.instance.getReferralConfirmWidget = ({String? message}) {
      return ReferralCodeConfirmSheet(message: message);
    };
  }

  _listenChat() async {
    final userId = await _prefs.userId;
    _chatVisitorBloc.getVisitorRooms(userId);
    _subs.add(
      _chatVisitorBloc.onNewLiveMessages.listen(
        (newMessages) {
          if ($ksChat.ChatBaseBloc.inChat != true) {
            for (var element in newMessages) {
              $ksChat.PushUtils.sendPushChat(
                  message: element.type == MessageType.liveChatVideoCall ||
                          element.type == MessageType.videoCall
                      ? "Đang gọi..."
                      : element.payload,
                  roomId: element.roomId,
                  messageId: element.id,
                  roomType: element.roomType,
                  title: element.owner);
            }
          }
        },
      ),
    );
  }

  _callBackOtp() {
    GlobalCallback.instance.openOtp = ({OtpHandler? blocHandler}) {
      return OtpUtils.goInputSoftOtp(_getContext());
    };
  }

  _listen() {
    GlobalCallback.instance.onChatOpen = (ChatModel data) async {
      if (data.openLiveChat == true || !_chatBloc.hasRooms()) {
        final departments = _chatVisitorBloc.livechatDepartments.valueOrNull ?? [];
        if (data.department?.isNotEmpty == true || departments.length == 1) {
          _openSupportChat(data.department ?? departments[0]?.sId,
              roomId: data.roomId,
              roomName: data.roomName,
              phone: data.phone,
              email: data.email,
              stage: data.stage);
        } else {
          go(_getContext(), const DepartmentsChoosePage());
        }
      } else {
        goPresent(_getContext(), QuickMessagePage());
      }
    };
    GlobalCallback.instance.onOpenScanQR = ({
      VoidCallback? onPressHint,
      bool? myCode,
    }) async {
      final result = await go(
        _getContext(),
        QrScanner(fromMiniApp: true, typecall: 0, myCode: myCode),
      );
      return (result is String) ? result : "";
    };

    GlobalCallback.instance.onGoSupportVietQrBanks = () {
      goPresent(_getContext(), const RequestMoneyListSupportBankVietQR());
    };

    GlobalCallback.instance.onGetProfile = () async {
      final profileInfo = await _prefs.getProfileInfo();

      return BaseProfile(
        // userId: profileInfo?.userId,
        userName: profileInfo?.fullName,
        phoneNumber: profileInfo?.phoneNumber,
        email: profileInfo?.email,
        idCardType: profileInfo?.idCardType,
        avatarUrl: profileInfo?.avatarUrl,
        // faceIdUrl: profileInfo?.faceIdUrl,
        // idCardFrontSideUrl: profileInfo?.idCardFrontSideUrl,
        // idCardBackSideUrl: profileInfo?.idCardBackSideUrl,
        // verifiedEmail: profileInfo?.verifiedEmail,
        // verified: profileInfo?.verified,
        // enabled: profileInfo?.enabled,
        // branchCode: profileInfo?.branchCode,
        fullAddress: profileInfo?.fullAddress,
        // firstSignUrl: profileInfo?.firstSignUrl,
        // secondSignUrl: profileInfo?.secondSignUrl,
        nationCode: profileInfo?.nationCode,
        provinceCode: profileInfo?.provinceCode,
        districtCode: profileInfo?.districtCode,
        // communeCode: profileInfo?.communeCode,
        // street: profileInfo?.street,
        // idCardNo: profileInfo?.idCardNo,
        // idCardIssuedDate: profileInfo?.idCardIssuedDate,
        // idCardExpiredDate: profileInfo?.idCardExpiredDate,
        // idCardIssuedBy: profileInfo?.idCardIssuedBy,
        fullName: profileInfo?.fullName,
        gender: profileInfo?.sex,
        dateOfBirth: profileInfo?.birthday.formatDMY,
        // permanentAddress: profileInfo?.permanentAddress,
        // bankAccounts: bankAccounts,
      );
    };
    GlobalCallback.instance.refreshToken = () => _loginBloc.refreshToken();


    GlobalCallback.instance.setIsClosedNotiBlockOverDraft = (bool? value) {
      _prefs.setIsClosedNotiBlockOverDraft(value ?? false);
    };

    GlobalCallback.instance.isClosedNotiBlockOverDraft = () {
      return _prefs.isClosedNotiBlockOverDraft();
    };
  }

  _openSupportChat(String? department,
      {String? roomId,
      String? roomName,
      String? email,
      String? phone,
      String? stage}) async {
    final profile = await _prefs.getProfileInfo();
    final userId = await _prefs.userId;
    DeviceInfo? deviceInfo = await _session.getDeviceInfo();
    final visitor = $ksChat.VisitorMiniApp(
        department: department ?? '',
        email: profile?.email ?? '',
        fullName: profile?.fullName ?? '',
        userId: userId ?? deviceInfo?.deviceId ?? '',
        phone: profile?.phoneNumber ?? '',
        roomId: roomId,
        stage: stage ?? StageCallLog.OTHER);
    go(
      navigator.currentState!.context,
      ChatVisitorPage(
        visitor: visitor,
        title: roomName ?? "KienlongBank",
        icon: ImageAssets.ic_logo,
      ),
    );
  }

  @override
  dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _subs.dispose();
    _settingsChange.close();
    DeepLink.instance.dispose();
  }

  _initSettings() async {
    _prefs = Injection.preferences;
    _env.changeLanguageSink.add(_prefs.languageCode);
    _env.changeThemeLightSink.add(_prefs.light);
    _env.changeThemesSink.add(_prefs.themes);
    _env.useSystemFontSizeSink.add(_prefs.useSystemFontSize);
    _env.changePartnerSink.add(_prefs.partner);
    _subs.add(Rx.combineLatest5<bool, String, int, bool, bool, bool>(
        _env.changeThemeLight,
        _env.changeLanguage,
        _env.changeThemes,
        _env.useSystemFontSizeStream,
        _env.changePartnerStream,
        (_, __, ___, ____, _____) => true).listen((value) async {
      if (!_settingsChange.isClosed) {
        _prefs.setLanguage(_env.language);
        _prefs.setLight(_env.isThemeLight);
        _prefs.setThemes(_env.themes);
        _prefs.setUseSystemFontSize(_env.useSystemFontSize);
        _prefs.setPartner(_env.partner);
        if (_settingsChange.valueOrNull != null) {
          S.load(Locale(_env.language));
        }
        _settingsChange.add(value);
      }
    }));
  }

  void _initCountdown() {
    defaultCountDown = sleepAfterSeconds;
    startCountDown(sleepAfterSeconds);
    _subs.add(countDownStream.listen((event) {
      _reconnectSocket();
      final isSleep = _session.isSleep.valueOrNull ?? false;
      if (event == 10 && !isSleep) {
        //_showDialogEndSession();
      }
    }));
    _subs.add(_session.isSleep.listen((value) {
      if (value != true) {
        startCountDown(sleepAfterSeconds);
      } else {
        pauseCountdown();
      }
    }));
  }

  @override
  Future onCountdownEnd() {
    logger.t('onCountdownEnd');
    final isSleep = _session.isSleep.valueOrNull ?? false;
    if (!isSleep && mounted) {
      goToLogin(navigator.currentState!.overlay?.context);
      return Future.value('end');
    }
    return Future.value('done');
  }

  _showDialogLogout() {
    return DialogUtil.alert(
        navigator.currentState!.overlay!.context,
        S
            .of(navigator.currentState!.overlay!.context)
            .session_login_expired_msg,
        title: S.of(navigator.currentState!.overlay!.context).common_title_app,
        onSubmit: () {
      goToLogin(navigator.currentState!.overlay!.context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: _settingsChange,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox();
        }
        return ScreenUtilInit(
          useInheritedMediaQuery: true,
          designSize: const Size(375, 812),
          builder: (_, __) => DynamicTheme(
            context,
            isThemeLight: _env.isThemeLight,
            child: _app(context),
          ),
        );
      },
    );
  }

  Widget _app(BuildContext context) {
    return Listener(
      onPointerDown: (e) {
        if (_session.isSleep.valueOrNull != true && remainTime > 0) {
          _session.isSleep.add(false);
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);

          if (!currentFocus.hasPrimaryFocus &&
              currentFocus.focusedChild != null) {
            FocusManager.instance.primaryFocus?.unfocus();
          }
        },
        child: LoadingOverlayWidget(
          key: LoadingOverlayWidget.loadingOverlayKey,
          child: MaterialApp(
            builder: (context, child) {
              final currentScale = MediaQuery.of(context).textScaleFactor;
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: TextScaler.linear(_env.useSystemFontSize == true
                      ? currentScale.clamp(0, 1.3)
                      : currentScale.clamp(0, 1.0)),
                ),
                child: child ?? Container(),
              );
            },
            navigatorObservers: [routeObserver],
            debugShowCheckedModeBanner: false,
            title: DialogUtil.defaultTitle,
            supportedLocales: S.delegate.supportedLocales,
            locale: const Locale('vi'),
            theme: getTheme(context, true),
            // theme: getTheme(context, _env.isThemeLight),
            themeMode: ThemeMode.light,
            // home: HomeTabPage(),
            home: SplashPage(isAutoLogin: isAutologin),
            navigatorKey: navigator,
            localizationsDelegates: const [
              KlbLocalizationDelegate(),
              MyShopLocalizationDelegate(),
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              // if (Platform.isIOS) FallbackCupertinoLocalisationsDelegate()
            ],
            onGenerateRoute: RouteGenerate.generateRoute,
          ),
        ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      onAppResume();
    } else {
      _chatBloc.repositoryChat.socket.reset();
    }
  }

  _reconnectSocket() async {
    final isSocketConnect =
        _chatBloc.chatSession.authStatus == $ksChat.ChatLoginStatus.LOGGED;

    if (isThrottling && !isSocketConnect) {
      return;
    } else {
      isThrottling = true;
      final connectionStatus = await _chatBloc.getNetworkStatus();
      final authInfo = await _chatBloc.getAuthInfo();
      final currentState = WidgetsBinding.instance.lifecycleState;

      ///
      if (!isSocketConnect &&
          connectionStatus != ConnectivityResult.none &&
          currentState == AppLifecycleState.resumed &&
          authInfo != null) {
        try {
          final value =
              await _chatBloc.login($ksChat.ChatBaseBloc.OAUTH_SERVICE_NAME);
          if (value != null) {
            _successConnect();
          } else {
            _delayReconnect();
          }
        } catch (e) {
          logger.e(e);
          _delayReconnect();
        }
        final userId = await _prefs.userId;
        _chatVisitorBloc.getVisitorRooms(userId);
      } else {
        _successConnect();
      }
    }
  }

  _successConnect() async {
    Future.delayed(const Duration(seconds: 2)).then((value) {
      _chatBloc.setDropConnect.add(false);
      isThrottling = false;
      numberTryConnect = 0;
    });
  }

  _delayReconnect() async {
    _chatBloc.setDropConnect.add(true);
    // Mỗi lần thử connect lại không thành công thì lại chờ thêm 5s. 5, 10, 15, 20, .... , 120
    final timeDelay = numberTryConnect == 0 ? 1 : numberTryConnect * 5;
    await Future.delayed(
      Duration(seconds: timeDelay),
      () {
        isThrottling = false;
        // numberTryConnect giới hạn tại 24 vì 5*24 = 120s = 2p
        numberTryConnect < 24 ? numberTryConnect++ : numberTryConnect = 24;
      },
    );
  }
}
