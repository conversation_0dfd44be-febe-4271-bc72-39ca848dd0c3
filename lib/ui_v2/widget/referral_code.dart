import 'package:common/model/view_data_model.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/referral/referral_model.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';

import '../../di/inject.dart';
import '../../generated/l10n.dart';
import '../../utils/utils.dart';
import 'constant.dart';
import 'loading/loading.dart';
import 'referral_code_confirm_sheet.dart';
import 'referral_widget.dart';

// class ReferralCodeWidget extends StatefulWidget {
//   const ReferralCodeWidget({
//     super.key,
//     required this.referralBloc,
//     required this.referralController,
//     this.titleReferral,
//     this.tooltipKey,
//     this.messageToolTip,
//     this.referralFocusNode,
//   });
//
//   final ReferralBloc referralBloc;
//   final String? titleReferral;
//
//   final TextEditingController referralController;
//   final FocusNode? referralFocusNode;
//   final GlobalKey<State<Tooltip>>? tooltipKey;
//   final String? messageToolTip;
//
//   @override
//   State<ReferralCodeWidget> createState() => _ReferralCodeWidgetState();
// }
//
// class _ReferralCodeWidgetState extends State<ReferralCodeWidget> {
//   bool _enableCallApi = true;
//   String? _referralCode;
//
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: kPaddingStandard,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             widget.titleReferral ?? S.of(context).invitation_code_optional,
//             style: StyleApp.titleStyle(context),
//           ),
//           SizedBox(height: kSmallPadding),
//           ReferralWidget(
//             textEditingController: widget.referralController,
//             focusNode: widget.referralFocusNode,
//             tooltipKey: widget.tooltipKey,
//             labelStyle: StyleApp.subtitle1(context)
//                 ?.copyWith(color: const Color.fromRGBO(51, 51, 51, 0.4)),
//             hintText: S.of(context).enter_referal_code,
//             labelText: S.of(context).referer_code,
//             messageTooltip:
//                 widget.messageToolTip ?? S.of(context).savings_tooltip_referral,
//             onBlur: (code) {
//               if (code.isEmpty) {
//                 _referralCode = code;
//                 widget.referralBloc.setEmptyModel();
//               } else {
//                 handleReferralCode(code);
//               }
//             },
//             onParseQr: (code) {
//               if (code?.isNotEmpty == true) {
//                 handleReferralCode(code!);
//                 widget.referralController.text = code;
//               }
//             },
//           ),
//           SizedBox(height: kSmallPadding),
//           _buildReferralInfo()
//         ],
//       ),
//     );
//   }
//
//   Widget _buildReferralInfo() {
//     return StreamBuilder<VDMReferral?>(
//       stream: widget.referralBloc.referralStream,
//       builder: (context, snapshot) {
//         final data = snapshot.data;
//         if (data?.data != null) {
//           return Container(
//             width: MediaQuery.of(context).size.width,
//             margin: const EdgeInsets.only(top: 4),
//             padding: EdgeInsets.symmetric(
//               vertical: kSmallPadding,
//               horizontal: kInnerPadding,
//             ),
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(4),
//               color: const Color.fromRGBO(51, 51, 51, 0.05),
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   S.of(context).referer,
//                   style: StyleApp.caption(context)?.copyWith(
//                     color: const Color.fromRGBO(51, 51, 51, 0.4),
//                   ),
//                 ),
//                 SizedBox(height: kTinyPadding),
//                 Text(
//                   data?.data?.fulName ?? '',
//                   style: StyleApp.subtitle1(context)?.copyWith(
//                     color: DynamicTheme.of(context)?.customColor.titleColor2,
//                   ),
//                 )
//               ],
//             ),
//           );
//         } else if (data?.status == ViewStatus.LOADING) {
//           return SizedBox(height: 56, child: ring);
//         }
//         return const SizedBox();
//       },
//     );
//   }
//
//   void handleReferralCode(String code) async {
//     if (code.trim().isNotEmpty && _enableCallApi && _referralCode != code) {
//       _enableCallApi = false;
//       var referral =
//           await widget.referralBloc.getReferralNiceAccount(referralCode: code);
//       if (referral != null) {
//         // widget.onSetReferralCode(code, referral.fulName);
//       }
//     }
//     _referralCode = widget.referralController.text;
//     _enableCallApi = true;
//   }
// }

mixin ReferralCode<T extends StatefulWidget> on State<T> {
  ReferralBloc get referralBloc => Injection.injector.get<ReferralBloc>();
  final referralFocusNode = FocusNode();
  final toolTipKey = GlobalKey<State<Tooltip>>();
  final referralController = TextEditingController();
  ReferralModel? _referralModel;
  String? _referralCode;
  bool _enableCallApi = true;

  //init
  String? referralTitle;
  String? messageToolTip;
  String? labelText;
  String? hintText;
  EdgeInsets? padding;
  bool? hasTitle;
  String? referralMessage;
  bool? showConfirmBottomSheet = true;
  CheckReferralCodeRequestFeatureEnum? feature;

  @override
  void initState() {
    super.initState();
    referralBloc.streamSubs
      ..add(referralBloc.errorStream.listen((err) {
        if (!err.isNullOrEmpty) {
          DialogUtil.alert(
            context,
            err,
            onSubmit: () =>
                FocusScope.of(context).requestFocus(referralFocusNode),
          );
        }
      }))
      ..add(referralBloc.referralStream.listen((event) {
        referralBloc.disableBtnSink.add(event.status == ViewStatus.LOADING);
      }));
  }

  ///init properties
  initReferral({
    String? referralTitle,
    String? messageToolTip,
    String? labelText,
    String? hintText,
    EdgeInsets? padding,
    bool? hasTitle,
    String? referralMessage,
    bool? showConfirmBottomSheet,
    CheckReferralCodeRequestFeatureEnum? feature,
  }) {
    this.referralTitle = referralTitle;
    this.messageToolTip = messageToolTip;
    this.labelText = labelText;
    this.hintText = hintText;
    this.padding = padding;
    this.hasTitle = hasTitle ?? true;
    this.referralMessage = referralMessage;
    this.showConfirmBottomSheet = showConfirmBottomSheet ?? true;
    this.feature = feature;
  }

  @override
  void dispose() {
    super.dispose();
    referralFocusNode.dispose();
    referralController.dispose();
    referralBloc.dispose();
  }

  bool _nextConfirm = false;

  bool get nextConfirm => _nextConfirm;

  void set nextConfirm(bool value) {
    _nextConfirm = value;
  }

  Future<void> onNext() async {
    if (referralController.text.trim().isEmpty) {
      if (showConfirmBottomSheet == true) {
        final hasRef = await goPresent(
          context,
          ReferralCodeConfirmSheet(message: referralMessage),
        );
        if (hasRef == true) {
          FocusScope.of(context).requestFocus(referralFocusNode);
          Future.delayed(const Duration(milliseconds: 1000), () {
            final dynamic tooltip = toolTipKey.currentState;
            tooltip?.ensureTooltipVisible();
          });
        } else {
          goToConfirm();
        }
      } else {
        goToConfirm();
      }
    } else {
      if (_referralModel != null) {
        goToConfirm(referralModel: _referralModel);
      } else {
        if (_referralCode != referralController.text) {
          _enableCallApi = false;
          referralFocusNode.unfocus();
          var result = await referralBloc.getReferral(
            referralCode: referralController.text,
            feature: feature,
          );
          if (result != null) {
            _referralModel = result;
            _referralCode = referralController.text;
            if (_nextConfirm) {
              goToConfirm(referralModel: _referralModel);
            }
          }
        } else {
          goToConfirm(referralModel: _referralModel);
        }
      }
    }
    FocusScope.of(context).unfocus();
    _enableCallApi = true;
  }

  void handleReferralCode(String code) async {
    if (code.trim().isNotEmpty && _enableCallApi && _referralCode != code) {
      _enableCallApi = false;
      _referralModel = await referralBloc.getReferral(
        referralCode: code,
        feature: feature,
      );
      if (_referralModel != null) {
        _referralCode = referralController.text;
      }
    }
    _enableCallApi = true;
  }

  goToConfirm({ReferralModel? referralModel});

  Widget referralWidget({bool? isSuffix}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (hasTitle == true) ...[
          SizedBox(height: kOuterPadding),
          Padding(
            padding: padding ?? kHorizontalPaddingStandard,
            child: Text(
              referralTitle ?? S.of(context).invitation_code_optional,
              style: StyleApp.titleStyle(context),
            ),
          ),
          SizedBox(height: kSmallPadding)
        ],
        Padding(
          padding: padding ?? kHorizontalPaddingStandard,
          child: ReferralWidget(
            isSuffix: isSuffix,
            textEditingController: referralController,
            focusNode: referralFocusNode,
            tooltipKey: toolTipKey,
            labelStyle: StyleApp.subtitle1(context)
                ?.copyWith(color: const Color.fromRGBO(51, 51, 51, 0.4)),
            hintText: hintText ?? S.of(context).enter_referal_code,
            labelText: labelText ?? S.of(context).referer_code,
            messageTooltip:
                messageToolTip ?? S.of(context).guide_referral_code_describe,
            onBlur: (code) {
              final dynamic tooltip = toolTipKey.currentState;
              tooltip?.deactivate();
              if (code.isEmpty) {
                _referralCode = code;
                referralBloc.setEmptyModel();
              } else {
                handleReferralCode(code);
              }
            },
            onParseQr: (code) {
              if (code?.isNotEmpty == true) {
                handleReferralCode(code!);
                referralController.text = code;
              }
            },
          ),
        ),
        SizedBox(height: kSmallPadding),
        StreamBuilder<VDMReferral>(
          stream: referralBloc.referralStream,
          builder: (context, snapshot) {
            final data = snapshot.data;
            if (data?.data != null) {
              return referralInfo(data!.data!);
            } else if (data?.status == ViewStatus.LOADING) {
              return ring;
            }
            return const SizedBox();
          },
        ),
        SizedBox(height: kOuterPadding),
      ],
    );
  }

  Widget referralInfo(ReferralModel data) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: padding ?? kHorizontalPaddingStandard,
      padding: EdgeInsets.symmetric(
        vertical: kSmallPadding,
        horizontal: kInnerPadding,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: const Color.fromRGBO(51, 51, 51, 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).referer,
            style: StyleApp.caption(context)?.copyWith(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
            ),
          ),
          SizedBox(height: kTinyPadding),
          Text(
            data.fulName ?? '',
            style: StyleApp.subtitle1(context)?.copyWith(
              color: DynamicTheme.of(context)?.customColor.titleColor2,
            ),
          )
        ],
      ),
    );
  }
}
