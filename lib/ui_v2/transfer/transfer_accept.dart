import 'package:common/model/loading_event.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/utils/global.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:common/widgets/common_dropdown.dart';
import 'package:common/widgets/common_textfield.dart';
import 'package:common/widgets/money_widget.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:keyboard_attachable/keyboard_attachable.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/transfer/transaction_id_model.dart';
import 'package:ksb_common/shared/assets.dart' as klb_common;
import 'package:ksb_common/shared/route_path.dart';
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/bottom_sheet_model.dart';
import 'package:mobile_banking/navigator.dart';
import 'package:mobile_banking/sunshine_app.dart' as sunshine_app;
import 'package:mobile_banking/ui_v2/payment/payment_tool.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import 'package:mobile_banking/ui_v2/qr_scanner/qr_scanner.dart';
import 'package:mobile_banking/ui_v2/transfer/base_list_choose_item.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_accept_bank_widget.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_accept_contact_widget.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_accept_setup_widget.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_confirm.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_contact.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_progress_page.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_schedule_success.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_success.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_template_page.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/common_textfield_money.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/item_option_bank_widget.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/transfer_big_money_note.dart';
import 'package:mobile_banking/ui_v2/widget/constant.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/ui_v2/widget/view/keyboard_header_widget.dart';
import 'package:mobile_banking/utils/auth_fatsh.dart';
import 'package:mobile_banking/utils/biometrics.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:mobile_banking/utils/otp_utils.dart';
import 'package:mobile_banking/utils/stringemoij.dart';

Future goToTransferAcceptV2(
  BuildContext context,
  TransferModel model, {
  VoidCallback? onPressDone,
  VoidCallback? onPressNew,
  bool? internalOnly,
}) async {
  final page = _TransferAccept._(
    model: model,
    onPressDone: onPressDone,
    onPressNew: onPressNew,
    internalOnly: internalOnly ?? false,
  );
  return goWithRoute(
    context,
    page,
    RoutePaths.transferHome,
  );
}

//**********
//9704 0601 2983 7294 CARD
class _TransferAccept extends StatefulWidget {
  final TransferModel? model;
  final VoidCallback? onPressDone;
  final VoidCallback? onPressNew;
  final bool internalOnly;

  //#region constructor

  const _TransferAccept._({
    this.model,
    this.onPressDone,
    this.onPressNew,
    this.internalOnly = false,
  });

  @override
  _ScreenState createState() => _ScreenState();
//endregion
}

class _ScreenState extends FlowTransactionState<_TransferAccept>
    with
        RouteAware,
        AuthTransactionMixin<_TransferAccept>,
        AuthTransactionConfirmMixin<_TransferAccept> {
  //#region init/dispose
  final _keyboardBloc = Injection.injector.get<KeyboardBloc>();
  final _showSuggestMoney = true;
  final keyMoney = GlobalKey<CommonTextFieldMoneyState>();
  final keyTargetAccount = GlobalKey<TransferAcceptContactWidgetState>();
  final _bloc = Injection.injector.get<TransferBloc>();
  final _blocAuth = Injection.injector.get<AuthenTransactionBloc>();
  final _contactBloc = Injection.injector.get<ContactListBloc>();
  bool? isCheckBackIcon;
  late Biometrics biometrics;
  final FocusNode _focusNodeAmount = FocusNode();
  final TextEditingController _amountTextController = TextEditingController();
  final TextEditingController _noteTextController = TextEditingController();
  TextEditingController _accountTextController = TextEditingController();
  final TextEditingController _accountAliasTextController =
      TextEditingController();
  final TextEditingController _accountNameTextController =
      TextEditingController();
  bool _pendingCheckName = false;
  bool _changeTypeInput = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    sunshine_app.routeObserver
        .subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void didPop() {
    super.didPop();
    _pendingCheckName = true;
  }

  @override
  void didPopNext() {
    _pendingCheckName = false;
  }

  @override
  void didPushNext() {
    _pendingCheckName = true;
  }

  @override
  String get firstRoute => RoutePaths.transferHome;

  @override
  void initState() {
    super.initState();
    biometrics = Biometrics();

    _init();

    _bloc.streamSubs.add(
      _bloc.statusStream.listen((event) {
        if (event != null) {
          listenError(event.convertModelToCommon());
        }
      }),
    );

    _bloc.streamSubs.add(_bloc.statusStream.listen((event) {
      if (event != null && event.statusCode == 4008202 && mounted) {
        DialogUtil.confirm(context, Text(event.message ?? ""),
            submitText: S.of(context).accept, onSubmit: () {
          _bloc.set247(false);
        });
      }
    }));

    _keyboardBloc.streamSubs
        .add(_keyboardBloc.moneyChangeStream.listen((event) {
      if (event != null && event.isNotEmpty) {
        keyMoney.currentState?.setMoneyText(event);
        _bloc.setAmount(Global.convertTextToMoney(event));
      }
    }));
  }

  _init() async {
    isCheckBackIcon = true;
    _contactBloc.setContactPopupData(widget.model?.isAccount == true
        ? TransferType.tai_khoan
        : TransferType.the);
    await _bloc.mergeModel(widget.model);
    _accountAliasTextController.text = _bloc.model?.targetAccountDisplayName ??
        _bloc.model?.targetAccountName ??
        '';

    _noteTextController.text = _bloc.model?.note ?? '';
    if (widget.model?.amount != null) {
      setMoneyText(widget.model?.amount?.currencyFormat);
      _bloc.setAmount(widget.model?.amount);
    }
    _bloc.streamSubs.add(_bloc.progressVisible.listen((event) {
      if (!mounted) return;
      setState(() {
        isCheckBackIcon = !event;
      });
    }));
  }

  setMoneyText(String? value) {
    _amountTextController
      ..text = value ?? ''
      ..selection =
          TextSelection.collapsed(offset: _amountTextController.text.length);
  }

  @override
  void dispose() {
    super.dispose();
    _keyboardBloc.dispose();
    _bloc.dispose();
    _blocAuth.dispose();
    _accountAliasTextController.dispose();
    _amountTextController.dispose();
    _noteTextController.dispose();
    //_accountTextController?.dispose();
    _accountNameTextController.dispose();
    sunshine_app.routeObserver.unsubscribe(this);
  }

  //#endregion

  //#region UI
  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      image: klb_common.ImageAssets.img_appbar,
      appBar: getAppBarDark(
        context,
        title: S.of(context).transfer,
        actions: [
          IconButton(
            onPressed: () {
              popUpPage(
                QrScanner(
                  typecall: 0,
                  onDetectedTransfer: (value) {
                    _onDetected(value);
                  },
                ),
                context,
              );
            },
            icon: ImageAssets.svgAssets(
              ImagesResource.ic_my_qr,
              color: Theme.of(context).cardColor,
            ),
          ),
        ],
      ),
      child: FooterLayout(
        footer: _showSuggestMoney
            ? KeyboardHeaderWidget(
                bloc: _keyboardBloc,
                onTapIcon: () {
                  _changeTypeInput = true;
                  var contactWidgetState = keyTargetAccount.currentState;
                  if (contactWidgetState?.hasFocus() == true) {
                    contactWidgetState?.switchInputType();
                    _keyboardBloc.inputTypeSink
                        .add(contactWidgetState?.inputType);
                  } else {
                    FocusScope.of(context).requestFocus(FocusNode());
                  }
                },
              )
            : Container(),
        child: _getBody(),
      ),
      //stream: _bloc.loadingScreenStream,
    );
  }

  Widget _rowAccounts(TransferModel model) {
    return Container(
      color: Theme.of(context).cardColor,
      padding: const EdgeInsets.only(top: 10, bottom: 12),
      child: ChooseAccountCard(
        accountNumberSelected: model.sourceAccountNumber,
        onSelectAccount: _bloc.setSourceAccount,
        onTap: () => _pendingCheckName = true,
        currentNoSelectedAccount: () {},
        firstInit: model.sourceCustomerName == null ||
            model.sourceCustomerName?.isEmpty == true,
      ),
    );
  }

  Widget _rowBanks(TransferModel data) {
    if (widget.internalOnly == true) return Container();

    return TransferAcceptBankWidget(
      currentSelect: _bloc.getTargetBank(),
      onChooseBank: (bank) {
        _chooseBank(bank, data);
      },
      currentNoChooseBank: () {
        _pendingCheckName = false;
      },
      onTap: () {
        _pendingCheckName = true;
      },
      isDisable: data.isDisableField(),
      backgroundColor: data.isDisableField()
          ? DynamicTheme.of(context)?.customColor.bgTextFieldGrey
          : null,
    );
  }

  _showWaringTransferNormal() {
    DialogUtil.alert(
      context,
      Text(S.of(context).not_support_247),
    );
  }

  Widget _rowTargetAccount(TransferModel? data) {
    if (data?.targetAccountNumber != _accountTextController.text) {
      _accountTextController.text = data?.targetAccountNumber ?? '';
    }
    return StreamBuilder<LoadingWidgetModel>(
        stream: _contactBloc.allStream,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            final List<ContactModel> contactList = snapshot.data?.data ?? [];
            return Autocomplete<ContactModel>(
              optionsBuilder: (textEditingValue) {
                if (textEditingValue.text == '') {
                  return const Iterable<ContactModel>.empty();
                }
                final result = contactList.where((option) {
                  final value = textEditingValue.text.toLowerCase();
                  final hasAccount = option.accountNo?.contains(value) == true;
                  final hasName =
                      option.accountName?.toLowerCase().contains(value) == true;
                  final hasAliasName = option.aliasName?.isNotEmpty == true &&
                      option.aliasName?.toLowerCase().contains(value) == true;
                  return hasAccount || hasName || hasAliasName;
                }).toList();
                return result;
              },
              onSelected: (option) {
                _onSelectedContact(option);
              },
              displayStringForOption: (option) => option.accountNo ?? '',
              optionsViewBuilder: (context, onSelected, value) {
                return _AutocompleteOptions<ContactModel>(
                    displayForOption: (option) {
                      return ItemOptionBankWidget(
                        contactModel: option,
                        onSelected: onSelected,
                        query: _accountTextController.text,
                      );
                    },
                    options: value,
                    maxOptionsHeight: MediaQuery.of(context).size.height / 2);
              },
              initialValue: _accountTextController.value,
              fieldViewBuilder: (
                context,
                textEditingController,
                focusNode,
                onFieldSubmitted,
              ) {
                _accountTextController = textEditingController;
                return TransferAcceptContactWidget(
                  key: keyTargetAccount,
                  focusNode: focusNode,
                  controller: textEditingController,
                  header: data?.isAccount == true
                      ? S.of(context).account_number
                      : S.of(context).card_number,
                  value: data?.targetAccountNumber,
                  name: data?.targetAccountName,
                  is247: data?.is247,
                  placeHolder: S.of(context).beneficiary,
                  displayName: data?.getTargetAccountDisplayName(),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(100),
                    // FilteringTextInputFormatter.allow(RegExp("[0-9A-Za-z]")),
                    // FilteringTextInputFormatter.deny(RegExp("[ ]")),
                  ],
                  onInputValueSubmit: (value) {},
                  onInputValueChange: (value) {
                    _pendingCheckName = false;
                    _bloc.targetAccountNumberChange(value);
                    _changeTypeInput = false;
                  },
                  onInputTap: () {
                    _keyboardBloc.setMoney(null);
                    _changeTypeInput = false;
                  },
                  focusChange: (value) {
                    if (!value && !_pendingCheckName && !_changeTypeInput) {
                      _bloc.setTargetAccount(
                          accountNumber:
                              _normalizeString(_accountTextController.text));
                    } else {
                      _pendingCheckName = false;
                    }
                    _changeTypeInput = false;
                    if (value == true) {
                      _keyboardBloc.inputTypeSink
                          .add(keyTargetAccount.currentState?.inputType);
                    } else {
                      _keyboardBloc.inputTypeSink.add(null);
                    }
                  },
                  onPressedContact: data?.isDisableField() ?? false
                      ? null
                      : () async {
                          _pendingCheckName = true;
                          final result = await goPresent(
                            context,
                            TransferContact(
                              contactType: data?.isAccount == true
                                  ? TransferType.tai_khoan
                                  : TransferType.the,
                            ),
                          );
                          if (result != null && result is ContactModel) {
                            _onSelectedContact(result);
                          }
                          _pendingCheckName = false;
                        },
                  onNamePressed: _editTargetName,
                  backgroundColor: data?.isDisableField() ?? false
                      ? DynamicTheme.of(context)?.customColor.bgTextFieldGrey
                      : null,
                  isEnable: !(data?.isDisableField() ?? false),
                );
              },
            );
          } else {
            return const SizedBox();
          }
        });
  }

  String _normalizeString(String input) {
    return TiengViet.parse(input).replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '');
  }

  _onSelectedContact(ContactModel result) async {
    await _bloc.setTargetContact(result);
    _accountAliasTextController.text = result.aliasName ?? '';
    _accountNameTextController.text = result.accountName ?? '';
    Future.delayed(const Duration(milliseconds: 300), () {
      _focusNodeAmount.requestFocus();
    });
  }

  Widget _rowTargetAccountName(TransferModel? data) {
    if (data?.targetAccountName != _accountNameTextController.text) {
      _accountNameTextController.text = data?.targetAccountName ?? '';
    }
    if (data?.is247 != true || data?.targetAccountName?.isNotEmpty == true) {
      return CommonTextField(
        padding: const EdgeInsets.only(top: 8),
        labelText: S.of(context).beneficiary,
        hintStyle: StyleApp.bodyStyle(context),
        backgroundColor: data?.is247 == true
            ? DynamicTheme.of(context)?.customColor.bgTextFieldGrey
            : null,
        readOnly: data?.is247 == true,
        enabled: data?.is247 != true,
        textController: _accountNameTextController,
        onChanged: (value) {
          _bloc.setTargetAccountName(value);
        },
      );
    }
    return Container();
  }

  _editTargetName() async {
    final newName = await _displayNameInputDialog(context);
    if (newName != null) {
      _bloc.setTargetAccountDisplayName(newName);
    }
  }

  Widget _rowNote(TransferModel? data) {
    if (data?.note != _noteTextController.text) {
      _noteTextController.text = data?.note ?? '';
    }
    return CommonTextField(
      textController: _noteTextController,
      labelStyle: StyleApp.bodyStyle(context),
      labelText: S.of(context).account_transact_detail_hint_text_content,
      suffixEnabled: true,
      suffix: InkWell(
        onTap: data?.note.isNullOrEmpty == false
            ? () {
                _noteTextController.text = "";
                _bloc.setNote("");
              }
            : null,
        child: data?.note.isNullOrEmpty == false
            ? Icon(
                Icons.cancel_rounded,
                color: StyleApp.captionColor(context),
              )
            : null,
      ),
      onChanged: (value) {
        _bloc.setNote(value);
      },
      onTap: () {
        _keyboardBloc.setMoney(null);
        setState(() {});
      },
      onCheckError: (value) {
        if (!validateNote(value)) {
          return S.of(context).enter_vietnamese_without_accent;
        }
      },
      maxLinesError: 2,
      backgroundColor: data?.isDisableAfScanVietQR ?? false
          ? DynamicTheme.of(context)?.customColor.bgTextFieldGrey
          : null,
      enabled: !(data?.isDisableAfScanVietQR ?? false),
    );
  }

  Widget _rowMoney(TransferModel model) {
    return CommonTextFieldMoneyWidget(
      key: keyMoney,
      labelText: S.of(context).saving_add_money_label_amount,
      labelStyle: StyleApp.bodyStyle(context),
      textInputType: TextInputType.number,
      textController: _amountTextController,
      valueText: model.amount != null && model.amount! > 0
          ? model.amount?.currencyFormat
          : '',
      maxLength: 18,
      focusNode: _focusNodeAmount,
      suffixEnabled: true,
      suffix: InkWell(
        onTap: model.amount != null && model.amount! > 0
            ? () {
                _amountTextController.clear();
                _bloc.setAmount(0);
              }
            : null,
        child: model.amount != null && model.amount! > 0
            ? Icon(
                Icons.cancel_rounded,
                color: StyleApp.captionColor(context),
              )
            : null,
      ),
      onInputTap: (value) {
        _keyboardBloc.setMoney(_bloc.model?.amount ?? 0);
      },
      onChanged: (value) {
        setState(() {});
        _bloc.setAmount(value);
        _keyboardBloc.setMoney(value);
      },
      onBlur: (value) {
        if (_bloc.shouldCheckBigMoney()) {
          _bloc.checkBigMoney();
        }
      },
      backgroundColor: model.isDisableAfScanVietQR ?? false
          ? DynamicTheme.of(context)?.customColor.bgTextFieldGrey
          : null,
      enabled: !(model.isDisableAfScanVietQR ?? false),
    );
  }

  Widget _rowSchedule(TransferModel? data) {
    return TransferAcceptSetupWidget(
      // checked247: data?.is247,
      onCheck247Change: (checked) {
        _bloc.set247(checked);
      },
      schedule: data?.schedule,
      onScheduleChange: (schedule) {
        _bloc.setSchedule(schedule);
      },
      checkSaveTemplate: data?.saveTemplate,
      onCheckSaveTemplateChange: (checked) {
        _bloc.setSaveTemplate(checked);
      },
    );
  }

  Widget buttonTemplate() {
    return TextButton(
      onPressed: () async {
        final result = await goPresent(
          context,
          BottomSheetWidget(
            height: MediaQuery.of(context).size.height * 0.95,
            title: S.of(context).transfer_template,
            child: TransferTemplatePage(
              transferType: _bloc.model?.isAccount,
            ),
          ),
        );
        if (result is TransferModel) {
          await _bloc.setSourceAccountTransfer(result);
          setMoneyText(_bloc.model?.amount?.currencyFormat);
        }
      },
      child: Text(S.of(context).transfer_template),
    );
  }

  _getForm(snapshot) {
    if (!snapshot.hasData) return Container();
    final data = snapshot.data;
    _accountAliasTextController.text = data.getTargetAccountDisplayName();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        _rowAccounts(data),
        Container(height: 8, color: Theme.of(context).dividerColor),
        Container(
          color: Theme.of(context).cardColor,
          margin: EdgeInsets.only(
            left: kOuterPadding,
            right: kOuterPadding,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        S.of(context).beneficiary,
                        style: StyleApp.subtitle1(
                          context,
                          true,
                        ),
                      ),
                    ),
                    buttonTemplate(),
                  ],
                ),
              ),
              _rowBanks(data),
              const SizedBox(height: 8),
              _rowTargetAccount(data),
              _rowTargetAccountName(data),
              _row247(data),
              _rowCity(data),
              Padding(
                padding: const EdgeInsets.only(top: 16, bottom: 16),
                child: Text(
                  S.of(context).transfer_information,
                  style: StyleApp.titleStyle(context),
                ),
              ),
              _rowMoney(data),
              if (_amountTextController.text.isNotEmpty &&
                  Global.convertTextToMoney(_amountTextController.text) > 0)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: kInnerPadding),
                  child: MoneyToTextWidget(
                    amount:
                        Global.convertTextToMoney(_amountTextController.text),
                  ),
                ),
              _rowFee(data),
              const SizedBox(height: 5),
              _rowNote(data),
              _rowSchedule(data),
              SizedBox(
                height: 70.h,
              )
            ],
          ),
        ),
        //  _rowBeneficiaryName(data),
        //SizedBox(height: 30)
      ],
    );
  }

  _getBody() {
    return LoadingView(
      loadingStream: _bloc.progressVisible,
      child: Container(
        color: Theme.of(context).cardColor,
        child: StreamBuilder<TransferModel>(
          stream: _bloc.modelStream,
          builder: (context, snapshot) {
            final data = snapshot.data;
            return Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      SingleChildScrollView(
                        child: _getForm(snapshot),
                      ),
                      _bigTransfer(),
                    ],
                  ),
                ),
                BottomButton(
                  isDisable: data == null ||
                      !data.canNext() ||
                      _bloc.isProgressVisible,
                  title: S.of(context).common_continue,
                  onTap: () {
                    //Hide keyboard when click continue to prevent typing
                    FocusScope.of(context).requestFocus(FocusNode());
                    _goToConfirmPage();
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _bigTransfer() {
    return StreamBuilder<BigMoneyNoteModel?>(
        stream: _bloc.bigAmountStream,
        builder: (context, snapshot) {
          if (!snapshot.hasData ||
              snapshot.data?.show == false ||
              _bloc.model?.is247 == false) {
            return const SizedBox();
          }
          return Positioned(
              bottom: 0,
              left: kOuterPadding,
              right: kOuterPadding,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF6D6E71),
                  borderRadius: BorderRadius.circular(kSmallPadding),
                ),
                child: IntrinsicHeight(
                  child: Padding(
                    padding: EdgeInsets.all(kOuterPadding),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        ImageAssets.svgAssets(ImagesResource.ic_alert),
                        SizedBox(width: kInnerPadding),
                        Expanded(
                          child: Text(
                            "Tự động tách lệnh với giao dịch giá trị lớn",
                            style: StyleApp.bodyText2(context)
                                ?.copyWith(color: Colors.white),
                          ),
                        ),
                        SizedBox(width: kInnerPadding),
                        VerticalDivider(
                          width: 1,
                          color: Colors.white,
                          indent: kTinyPadding,
                          endIndent: kTinyPadding,
                        ),
                        SizedBox(width: kInnerPadding),
                        InkWell(
                          onTap: () {
                            final data = snapshot.data;
                            if (data?.params?.isNotEmpty == true) {
                              goPresent(
                                context,
                                TransferBigMoneyNote(data: data),
                              );
                            }
                          },
                          child: Text(
                            S.of(context).detail,
                            style: StyleApp.bodyText2(context, true)
                                ?.copyWith(color: Colors.white),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ));
        });
  }

  Widget _row247(TransferModel? data) {
    if (data == null ||
        data.isTargetBankInner() == true ||
        data.isAccount == false ||
        data.targetBankIdNapas.isNullOrEmpty) {
      return Container();
    }
    return Padding(
      padding: const EdgeInsets.only(top: 10, bottom: 10),
      child: Row(
        children: [
          Expanded(
            child: Text(
              S.of(context).fast_transfer_247,
              style: StyleApp.titleStyle(context),
            ),
          ),
          Switch.adaptive(
            value: data.is247 == true,
            activeColor: const Color(0xFF34C759),
            onChanged: (value) {
              _bloc.set247(value);
            },
          )
        ],
      ),
    );
  }

  Future _displayNameInputDialog(BuildContext context) async {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 15, left: 15, right: 15),
                child: Text(
                  S.of(context).account_qr_bottomsheet_des_name,
                  style: StyleApp.titleStyle(context),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 15, left: 15, right: 15),
                child: Text(
                  S.of(context).guide_name_reminiscent,
                  style: StyleApp.descStyle(context),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          content: TextField(
            onChanged: (value) {},
            controller: _accountAliasTextController,
            decoration: InputDecoration(
                hintText: S.of(context).account_qr_bottomsheet_des_name),
            style: StyleApp.textFieldStyle(context),
            textInputAction: TextInputAction.done,
            keyboardType: TextInputType.text,
            inputFormatters: [
              FilteringTextInputFormatter.deny(RegExp(r)),
              LengthLimitingTextInputFormatter(255),
              FilteringTextInputFormatter.deny(RegExp(
                  r'(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])')),
              FilteringTextInputFormatter.deny(specialCharacters),
              FilteringTextInputFormatter.deny(RegExp(r'[/\\]')),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                S.of(context).common_update,
                style: StyleApp.buttonStyle(context,
                    color: Theme.of(context).primaryColor),
              ),
              onPressed: () {
                Navigator.pop(context, _accountAliasTextController.text);
              },
            ),
            TextButton(
              child: Text(S.of(context).common_cancel,
                  style: StyleApp.settingStyle(context,
                      color: Theme.of(context).primaryColor)),
              onPressed: () {
                Navigator.pop(context, null);
              },
            ),
          ],
        );
      },
    );
  }

  Widget _rowCity(TransferModel? data) {
    if (data == null) {
      return Container();
    }
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      transitionBuilder: (Widget widget, Animation<double> animation) {
        return SizeTransition(
          sizeFactor: animation,
          child: widget,
        );
      },
      child: data.is247 == true
          ? Container()
          : Column(
              children: [
                CommonDropdown(
                  margin: const EdgeInsets.only(top: 8),
                  onTap: _openChooseCity,
                  title: S.of(context).common_choose_city,
                  value: data.cityName,
                ),
                CommonDropdown(
                  margin: const EdgeInsets.only(top: 8),
                  onTap: _openChooseBranch,
                  title: S.of(context).branch,
                  value: data.targetBranchName,
                ),
                const SizedBox(height: 12),
              ],
            ),
    );
  }

  //#endregion UI

  //#region Follow
  _openChooseCity() async {
    popUpPage(
      BaseListChooseItem(
        streamItems: _bloc.citiesStream,
        title: S.of(context).common_choose_city,
        onTap: (item) {
          Navigator.of(context).pop();
          _bloc.setCity(item?.id, item?.title);
        },
      ),
      context,
    );
  }

  _openChooseBranch() async {
    if (_bloc.model?.cityCode != null) {
      popUpPage(
        BaseListChooseItem(
          streamItems: _bloc.branchesStream,
          title: S.of(context).bank_branch,
          onTap: (item) {
            Navigator.of(context).pop();
            _bloc.setBankBranch(item?.id, item?.title);
          },
        ),
        context,
      );
    } else {
      _bloc.addError(S.of(context).please_select_city_district);
    }
  }

  _goToConfirmPage() async {
    await _bloc.validateData();
    onGoToConfirm(
      nextToConfirmPage: nextToConfirmPage,
      nextStep: _bloc.step,
      message: _bloc.messageDialog,
    );
  }

  nextToConfirmPage() {
    if (!mounted) return;
    go(
      context,
      TransferConfirm(
        model: _bloc.model,
        bigTransaction: _bloc.bigTransaction,
        onConfirm: _goInputSoftOtp,
        loadingStream: _bloc.progressVisible,
        onChangeCategory: (category) {
          _bloc.setCategory(
            category.id,
            category.image,
            category.title,
            category.imageType,
          );
        },
      ),
    ).then((value) => setPassAuthFaceID(false));
  }

  _goInputSoftOtp() async {
    if (sunshine_app.moduleConfig == null ||
        sunshine_app.moduleConfig
                ?.shouldShowAwsLiveness(_bloc.model?.amount ?? 0) ==
            true) {
      final livenessSession = await _bloc.createTransactionLivenessSession();
      if (livenessSession == null) return;
      if (!passAuthFaceID) {
        await authByFaceIDWithAWSLiveness(
          showFaceID: _bloc.showFaceID,
          transactionNo: _bloc.transactionNumber,
          accessKeyId: livenessSession.accessKeyId,
          region: livenessSession.region,
          secretAccessKey: livenessSession.secretAccessKey,
          sessionId: livenessSession.sessionId,
          sessionToken: livenessSession.sessionToken,
          onComplete: () async {
            if (livenessSession.sessionId?.isNotEmpty == true) {
              return await _bloc.getResultLiveness(
                livenessSession.sessionId!,
                _bloc.transactionNumber!,
              );
            }
            return false;
          },
        );
      }
      if (!passAuthFaceID) return;
    } else {
      if (!passAuthFaceID) {
        await authByFaceID(
          showFaceID: _bloc.showFaceID,
          transactionNo: _bloc.transactionNumber,
        );
      }
      if (!passAuthFaceID) return;
    }

    final hasEToken = await _bloc.hasEToken();
    if (hasEToken) {
      final data = await _blocAuth.getetoken();
      final secret = OtpUtils.generateSecretAdvanceOtp(
        cifNumber: _bloc.model?.cifNumber,
        sourceAccountNumber: _bloc.model?.sourceAccountNumber,
        amount: _bloc.model?.amount?.toInt(),
        targetBankCode: _bloc.model?.is247 == true
            ? _bloc.model?.targetBankCode
            : _bloc.model?.targetBankCitad.toString(),
        targetAccountNumber: _bloc.model?.targetAccountNumber,
        transactionNumber: _bloc.model?.transactionNumber,
        secretKey: data,
      );
      if (!mounted) return;
      final value = await FastAuth()
          .checkAuth(context, _bloc.model?.amount ?? 0, data, secret: secret);

      if (value?.isNotEmpty == true) {
        _bloc.setSoftOtp(value);
        await _bloc.startTransfer(
          enableAwsLiveness: sunshine_app.moduleConfig?.shouldShowAwsLiveness(
            _bloc.model?.amount ?? 0,
          ),
        );
        if (!mounted) return;
        popUntilWithRoute(context, RoutePaths.transferHome);
        if (_bloc.model?.isNow == false) {
          go(
            context,
            TransferScheduleSuccess(
              model: _bloc.model,
              onPressNew: _onPressNew,
              onPressDone: _onPressDone,
              onPressCreateSchedule: _openSchedulePage,
            ),
          );
        } else {
          if (_bloc.model?.advanceTransaction != null) {
            go(
                context,
                TransferProgressPage(
                    transactionId: _bloc.model?.advancedTransactionId,
                    transferModel: _bloc.model,
                    onConfirm: (data) {
                      go(
                        context,
                        TransferSuccess(
                          model: _bloc.model,
                          bigModel: data,
                          onPressNew: _onPressNew,
                          onPressDone: _onPressDone,
                          onPressCreateSchedule: _openSchedulePageSuccess,
                          onPressHome: () =>
                              goToHomePage(context, popOnly: true),
                        ),
                      );
                    }));
          } else {
            go(
              context,
              TransferSuccess(
                model: _bloc.model,
                onPressNew: _onPressNew,
                onPressDone: _onPressDone,
                onPressCreateSchedule: _openSchedulePageSuccess,
                onPressHome: () => goToHomePage(context, popOnly: true),
              ),
            );
          }
        }
      }
    } else {
      if (!mounted) return;
      goToSetupToken(context);
    }
  }

  _onPressNew() {
    keyMoney.currentState?.setMoneyText('');
    _bloc.resetModel();
    widget.onPressNew?.call();
  }

  _onPressDone() {
    Navigator.of(context).pop();
    if (widget.onPressDone != null) {
      widget.onPressDone!();
    }
  }

  _openSchedulePageSuccess() {
    Navigator.of(context).pop();
    go(context, const PaymentToolV2(currentTab: 0));
  }

  _openSchedulePage() {
    Navigator.of(context).pop();
    if (widget.onPressNew != null) {
      widget.onPressNew!();
    }
  }

  _rowFee(data) {
    return StreamBuilder<BigMoneyNoteModel?>(
        stream: _bloc.bigAmountStream,
        builder: (context, snapshot) {
          if (!snapshot.hasData ||
              snapshot.data?.show != true ||
              _bloc.model?.is247 == false) {
            return const SizedBox();
          }
          return Padding(
            padding: EdgeInsets.only(top: kOuterPadding, bottom: kOuterPadding),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    S.of(context).separation_fee,
                    style: StyleApp.bodyText2(context),
                  ),
                ),
                Text(
                  snapshot.data?.params?[2].value ?? '',
                  style: StyleApp.bodyText2(context),
                )
              ],
            ),
          );
        });
  }

  _chooseBank(BaseItem bank, TransferModel data) {
    logger.t(bank);
    _pendingCheckName = false;
    bool isNotSupport247 =
        bank.bankIdNapas == null || bank.bankIdNapas?.isEmpty == true;
    if (data.isAccount == false && isNotSupport247) {
      DialogUtil.confirm(
        context,
        Text(S.of(context).not_support_247_via_card_number),
        onSubmit: () {
          _bloc.setIsAccount(true);
          _bloc.notifyModelChange();
          setState(() {});
        },
      );
      return;
    }
    if (data.isAccount != false && isNotSupport247 && data.is247 == true) {
      _showWaringTransferNormal();
    }
    _bloc.setTargetBank(bank, clearState: true);
    _keyboardBloc.setMoney(0.0);
    _amountTextController.clear();
  }

  void _onDetected(TransferModel? value) async {
    Navigator.of(context).pop();
    await _bloc.mergeModel(value);
    setMoneyText(value?.amount?.currencyFormat);
  }
}

class _AutocompleteOptions<T extends Object> extends StatelessWidget {
  const _AutocompleteOptions({
    Key? key,
    required this.displayForOption,
    required this.options,
    required this.maxOptionsHeight,
  }) : super(key: key);

  final Widget Function(T option) displayForOption;

  final Iterable<T> options;
  final double maxOptionsHeight;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topLeft,
      child: Padding(
        padding: const EdgeInsets.only(right: 32.0),
        child: Material(
          elevation: 3.0,
          child: ConstrainedBox(
            constraints: BoxConstraints(maxHeight: maxOptionsHeight),
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: options.length,
              itemBuilder: (BuildContext context, int index) {
                final T option = options.elementAt(index);
                return displayForOption(option);
              },
              separatorBuilder: (context, index) => const Divider(height: 1),
            ),
          ),
        ),
      ),
    );
  }
}
