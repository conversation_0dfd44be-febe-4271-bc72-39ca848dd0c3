part of "./sunshine_app.dart";

/// this part is for Thong tu 17, 18.
_initGlobalCallbackFor2345() {
  GlobalCallback.instance.callBackForBiology = CallBackForBiology(
    updateCCCD: updateCCCD,
    handleError: listenError,
    handleNotAuth: handleNotAuth,
    authFaceID: authFaceID,
    authFaceIDV2: authFaceIDV2,
    handleGoToLogin: handleGoToLogin,
  );
}

updateCCCD(
  BuildContext context, {
  required String phoneNumber,
  String? firstRoute,
  String? route,
}) async {
  if (!context.mounted) return;
  final result = await go(
    context,
    ViewCaptureWarningWidget(
      onTap: () async {
        if (context.mounted) Navigator.pop(context);
      },
    ),
  );

  if (result == null && context.mounted) {
    final value = await go(
      context,
      UpdateIdentificationCardPage(phoneNumber: phoneNumber),
    );

    if (value == true && context.mounted) {
      go(
        context,
        SuccessfulAuthenticationPage(
          onSuccess: () {
            Injection.injector
                .get<Environment>()
                .appEventSink
                .add(AppEvent.UPDATE_CHIP_ID);
            popUntilWithRoute(context, route ?? RoutePaths.home);
          },
        ),
      );
    } else if (!firstRoute.isNullOrEmpty && context.mounted) {
      popUntilWithRoute(context, firstRoute!);
    }
  } else if (result == true && context.mounted) {
    if (!firstRoute.isNullOrEmpty) popUntilWithRoute(context, firstRoute!);
  }
}

listenError(
  BuildContext context, {
  base_response_common.BaseResponseModel? status,
  Function? updateCCCD,
  Function? handleGoToLogin,
}) {
  if (status?.isSuccess != true &&
      status?.statusCode != 200 &&
      status?.message.isNullOrEmpty != true) {
    switch (status?.statusCode) {
      case StatusCode.NONE_FACE_LIMITED_TRANSACTION:
      case StatusCode.NEW_DEVICE_NOT_VALIDATE_FACE_ID:
        DialogUtil.alert(
          context,
          status!.message,
          submit: S.of(context).verify_now,
          onSubmit: () => updateCCCD?.call(),
          barrierDismissible: true,
        );
        break;
      case StatusCode.AUTH_BCA_ERROR_AND_LOCK_CIF:
        DialogUtil.alert(
          context,
          canPop: false,
          status?.message,
          onSubmit: () => handleGoToLogin?.call(),
        );
        break;
      default:
        DialogUtil.alert(context, status?.message);
        break;
    }
  }
}

handleGoToLogin(BuildContext context) => goToLogin(context);

handleNotAuth(
  BuildContext context, {
  required VoidCallback onCancel,
  Function? onSubmit,
  String? message,
}) {
  if (message.isNullOrEmpty) return;
  DialogUtil.alert(
    context,
    MarkdownCustom(
      data: message!,
      styleSheet: MarkdownStyleSheet(
        textAlign: Platform.isIOS ? WrapAlignment.center : WrapAlignment.start,
        p: Theme.of(context).dialogTheme.contentTextStyle,
        strong: Theme.of(context)
            .dialogTheme
            .contentTextStyle
            ?.copyWith(fontWeight: FontWeight.w600),
      ),
    ),
    submit: S.of(context).verify_now,
    onSubmit: () => onSubmit?.call(),
    barrierDismissible: true, // user can press other to close dialog,
    canPop: false,
  );
}

Future<dynamic> authFaceID(
  BuildContext context, {
  required String? transactionNo,
  required String phoneNumber,
  required String firstRoute,
  String? routeConfirm,
}) async {
  final bloc = Injection.injector.get<AuthTransactionEKycBloc>()
    ..setTransactionNo(transactionNo);
  final result = await go(
    context,
    AuthTransactionEKycSunshine(
      bloc: bloc,
      personId: phoneNumber,
      onAuthFailedThreeTimes: (message) => _onAuthFailedThreeTimes(
        context,
        firstRoute: firstRoute,
        phoneNumber: phoneNumber,
        message: message,
        routeConfirm: routeConfirm,
      ),
    ),
  );
  bloc.dispose();
  return result;
}

Future<bool?> authFaceIDV2(
  BuildContext context, {
  required String? transactionNo,
  required String? sessionId,
  required String? sessionToken,
  required String? region,
  required String? accessKeyId,
  required String? secretAccessKey,
  Function()? onComplete,
  ValueChanged<String>? onError,
}) async {
  final result = await go<bool>(
      context,
      FaceLivenessDetectorWidget(
        sessionId: sessionId ?? '',
        region: region ?? '',
        accessKeyId: accessKeyId,
        secretAccessKey: secretAccessKey,
        sessionToken: sessionToken,
        onComplete: onComplete,
        onError: onError ??
            (value) {
              String? message;
              Function? onSubmit;
              String? submitText;

              final livenssError = FaceLivenessError.fromCode(value);

              switch (livenssError) {
                case FaceLivenessError.sessionTimedOut:
                  message =
                      'Phiên làm việc đã hết thời gian. Vui lòng thử lại.';
                  break;
                case FaceLivenessError.sessionNotFound:
                  message = 'Không tìm thấy phiên làm việc. Vui lòng thử lại.';

                  break;
                case FaceLivenessError.userCancelled:
                  break;
                case FaceLivenessError.cameraPermissionDenied:
                  message =
                      'Ứng dụng cần quyền truy cập camera để xác thực. Vui lòng cấp quyền trong Cài đặt.';
                  onSubmit = () async {
                    // Mở cài đặt ứng dụng để người dùng có thể cấp quyền camera
                    await AppSettings.openAppSettings();
                    submitText = 'Mở Cài đặt';
                  };
                  break;
                case FaceLivenessError.accessDenied:
                  message = 'Không có quyền truy cập. Vui lòng liên hệ hỗ trợ.';
                  break;
                case FaceLivenessError.initializationError:
                  message = 'Lỗi khởi tạo hệ thống. Vui lòng thử lại sau.';
                  break;
                case FaceLivenessError.error:
                case null:
                  message =
                      'Đã xảy ra lỗi trong quá trình xác thực. Vui lòng thử lại.';
                  break;
              }

              if (!message.isNullOrEmpty) {
                DialogUtil.alert(
                  context,
                  message,
                  barrierDismissible: true,
                  onSubmit: onSubmit,
                  submit: submitText,
                );
              }
            },
      ));
  if (result is bool) return result;
  return false;
}

_onAuthFailedThreeTimes(
  BuildContext context, {
  required String phoneNumber,
  required String firstRoute,
  String? message,
  String? routeConfirm,
}) {
  if (message.isNullOrEmpty) return;
  DialogUtil.confirm(
    context,
    Text(message!),
    cancelText: S.of(context).common_update_now,
    submitText: S.of(context).retry,
    styleConfirm: const TextStyle(fontWeight: FontWeight.w600),
    onCancel: () {
      Navigator.pop(context);
      updateCCCD(
        context,
        phoneNumber: phoneNumber,
        firstRoute: firstRoute,
        route: routeConfirm,
      );
    },
    onSubmit: () => popUntilWithRoute(context, firstRoute),
  );
}

class MarkdownCustom extends MarkdownWidget {
  const MarkdownCustom({
    super.key,
    this.shrinkWrap = true,
    required super.data,
    super.styleSheet,
    super.imageBuilder,
  });

  final bool shrinkWrap;

  @override
  Widget build(BuildContext context, List<Widget>? children) {
    if (children!.length == 1) {
      var child = children.first;
      if (child is Column) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: child.children,
        );
      }
      return children.single;
    }
    return Column(
      mainAxisSize: shrinkWrap ? MainAxisSize.min : MainAxisSize.max,
      crossAxisAlignment:
          fitContent ? CrossAxisAlignment.start : CrossAxisAlignment.stretch,
      children: children,
    );
  }
}
