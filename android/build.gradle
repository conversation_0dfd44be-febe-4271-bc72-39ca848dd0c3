allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'app/libs'
        }
    }
}

ext {
    targetCompatibility = JavaVersion.VERSION_17
}

rootProject.buildDir = '../build'
subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    compileSdkVersion 35
                    if (namespace == null) {
                        namespace project.group
                    }
                }
            }
            // For qr_code_scanner
            // jvm target compatibility should be set to the same Java version
            if (project.hasProperty("kotlin")) {
                project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).all {
                    kotlinOptions {
                        jvmTarget = "1.8"
                    }
                }
            }
            
            // Force all projects to use Java 8 for compatibility
            if (project.hasProperty('android')) {
                project.android {
                    compileOptions {
                        sourceCompatibility JavaVersion.VERSION_1_8
                        targetCompatibility JavaVersion.VERSION_1_8
                    }
                }
            }
        }
}
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}
tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
