#=== Test proguard-rules.pro quick ===#
# cd android
# ./gradlew clean bundleRelease -p . -PversionCode=1
# cd ../build/app/outputs/bundle/release
# bundletool build-apks --bundle=app-release.aab --output=app-release.apks --mode=universal
# mv app-release.apks app-release.zip
# unzip app-release.zip 
# adb install -r universal.apk

#=== Flutter Wrapper ===#
# https://stackoverflow.com/a/76806805
#-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
#-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

-include proguard-rules-jitsi.pro
-include proguard-rules-mlkit.pro

-keep class com.facebook.systrace.** { *; }

-keepclassmembers class com.android.installreferrer.api.** {
  *;
}

# Hermes
-keep class com.facebook.hermes.unicode.** { *; }

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
-keepclassmembers class **.R$* {
    public static <fields>;
}
# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.
-dontwarn android.support.**
# Needed by google-http-client to keep generic types and @Key annotations accessed via reflection
-keepclassmembers class * {
  @com.google.api.client.util.Key <fields>;
}
# Needed just to be safe in terms of keeping Google API service model classes
-keep class com.google.api.services.*.model.*
-keep class com.google.api.client.**
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault
# See https://groups.google.com/forum/#!topic/guava-discuss/YCZzeCiIVoI
-dontwarn com.google.common.collect.MinMaxPriorityQueue
-dontobfuscate
# Assume dependency libraries Just Work(TM)
-dontwarn com.google.android.youtube.**
-dontwarn com.google.android.analytics.**
-dontwarn com.google.common.**
# Don't discard Guava classes that raise warnings
-keep class com.google.common.collect.MapMakerInternalMap$ReferenceEntry
-keep class com.google.common.cache.LocalCache$ReferenceEntry
# Make sure that Google Analytics doesn't get removed
-keep class com.google.analytics.tracking.android.CampaignTrackingReceiver
## BEGIN -- Google Play Services proguard.txt
-keep class * extends java.util.ListResourceBundle {
    protected java.lang.Object[][] getContents();
}
# Keep SafeParcelable value, needed for reflection. This is required to support backwards
# compatibility of some classes.
-keep public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}
# Keep the names of classes/members we need for client functionality.
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
    @com.google.android.gms.common.annotation.KeepName *;
}
# Needed for Parcelable/SafeParcelable Creators to not get stripped
-keepnames class * implements android.os.Parcelable {
    public static final ** CREATOR;
}
## END -- Google Play Services proguard.txt
# Other settings
-keep class com.android.**
-keep class com.google.android.**
-keep class com.google.android.gms.**
-keep class com.google.android.gms.location.**
-keep class com.google.api.client.**
-keep class com.google.maps.android.**
-keep class androidx.appcompat.** { *; }
-keep class libcore.**
-dontwarn org.junit.**
-dontwarn org.mockito.**
-dontwarn org.robolectric.**
-dontwarn com.bumptech.glide.GlideTest

## Keep class passport reader plugin nfc
-keep class net.sf.scuba.** { *; }
-keep class org.jmrtd.** { *; }
-keep class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

#vnpay sdks
-keep class com.vnpay.** { *; }
-dontwarn com.vnpay.**
-keep class com.google.android.gms.location.** { *; }
-dontwarn com.google.android.gms.location.**
-keep class kotlinx.** { *; }
-keep class kotlin.** { *; }

## AWS SDK Kotlin keep rules
-keep class aws.sdk.kotlin.** { *; }
-keep class aws.smithy.kotlin.** { *; }
-keep class kotlinx.coroutines.** { *; }
-dontwarn aws.sdk.kotlin.**
-dontwarn aws.smithy.kotlin.**
-dontwarn kotlinx.coroutines.**

