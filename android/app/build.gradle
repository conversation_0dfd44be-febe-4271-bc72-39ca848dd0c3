plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def firebaseCoreProject = findProject(':firebase_core')
if (firebaseCoreProject == null) {
    throw new GradleException('Could not find the firebase_core FlutterFire plugin, have you added it as a dependency in your pubspec?')
} else if (!firebaseCoreProject.properties['FirebaseSDKVersion']) {
    throw new GradleException('A newer version of the firebase_core FlutterFire plugin is required, please update your firebase_core pubspec dependency.')
}

def getRootProjectExtOrCoreProperty(name, firebaseCoreProject) {
    if (!rootProject.ext.has('FlutterFire')) return firebaseCoreProject.properties[name]
    if (!rootProject.ext.get('FlutterFire')[name]) return firebaseCoreProject.properties[name]
    return rootProject.ext.get('FlutterFire').get(name)
}

android {
    namespace "com.sunshine.mobile_banking"

    compileSdkVersion 35
    ndkVersion '27.0.********'

    packagingOptions {
        pickFirst '**/*.so'
    }

    externalNativeBuild {
        cmake {
            path "../CMakeLists.txt"
        }
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = "1.8"
        freeCompilerArgs += ["-Xsuppress-version-warnings"]
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.kienlongbank.mobile_banking"
        minSdkVersion 24
        targetSdkVersion 35
        multiDexEnabled true
        versionCode project.hasProperty('versionCode') ? project.property('versionCode') as int : flutterVersionCode.toInteger()
        versionName flutterVersionName
        vectorDrawables.useSupportLibrary = true
    }

    signingConfigs {
        release {
            keyAlias 'super-app'
            keyPassword 'Sunshine@123'
            storeFile file('../sunshine_keystore.jks')
            storePassword 'Sunshine@2017'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            // Add below 3 lines for proguard
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug

            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        viewBinding true
    }

    // enable viewbiding
    buildFeatures {
        viewBinding = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.google.mlkit:face-detection:16.1.5'
    implementation platform('com.google.firebase:firebase-bom:28.3.0')
    implementation "com.google.firebase:firebase-messaging-ktx"
    implementation "com.google.firebase:firebase-crashlytics"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

    ///vietlott
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation "androidx.appcompat:appcompat:1.3.1"
    implementation "androidx.activity:activity-ktx:1.2.0-alpha03"
    implementation 'com.google.android.gms:play-services-location:18.0.0'

    // ViewModel and LiveData"
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.2.0")
    implementation("androidx.lifecycle:lifecycle-common-java8:2.0.0")
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"

    // GSON FOR NETWORK
    implementation 'com.google.code.gson:gson:2.8.5'

    // LOAD IMAGE
    implementation 'com.squareup.picasso:picasso:2.5.2'

    //pdf viewer
    // implementation 'com.github.DImuthuUpe:AndroidPdfViewer:3.0.0-beta.1'  // Commented out to avoid conflict


    // OKHTTP NETWORK
    implementation 'com.squareup.okhttp3:okhttp:4.7.2'
    //RETROFIT
    implementation "com.squareup.retrofit2:retrofit:2.6.0"
    implementation "com.squareup.retrofit2:converter-gson:2.5.0"
    implementation("com.squareup.okhttp3:logging-interceptor:3.12.2")

    // VNPay SDKs
    implementation files('libs/vnpay_sdks/com/vietlott/sdkcorekit/sdkcorekit/2.0.17-NTkyLjAuMTc=/sdkcorekit-2.0.17-NTkyLjAuMTc=.aar')
    implementation files('libs/vnpay_sdks/com/vietlott/miniapp/miniapp/1.4.11-NjYxLjQuMTE=/miniapp-1.4.11-NjYxLjQuMTE=.aar')
    implementation files('libs/vnpay_sdks/com/vietlott/vnpsdkiframe/vnpsdkiframe/1.5.18-NjYxLjUuMTg=/vnpsdkiframe-1.5.18-NjYxLjUuMTg=.aar')
    implementation files('libs/vnpay_sdks/com/vietlott/networkconnect/networkconnect/vnpnetworkconnect-1.0.11-NDl2bnBuZXR3b3JrY29ubmVjdC0xLjAuMTE=/networkconnect-vnpnetworkconnect-1.0.11-NDl2bnBuZXR3b3JrY29ubmVjdC0xLjAuMTE=.aar')
    implementation files('libs/vnpay_sdks/com/vietlott/supersecure/supersecure/1.0.22/supersecure-1.0.22.aar')
}
