PODS:
  - Alamofire (4.9.1)
  - app_settings (5.1.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity (0.0.1):
    - Flutter
    - Reachability
  - contacts_service (0.2.2):
    - Flutter
  - CryptoSwift (1.8.4)
  - device_info (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Analytics (10.25.0):
    - Firebase/Core
  - Firebase/Core (10.25.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.25.0)
  - Firebase/CoreOnly (10.25.0):
    - FirebaseCore (= 10.25.0)
  - Firebase/Crashlytics (10.25.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.25.0)
  - Firebase/Messaging (10.25.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.25.0)
  - Firebase/RemoteConfig (10.25.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.25.0)
  - firebase_analytics (10.10.7):
    - Firebase/Analytics (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_core (2.32.0):
    - Firebase/CoreOnly (= 10.25.0)
    - Flutter
  - firebase_crashlytics (3.5.7):
    - Firebase/Crashlytics (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.9.4):
    - Firebase/Messaging (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (4.4.7):
    - Firebase/RemoteConfig (= 10.25.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.25.0):
    - FirebaseAnalytics/AdIdSupport (= 10.25.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.25.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.25.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.25.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.29.0)
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_jailbreak_detection (1.0.0):
    - Flutter
    - IOSSecuritySuite
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - geocoding (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - Giphy (2.1.20):
    - libwebp
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps
  - google_mlkit_barcode_scanning (0.12.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - google_mlkit_commons (0.8.1):
    - Flutter
    - MLKitVision
  - google_mlkit_face_detection (0.11.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/FaceDetection (~> 6.0.0)
  - google_mlkit_text_recognition (0.13.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/TextRecognition (~> 6.0.0)
  - GoogleAppMeasurement (10.25.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.25.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.25.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleMLKit/BarcodeScanning (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 5.0.0)
  - GoogleMLKit/FaceDetection (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitFaceDetection (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleMLKit/TextRecognition (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitTextRecognition (~> 4.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (3.5.0)
  - home_widget (0.0.1):
    - Flutter
  - http_certificate_pinning (1.0.3):
    - Alamofire (~> 4.9.1)
    - CryptoSwift
    - Flutter
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_gallery_saver (2.0.2):
    - Flutter
  - IOSSecuritySuite (2.2.0)
  - jitsi_meet (0.0.1):
    - Flutter
    - JitsiMeetSDK
  - JitsiMeetSDK (6.2.1):
    - Giphy (= 2.1.20)
    - JitsiWebRTC (~> 106.0)
  - JitsiWebRTC (106.0.0)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - map_launcher (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta5)
  - MLKitBarcodeScanning (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitFaceDetection (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitTextRecognition (4.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitTextRecognitionCommon (= 3.0.0)
    - MLKitVision (~> 7.0)
  - MLKitTextRecognitionCommon (3.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - mobile_scanner (5.1.1):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - NFCPassportReader (1.1.9):
    - OpenSSL-Universal (= 1.1.180)
  - onesignal_flutter (5.0.3):
    - Flutter
    - OneSignalXCFramework (= 5.0.2)
  - OneSignalXCFramework (5.0.2):
    - OneSignalXCFramework/OneSignalComplete (= 5.0.2)
  - OneSignalXCFramework/OneSignal (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.0.2):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.0.2)
  - OneSignalXCFramework/OneSignalExtension (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.0.2):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.0.2):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.0.2):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - open_file_ios (0.0.1):
    - Flutter
  - OpenSSL-Universal (1.1.180)
  - package_info (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - passport_reader_plugin (0.0.1):
    - Flutter
    - NFCPassportReader (~> 1.1.9)
  - permission_handler_apple (9.3.0):
    - Flutter
  - Pods_VNPaySDKCoreKit (2.0.60)
  - Pods_VNPaySDKCoreMiniApp (1.4.32):
    - Pods_VNPaySDKCoreKit
  - Pods_VNPSDKIFrame (1.4.24):
    - Pods_VNPaySDKCoreKit
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - Reachability (3.7.6)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - share (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_ios (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - SSZipArchive (2.4.3)
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.6.1)
  - uni_links (0.0.1):
    - Flutter
  - vibration (1.7.5):
    - Flutter
  - video_compress (0.3.0):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity (from `.symlinks/plugins/connectivity/ios`)
  - contacts_service (from `.symlinks/plugins/contacts_service/ios`)
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Firebase/Messaging
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_jailbreak_detection (from `.symlinks/plugins/flutter_jailbreak_detection/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geocoding (from `.symlinks/plugins/geocoding/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_mlkit_barcode_scanning (from `.symlinks/plugins/google_mlkit_barcode_scanning/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_face_detection (from `.symlinks/plugins/google_mlkit_face_detection/ios`)
  - google_mlkit_text_recognition (from `.symlinks/plugins/google_mlkit_text_recognition/ios`)
  - GoogleUtilities
  - home_widget (from `.symlinks/plugins/home_widget/ios`)
  - http_certificate_pinning (from `.symlinks/plugins/http_certificate_pinning/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - jitsi_meet (from `.symlinks/plugins/jitsi_meet/ios`)
  - JitsiMeetSDK (from `https://git-internal.kienlongbank.co/shared/jitsi-meet-ios-sdk-releases.git`, tag `6.2.1`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - onesignal_flutter (from `.symlinks/plugins/onesignal_flutter/ios`)
  - OneSignalXCFramework (< 6.0, >= 5.0.0)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - passport_reader_plugin (from `.symlinks/plugins/passport_reader_plugin/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - Pods_VNPaySDKCoreKit (from `./VNPaySDK/VietLott/CoreKit`)
  - Pods_VNPaySDKCoreMiniApp (from `./VNPaySDK/VietLott/CoreSDK`)
  - Pods_VNPSDKIFrame (from `./VNPaySDK/VietLott/Iframe`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - SSZipArchive (= 2.4.3)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)

SPEC REPOS:
  trunk:
    - Alamofire
    - CryptoSwift
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - FMDB
    - Giphy
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - IOSSecuritySuite
    - JitsiWebRTC
    - libwebp
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitFaceDetection
    - MLKitTextRecognition
    - MLKitTextRecognitionCommon
    - MLKitVision
    - MTBBarcodeScanner
    - nanopb
    - NFCPassportReader
    - OneSignalXCFramework
    - OpenSSL-Universal
    - PromisesObjC
    - PromisesSwift
    - Reachability
    - SDWebImage
    - SSZipArchive
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity:
    :path: ".symlinks/plugins/connectivity/ios"
  contacts_service:
    :path: ".symlinks/plugins/contacts_service/ios"
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_jailbreak_detection:
    :path: ".symlinks/plugins/flutter_jailbreak_detection/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geocoding:
    :path: ".symlinks/plugins/geocoding/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_mlkit_barcode_scanning:
    :path: ".symlinks/plugins/google_mlkit_barcode_scanning/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_face_detection:
    :path: ".symlinks/plugins/google_mlkit_face_detection/ios"
  google_mlkit_text_recognition:
    :path: ".symlinks/plugins/google_mlkit_text_recognition/ios"
  home_widget:
    :path: ".symlinks/plugins/home_widget/ios"
  http_certificate_pinning:
    :path: ".symlinks/plugins/http_certificate_pinning/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  jitsi_meet:
    :path: ".symlinks/plugins/jitsi_meet/ios"
  JitsiMeetSDK:
    :git: https://git-internal.kienlongbank.co/shared/jitsi-meet-ios-sdk-releases.git
    :tag: 6.2.1
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  onesignal_flutter:
    :path: ".symlinks/plugins/onesignal_flutter/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  passport_reader_plugin:
    :path: ".symlinks/plugins/passport_reader_plugin/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  Pods_VNPaySDKCoreKit:
    :path: "./VNPaySDK/VietLott/CoreKit"
  Pods_VNPaySDKCoreMiniApp:
    :path: "./VNPaySDK/VietLott/CoreSDK"
  Pods_VNPSDKIFrame:
    :path: "./VNPaySDK/VietLott/Iframe"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"

CHECKOUT OPTIONS:
  JitsiMeetSDK:
    :git: https://git-internal.kienlongbank.co/shared/jitsi-meet-ios-sdk-releases.git
    :tag: 6.2.1

SPEC CHECKSUMS:
  Alamofire: 85e8a02c69d6020a0d734f6054870d7ecb75cf18
  app_settings: 3507c575c2b18a462c99948f61d5de21d4420999
  camera_avfoundation: 6841f9d1468415f905b3f5ea52ab1466ca506730
  connectivity: e7d9fd79099f39025737062855c00248b7afcdf0
  contacts_service: 7c2ed10147f55891576c6a582f431e385d8de92d
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  device_info: 52e8c0c9c61def8d0a92bf175f5f500abbea04bc
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  Firebase: 0312a2352584f782ea56f66d91606891d4607f06
  firebase_analytics: e7280be84e7ff1c1e5859bd8e48de410b07e0b73
  firebase_core: 3b49a055ff54114cae400581c13671fe53936c36
  firebase_crashlytics: 66f38fa4dd09ba785fb909ee14e97b02693162f4
  firebase_messaging: 30fa3ec8cd0dc8a860b7817548911b97345a0875
  firebase_remote_config: 3822d0d58afaeccfbeac01b5d3565dd506884287
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAnalytics: ec00fe8b93b41dc6fe4a28784b8e51da0647a248
  FirebaseCore: 7ec4d0484817f12c3373955bc87762d96842d483
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 4b96efb0ce73b38b2a85e8b8bd1bd8f63f09d015
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 88950ba9485052891ebe26f6c43a52bb62248952
  FirebaseRemoteConfig: 9f3935cefecd85d5b312192117f444957de24a75
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  FirebaseSharedSwift: 20530f495084b8d840f78a100d8c5ee613375f6e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_downloader: 78da0da1084e709cbfd3b723c7ea349c71681f09
  flutter_jailbreak_detection: a74360daaf5cbc80639959463b4b1395873c90b5
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 2c2ff13db9e0a5647389bff88b0ecac56e3f3418
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  geocoding: 942dfd796e4eae1e70d4f79d68cacc3482267bc9
  geolocator_apple: 1d9e8e718b0aee61b8573d2d531eff76d00f383b
  Giphy: b6d5087521d251bb8c99cdc0eb07bbdf86d142d5
  google_maps_flutter_ios: d2d2ab8df20d7bbc79ea6f776bf468754b8d5d15
  google_mlkit_barcode_scanning: 27e95b3b57f7b3eb25fbff20176a61c807e0a17c
  google_mlkit_commons: 414fc36ac51758c806381de33a1d148d2ba71ce4
  google_mlkit_face_detection: d782e03ecc0b87ab828ab41ed6a865b04ac100f4
  google_mlkit_text_recognition: 6dd0122107954e64667d826da71de686152109fe
  GoogleAppMeasurement: 9abf64b682732fed36da827aa2a68f0221fd2356
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  home_widget: f169fc41fd807b4d46ab6615dc44d62adbf9f64f
  http_certificate_pinning: a1b2e2a4ccd3eb331aa236e6a48809001b5b44e8
  image_cropper: 655b3ba703c9e15e3111e79151624d6154288774
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  IOSSecuritySuite: ba76f34cb367b9230934ac37904e26a40d033e3d
  jitsi_meet: 13f2608990926f402a634a6e1680b290515e52a4
  JitsiMeetSDK: 3b1d115e80c6cd2ef961266aa3b5558fbcdbb01d
  JitsiWebRTC: f441eb0e2d67f0588bf24e21c5162e97342714fb
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  map_launcher: fe43bda6720bb73c12fcc1bdd86123ff49a4d4d6
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitBarcodeScanning: 10ca0845a6d15f2f6e911f682a1998b68b973e8b
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitFaceDetection: 7c0e8bf09ddd27105da32d088fca978a99fc30cc
  MLKitTextRecognition: c83c18ad25496f2077f6ec93c5940487ff2eb343
  MLKitTextRecognitionCommon: c0b3a63d529296a19bce1f8bc8a513644ed4d1f6
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  mobile_scanner: ba17a89d6a2d1847dad8cad0335856fd4b4ce1f6
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  NFCPassportReader: 3a4b15d28c89a1f747c957f2bce1230c268dba25
  onesignal_flutter: 3bf9ddeffd92e428b400965400ff28a1bef0865c
  OneSignalXCFramework: d47a350b15e106a5dc310e3f97867e28dad6589e
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  package_info: cce50adca9873c79f931618469d2114b91d71189
  package_info_plus: 580e9a5f1b6ca5594e7c9ed5f92d1dfb2a66b5e1
  passport_reader_plugin: 858a0fc660e00002a8fbec429023ff6221e79d29
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  Pods_VNPaySDKCoreKit: e8e3906b415d13088b07193e08ef4b8ec352e3b5
  Pods_VNPaySDKCoreMiniApp: dea4ce4b9c1b1fc9f1b4e6a2c65b19d0007e4ec4
  Pods_VNPSDKIFrame: 8736ebb6790bf39917858fd18d3e0936242f3bc6
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  Reachability: fd0ecd23705e2599e4cceeb943222ae02296cbc6
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  share: a34936589f3090d59481bcdc5c30cc9dd47c75f6
  share_plus: 011d6fb4f9d2576b83179a3a5c5e323202cdabcf
  shared_preferences_ios: 7ee5572d25d2f093bc27a9ede31993d8737a711d
  sqflite: 954affaf2567c73cda074440299a625e3b2cbf8a
  SSZipArchive: fe6a26b2a54d5a0890f2567b5cc6de5caa600aef
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  uni_links: ed8c961e47ed9ce42b6d91e1de8049e38a4b3152
  vibration: 8e2f50fc35bb736f9eecb7dd9f7047fbb6a6e888
  video_compress: f2133a07762889d67f0711ac831faa26f956980e

PODFILE CHECKSUM: 0a61c65f5b86ee7c4380fee9f4cfc7cda1ab3002

COCOAPODS: 1.16.2
