# Uncomment this line to define a global platform for your project
platform :ios, '14.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

# Đ<PERSON>nh nghĩa biến đúng cú pháp Ruby
VNPAY_IOS_SDK_IFRAME_URL = "./VNPaySDK/VietLott/Iframe"
VNPAY_IOS_SDK_CORESDK_URL = "./VNPaySDK/VietLott/CoreSDK"
VNPAY_IOS_SDK_COREKIT_URL = "./VNPaySDK/VietLott/CoreKit"

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!


  pod 'JitsiMeetSDK', :git => 'https://git-internal.kienlongbank.co/shared/jitsi-meet-ios-sdk-releases.git', :tag => '6.2.1'

  #Fix error: Multiple commands produce GoogleUtilities
  #follow: https://github.com/orgs/codemagic-ci-cd/discussions/2416
  pod 'GoogleUtilities'

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  pod 'Pods_VNPaySDKCoreKit', :path => VNPAY_IOS_SDK_COREKIT_URL
  pod 'Pods_VNPaySDKCoreMiniApp', :path => VNPAY_IOS_SDK_CORESDK_URL
  pod 'SSZipArchive', '2.4.3'
  pod 'Pods_VNPSDKIFrame', :path => VNPAY_IOS_SDK_IFRAME_URL
end

target 'OneSignalNotificationServiceExtension' do
  use_frameworks!
#   pod 'OneSignal', '>= 2.9.3', '< 3.0'
    pod 'OneSignalXCFramework', '>= 5.0.0', '< 6.0'
    pod 'Firebase/Messaging'

    #Fix error: Multiple commands produce GoogleUtilities
    #follow: https://github.com/orgs/codemagic-ci-cd/discussions/2416
    pod 'GoogleUtilities'
end

post_install do |installer|
  bitcode_strip_path = `xcrun --find bitcode_strip`.chop!

    def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      framework_path = File.join(Dir.pwd, framework_relative_path)
      if File.exist?(framework_path)
        command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
        puts "Stripping bitcode: #{command}"
        system(command)
      else
        puts "Framework not found: #{framework_path}"
      end
    end

   framework_paths = [
       "Pods/Giphy/GiphySDK/GiphyUISDK.xcframework/ios-arm64_armv7/GiphyUISDK.framework/GiphyUISDK",
       "Pods/OpenSSL-Universal/Frameworks/OpenSSL.xcframework/ios-arm64_arm64e_armv7_armv7s/OpenSSL.framework/OpenSSL"
     ]

   framework_paths.each do |framework_relative_path|
      strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
   end

  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    # Workaround https://github.com/flutter/flutter/issues/111475.
    target_is_resource_bundle = target.respond_to?(:product_type) && target.product_type == 'com.apple.product-type.bundle'
    target.build_configurations.each do |config|
      if target_is_resource_bundle
        config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        config.build_settings['CODE_SIGNING_REQUIRED'] = 'NO'
        config.build_settings['CODE_SIGNING_IDENTITY'] = '-'
        config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = '-'
      end
    end

    target.build_configurations.each do |config|
          config.build_settings['SUPPORTS_MACCATALYST'] = 'NO'
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = "14.0"
          config.build_settings['ENABLE_BITCODE'] = 'NO'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
                            '$(inherited)',

                                    ## dart: PermissionGroup.calendar
                                    'PERMISSION_EVENTS=1',

                                    ## dart: PermissionGroup.reminders
                                    'PERMISSION_REMINDERS=1',

                                    ## dart: PermissionGroup.contacts
                                    'PERMISSION_CONTACTS=1',

                                    ## dart: PermissionGroup.camera
                                    'PERMISSION_CAMERA=1',

                                    ## dart: PermissionGroup.microphone
                                    'PERMISSION_MICROPHONE=1',

                                    ## dart: PermissionGroup.speech
                                    'PERMISSION_SPEECH_RECOGNIZER=1',

                                    ## dart: PermissionGroup.photos
                                    'PERMISSION_PHOTOS=1',

                                    ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
                                    'PERMISSION_LOCATION=1',

                                    ## dart: PermissionGroup.notification
                                    'PERMISSION_NOTIFICATIONS=1',

                                    ## dart: PermissionGroup.mediaLibrary
                                    'PERMISSION_MEDIA_LIBRARY=1',

                                    ## dart: PermissionGroup.sensors
                                    'PERMISSION_SENSORS=1',

                                    ## dart: PermissionGroup.bluetooth
                                    'PERMISSION_BLUETOOTH=1',

                                    ## dart: PermissionGroup.appTrackingTransparency
                                    'PERMISSION_APP_TRACKING_TRANSPARENCY=1',

                                    ## dart: PermissionGroup.criticalAlerts
                                    'PERMISSION_CRITICAL_ALERTS=1',
                                    ]
    end
  end
end



