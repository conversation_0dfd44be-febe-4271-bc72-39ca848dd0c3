<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>vi</string>
	</array>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>KLB-STAGING</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>KienlongBank App muốn truy cập bộ sưu tập để lưu trữ video clip hoặc viết bình luận bằng audio</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>KienlongBank App muốn truy cập Bluetooth để xác định vị trí của bạn trong căn hộ khi bạn ghé thăm</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>KienlongBank App muốn truy cập Bluetooth để xác định vị trí của bạn trong căn hộ khi bạn ghé thăm</string>
	<key>NSCalendarsUsageDescription</key>
	<string>KienlongBank App muốn lịch truy cập để giúp bạn đặt hẹn với nhân viên bán hàng</string>
	<key>NSCameraUsageDescription</key>
	<string>KienlongBank sử dụng máy ảnh trong chụp ảnh chứng từ, quét mã thanh toán, chuyển tiền </string>
	<key>NSContactsUsageDescription</key>
	<string>KienlongBank App cần truy cập danh bạ để lấy số điện thoại</string>
	<key>NSFaceIDUsageDescription</key>
	<string>KienlongBank App sử dụng FaceID để  xác thực sinh trắc học một cách nhanh chóng</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>KienlongBank sử dụng vị trí trong tìm chi nhánh, đường đi, trợ giúp</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>KienlongBank sử dụng vị trí trong tìm chi nhánh, đường đi, trợ giúp</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>KienlongBank sử dụng vị trí trong tìm chi nhánh, đường đi, trợ giúp</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>KienlongBank cần truy cập microphone trong giao tiếp với bạn</string>
	<key>NSMotionUsageDescription</key>
	<string>KienlongBank App sử dụng cảm biến chuyển động để xem ảnh 360 độ</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>KienlongBank cần truy cập kho ảnh trong thanh toán, chuyển tiền, cá nhân hóa ứng dụng</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>KienlongBank cần truy cập kho ảnh trong thanh toán, chuyển tiền, cá nhân hóa ứng dụng</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>KienlongBank App hỗ trợ tìm kiếm bằng giọng nói</string>
	<key>FlutterDeepLinkingEnabled</key>
    <true/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>ksbank.co</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ksbank</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>lixi.kienlongbank.com</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>https</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>lixi.kienlongbank.co</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>https</string>
			</array>
		</dict>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
        <key>NSAllowsArbitraryLoadsInWebContent</key>
        <true/>
    </dict>
    <key>LSApplicationQueriesSchemes</key>
    <array>
        <string>comgooglemaps</string>
        <string>baidumap</string>
        <string>iosamap</string>
        <string>waze</string>
        <string>yandexmaps</string>
        <string>yandexnavi</string>
        <string>citymapper</string>
        <string>mapswithme</string>
        <string>osmandmaps</string>
        <string>dgis</string>
        <string>qqmap</string>
        <string>cydia</string>
        <string>undecimus</string>
        <string>sileo</string>
        <string>zbra</string>
        <string>ksfinance</string>
    </array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NFCReaderUsageDescription</key>
    <string>KienlongBank App sử dụng NFC để đọc thông tin CCCD gắn chip</string>
    <key>com.apple.developer.nfc.readersession.formats</key>
    <array>
        <string>NDEF</string>
        <string>TAG</string>
    </array>
    <key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
    <array>
        <string>A0000002471001</string>
    </array>
    <key>FLTEnableImpeller</key>
    <true/>
</dict>
</plist>
